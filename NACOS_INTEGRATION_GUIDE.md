# Nexus微服务与现有Nacos配置整合指南

## 🎯 整合策略

基于您现有的`nexus-control-plane.yaml`配置，我们采用以下整合策略：

### 1. 配置共存
- **保留现有配置**: `nexus-control-plane.yaml` 保持不变
- **新增微服务配置**: 为新的6个微服务创建独立配置文件
- **共享基础设施**: 使用相同的数据库、Redis、RabbitMQ

### 2. 资源隔离
- **数据库**: 共享同一个PostgreSQL数据库，使用不同的表前缀
- **Redis**: 使用不同的数据库编号避免冲突
- **端口**: 分配不同的端口避免冲突

## 📋 配置文件清单

### 现有配置（保持不变）
- `nexus-control-plane.yaml` - 现有控制平面配置

### 新增配置文件
1. `nexus-common-config.yml` - 公共配置（数据库、Redis、RabbitMQ）
2. `nexus-auth-service.yml` - 认证服务配置
3. `nexus-mcp-local-service.yml` - 本地MCP服务配置
4. `nexus-mcp-remote-service.yml` - 远程MCP服务配置
5. `nexus-subscription-service.yml` - 订阅服务配置
6. `nexus-gateway.yml` - API网关配置

## 🔧 配置导入步骤

### 步骤1: 准备Nacos环境
确保Nacos服务正在运行并可访问。

### 步骤2: 创建命名空间（可选）
在Nacos控制台创建新的命名空间：
- 命名空间ID: `nexus-microservices`
- 命名空间名: `Nexus微服务`
- 描述: `Nexus微服务配置命名空间`

### 步骤3: 导入配置文件

**方式A: 使用脚本自动导入**
```bash
cd nacos-configs
chmod +x import-configs.sh
./import-configs.sh http://localhost:8848 nacos nacos
```

**方式B: 手动导入**
1. 登录Nacos控制台: http://localhost:8848/nacos
2. 进入"配置管理" -> "配置列表"
3. 点击"+"创建配置
4. 逐个导入以下配置：

| Data ID | Group | 格式 | 描述 |
|---------|-------|------|------|
| nexus-common-config.yml | DEFAULT_GROUP | YAML | 公共配置 |
| nexus-auth-service.yml | DEFAULT_GROUP | YAML | 认证服务 |
| nexus-mcp-local-service.yml | DEFAULT_GROUP | YAML | 本地MCP服务 |
| nexus-mcp-remote-service.yml | DEFAULT_GROUP | YAML | 远程MCP服务 |
| nexus-subscription-service.yml | DEFAULT_GROUP | YAML | 订阅服务 |
| nexus-gateway.yml | DEFAULT_GROUP | YAML | API网关 |

## 🗄️ 数据库配置说明

### 共享数据库配置
所有微服务使用相同的Neon PostgreSQL数据库：
```yaml
spring:
  datasource:
    url: *****************************************************************************************************************************************************************************
    username: neondb_owner
    password: npg_kc2S7QGCPbEh
```

### 表结构兼容性
新的微服务将创建以下表：
- `users` - 用户表
- `api_keys` - API密钥表
- `service_configs` - 服务配置表
- `subscriptions` - 订阅表
- `permissions` - 权限表
- `local_mcp_services` - 本地MCP服务表
- `remote_mcp_services` - 远程MCP服务表

这些表与现有的control-plane表不冲突。

## 🔴 Redis数据库分配

为避免数据冲突，各服务使用不同的Redis数据库：

| 服务 | Redis DB | 用途 |
|------|----------|------|
| nexus-control-plane | 0 | 现有服务（保持不变） |
| nexus-auth-service | 1 | 认证缓存、JWT令牌 |
| nexus-mcp-remote-service | 2 | 远程服务缓存 |
| nexus-mcp-local-service | 3 | 本地服务状态 |
| nexus-subscription-service | 4 | 订阅和权限缓存 |
| nexus-gateway | 5 | 限流、路由缓存 |

## 🚪 端口分配

| 服务 | 端口 | 说明 |
|------|------|------|
| nexus-control-plane | 8080 | 现有服务（需要修改） |
| nexus-gateway | 8080 | API网关（新的统一入口） |
| nexus-auth-service | 8081 | 认证服务 |
| nexus-mcp-local-service | 8082 | 本地MCP服务 |
| nexus-mcp-remote-service | 8083 | 远程MCP服务 |
| nexus-subscription-service | 8084 | 订阅服务 |

**重要**: 需要将现有的control-plane端口改为其他端口（如8085），让新的gateway使用8080作为统一入口。

## 🔄 服务启动顺序

建议按以下顺序启动服务：

1. **基础设施**: PostgreSQL, Redis, RabbitMQ, Nacos
2. **认证服务**: nexus-auth-service (8081)
3. **订阅服务**: nexus-subscription-service (8084)
4. **MCP服务**: nexus-mcp-local-service (8082), nexus-mcp-remote-service (8083)
5. **API网关**: nexus-gateway (8080)
6. **现有服务**: nexus-control-plane (修改为8085端口)

## 🔧 现有服务修改建议

### 1. 修改control-plane端口
在`nexus-control-plane.yaml`中修改：
```yaml
server:
  port: 8085  # 从8080改为8085
```

### 2. 集成认证服务
现有的control-plane可以调用新的认证服务进行用户验证：
```yaml
# 在control-plane配置中添加
feign:
  client:
    config:
      nexus-auth-service:
        url: http://localhost:8081/auth
```

### 3. 使用统一网关
所有外部请求通过新的gateway (8080) 进入，gateway会路由到相应的服务。

## 🚀 启动新的微服务架构

### 使用Nacos配置启动
修改各服务的`application.yml`，启用Nacos配置：

```yaml
spring:
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        namespace: nexus-microservices  # 如果使用了新命名空间
      config:
        server-addr: 127.0.0.1:8848
        namespace: nexus-microservices  # 如果使用了新命名空间
        file-extension: yml
        shared-configs:
          - data-id: nexus-common-config.yml
            group: DEFAULT_GROUP
            refresh: true
```

### 启动命令
```bash
# 启动各个微服务
java -jar nexus-auth-service.jar --spring.profiles.active=nacos
java -jar nexus-subscription-service.jar --spring.profiles.active=nacos
java -jar nexus-mcp-local-service.jar --spring.profiles.active=nacos
java -jar nexus-mcp-remote-service.jar --spring.profiles.active=nacos
java -jar nexus-gateway.jar --spring.profiles.active=nacos
```

## 🔍 验证配置

### 1. 检查Nacos配置
访问Nacos控制台，确认所有配置文件已正确导入。

### 2. 检查服务注册
在Nacos的"服务管理"中查看服务注册情况。

### 3. 测试API访问
```bash
# 测试网关健康检查
curl http://localhost:8080/actuator/health

# 测试认证服务
curl http://localhost:8080/auth/api/v1/auth/health

# 测试UnifiedMCP接口
curl http://localhost:8080/api/v1/unified-mcp/server/info
```

## 🚨 注意事项

1. **数据备份**: 在修改现有配置前，请备份数据库和配置文件
2. **端口冲突**: 确保修改control-plane的端口避免冲突
3. **逐步迁移**: 建议先在测试环境验证配置正确性
4. **监控日志**: 启动过程中密切关注各服务的日志输出
5. **配置刷新**: 修改Nacos配置后，服务会自动刷新（如果启用了refresh）

## 📞 技术支持

如果在配置过程中遇到问题：
1. 检查Nacos连接是否正常
2. 确认数据库连接配置正确
3. 查看服务启动日志
4. 验证端口是否被占用

这样的整合方案既保持了现有系统的稳定性，又引入了新的微服务架构，实现了平滑过渡。
