# Nexus微服务架构 - 项目总结

## 🎯 项目概述

Nexus微服务架构是一个完整的MCP（Model Context Protocol）服务管理平台，提供统一的MCP服务接口和管理能力。该项目采用Spring Boot微服务架构，支持多种MCP服务的注册、管理和调用。

## 🏗️ 架构设计

### 核心理念
- **统一接口**: 通过UnifiedMCP提供标准化的MCP协议接口
- **多服务支持**: 支持本地和远程两种MCP服务部署模式
- **权限控制**: 基于订阅的细粒度权限管理
- **高可用性**: 服务发现、负载均衡、熔断保护
- **可观测性**: 完整的监控、日志和统计功能

### 服务架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户/客户端    │    │    第三方MCP     │    │   管理员界面     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   API网关(8080)  │
                    │  - 路由管理      │
                    │  - 认证授权      │
                    │  - 限流熔断      │
                    └─────────────────┘
                                 │
        ┌────────────────────────┼────────────────────────┐
        │                       │                        │
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ 认证服务     │    │ 订阅服务     │    │ 本地MCP服务  │    │ 远程MCP服务  │
│ (8081)      │    │ (8084)      │    │ (8082)      │    │ (8083)      │
│ - JWT管理   │    │ - 权限控制   │    │ - 本地部署   │    │ - 远程调用   │
│ - 用户管理   │    │ - 使用统计   │    │ - 进程管理   │    │ - UnifiedMCP │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
        │                       │                        │                │
        └───────────────────────┼────────────────────────┼────────────────┘
                                │                        │
                    ┌─────────────────┐    ┌─────────────────┐
                    │   PostgreSQL    │    │     Redis       │
                    │   (数据存储)     │    │   (缓存/限流)    │
                    └─────────────────┘    └─────────────────┘
```

## 📦 模块详解

### 1. nexus-common (公共模块)
**职责**: 提供所有微服务共享的基础组件
- **实体类**: User, Subscription, Permission等
- **DTO类**: 统一的数据传输对象
- **异常处理**: 统一的异常定义和处理
- **工具类**: 公共工具和常量定义
- **配置**: 缓存、数据库等公共配置

### 2. nexus-auth-service (认证服务)
**端口**: 8081  
**职责**: 用户认证和授权管理
- **JWT管理**: 令牌生成、验证、刷新
- **用户管理**: 注册、登录、密码管理
- **API密钥**: 生成和验证API密钥
- **安全防护**: 限流、防暴力破解

**核心接口**:
- `POST /auth/api/v1/auth/login` - 用户登录
- `POST /auth/api/v1/auth/register` - 用户注册
- `POST /auth/api/v1/auth/validate/token` - 验证JWT令牌
- `POST /auth/api/v1/auth/validate/api-key` - 验证API密钥

### 3. nexus-mcp-local-service (本地MCP服务)
**端口**: 8082  
**职责**: 管理需要在用户本地部署的MCP服务
- **服务注册**: 注册本地MCP服务
- **进程管理**: 启动、停止、重启服务进程
- **健康检查**: 监控服务健康状态
- **多类型支持**: NPX, UV, Python, Node.js, Docker等

**支持的服务类型**:
- `NPX`: Node.js包管理器服务
- `UV`: Python包管理器服务
- `PYTHON`: Python脚本服务
- `NODE`: Node.js应用服务
- `DOCKER`: Docker容器服务
- `CUSTOM`: 自定义命令服务

### 4. nexus-mcp-remote-service (远程MCP服务)
**端口**: 8083  
**职责**: 管理可以在服务端部署的MCP服务，提供UnifiedMCP统一接口
- **UnifiedMCP接口**: 标准MCP协议实现
- **工具执行**: 调用远程MCP工具
- **资源访问**: 访问远程MCP资源
- **异步处理**: 支持异步任务执行

**核心功能 - UnifiedMCP**:
- `GET /api/v1/unified-mcp/server/info` - 获取服务信息
- `GET /api/v1/unified-mcp/tools/list` - 列出所有工具
- `POST /api/v1/unified-mcp/tools/call` - 调用MCP工具
- `GET /api/v1/unified-mcp/resources/list` - 列出所有资源
- `POST /api/v1/unified-mcp/resources/read` - 读取MCP资源

### 5. nexus-subscription-service (订阅服务)
**端口**: 8084  
**职责**: 用户订阅和权限管理
- **订阅管理**: 创建、更新、取消订阅
- **权限控制**: 细粒度的工具和资源权限
- **使用统计**: API调用次数和使用情况
- **生命周期**: 订阅到期、续费管理

**权限类型**:
- `SERVICE`: 服务级权限
- `TOOL`: 工具级权限
- `RESOURCE`: 资源级权限

### 6. nexus-gateway (API网关)
**端口**: 8080  
**职责**: 统一API入口和流量管理
- **路由管理**: 请求路由到对应微服务
- **认证授权**: JWT验证和权限检查
- **限流熔断**: 保护后端服务
- **负载均衡**: 服务实例负载均衡

## 🔧 技术栈

### 后端框架
- **Spring Boot 2.7.x**: 微服务框架
- **Spring Cloud 2021.x**: 微服务治理
- **Spring Cloud Gateway**: API网关
- **Spring Security**: 安全框架

### 数据存储
- **PostgreSQL**: 主数据库
- **Redis**: 缓存和限流
- **RabbitMQ**: 消息队列

### 服务治理
- **Nacos**: 服务发现和配置中心
- **Resilience4j**: 熔断器
- **Bucket4j**: 限流器

### 监控运维
- **Actuator**: 健康检查和指标
- **Prometheus**: 指标收集
- **Grafana**: 监控仪表板
- **Docker**: 容器化部署

## 🚀 部署方案

### 开发环境
```bash
# 1. 启动基础设施
docker-compose up -d postgres redis rabbitmq nacos

# 2. 编译项目
mvn clean package -DskipTests

# 3. 启动服务
./start.sh
```

### 生产环境
- **容器化部署**: 使用Docker和Kubernetes
- **服务网格**: 可选Istio进行流量管理
- **数据库**: 使用云数据库服务
- **监控**: 集成APM和日志系统

## 📊 核心特性

### 1. UnifiedMCP统一接口
- **标准协议**: 完全兼容MCP协议规范
- **多服务整合**: 统一访问本地和远程MCP服务
- **透明代理**: 用户无需关心服务分布
- **异步支持**: 支持长时间运行的任务

### 2. 权限管理
- **基于订阅**: 用户需要订阅服务才能访问
- **细粒度控制**: 可控制到具体工具和资源
- **动态权限**: 支持权限的动态启用/禁用
- **使用限制**: 支持调用次数和时间限制

### 3. 服务管理
- **自动发现**: 服务自动注册和发现
- **健康监控**: 实时监控服务健康状态
- **故障恢复**: 自动重启和故障转移
- **版本管理**: 支持服务版本控制

### 4. 安全防护
- **多重认证**: JWT + API密钥双重认证
- **限流保护**: 多维度限流策略
- **熔断机制**: 防止服务雪崩
- **数据加密**: 敏感数据加密存储

## 📈 性能优化

### 缓存策略
- **Redis缓存**: 用户信息、权限、令牌缓存
- **本地缓存**: 服务配置和元数据缓存
- **CDN**: 静态资源CDN加速

### 数据库优化
- **连接池**: HikariCP连接池优化
- **索引优化**: 关键查询字段索引
- **读写分离**: 主从数据库分离
- **分库分表**: 大数据量分片策略

### 异步处理
- **消息队列**: RabbitMQ异步任务
- **线程池**: 自定义线程池配置
- **批处理**: 批量操作优化

## 🔍 监控和运维

### 监控指标
- **业务指标**: API调用量、成功率、响应时间
- **系统指标**: CPU、内存、磁盘、网络
- **应用指标**: JVM、数据库连接池、缓存命中率

### 日志管理
- **结构化日志**: JSON格式日志
- **日志聚合**: ELK或类似方案
- **链路追踪**: 分布式链路追踪

### 告警机制
- **阈值告警**: 指标超过阈值告警
- **异常告警**: 错误率异常告警
- **业务告警**: 业务指标异常告警

## 🛡️ 安全考虑

### 认证授权
- **JWT令牌**: 无状态认证
- **权限控制**: RBAC权限模型
- **API密钥**: 服务间认证

### 数据安全
- **传输加密**: HTTPS/TLS加密
- **存储加密**: 敏感数据加密
- **访问控制**: 数据库访问控制

### 网络安全
- **防火墙**: 网络层防护
- **DDoS防护**: 流量清洗
- **入侵检测**: 异常行为检测

## 🔮 扩展规划

### 功能扩展
- **多租户**: 支持多租户架构
- **工作流**: MCP工具编排和工作流
- **插件系统**: 可插拔的扩展机制
- **AI集成**: 智能推荐和优化

### 技术升级
- **Spring Boot 3.x**: 升级到最新版本
- **GraalVM**: 原生镜像支持
- **WebAssembly**: WASM运行时支持
- **边缘计算**: 边缘节点部署

## 📝 开发指南

### 添加新服务
1. 继承nexus-common模块
2. 实现服务接口
3. 配置服务发现
4. 添加健康检查
5. 集成监控指标

### 扩展MCP功能
1. 定义工具/资源接口
2. 实现执行逻辑
3. 注册到UnifiedMCP
4. 配置权限控制
5. 添加使用文档

## 🤝 贡献指南

### 代码规范
- **Java**: Google Java Style
- **命名**: 驼峰命名法
- **注释**: JavaDoc注释
- **测试**: 单元测试覆盖

### 提交流程
1. Fork项目
2. 创建特性分支
3. 编写代码和测试
4. 提交Pull Request
5. 代码审查和合并

## 📞 联系信息

- **项目维护**: Nexus Team
- **技术支持**: <EMAIL>
- **文档**: https://docs.nexus.com
- **社区**: https://community.nexus.com

---

**Nexus微服务架构** - 让MCP服务管理更简单、更强大、更安全！
