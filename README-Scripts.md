# Nexus 微服务管理脚本使用说明

## 📁 脚本文件说明

### 🚀 启动脚本
- **`start-all-services.bat`** - 依次启动所有微服务和前端
- **`stop-all-services.bat`** - 停止所有运行中的服务
- **`restart-all-services.bat`** - 重启所有服务（先停止再启动）

### 🧹 清理脚本
- **`cleanup.bat`** - 清理所有垃圾文件和编译输出

## 🎯 使用方法

### 1. 启动所有服务
```bash
# 双击运行或在命令行执行
start-all-services.bat
```

**功能：**
- ✅ 检查Java和Maven环境
- ✅ 编译所有微服务
- ✅ 按顺序启动8个微服务
- ✅ 启动前端开发服务器（如果存在）
- ✅ 显示所有服务状态和访问地址

**启动顺序：**
1. Auth Service (端口8081)
2. MCP Local Service (端口8082)  
3. MCP Remote Service (端口8083)
4. Subscription Service (端口8084)
5. Market Service (端口8085)
6. Chain Service (端口8086)
7. Realtime Service (端口8087)
8. Gateway (端口8080)
9. Frontend (端口3000，如果存在)

### 2. 停止所有服务
```bash
stop-all-services.bat
```

**功能：**
- 🛑 停止所有Nexus相关Java进程
- 🛑 释放所有微服务端口(8080-8087)
- 🛑 停止前端Node.js进程
- 🧹 清理运行时临时文件
- ✅ 验证所有端口已释放

### 3. 重启所有服务
```bash
restart-all-services.bat
```

**功能：**
- 先执行停止脚本
- 等待5秒确保完全停止
- 再执行启动脚本

### 4. 清理项目文件
```bash
cleanup.bat
```

**清理内容：**
- 🗑️ Maven编译输出 (`target/`)
- 🗑️ 日志文件 (`*.log`, `logs/`)
- 🗑️ 前端依赖 (`node_modules/`)
- 🗑️ 前端构建输出 (`dist/`, `build/`)
- 🗑️ 系统文件 (`.DS_Store`, `Thumbs.db`)
- 🗑️ 临时文件 (`*.tmp`, `*.temp`)
- 🗑️ 备份文件 (`*.bak`, `*.backup`)
- 🗑️ IDE配置文件 (`.idea/`, `.vscode/`, `*.iml`)
- 🗑️ 其他垃圾文件

## 📊 服务访问地址

启动完成后，可以通过以下地址访问各个服务：

| 服务名称 | 端口 | 访问地址 | 说明 |
|---------|------|----------|------|
| API Gateway | 8080 | http://localhost:8080 | 统一网关入口 |
| Auth Service | 8081 | http://localhost:8081 | 认证服务 |
| MCP Local | 8082 | http://localhost:8082 | MCP本地服务 |
| MCP Remote | 8083 | http://localhost:8083 | MCP远程服务 |
| Subscription | 8084 | http://localhost:8084 | 订阅服务 |
| Market Service | 8085 | http://localhost:8085 | 市场服务 |
| Chain Service | 8086 | http://localhost:8086 | 链服务 |
| Realtime Service | 8087 | http://localhost:8087 | 实时服务 |
| Frontend | 3000 | http://localhost:3000 | 前端应用 |

## 📝 日志文件

启动后，所有服务的日志文件会保存在 `logs/` 目录下：

- `logs/Auth-Service.log`
- `logs/MCP-Local-Service.log`
- `logs/MCP-Remote-Service.log`
- `logs/Subscription-Service.log`
- `logs/Market-Service.log`
- `logs/Chain-Service.log`
- `logs/Realtime-Service.log`
- `logs/Gateway.log`
- `logs/frontend.log`

## ⚠️ 注意事项

### 环境要求
- ✅ Java 8 或更高版本
- ✅ Maven 3.6 或更高版本
- ✅ Node.js 和 npm（如果有前端项目）

### 内存配置
- 大部分服务：256MB堆内存
- Gateway和Market Service：512MB堆内存
- 可根据实际情况调整脚本中的内存参数

### 启动时间
- 每个服务启动间隔：15秒
- 总启动时间：约2-3分钟
- 如果服务启动失败，请检查对应的日志文件

### 端口冲突
如果遇到端口被占用的问题：
1. 运行 `stop-all-services.bat` 停止所有服务
2. 手动检查端口占用：`netstat -ano | findstr :端口号`
3. 手动停止进程：`taskkill /pid 进程ID /f`

## 🔧 故障排除

### 服务启动失败
1. 检查对应的日志文件
2. 确认Java和Maven环境正确
3. 确认端口没有被其他程序占用
4. 尝试单独启动失败的服务进行调试

### 前端启动失败
1. 确认Node.js和npm已安装
2. 检查 `logs/frontend.log` 日志
3. 手动进入前端目录执行 `npm install` 和 `npm run dev`

### 清理脚本使用
- 清理前会有确认提示，输入 `y` 确认
- 清理操作不可逆，请确保重要文件已备份
- 清理后需要重新编译才能启动服务

## 📞 技术支持

如果遇到问题，请：
1. 查看相关日志文件
2. 检查环境配置
3. 确认网络连接正常
4. 联系技术支持团队
