# Nexus微服务架构

一个完整的MCP（Model Context Protocol）服务管理平台，提供统一的MCP服务接口和管理能力。

## 🏗️ 架构概览

Nexus微服务架构包含以下核心模块：

### 核心服务

- **nexus-gateway** (端口: 8080) - API网关
  - 统一API入口和路由管理
  - JWT认证和订阅权限验证
  - 限流、熔断、负载均衡
  - 跨域处理和安全防护

- **nexus-auth-service** (端口: 8081) - 认证服务
  - JWT令牌管理和验证
  - 用户认证和授权
  - API密钥管理
  - 限流和安全防护

- **nexus-mcp-local-service** (端口: 8082) - 本地MCP服务
  - 管理需要在用户本地部署的MCP服务
  - 服务注册、启动、停止、健康检查
  - 支持NPX、UV、Python、Node.js、Docker等多种服务类型

- **nexus-mcp-remote-service** (端口: 8083) - 远程MCP服务
  - 管理可以在服务端部署的MCP服务
  - **UnifiedMCP统一接口** - 整合所有MCP服务的核心功能
  - MCP工具执行器和资源访问器
  - 异步任务处理

- **nexus-subscription-service** (端口: 8084) - 订阅服务
  - 用户订阅管理
  - 权限控制和访问验证
  - 使用统计和限制管理
  - 订阅生命周期管理

### 公共模块

- **nexus-common** - 公共模块
  - 统一的实体类、DTO、异常处理
  - 公共工具类和常量定义
  - 统一的响应格式和缓存配置

## 🔧 技术栈

- **框架**: Spring Boot 2.7.x, Spring Cloud 2021.x
- **服务发现**: Nacos
- **数据库**: PostgreSQL + Redis
- **消息队列**: RabbitMQ
- **网关**: Spring Cloud Gateway
- **认证**: JWT
- **限流**: Redis + Bucket4j
- **监控**: Actuator + Prometheus + Grafana
- **文档**: OpenAPI 3.0

## 🚀 快速开始

### 前置要求

- Java 11+
- Maven 3.6+
- Docker & Docker Compose

### 启动服务

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd nexus-microservices
   ```

2. **一键启动**
   ```bash
   chmod +x start.sh
   ./start.sh
   ```

3. **手动启动**
   ```bash
   # 编译项目
   mvn clean package -DskipTests
   
   # 启动基础设施
   docker-compose up -d postgres redis rabbitmq nacos
   
   # 启动应用服务
   docker-compose up -d
   ```

### 服务访问

- **API网关**: http://localhost:8080
- **UnifiedMCP接口**: http://localhost:8080/api/v1/unified-mcp
- **Nacos控制台**: http://localhost:8848/nacos (nacos/nacos)
- **Grafana监控**: http://localhost:3000 (admin/admin123)

## 📚 API文档

### UnifiedMCP核心接口

#### 获取服务信息
```http
GET /api/v1/unified-mcp/server/info
```

#### 列出所有工具
```http
GET /api/v1/unified-mcp/tools/list
```

#### 调用MCP工具
```http
POST /api/v1/unified-mcp/tools/call
Content-Type: application/json
Authorization: Bearer <jwt-token>

{
  "jsonrpc": "2.0",
  "method": "tools/call",
  "params": {
    "name": "tool-name",
    "arguments": {
      "param1": "value1"
    }
  },
  "id": 1
}
```

#### 列出所有资源
```http
GET /api/v1/unified-mcp/resources/list
```

#### 读取MCP资源
```http
POST /api/v1/unified-mcp/resources/read
Content-Type: application/json
Authorization: Bearer <jwt-token>

{
  "jsonrpc": "2.0",
  "method": "resources/read",
  "params": {
    "uri": "resource-uri"
  },
  "id": 1
}
```

### 认证接口

#### 用户登录
```http
POST /auth/api/v1/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}
```

#### 创建API密钥
```http
POST /auth/api/v1/api-keys
Content-Type: application/json
Authorization: Bearer <jwt-token>

{
  "keyName": "my-api-key",
  "permissions": ["MCP_ACCESS"]
}
```

## 🔑 默认账户

- **管理员账户**
  - 用户名: `admin`
  - 邮箱: `<EMAIL>`
  - 密码: `admin123`

## 🏗️ 部署架构

```
用户请求 → API网关(8080) → 各微服务
                ↓
        认证服务(8081) - JWT验证
        订阅服务(8084) - 权限验证
        本地MCP(8082) - 本地服务管理
        远程MCP(8083) - UnifiedMCP接口
```

## 🔧 配置说明

### 环境变量

- `SPRING_PROFILES_ACTIVE`: 运行环境 (dev/prod/docker)
- `JWT_SECRET`: JWT密钥
- `SPRING_DATASOURCE_URL`: 数据库连接URL
- `SPRING_REDIS_HOST`: Redis主机地址

### 数据库配置

默认使用PostgreSQL数据库，连接信息：
- 主机: localhost:5432
- 数据库: nexus
- 用户名: nexus
- 密码: nexus123

## 📊 监控和日志

### Prometheus指标

所有服务都暴露了Prometheus指标端点：
- `/actuator/prometheus`

### Grafana仪表板

访问 http://localhost:3000 查看监控仪表板

### 日志查看

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f nexus-gateway
```

## 🛠️ 开发指南

### 添加新的MCP服务

1. **注册远程MCP服务**
   ```http
   POST /mcp-remote/api/v1/remote-mcp/register
   ```

2. **注册本地MCP服务**
   ```http
   POST /mcp-local/api/v1/local-mcp/register
   ```

### 扩展工具和资源

1. 在远程MCP服务中添加新的工具定义
2. 实现工具执行逻辑
3. 通过UnifiedMCP接口自动暴露

## 🔒 安全考虑

- 所有API都需要JWT认证
- 基于订阅的权限控制
- API限流和熔断保护
- 敏感信息加密存储

## 🚀 性能优化

- Redis缓存加速
- 连接池优化
- 异步任务处理
- 负载均衡

## 🐛 故障排除

### 常见问题

1. **服务启动失败**
   - 检查端口是否被占用
   - 确认数据库连接正常
   - 查看服务日志

2. **认证失败**
   - 检查JWT令牌是否有效
   - 确认用户权限设置

3. **MCP工具调用失败**
   - 检查服务是否注册
   - 确认订阅权限
   - 查看服务健康状态

### 日志级别调整

在application.yml中调整日志级别：
```yaml
logging:
  level:
    com.nexus: DEBUG
```

## 📝 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📞 联系我们

- 项目主页: [GitHub Repository]
- 文档: [Documentation]
- 问题反馈: [Issues]
