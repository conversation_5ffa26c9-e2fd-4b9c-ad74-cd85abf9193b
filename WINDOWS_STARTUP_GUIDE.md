# Windows启动指南

## 🚀 一键启动所有微服务

### 方法1：完整启动（推荐）
```cmd
start-all-services.cmd
```

**功能**：
- ✅ 完整的环境检查
- ✅ 自动编译所有项目
- ✅ 按顺序启动所有服务
- ✅ 详细的启动日志
- ✅ 每个服务独立窗口显示
- ✅ 自动打开Nacos控制台

### 方法2：快速启动
```cmd
quick-start.cmd
```

**功能**：
- ✅ 快速编译和启动
- ✅ 服务在后台运行
- ✅ 最小化资源占用
- ✅ 适合开发调试

## 🛑 停止所有服务

```cmd
stop-all-services.cmd
```

**功能**：
- ✅ 自动查找所有Nexus相关进程
- ✅ 强制停止所有服务
- ✅ 清理端口占用

## 📋 前置条件

### 必需环境
1. **Java 11+** - 已安装并配置PATH
2. **Maven 3.6+** - 已安装并配置PATH
3. **Nacos** - 已启动并运行在8848端口

### 检查环境
```cmd
# 检查Java
java -version

# 检查Maven
mvn -version

# 检查Nacos
curl http://localhost:8848/nacos/v1/ns/operator/metrics
```

## 🔧 Nacos配置

### 首次使用需要导入配置
```cmd
cd nacos-configs
import-configs-public.cmd
```

### 手动导入配置
1. 访问 http://localhost:8848/nacos
2. 登录 (nacos/nacos)
3. 进入"配置管理" -> "配置列表"
4. 导入以下配置文件：
   - nexus-common-config.yml
   - nexus-auth-service.yml
   - nexus-mcp-local-service.yml
   - nexus-mcp-remote-service.yml
   - nexus-subscription-service.yml
   - nexus-gateway.yml

## 🌐 服务访问地址

| 服务 | 地址 | 说明 |
|------|------|------|
| API网关 | http://localhost:8080 | 统一入口 |
| UnifiedMCP | http://localhost:8080/api/v1/unified-mcp | 核心MCP接口 |
| 认证服务 | http://localhost:8081/auth | 用户认证 |
| 本地MCP | http://localhost:8082/mcp-local | 本地服务管理 |
| 远程MCP | http://localhost:8083/mcp-remote | 远程服务管理 |
| 订阅服务 | http://localhost:8084/subscription | 订阅管理 |
| Nacos控制台 | http://localhost:8848/nacos | 配置管理 |

## 📚 API文档

- **网关Swagger**: http://localhost:8080/swagger-ui.html
- **认证服务Swagger**: http://localhost:8081/auth/swagger-ui.html

## 🔑 默认账户

- **用户名**: admin
- **邮箱**: <EMAIL>
- **密码**: admin123

## 🔍 健康检查

```cmd
# 检查API网关
curl http://localhost:8080/actuator/health

# 检查认证服务
curl http://localhost:8081/auth/actuator/health

# 检查所有服务注册状态
# 访问 http://localhost:8848/nacos -> 服务管理 -> 服务列表
```

## 🐛 故障排除

### 1. 端口被占用
```cmd
# 查看端口占用
netstat -ano | findstr :8080

# 停止占用进程
taskkill /pid <PID> /f
```

### 2. 服务启动失败
- 检查Java和Maven环境变量
- 确认Nacos服务正在运行
- 查看服务启动窗口的错误信息
- 检查端口是否被占用

### 3. Nacos连接失败
```cmd
# 启动Nacos
cd nacos\bin
startup.cmd -m standalone

# 检查Nacos状态
curl http://localhost:8848/nacos/v1/ns/operator/metrics
```

### 4. 编译失败
```cmd
# 清理并重新编译
mvn clean install -DskipTests

# 检查Maven仓库
mvn dependency:resolve
```

## 📝 开发建议

### 1. IDE配置
- 推荐使用IntelliJ IDEA
- 安装Lombok插件
- 配置Maven和Java环境

### 2. 调试模式
```cmd
# 启动单个服务进行调试
cd nexus-auth-service
java -Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=5005 -jar target\nexus-auth-service-*.jar
```

### 3. 日志查看
- 每个服务在独立窗口显示日志
- 关键错误信息会在启动窗口显示
- 可以通过Actuator端点查看详细信息

## 🔄 常用操作

### 重启所有服务
```cmd
stop-all-services.cmd
start-all-services.cmd
```

### 重启单个服务
1. 关闭对应的服务窗口
2. 重新运行对应的启动命令

### 更新配置
1. 修改Nacos中的配置
2. 配置会自动刷新到服务中（如果启用了refresh）

## 💡 性能优化

### 1. JVM参数调优
在启动脚本中已设置：
```cmd
-Xms256m -Xmx512m -XX:+UseG1GC
```

### 2. 并行编译
```cmd
mvn clean package -DskipTests -T 4
```

### 3. 跳过测试
```cmd
mvn clean package -DskipTests
```

## 🎯 总结

使用 `start-all-services.cmd` 可以一键启动整个Nexus微服务架构：

1. **自动环境检查** - 确保Java、Maven、Nacos环境正常
2. **自动配置导入** - 检查并导入Nacos配置
3. **自动编译构建** - 编译所有微服务项目
4. **按序启动服务** - 按依赖关系启动各个服务
5. **状态验证** - 自动打开健康检查和控制台

这样您就可以快速开始使用Nexus微服务架构了！
