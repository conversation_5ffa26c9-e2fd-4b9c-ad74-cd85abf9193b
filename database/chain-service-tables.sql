-- Nexus Chain Service 数据库表结构
-- 服务链相关表的创建SQL

-- 服务链表
CREATE TABLE IF NOT EXISTS service_chains (
    id BIGSERIAL PRIMARY KEY,
    chain_id VARCHAR(100) UNIQUE NOT NULL,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name VA<PERSON><PERSON><PERSON>(200) NOT NULL,
    description TEXT,
    chain_config TEXT NOT NULL,
    template_id VARCHAR(100),
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    version INTEGER DEFAULT 1,
    is_public BOOLEAN DEFAULT FALSE,
    tags VARCHAR(500),
    category VARCHAR(100),
    execution_count BIGINT DEFAULT 0,
    success_count BIGINT DEFAULT 0,
    last_executed_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

-- 创建服务链表索引
CREATE INDEX IF NOT EXISTS idx_service_chains_chain_id ON service_chains(chain_id);
CREATE INDEX IF NOT EXISTS idx_service_chains_user_id ON service_chains(user_id);
CREATE INDEX IF NOT EXISTS idx_service_chains_status ON service_chains(status);
CREATE INDEX IF NOT EXISTS idx_service_chains_category ON service_chains(category);
CREATE INDEX IF NOT EXISTS idx_service_chains_is_public ON service_chains(is_public);
CREATE INDEX IF NOT EXISTS idx_service_chains_template_id ON service_chains(template_id);
CREATE INDEX IF NOT EXISTS idx_service_chains_execution_count ON service_chains(execution_count);
CREATE INDEX IF NOT EXISTS idx_service_chains_last_executed_at ON service_chains(last_executed_at);

-- 服务链执行记录表
CREATE TABLE IF NOT EXISTS chain_executions (
    id BIGSERIAL PRIMARY KEY,
    execution_id VARCHAR(100) UNIQUE NOT NULL,
    chain_id VARCHAR(100) NOT NULL,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    input_data TEXT,
    output_data TEXT,
    error_message TEXT,
    error_step VARCHAR(100),
    total_steps INTEGER,
    completed_steps INTEGER DEFAULT 0,
    current_step VARCHAR(100),
    execution_time_ms BIGINT,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    is_async BOOLEAN DEFAULT FALSE,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 0,
    execution_metadata TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建执行记录表索引
CREATE INDEX IF NOT EXISTS idx_chain_executions_execution_id ON chain_executions(execution_id);
CREATE INDEX IF NOT EXISTS idx_chain_executions_chain_id ON chain_executions(chain_id);
CREATE INDEX IF NOT EXISTS idx_chain_executions_user_id ON chain_executions(user_id);
CREATE INDEX IF NOT EXISTS idx_chain_executions_status ON chain_executions(status);
CREATE INDEX IF NOT EXISTS idx_chain_executions_started_at ON chain_executions(started_at);
CREATE INDEX IF NOT EXISTS idx_chain_executions_completed_at ON chain_executions(completed_at);
CREATE INDEX IF NOT EXISTS idx_chain_executions_is_async ON chain_executions(is_async);

-- 为服务链表创建更新时间触发器
CREATE TRIGGER update_service_chains_updated_at 
    BEFORE UPDATE ON service_chains 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 为执行记录表创建更新时间触发器
CREATE TRIGGER update_chain_executions_updated_at 
    BEFORE UPDATE ON chain_executions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入示例服务链模板
INSERT INTO service_chains (
    chain_id, user_id, name, description, chain_config, 
    template_id, status, category, is_public, created_by, updated_by
) VALUES (
    'template-simple-chain', 1, '简单工具链模板', '一个包含两个步骤的简单工具链示例',
    '{"steps":[{"stepId":"step1","name":"获取数据","serviceName":"data-service","toolName":"fetch-data","config":{"staticParams":{"source":"api"},"timeout":30000},"order":1,"enabled":true},{"stepId":"step2","name":"处理数据","serviceName":"process-service","toolName":"transform-data","config":{"dynamicParams":{"input":"${steps.step1.output}"},"timeout":60000},"dependsOn":["step1"],"order":2,"enabled":true}],"globalParams":{"timeout":300000,"maxRetries":3},"enableParallel":false}',
    'simple-chain-template', 'ACTIVE', 'template', true, 'system', 'system'
) ON CONFLICT (chain_id) DO NOTHING;

COMMIT;
