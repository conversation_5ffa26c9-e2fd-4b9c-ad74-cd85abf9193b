-- Nexus微服务数据库初始化脚本
-- 创建所有必要的表结构

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    avatar_url VARCHAR(500),
    role VARCHAR(20) NOT NULL DEFAULT 'USER',
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    email_verified BOOLEAN DEFAULT FALSE,
    last_login_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建用户表索引
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);

-- API密钥表
CREATE TABLE IF NOT EXISTS api_keys (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    key_name VARCHAR(100) NOT NULL,
    key_hash VARCHAR(255) NOT NULL,
    key_prefix VARCHAR(20) NOT NULL,
    permissions JSONB,
    expires_at TIMESTAMP,
    last_used_at TIMESTAMP,
    usage_count BIGINT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建API密钥表索引
CREATE INDEX IF NOT EXISTS idx_api_keys_user_id ON api_keys(user_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_key_prefix ON api_keys(key_prefix);
CREATE INDEX IF NOT EXISTS idx_api_keys_is_active ON api_keys(is_active);

-- 服务配置表
CREATE TABLE IF NOT EXISTS service_configs (
    id BIGSERIAL PRIMARY KEY,
    service_name VARCHAR(100) UNIQUE NOT NULL,
    display_name VARCHAR(200) NOT NULL,
    description TEXT,
    service_type VARCHAR(50) NOT NULL,
    endpoint VARCHAR(500),
    protocol_type VARCHAR(20) DEFAULT 'HTTP',
    auth_config JSONB,
    config_params JSONB,
    metadata JSONB,
    is_enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建服务配置表索引
CREATE INDEX IF NOT EXISTS idx_service_configs_service_name ON service_configs(service_name);
CREATE INDEX IF NOT EXISTS idx_service_configs_service_type ON service_configs(service_type);
CREATE INDEX IF NOT EXISTS idx_service_configs_is_enabled ON service_configs(is_enabled);

-- 订阅表
CREATE TABLE IF NOT EXISTS subscriptions (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    service_config_id BIGINT NOT NULL REFERENCES service_configs(id) ON DELETE CASCADE,
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    start_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMP,
    call_limit BIGINT,
    used_calls BIGINT DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, service_config_id)
);

-- 创建订阅表索引
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_service_config_id ON subscriptions(service_config_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_subscriptions_end_date ON subscriptions(end_date);

-- 权限表
CREATE TABLE IF NOT EXISTS permissions (
    id BIGSERIAL PRIMARY KEY,
    subscription_id BIGINT NOT NULL REFERENCES subscriptions(id) ON DELETE CASCADE,
    permission_type VARCHAR(20) NOT NULL,
    tool_name VARCHAR(100),
    resource_name VARCHAR(200),
    description TEXT,
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建权限表索引
CREATE INDEX IF NOT EXISTS idx_permissions_subscription_id ON permissions(subscription_id);
CREATE INDEX IF NOT EXISTS idx_permissions_permission_type ON permissions(permission_type);
CREATE INDEX IF NOT EXISTS idx_permissions_tool_name ON permissions(tool_name);
CREATE INDEX IF NOT EXISTS idx_permissions_resource_name ON permissions(resource_name);
CREATE INDEX IF NOT EXISTS idx_permissions_enabled ON permissions(enabled);

-- 本地MCP服务表
CREATE TABLE IF NOT EXISTS local_mcp_services (
    id BIGSERIAL PRIMARY KEY,
    service_name VARCHAR(100) UNIQUE NOT NULL,
    display_name VARCHAR(200) NOT NULL,
    description TEXT,
    service_type VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'INACTIVE',
    version VARCHAR(50),
    agent_id VARCHAR(100) NOT NULL,
    command TEXT,
    working_directory VARCHAR(500),
    environment_variables JSONB,
    config_params JSONB,
    metadata JSONB,
    health_check JSONB,
    last_health_check_at TIMESTAMP,
    health_status VARCHAR(20) DEFAULT 'UNKNOWN',
    health_error TEXT,
    started_at TIMESTAMP,
    stopped_at TIMESTAMP,
    process_id BIGINT,
    port INTEGER,
    auto_start BOOLEAN DEFAULT FALSE,
    max_restarts INTEGER DEFAULT 3,
    current_restarts INTEGER DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建本地MCP服务表索引
CREATE INDEX IF NOT EXISTS idx_local_mcp_service_name ON local_mcp_services(service_name);
CREATE INDEX IF NOT EXISTS idx_local_mcp_service_type ON local_mcp_services(service_type);
CREATE INDEX IF NOT EXISTS idx_local_mcp_service_status ON local_mcp_services(status);
CREATE INDEX IF NOT EXISTS idx_local_mcp_agent_id ON local_mcp_services(agent_id);

-- 远程MCP服务表
CREATE TABLE IF NOT EXISTS remote_mcp_services (
    id BIGSERIAL PRIMARY KEY,
    service_name VARCHAR(100) UNIQUE NOT NULL,
    display_name VARCHAR(200) NOT NULL,
    description TEXT,
    service_type VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'INACTIVE',
    version VARCHAR(50),
    endpoint VARCHAR(500) NOT NULL,
    protocol_type VARCHAR(20) NOT NULL DEFAULT 'HTTP',
    auth_config JSONB,
    config_params JSONB,
    metadata JSONB,
    tools JSONB,
    resources JSONB,
    health_check JSONB,
    last_health_check_at TIMESTAMP,
    health_status VARCHAR(20) DEFAULT 'UNKNOWN',
    health_error TEXT,
    priority INTEGER DEFAULT 0,
    weight INTEGER DEFAULT 100,
    cache_enabled BOOLEAN DEFAULT TRUE,
    cache_ttl INTEGER DEFAULT 300,
    max_concurrent_requests INTEGER DEFAULT 10,
    request_timeout INTEGER DEFAULT 30000,
    max_retries INTEGER DEFAULT 3,
    retry_delay INTEGER DEFAULT 1000,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建远程MCP服务表索引
CREATE INDEX IF NOT EXISTS idx_remote_mcp_service_name ON remote_mcp_services(service_name);
CREATE INDEX IF NOT EXISTS idx_remote_mcp_service_type ON remote_mcp_services(service_type);
CREATE INDEX IF NOT EXISTS idx_remote_mcp_service_status ON remote_mcp_services(status);
CREATE INDEX IF NOT EXISTS idx_remote_mcp_endpoint ON remote_mcp_services(endpoint);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为所有表创建更新时间触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_api_keys_updated_at BEFORE UPDATE ON api_keys FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_service_configs_updated_at BEFORE UPDATE ON service_configs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_subscriptions_updated_at BEFORE UPDATE ON subscriptions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_permissions_updated_at BEFORE UPDATE ON permissions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_local_mcp_services_updated_at BEFORE UPDATE ON local_mcp_services FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_remote_mcp_services_updated_at BEFORE UPDATE ON remote_mcp_services FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入初始数据
-- 创建管理员用户
INSERT INTO users (username, email, password_hash, full_name, role, status, email_verified) 
VALUES ('admin', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLyifkPiHK4e', 'System Administrator', 'ADMIN', 'ACTIVE', true)
ON CONFLICT (username) DO NOTHING;

-- 创建示例服务配置
INSERT INTO service_configs (service_name, display_name, description, service_type, endpoint, protocol_type, is_enabled)
VALUES 
    ('unified-mcp', 'Unified MCP Service', '统一MCP服务接口', 'MCP_UNIFIED', 'http://localhost:8083/mcp-remote/api/v1/unified-mcp', 'HTTP', true),
    ('local-mcp', 'Local MCP Service', '本地MCP服务管理', 'MCP_LOCAL', 'http://localhost:8082/mcp-local', 'HTTP', true)
ON CONFLICT (service_name) DO NOTHING;

-- 提交事务
COMMIT;
