-- 初始化管理员用户
-- 密码: admin123 (BCrypt加密)

INSERT INTO users (id, username, email, password_hash, role, status, created_at, updated_at) 
VALUES (
    1, 
    'admin', 
    '<EMAIL>', 
    '$2a$10$N.zmdr9k7uOLQvQHbh/2xuXo/1Q5SJJzVNNIZ7mKO7d7dqF8qS/3u', 
    'ADMIN', 
    'ACTIVE', 
    NOW(), 
    NOW()
) ON CONFLICT (email) DO NOTHING;

-- 创建API密钥
INSERT INTO api_keys (id, user_id, key_name, key_hash, permissions, status, created_at, expires_at)
VALUES (
    1,
    1,
    'admin-default-key',
    'nxs_admin_default_api_key_2024',
    'ALL',
    'ACTIVE',
    NOW(),
    NOW() + INTERVAL '1 year'
) ON CONFLICT (key_hash) DO NOTHING;
