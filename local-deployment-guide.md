# Nexus微服务本地部署指南

## 🎯 部署方案选择

### 方案1：完全本地部署（无Docker）

#### 1.1 安装基础设施

**PostgreSQL数据库**
```bash
# Windows (使用Chocolatey)
choco install postgresql

# 或下载安装包
# https://www.postgresql.org/download/windows/

# 创建数据库
psql -U postgres
CREATE DATABASE nexus;
CREATE USER nexus WITH PASSWORD 'nexus123';
GRANT ALL PRIVILEGES ON DATABASE nexus TO nexus;
```

**Redis缓存**
```bash
# Windows (使用Chocolatey)
choco install redis-64

# 或下载Windows版本
# https://github.com/microsoftarchive/redis/releases

# 启动Redis
redis-server
```

**RabbitMQ消息队列**
```bash
# 下载安装包
# https://www.rabbitmq.com/download.html

# 启动管理界面
rabbitmq-plugins enable rabbitmq_management

# 访问: http://localhost:15672 (guest/guest)
```

#### 1.2 配置文件修改

**数据库配置** (所有服务的application.yml)
```yaml
spring:
  datasource:
    url: **************************************
    username: nexus
    password: nexus123
  
  redis:
    host: localhost
    port: 6379
  
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
```

#### 1.3 启动服务（无Nacos）

**方式A：直接启动（推荐用于开发）**
```bash
# 1. 编译项目
mvn clean package -DskipTests

# 2. 启动服务（按顺序）
# 认证服务
cd nexus-auth-service
mvn spring-boot:run

# 订阅服务
cd nexus-subscription-service  
mvn spring-boot:run

# 本地MCP服务
cd nexus-mcp-local-service
mvn spring-boot:run

# 远程MCP服务
cd nexus-mcp-remote-service
mvn spring-boot:run

# API网关
cd nexus-gateway
mvn spring-boot:run
```

**方式B：JAR包启动**
```bash
# 编译
mvn clean package -DskipTests

# 启动
java -jar nexus-auth-service/target/nexus-auth-service-1.0.0-SNAPSHOT.jar
java -jar nexus-subscription-service/target/nexus-subscription-service-1.0.0-SNAPSHOT.jar
java -jar nexus-mcp-local-service/target/nexus-mcp-local-service-1.0.0-SNAPSHOT.jar
java -jar nexus-mcp-remote-service/target/nexus-mcp-remote-service-1.0.0-SNAPSHOT.jar
java -jar nexus-gateway/target/nexus-gateway-1.0.0-SNAPSHOT.jar
```

### 方案2：使用Nacos（可选）

#### 2.1 安装Nacos
```bash
# 下载Nacos
# https://github.com/alibaba/nacos/releases

# 解压后启动
cd nacos/bin
# Windows
startup.cmd -m standalone
# Linux/Mac  
sh startup.sh -m standalone

# 访问控制台: http://localhost:8848/nacos
# 默认账号: nacos/nacos
```

#### 2.2 Nacos配置

**在Nacos控制台创建配置**

1. **公共配置** (Data ID: nexus-common-config.yml)
```yaml
# 数据库配置
spring:
  datasource:
    url: **************************************
    username: nexus
    password: nexus123
    driver-class-name: org.postgresql.Driver
  
  # Redis配置
  redis:
    host: localhost
    port: 6379
    database: 0
  
  # RabbitMQ配置
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest

# JWT配置
nexus:
  auth:
    jwt:
      secret: nexus-jwt-secret-key-2024
      expiration: 86400
```

2. **各服务专用配置**
- nexus-auth-service.yml
- nexus-subscription-service.yml  
- nexus-mcp-local-service.yml
- nexus-mcp-remote-service.yml
- nexus-gateway.yml

#### 2.3 启用Nacos配置

修改各服务的`application.yml`：
```yaml
spring:
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        namespace: nexus-microservices
      config:
        server-addr: 127.0.0.1:8848
        namespace: nexus-microservices
        file-extension: yml
        shared-configs:
          - data-id: nexus-common-config.yml
            refresh: true
```

### 方案3：混合部署

**基础设施使用Docker，应用本地运行**
```bash
# 只启动基础设施
docker-compose up -d postgres redis rabbitmq

# 应用本地运行
mvn spring-boot:run
```

## 🚀 快速启动脚本

### 本地启动脚本 (start-local.bat)
```batch
@echo off
echo 启动Nexus微服务 - 本地模式

echo 检查数据库连接...
psql -h localhost -U nexus -d nexus -c "SELECT 1;" > nul 2>&1
if errorlevel 1 (
    echo 数据库连接失败，请检查PostgreSQL是否启动
    pause
    exit /b 1
)

echo 初始化数据库...
psql -h localhost -U nexus -d nexus -f database/init.sql

echo 启动认证服务...
start "Auth Service" cmd /k "cd nexus-auth-service && mvn spring-boot:run"
timeout /t 20

echo 启动订阅服务...
start "Subscription Service" cmd /k "cd nexus-subscription-service && mvn spring-boot:run"
timeout /t 20

echo 启动本地MCP服务...
start "Local MCP Service" cmd /k "cd nexus-mcp-local-service && mvn spring-boot:run"
timeout /t 20

echo 启动远程MCP服务...
start "Remote MCP Service" cmd /k "cd nexus-mcp-remote-service && mvn spring-boot:run"
timeout /t 20

echo 启动API网关...
start "Gateway" cmd /k "cd nexus-gateway && mvn spring-boot:run"

echo 所有服务启动完成！
echo 访问地址: http://localhost:8080
pause
```

### Linux/Mac启动脚本 (start-local.sh)
```bash
#!/bin/bash

echo "启动Nexus微服务 - 本地模式"

# 检查数据库
if ! psql -h localhost -U nexus -d nexus -c "SELECT 1;" > /dev/null 2>&1; then
    echo "数据库连接失败，请检查PostgreSQL是否启动"
    exit 1
fi

# 初始化数据库
psql -h localhost -U nexus -d nexus -f database/init.sql

# 启动服务
echo "启动认证服务..."
cd nexus-auth-service && mvn spring-boot:run > ../logs/auth.log 2>&1 &
sleep 20

echo "启动订阅服务..."
cd ../nexus-subscription-service && mvn spring-boot:run > ../logs/subscription.log 2>&1 &
sleep 20

echo "启动本地MCP服务..."
cd ../nexus-mcp-local-service && mvn spring-boot:run > ../logs/mcp-local.log 2>&1 &
sleep 20

echo "启动远程MCP服务..."
cd ../nexus-mcp-remote-service && mvn spring-boot:run > ../logs/mcp-remote.log 2>&1 &
sleep 20

echo "启动API网关..."
cd ../nexus-gateway && mvn spring-boot:run > ../logs/gateway.log 2>&1 &

echo "所有服务启动完成！"
echo "访问地址: http://localhost:8080"
```

## 🔧 配置说明

### 无Nacos配置
如果不使用Nacos，需要在每个服务的`application.yml`中注释掉Nacos相关配置：

```yaml
# 注释掉这些配置
# spring:
#   cloud:
#     nacos:
#       discovery:
#         server-addr: 127.0.0.1:8848
#       config:
#         server-addr: 127.0.0.1:8848
```

### 端口配置
确保以下端口未被占用：
- 8080: API网关
- 8081: 认证服务
- 8082: 本地MCP服务
- 8083: 远程MCP服务
- 8084: 订阅服务
- 5432: PostgreSQL
- 6379: Redis
- 5672: RabbitMQ
- 8848: Nacos (可选)

## 🐛 常见问题

### 1. 数据库连接失败
```bash
# 检查PostgreSQL是否启动
pg_ctl status

# 检查用户权限
psql -U postgres -c "\du"
```

### 2. Redis连接失败
```bash
# 检查Redis是否启动
redis-cli ping

# 应该返回 PONG
```

### 3. 端口被占用
```bash
# Windows查看端口占用
netstat -ano | findstr :8080

# Linux/Mac查看端口占用
lsof -i :8080
```

### 4. 服务启动失败
查看日志文件或控制台输出，常见原因：
- 数据库连接失败
- 端口被占用
- 依赖服务未启动
- 配置文件错误

## 📝 开发建议

### IDE配置
推荐使用IntelliJ IDEA或Eclipse：
1. 导入Maven项目
2. 配置JDK 11+
3. 安装Lombok插件
4. 配置代码格式化

### 调试模式
```bash
# 启用调试模式
mvn spring-boot:run -Dspring-boot.run.jvmArguments="-Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=5005"
```

### 热重载
```bash
# 启用开发者工具
mvn spring-boot:run -Dspring-boot.run.fork=false
```

这样您就可以根据自己的环境选择最适合的部署方案了！
