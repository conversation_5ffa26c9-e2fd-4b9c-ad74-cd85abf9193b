2025-07-27 20:54:24 [main] INFO  [com.nexus.gateway.NexusGatewayApplication] - Starting NexusGatewayApplication v0.0.1-SNAPSHOT using Java 1.8.0_202 on DESKTOP-7E5G8UV with PID 21132 (D:\nexus_agent_service\nexus-microservices\nexus-gateway\target\nexus-gateway-0.0.1-SNAPSHOT.jar started by adm1n in D:\nexus_agent_service\nexus-microservices)
2025-07-27 20:54:24 [main] DEBUG [com.nexus.gateway.NexusGatewayApplication] - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-27 20:54:24 [main] INFO  [com.nexus.gateway.NexusGatewayApplication] - The following 1 profile is active: "local"
2025-07-27 20:54:24 [main] INFO  [c.a.cloud.nacos.configdata.NacosConfigDataLoader] - [Nacos Config] Load config[dataId=nexus-gateway.yml, group=DEFAULT_GROUP] success
2025-07-27 20:54:32 [main] INFO  [o.s.d.r.config.RepositoryConfigurationDelegate] - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 20:54:32 [main] INFO  [o.s.d.r.config.RepositoryConfigurationDelegate] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-27 20:54:32 [main] INFO  [o.s.d.r.config.RepositoryConfigurationDelegate] - Finished Spring Data repository scanning in 61 ms. Found 0 Redis repository interfaces.
2025-07-27 20:54:34 [main] INFO  [o.springframework.cloud.context.scope.GenericScope] - BeanFactory id=17f20ea9-c95a-3e9d-8179-0f7e81021130
2025-07-27 20:54:35 [main] INFO  [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-27 20:54:35 [main] INFO  [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-27 20:54:35 [main] INFO  [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-27 20:54:37 [main] INFO  [com.nexus.gateway.service.JwtService] - JWT Service��ʼ�� - ��Կ: nexus-microservices-...
2025-07-27 20:54:37 [main] INFO  [com.nexus.gateway.service.JwtService] - JWT Service��ʼ����� - ����ʱ��: 86400000ms
2025-07-27 20:54:39 [main] INFO  [org.redisson.Version] - Redisson 3.17.7
2025-07-27 20:54:41 [redisson-netty-3-6] INFO  [o.r.connection.pool.MasterPubSubConnectionPool] - 1 connections initialized for localhost/127.0.0.1:6379
2025-07-27 20:54:42 [redisson-netty-3-14] INFO  [org.redisson.connection.pool.MasterConnectionPool] - 5 connections initialized for localhost/127.0.0.1:6379
2025-07-27 20:54:47 [main] INFO  [o.a.r.s.s.RocketMQMessageListenerContainerRegistrar] - Register the listener to container, listenerBeanName:auditLogConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-27 20:54:47 [main] INFO  [c.n.common.mcp.config.StandardMcpServiceRegistry] - ע��ٷ�MCP����
2025-07-27 20:54:47 [main] DEBUG [c.n.common.mcp.config.StandardMcpServiceRegistry] - ע��MCP����: filesystem -> @modelcontextprotocol/server-filesystem
2025-07-27 20:54:47 [main] DEBUG [c.n.common.mcp.config.StandardMcpServiceRegistry] - ע��MCP����: git -> mcp-server-git
2025-07-27 20:54:47 [main] DEBUG [c.n.common.mcp.config.StandardMcpServiceRegistry] - ע��MCP����: github -> @modelcontextprotocol/server-github
2025-07-27 20:54:47 [main] DEBUG [c.n.common.mcp.config.StandardMcpServiceRegistry] - ע��MCP����: memory -> @modelcontextprotocol/server-memory
2025-07-27 20:54:47 [main] DEBUG [c.n.common.mcp.config.StandardMcpServiceRegistry] - ע��MCP����: time -> @modelcontextprotocol/server-time
2025-07-27 20:54:47 [main] DEBUG [c.n.common.mcp.config.StandardMcpServiceRegistry] - ע��MCP����: fetch -> @modelcontextprotocol/server-fetch
2025-07-27 20:54:47 [main] DEBUG [c.n.common.mcp.config.StandardMcpServiceRegistry] - ע��MCP����: sequential-thinking -> @modelcontextprotocol/server-sequentialthinking
2025-07-27 20:54:47 [main] INFO  [c.n.common.mcp.config.StandardMcpServiceRegistry] - ע������MCP����
2025-07-27 20:54:47 [main] DEBUG [c.n.common.mcp.config.StandardMcpServiceRegistry] - ע��MCP����: brave-search -> mcp-server-brave-search
2025-07-27 20:54:47 [main] DEBUG [c.n.common.mcp.config.StandardMcpServiceRegistry] - ע��MCP����: postgres -> @modelcontextprotocol/server-postgres
2025-07-27 20:54:47 [main] DEBUG [c.n.common.mcp.config.StandardMcpServiceRegistry] - ע��MCP����: sqlite -> mcp-server-sqlite
2025-07-27 20:54:47 [main] DEBUG [c.n.common.mcp.config.StandardMcpServiceRegistry] - ע��MCP����: puppeteer -> @modelcontextprotocol/server-puppeteer
2025-07-27 20:54:47 [main] DEBUG [c.n.common.mcp.notification.McpNotificationManager] - ע��MCP֪ͨ������: * -> McpServiceDiscovery
2025-07-27 20:54:47 [main] INFO  [com.nexus.common.mcp.discovery.McpServiceDiscovery] - MCP������������
2025-07-27 20:54:48 [main] INFO  [com.nexus.common.config.RocketMQConfig] - ��ʼ��RocketMQTemplate��NameServer: localhost:9876, ProducerGroup: nexus-producer-group
2025-07-27 20:54:58 [main] INFO  [o.s.c.gateway.route.RouteDefinitionRouteLocator] - Loaded RoutePredicateFactory [After]
2025-07-27 20:54:58 [main] INFO  [o.s.c.gateway.route.RouteDefinitionRouteLocator] - Loaded RoutePredicateFactory [Before]
2025-07-27 20:54:58 [main] INFO  [o.s.c.gateway.route.RouteDefinitionRouteLocator] - Loaded RoutePredicateFactory [Between]
2025-07-27 20:54:58 [main] INFO  [o.s.c.gateway.route.RouteDefinitionRouteLocator] - Loaded RoutePredicateFactory [Cookie]
2025-07-27 20:54:58 [main] INFO  [o.s.c.gateway.route.RouteDefinitionRouteLocator] - Loaded RoutePredicateFactory [Header]
2025-07-27 20:54:58 [main] INFO  [o.s.c.gateway.route.RouteDefinitionRouteLocator] - Loaded RoutePredicateFactory [Host]
2025-07-27 20:54:58 [main] INFO  [o.s.c.gateway.route.RouteDefinitionRouteLocator] - Loaded RoutePredicateFactory [Method]
2025-07-27 20:54:58 [main] INFO  [o.s.c.gateway.route.RouteDefinitionRouteLocator] - Loaded RoutePredicateFactory [Path]
2025-07-27 20:54:58 [main] INFO  [o.s.c.gateway.route.RouteDefinitionRouteLocator] - Loaded RoutePredicateFactory [Query]
2025-07-27 20:54:58 [main] INFO  [o.s.c.gateway.route.RouteDefinitionRouteLocator] - Loaded RoutePredicateFactory [ReadBody]
2025-07-27 20:54:58 [main] INFO  [o.s.c.gateway.route.RouteDefinitionRouteLocator] - Loaded RoutePredicateFactory [RemoteAddr]
2025-07-27 20:54:58 [main] INFO  [o.s.c.gateway.route.RouteDefinitionRouteLocator] - Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-07-27 20:54:58 [main] INFO  [o.s.c.gateway.route.RouteDefinitionRouteLocator] - Loaded RoutePredicateFactory [Weight]
2025-07-27 20:54:58 [main] INFO  [o.s.c.gateway.route.RouteDefinitionRouteLocator] - Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-07-27 20:55:00 [main] INFO  [o.s.b.actuate.endpoint.web.EndpointLinksResolver] - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-07-27 20:55:01 [main] WARN  [o.a.r.s.autoconfigure.RocketMQAutoConfiguration] - The necessary spring property 'rocketmq.name-server' is not defined, all rockertmq beans creation are skipped!
2025-07-27 20:55:03 [main] WARN  [o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger] - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-27 20:55:08 [main] INFO  [o.a.r.s.support.DefaultRocketMQListenerContainer] - running container: DefaultRocketMQListenerContainer{consumerGroup='log-consumer-group', namespace='', namespaceV2='', nameServer='localhost:9876', topic='log-event-topic', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='audit-log', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-27 20:55:08 [main] INFO  [o.s.boot.web.embedded.netty.NettyWebServer] - Netty started on port 8080
2025-07-27 20:55:09 [main] INFO  [c.a.n.p.auth.spi.client.ClientAuthPluginManager] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-27 20:55:09 [main] INFO  [c.a.n.p.auth.spi.client.ClientAuthPluginManager] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-27 20:55:09 [main] INFO  [c.a.cloud.nacos.registry.NacosServiceRegistry] - nacos registry, DEFAULT_GROUP nexus-gateway *************:8080 register finished
2025-07-27 20:55:10 [main] INFO  [c.a.c.n.discovery.GatewayLocatorHeartBeatPublisher] - Start nacos gateway locator heartBeat task scheduler.
2025-07-27 20:55:10 [main] INFO  [com.nexus.gateway.NexusGatewayApplication] - Started NexusGatewayApplication in 74.938 seconds (JVM running for 77.446)
2025-07-27 20:55:10 [main] INFO  [c.a.cloud.nacos.refresh.NacosContextRefresher] - [Nacos Config] Listening config: dataId=nexus-gateway.yml, group=DEFAULT_GROUP
2025-07-27 20:55:17 [pool-6-thread-1] DEBUG [com.nexus.common.mcp.discovery.McpServiceDiscovery] - ִ��MCP���񽡿����
2025-07-27 20:56:17 [pool-6-thread-1] DEBUG [com.nexus.common.mcp.discovery.McpServiceDiscovery] - ִ��MCP���񽡿����
2025-07-27 20:57:17 [pool-6-thread-1] DEBUG [com.nexus.common.mcp.discovery.McpServiceDiscovery] - ִ��MCP���񽡿����
2025-07-27 20:58:17 [pool-6-thread-1] DEBUG [com.nexus.common.mcp.discovery.McpServiceDiscovery] - ִ��MCP���񽡿����
2025-07-27 20:59:17 [pool-6-thread-1] DEBUG [com.nexus.common.mcp.discovery.McpServiceDiscovery] - ִ��MCP���񽡿����
2025-07-27 21:00:17 [pool-6-thread-1] DEBUG [com.nexus.common.mcp.discovery.McpServiceDiscovery] - ִ��MCP���񽡿����
2025-07-27 21:01:17 [pool-6-thread-1] DEBUG [com.nexus.common.mcp.discovery.McpServiceDiscovery] - ִ��MCP���񽡿����
2025-07-27 21:02:17 [pool-6-thread-1] DEBUG [com.nexus.common.mcp.discovery.McpServiceDiscovery] - ִ��MCP���񽡿����
2025-07-27 21:03:17 [pool-6-thread-1] DEBUG [com.nexus.common.mcp.discovery.McpServiceDiscovery] - ִ��MCP���񽡿����
2025-07-27 21:04:17 [pool-6-thread-1] DEBUG [com.nexus.common.mcp.discovery.McpServiceDiscovery] - ִ��MCP���񽡿����
2025-07-27 21:05:17 [pool-6-thread-1] DEBUG [com.nexus.common.mcp.discovery.McpServiceDiscovery] - ִ��MCP���񽡿����
2025-07-27 21:21:46 [main] INFO  [com.nexus.gateway.NexusGatewayApplication] - Starting NexusGatewayApplication v0.0.1-SNAPSHOT using Java 1.8.0_202 on DESKTOP-7E5G8UV with PID 14324 (D:\nexus_agent_service\nexus-microservices\nexus-gateway\target\nexus-gateway-0.0.1-SNAPSHOT.jar started by adm1n in D:\nexus_agent_service\nexus-microservices)
2025-07-27 21:21:46 [main] DEBUG [com.nexus.gateway.NexusGatewayApplication] - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-27 21:21:46 [main] INFO  [com.nexus.gateway.NexusGatewayApplication] - The following 1 profile is active: "local"
2025-07-27 21:21:46 [main] INFO  [c.a.cloud.nacos.configdata.NacosConfigDataLoader] - [Nacos Config] Load config[dataId=nexus-gateway.yml, group=DEFAULT_GROUP] success
2025-07-27 21:21:51 [main] INFO  [o.s.d.r.config.RepositoryConfigurationDelegate] - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 21:21:51 [main] INFO  [o.s.d.r.config.RepositoryConfigurationDelegate] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-27 21:21:51 [main] INFO  [o.s.d.r.config.RepositoryConfigurationDelegate] - Finished Spring Data repository scanning in 33 ms. Found 0 Redis repository interfaces.
2025-07-27 21:21:53 [main] INFO  [o.springframework.cloud.context.scope.GenericScope] - BeanFactory id=17f20ea9-c95a-3e9d-8179-0f7e81021130
2025-07-27 21:21:53 [main] INFO  [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-27 21:21:53 [main] INFO  [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-27 21:21:53 [main] INFO  [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-27 21:21:54 [main] INFO  [com.nexus.gateway.service.JwtService] - JWT Service��ʼ�� - ��Կ: nexus-microservices-...
2025-07-27 21:21:54 [main] INFO  [com.nexus.gateway.service.JwtService] - JWT Service��ʼ����� - ����ʱ��: 86400000ms
2025-07-27 21:21:55 [main] INFO  [org.redisson.Version] - Redisson 3.17.7
2025-07-27 21:21:58 [redisson-netty-3-8] INFO  [o.r.connection.pool.MasterPubSubConnectionPool] - 1 connections initialized for localhost/127.0.0.1:6379
2025-07-27 21:21:59 [redisson-netty-3-14] INFO  [org.redisson.connection.pool.MasterConnectionPool] - 5 connections initialized for localhost/127.0.0.1:6379
2025-07-27 21:22:03 [main] INFO  [o.a.r.s.s.RocketMQMessageListenerContainerRegistrar] - Register the listener to container, listenerBeanName:auditLogConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-27 21:22:03 [main] INFO  [c.n.common.mcp.config.StandardMcpServiceRegistry] - ע��ٷ�MCP����
2025-07-27 21:22:03 [main] DEBUG [c.n.common.mcp.config.StandardMcpServiceRegistry] - ע��MCP����: filesystem -> @modelcontextprotocol/server-filesystem
2025-07-27 21:22:03 [main] DEBUG [c.n.common.mcp.config.StandardMcpServiceRegistry] - ע��MCP����: git -> mcp-server-git
2025-07-27 21:22:03 [main] DEBUG [c.n.common.mcp.config.StandardMcpServiceRegistry] - ע��MCP����: github -> @modelcontextprotocol/server-github
2025-07-27 21:22:03 [main] DEBUG [c.n.common.mcp.config.StandardMcpServiceRegistry] - ע��MCP����: memory -> @modelcontextprotocol/server-memory
2025-07-27 21:22:03 [main] DEBUG [c.n.common.mcp.config.StandardMcpServiceRegistry] - ע��MCP����: time -> @modelcontextprotocol/server-time
2025-07-27 21:22:03 [main] DEBUG [c.n.common.mcp.config.StandardMcpServiceRegistry] - ע��MCP����: fetch -> @modelcontextprotocol/server-fetch
2025-07-27 21:22:03 [main] DEBUG [c.n.common.mcp.config.StandardMcpServiceRegistry] - ע��MCP����: sequential-thinking -> @modelcontextprotocol/server-sequentialthinking
2025-07-27 21:22:03 [main] INFO  [c.n.common.mcp.config.StandardMcpServiceRegistry] - ע������MCP����
2025-07-27 21:22:03 [main] DEBUG [c.n.common.mcp.config.StandardMcpServiceRegistry] - ע��MCP����: brave-search -> mcp-server-brave-search
2025-07-27 21:22:03 [main] DEBUG [c.n.common.mcp.config.StandardMcpServiceRegistry] - ע��MCP����: postgres -> @modelcontextprotocol/server-postgres
2025-07-27 21:22:03 [main] DEBUG [c.n.common.mcp.config.StandardMcpServiceRegistry] - ע��MCP����: sqlite -> mcp-server-sqlite
2025-07-27 21:22:03 [main] DEBUG [c.n.common.mcp.config.StandardMcpServiceRegistry] - ע��MCP����: puppeteer -> @modelcontextprotocol/server-puppeteer
2025-07-27 21:22:03 [main] DEBUG [c.n.common.mcp.notification.McpNotificationManager] - ע��MCP֪ͨ������: * -> McpServiceDiscovery
2025-07-27 21:22:03 [main] INFO  [com.nexus.common.mcp.discovery.McpServiceDiscovery] - MCP������������
2025-07-27 21:22:03 [main] INFO  [com.nexus.common.config.RocketMQConfig] - ��ʼ��RocketMQTemplate��NameServer: localhost:9876, ProducerGroup: nexus-producer-group
2025-07-27 21:22:10 [main] INFO  [o.s.c.gateway.route.RouteDefinitionRouteLocator] - Loaded RoutePredicateFactory [After]
2025-07-27 21:22:10 [main] INFO  [o.s.c.gateway.route.RouteDefinitionRouteLocator] - Loaded RoutePredicateFactory [Before]
2025-07-27 21:22:10 [main] INFO  [o.s.c.gateway.route.RouteDefinitionRouteLocator] - Loaded RoutePredicateFactory [Between]
2025-07-27 21:22:10 [main] INFO  [o.s.c.gateway.route.RouteDefinitionRouteLocator] - Loaded RoutePredicateFactory [Cookie]
2025-07-27 21:22:10 [main] INFO  [o.s.c.gateway.route.RouteDefinitionRouteLocator] - Loaded RoutePredicateFactory [Header]
2025-07-27 21:22:10 [main] INFO  [o.s.c.gateway.route.RouteDefinitionRouteLocator] - Loaded RoutePredicateFactory [Host]
2025-07-27 21:22:10 [main] INFO  [o.s.c.gateway.route.RouteDefinitionRouteLocator] - Loaded RoutePredicateFactory [Method]
2025-07-27 21:22:10 [main] INFO  [o.s.c.gateway.route.RouteDefinitionRouteLocator] - Loaded RoutePredicateFactory [Path]
2025-07-27 21:22:10 [main] INFO  [o.s.c.gateway.route.RouteDefinitionRouteLocator] - Loaded RoutePredicateFactory [Query]
2025-07-27 21:22:10 [main] INFO  [o.s.c.gateway.route.RouteDefinitionRouteLocator] - Loaded RoutePredicateFactory [ReadBody]
2025-07-27 21:22:10 [main] INFO  [o.s.c.gateway.route.RouteDefinitionRouteLocator] - Loaded RoutePredicateFactory [RemoteAddr]
2025-07-27 21:22:10 [main] INFO  [o.s.c.gateway.route.RouteDefinitionRouteLocator] - Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-07-27 21:22:10 [main] INFO  [o.s.c.gateway.route.RouteDefinitionRouteLocator] - Loaded RoutePredicateFactory [Weight]
2025-07-27 21:22:10 [main] INFO  [o.s.c.gateway.route.RouteDefinitionRouteLocator] - Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-07-27 21:22:13 [main] INFO  [o.s.b.actuate.endpoint.web.EndpointLinksResolver] - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-07-27 21:22:14 [main] WARN  [o.a.r.s.autoconfigure.RocketMQAutoConfiguration] - The necessary spring property 'rocketmq.name-server' is not defined, all rockertmq beans creation are skipped!
2025-07-27 21:22:15 [main] WARN  [o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger] - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-27 21:22:19 [main] INFO  [o.a.r.s.support.DefaultRocketMQListenerContainer] - running container: DefaultRocketMQListenerContainer{consumerGroup='log-consumer-group', namespace='', namespaceV2='', nameServer='localhost:9876', topic='log-event-topic', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='audit-log', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-27 21:22:19 [main] INFO  [o.s.boot.web.embedded.netty.NettyWebServer] - Netty started on port 8080
2025-07-27 21:22:20 [main] INFO  [c.a.n.p.auth.spi.client.ClientAuthPluginManager] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-27 21:22:20 [main] INFO  [c.a.n.p.auth.spi.client.ClientAuthPluginManager] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-27 21:22:20 [main] INFO  [c.a.cloud.nacos.registry.NacosServiceRegistry] - nacos registry, DEFAULT_GROUP nexus-gateway *************:8080 register finished
2025-07-27 21:22:20 [main] INFO  [c.a.c.n.discovery.GatewayLocatorHeartBeatPublisher] - Start nacos gateway locator heartBeat task scheduler.
2025-07-27 21:22:21 [main] INFO  [com.nexus.gateway.NexusGatewayApplication] - Started NexusGatewayApplication in 54.052 seconds (JVM running for 55.653)
2025-07-27 21:22:21 [main] INFO  [c.a.cloud.nacos.refresh.NacosContextRefresher] - [Nacos Config] Listening config: dataId=nexus-gateway.yml, group=DEFAULT_GROUP
2025-07-27 21:22:33 [pool-6-thread-1] DEBUG [com.nexus.common.mcp.discovery.McpServiceDiscovery] - ִ��MCP���񽡿����
2025-07-27 21:23:33 [pool-6-thread-1] DEBUG [com.nexus.common.mcp.discovery.McpServiceDiscovery] - ִ��MCP���񽡿����
2025-07-27 21:24:33 [pool-6-thread-1] DEBUG [com.nexus.common.mcp.discovery.McpServiceDiscovery] - ִ��MCP���񽡿����
2025-07-27 21:25:33 [pool-6-thread-1] DEBUG [com.nexus.common.mcp.discovery.McpServiceDiscovery] - ִ��MCP���񽡿����
2025-07-27 21:26:33 [pool-6-thread-1] DEBUG [com.nexus.common.mcp.discovery.McpServiceDiscovery] - ִ��MCP���񽡿����
2025-07-27 21:27:33 [pool-6-thread-1] DEBUG [com.nexus.common.mcp.discovery.McpServiceDiscovery] - ִ��MCP���񽡿����
2025-07-27 21:28:33 [pool-6-thread-1] DEBUG [com.nexus.common.mcp.discovery.McpServiceDiscovery] - ִ��MCP���񽡿����
2025-07-27 21:29:33 [pool-6-thread-1] DEBUG [com.nexus.common.mcp.discovery.McpServiceDiscovery] - ִ��MCP���񽡿����
2025-07-27 21:30:33 [pool-6-thread-1] DEBUG [com.nexus.common.mcp.discovery.McpServiceDiscovery] - ִ��MCP���񽡿����
2025-07-27 21:31:33 [pool-6-thread-1] DEBUG [com.nexus.common.mcp.discovery.McpServiceDiscovery] - ִ��MCP���񽡿����
2025-07-27 21:31:43 [Thread-7] WARN  [com.alibaba.nacos.common.notify.NotifyCenter] - [NotifyCenter] Start destroying Publisher
2025-07-27 21:31:43 [Thread-2] WARN  [com.alibaba.nacos.common.http.HttpClientBeanHolder] - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-27 21:31:43 [Thread-7] WARN  [com.alibaba.nacos.common.notify.NotifyCenter] - [NotifyCenter] Destruction of the end
2025-07-27 21:31:43 [Thread-2] WARN  [com.alibaba.nacos.common.http.HttpClientBeanHolder] - [HttpClientBeanHolder] Destruction of the end
2025-07-27 21:31:43 [SpringApplicationShutdownHook] INFO  [c.a.cloud.nacos.registry.NacosServiceRegistry] - De-registering from Nacos Server now...
2025-07-27 21:31:43 [SpringApplicationShutdownHook] INFO  [c.a.cloud.nacos.registry.NacosServiceRegistry] - De-registration finished.
2025-07-27 21:31:43 [SpringApplicationShutdownHook] INFO  [c.n.common.mcp.process.McpServiceProcessManager] - Shutting down MCP service process manager
2025-07-27 21:31:43 [SpringApplicationShutdownHook] INFO  [c.n.common.mcp.process.McpServiceProcessManager] - MCP service process manager shutdown completed
2025-07-27 21:31:43 [SpringApplicationShutdownHook] INFO  [com.nexus.common.mcp.discovery.McpServiceDiscovery] - MCP�������ѹر�
2025-07-27 21:31:43 [SpringApplicationShutdownHook] INFO  [o.a.r.s.support.DefaultRocketMQListenerContainer] - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='log-consumer-group', namespace='', namespaceV2='', nameServer='localhost:9876', topic='log-event-topic', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='audit-log', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
