2025-07-27 20:54:10.813  INFO 20780 --- [main] c.n.r.NexusRealtimeServiceApplication    : Starting NexusRealtimeServiceApplication v0.0.1-SNAPSHOT using Java 1.8.0_202 on DESKTOP-7E5G8UV with PID 20780 (D:\nexus_agent_service\nexus-microservices\nexus-realtime-service\target\nexus-realtime-service-0.0.1-SNAPSHOT.jar started by adm1n in D:\nexus_agent_service\nexus-microservices)
2025-07-27 20:54:10.858  INFO 20780 --- [main] c.n.r.NexusRealtimeServiceApplication    : No active profile set, falling back to 1 default profile: "default"
2025-07-27 20:54:11.422  INFO 20780 --- [main] c.a.c.n.c.NacosConfigDataLoader          : [Nacos Config] Load config[dataId=nexus-realtime-service.yml, group=DEFAULT_GROUP] success
2025-07-27 20:54:11.427  WARN 20780 --- [main] o.s.b.c.config.ConfigDataEnvironment     : Property 'spring.profiles' imported from location 'class path resource [application.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [application.yml] from nexus-realtime-service-0.0.1-SNAPSHOT.jar - 79:13]
2025-07-27 20:54:17.048  INFO 20780 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 20:54:17.059  INFO 20780 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-27 20:54:17.108  INFO 20780 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 17 ms. Found 0 JPA repository interfaces.
2025-07-27 20:54:17.149  INFO 20780 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 20:54:17.153  INFO 20780 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-27 20:54:17.184  INFO 20780 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 0 Redis repository interfaces.
2025-07-27 20:54:18.607  INFO 20780 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=1a42c24d-7a63-3f69-baeb-d58bfcbfd203
2025-07-27 20:54:24.214  INFO 20780 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8087 (http)
2025-07-27 20:54:24.358  INFO 20780 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-27 20:54:24.363  INFO 20780 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-27 20:54:24.896  INFO 20780 --- [main] o.a.c.c.C.[.[localhost].[/realtime]      : Initializing Spring embedded WebApplicationContext
2025-07-27 20:54:24.898  INFO 20780 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 13463 ms
2025-07-27 20:54:26.623  INFO 20780 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-27 20:54:26.860  INFO 20780 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-07-27 20:54:27.603  INFO 20780 --- [main] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-27 20:54:28.694  INFO 20780 --- [main] com.zaxxer.hikari.HikariDataSource       : NexusMicroservicesPool - Starting...
2025-07-27 20:54:37.425  INFO 20780 --- [main] com.zaxxer.hikari.HikariDataSource       : NexusMicroservicesPool - Start completed.
2025-07-27 20:54:37.545  INFO 20780 --- [main] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.PostgreSQLDialect
2025-07-27 20:54:40.815  INFO 20780 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-27 20:54:40.869  INFO 20780 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 20:54:43.011  INFO 20780 --- [main] org.redisson.Version                     : Redisson 3.17.7
2025-07-27 20:54:46.080  INFO 20780 --- [redisson-netty-2-8] o.r.c.pool.MasterPubSubConnectionPool    : 1 connections initialized for localhost/127.0.0.1:6379
2025-07-27 20:54:46.269  INFO 20780 --- [redisson-netty-2-14] o.r.c.pool.MasterConnectionPool          : 5 connections initialized for localhost/127.0.0.1:6379
2025-07-27 20:54:47.306  INFO 20780 --- [main] c.n.realtime.service.RealtimeService     : Realtime Service initialized
2025-07-27 20:54:47.308  INFO 20780 --- [main] c.n.realtime.manager.ConnectionManager   : ��ʼ�����ӹ�����...
2025-07-27 20:54:47.314  INFO 20780 --- [main] c.n.realtime.manager.ConnectionManager   : ���ӹ�������ʼ�����
2025-07-27 20:54:50.646  INFO 20780 --- [main] ocketMQMessageListenerContainerRegistrar : Register the listener to container, listenerBeanName:auditLogConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-27 20:54:50.654  INFO 20780 --- [main] c.n.c.m.c.StandardMcpServiceRegistry     : ע��ٷ�MCP����
2025-07-27 20:54:50.658  INFO 20780 --- [main] c.n.c.m.c.StandardMcpServiceRegistry     : ע������MCP����
2025-07-27 20:54:50.680  INFO 20780 --- [main] c.n.c.mcp.discovery.McpServiceDiscovery  : MCP������������
2025-07-27 20:54:50.742  INFO 20780 --- [main] com.nexus.common.config.RocketMQConfig   : ��ʼ��RocketMQTemplate��NameServer: localhost:9876, ProducerGroup: nexus-producer-group
2025-07-27 20:54:51.287  WARN 20780 --- [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-27 20:55:00.708  INFO 20780 --- [main] g.s.a.GrpcServerFactoryAutoConfiguration : Detected grpc-netty-shaded: Creating ShadedNettyGrpcServerFactory
2025-07-27 20:55:00.825  WARN 20780 --- [main] o.a.r.s.a.RocketMQAutoConfiguration      : The necessary spring property 'rocketmq.name-server' is not defined, all rockertmq beans creation are skipped!
2025-07-27 20:55:01.417  INFO 20780 --- [main] n.d.b.g.c.a.GrpcClientAutoConfiguration  : Detected grpc-netty-shaded: Creating ShadedNettyChannelFactory + InProcessChannelFactory
2025-07-27 20:55:02.905  INFO 20780 --- [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-27 20:55:08.146  INFO 20780 --- [main] a.r.s.s.DefaultRocketMQListenerContainer : running container: DefaultRocketMQListenerContainer{consumerGroup='log-consumer-group', namespace='', namespaceV2='', nameServer='localhost:9876', topic='log-event-topic', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='audit-log', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-27 20:55:08.165  INFO 20780 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8087 (http) with context path '/realtime'
2025-07-27 20:55:08.197  INFO 20780 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-27 20:55:08.197  INFO 20780 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-27 20:55:08.361  INFO 20780 --- [main] c.a.c.n.registry.NacosServiceRegistry    : nacos registry, DEFAULT_GROUP nexus-realtime-service *************:8087 register finished
2025-07-27 20:55:09.041  INFO 20780 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler     : Starting...
2025-07-27 20:55:09.043  INFO 20780 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler     : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2bfeb1ef]]
2025-07-27 20:55:09.044  INFO 20780 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler     : Started.
2025-07-27 20:55:09.180  INFO 20780 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: grpc.health.v1.Health, bean: grpcHealthService, class: io.grpc.protobuf.services.HealthServiceImpl
2025-07-27 20:55:09.181  INFO 20780 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: grpc.reflection.v1alpha.ServerReflection, bean: protoReflectionService, class: io.grpc.protobuf.services.ProtoReflectionService
2025-07-27 20:55:09.940  INFO 20780 --- [main] n.d.b.g.s.s.GrpcServerLifecycle          : gRPC Server started, listening on address: *, port: 9090
2025-07-27 20:55:09.997  INFO 20780 --- [main] c.n.r.NexusRealtimeServiceApplication    : Started NexusRealtimeServiceApplication in 83.635 seconds (JVM running for 87.29)
2025-07-27 20:55:10.087  INFO 20780 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=nexus-realtime-service.yml, group=DEFAULT_GROUP
2025-07-27 20:55:51.158  INFO 20780 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats    : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-27 21:21:34.620  INFO 2648 --- [main] c.n.r.NexusRealtimeServiceApplication    : Starting NexusRealtimeServiceApplication v0.0.1-SNAPSHOT using Java 1.8.0_202 on DESKTOP-7E5G8UV with PID 2648 (D:\nexus_agent_service\nexus-microservices\nexus-realtime-service\target\nexus-realtime-service-0.0.1-SNAPSHOT.jar started by adm1n in D:\nexus_agent_service\nexus-microservices)
2025-07-27 21:21:34.636  INFO 2648 --- [main] c.n.r.NexusRealtimeServiceApplication    : No active profile set, falling back to 1 default profile: "default"
2025-07-27 21:21:34.821  INFO 2648 --- [main] c.a.c.n.c.NacosConfigDataLoader          : [Nacos Config] Load config[dataId=nexus-realtime-service.yml, group=DEFAULT_GROUP] success
2025-07-27 21:21:34.982  WARN 2648 --- [main] o.s.b.c.config.ConfigDataEnvironment     : Property 'spring.profiles' imported from location 'class path resource [application.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [application.yml] from nexus-realtime-service-0.0.1-SNAPSHOT.jar - 79:13]
2025-07-27 21:21:38.833  INFO 2648 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 21:21:38.841  INFO 2648 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-27 21:21:38.929  INFO 2648 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 30 ms. Found 0 JPA repository interfaces.
2025-07-27 21:21:39.039  INFO 2648 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 21:21:39.044  INFO 2648 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-27 21:21:39.076  INFO 2648 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 0 Redis repository interfaces.
2025-07-27 21:21:40.101  INFO 2648 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=1a42c24d-7a63-3f69-baeb-d58bfcbfd203
2025-07-27 21:21:44.159  INFO 2648 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8087 (http)
2025-07-27 21:21:44.195  INFO 2648 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-27 21:21:44.196  INFO 2648 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-27 21:21:44.544  INFO 2648 --- [main] o.a.c.c.C.[.[localhost].[/realtime]      : Initializing Spring embedded WebApplicationContext
2025-07-27 21:21:44.545  INFO 2648 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 9548 ms
2025-07-27 21:21:45.493  INFO 2648 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-27 21:21:45.741  INFO 2648 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-07-27 21:21:46.547  INFO 2648 --- [main] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-27 21:21:47.044  INFO 2648 --- [main] com.zaxxer.hikari.HikariDataSource       : NexusMicroservicesPool - Starting...
2025-07-27 21:21:52.480  INFO 2648 --- [main] com.zaxxer.hikari.HikariDataSource       : NexusMicroservicesPool - Start completed.
2025-07-27 21:21:52.574  INFO 2648 --- [main] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.PostgreSQLDialect
2025-07-27 21:21:55.683  INFO 2648 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-27 21:21:55.709  INFO 2648 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 21:21:57.438  INFO 2648 --- [main] org.redisson.Version                     : Redisson 3.17.7
2025-07-27 21:22:00.828  INFO 2648 --- [redisson-netty-2-6] o.r.c.pool.MasterPubSubConnectionPool    : 1 connections initialized for localhost/127.0.0.1:6379
2025-07-27 21:22:00.886  INFO 2648 --- [redisson-netty-2-14] o.r.c.pool.MasterConnectionPool          : 5 connections initialized for localhost/127.0.0.1:6379
2025-07-27 21:22:01.479  INFO 2648 --- [main] c.n.realtime.service.RealtimeService     : Realtime Service initialized
2025-07-27 21:22:01.480  INFO 2648 --- [main] c.n.realtime.manager.ConnectionManager   : ��ʼ�����ӹ�����...
2025-07-27 21:22:01.483  INFO 2648 --- [main] c.n.realtime.manager.ConnectionManager   : ���ӹ�������ʼ�����
2025-07-27 21:22:04.381  INFO 2648 --- [main] ocketMQMessageListenerContainerRegistrar : Register the listener to container, listenerBeanName:auditLogConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-27 21:22:04.390  INFO 2648 --- [main] c.n.c.m.c.StandardMcpServiceRegistry     : ע��ٷ�MCP����
2025-07-27 21:22:04.397  INFO 2648 --- [main] c.n.c.m.c.StandardMcpServiceRegistry     : ע������MCP����
2025-07-27 21:22:04.421  INFO 2648 --- [main] c.n.c.mcp.discovery.McpServiceDiscovery  : MCP������������
2025-07-27 21:22:04.501  INFO 2648 --- [main] com.nexus.common.config.RocketMQConfig   : ��ʼ��RocketMQTemplate��NameServer: localhost:9876, ProducerGroup: nexus-producer-group
2025-07-27 21:22:04.912  WARN 2648 --- [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-27 21:22:11.168  INFO 2648 --- [main] g.s.a.GrpcServerFactoryAutoConfiguration : Detected grpc-netty-shaded: Creating ShadedNettyGrpcServerFactory
2025-07-27 21:22:11.284  WARN 2648 --- [main] o.a.r.s.a.RocketMQAutoConfiguration      : The necessary spring property 'rocketmq.name-server' is not defined, all rockertmq beans creation are skipped!
2025-07-27 21:22:12.186  INFO 2648 --- [main] n.d.b.g.c.a.GrpcClientAutoConfiguration  : Detected grpc-netty-shaded: Creating ShadedNettyChannelFactory + InProcessChannelFactory
2025-07-27 21:22:13.752  INFO 2648 --- [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-27 21:22:18.777  INFO 2648 --- [main] a.r.s.s.DefaultRocketMQListenerContainer : running container: DefaultRocketMQListenerContainer{consumerGroup='log-consumer-group', namespace='', namespaceV2='', nameServer='localhost:9876', topic='log-event-topic', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='audit-log', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-27 21:22:18.797  INFO 2648 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8087 (http) with context path '/realtime'
2025-07-27 21:22:18.826  INFO 2648 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-27 21:22:18.826  INFO 2648 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-27 21:22:18.988  INFO 2648 --- [main] c.a.c.n.registry.NacosServiceRegistry    : nacos registry, DEFAULT_GROUP nexus-realtime-service *************:8087 register finished
2025-07-27 21:22:19.538  INFO 2648 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler     : Starting...
2025-07-27 21:22:19.539  INFO 2648 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler     : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@46044faa]]
2025-07-27 21:22:19.540  INFO 2648 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler     : Started.
2025-07-27 21:22:19.661  INFO 2648 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: grpc.health.v1.Health, bean: grpcHealthService, class: io.grpc.protobuf.services.HealthServiceImpl
2025-07-27 21:22:19.662  INFO 2648 --- [main] n.d.b.g.s.s.AbstractGrpcServerFactory    : Registered gRPC service: grpc.reflection.v1alpha.ServerReflection, bean: protoReflectionService, class: io.grpc.protobuf.services.ProtoReflectionService
2025-07-27 21:22:20.214  INFO 2648 --- [main] n.d.b.g.s.s.GrpcServerLifecycle          : gRPC Server started, listening on address: *, port: 9090
2025-07-27 21:22:20.249  INFO 2648 --- [main] c.n.r.NexusRealtimeServiceApplication    : Started NexusRealtimeServiceApplication in 63.06 seconds (JVM running for 64.893)
2025-07-27 21:22:20.306  INFO 2648 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=nexus-realtime-service.yml, group=DEFAULT_GROUP
2025-07-27 21:23:04.810  INFO 2648 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats    : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-27 21:31:42.346  WARN 2648 --- [Thread-7] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Start destroying Publisher
2025-07-27 21:31:42.347  WARN 2648 --- [Thread-2] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-27 21:31:42.347  WARN 2648 --- [Thread-7] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Destruction of the end
2025-07-27 21:31:42.349  WARN 2648 --- [Thread-2] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Destruction of the end
2025-07-27 21:31:42.352  INFO 2648 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler     : Stopping...
2025-07-27 21:31:42.353  INFO 2648 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler     : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@46044faa]]
2025-07-27 21:31:42.354  INFO 2648 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler     : Stopped.
2025-07-27 21:31:42.381  INFO 2648 --- [SpringApplicationShutdownHook] n.d.b.g.s.s.GrpcServerLifecycle          : Completed gRPC server shutdown
2025-07-27 21:31:45.813  INFO 2648 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registering from Nacos Server now...
2025-07-27 21:31:45.820  INFO 2648 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registration finished.
2025-07-27 21:31:45.828  INFO 2648 --- [SpringApplicationShutdownHook] c.n.c.m.p.McpServiceProcessManager       : Shutting down MCP service process manager
2025-07-27 21:31:45.830  INFO 2648 --- [SpringApplicationShutdownHook] c.n.c.m.p.McpServiceProcessManager       : MCP service process manager shutdown completed
2025-07-27 21:31:45.830  INFO 2648 --- [SpringApplicationShutdownHook] c.n.c.mcp.discovery.McpServiceDiscovery  : MCP�������ѹر�
2025-07-27 21:31:45.831  INFO 2648 --- [SpringApplicationShutdownHook] a.r.s.s.DefaultRocketMQListenerContainer : container destroyed, DefaultRocketMQListenerContainer{consumerGroup='log-consumer-group', namespace='', namespaceV2='', nameServer='localhost:9876', topic='log-event-topic', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='audit-log', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-27 21:31:45.872  INFO 2648 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 21:31:45.879  INFO 2648 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : NexusMicroservicesPool - Shutdown initiated...
2025-07-27 21:31:45.890  INFO 2648 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : NexusMicroservicesPool - Shutdown completed.
