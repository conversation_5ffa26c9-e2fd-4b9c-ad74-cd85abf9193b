# Nexus Auth Service Configuration
# Data ID: nexus-auth-service.yml, Group: DEFAULT_GROUP

server:
  port: 8081
  servlet:
    context-path: /auth

spring:
  application:
    name: nexus-auth-service

  # Redis database allocation
  redis:
    database: 1  # Use database 1 to avoid conflicts

  # RocketMQ configuration
  rocketmq:
    name-server: localhost:9876
    producer:
      group: nexus-auth-producer-group
      send-message-timeout: 3000
      retry-times-when-send-failed: 2
    consumer:
      group: nexus-auth-consumer-group

# Authentication service configuration
nexus:
  auth:
    # JWT configuration
    jwt:
      secret: nexus-microservices-jwt-secret-key-2024
      expiration: 86400 # 24 hours
      refresh-expiration: 604800 # 7 days

    # API key configuration
    api-key:
      prefix: nxs_
      length: 32

    # Security configuration
    security:
      # Password policy
      password:
        min-length: 8
        require-uppercase: true
        require-lowercase: true
        require-numbers: true
        require-special-chars: false

      # Login restrictions
      login:
        max-attempts: 5
        lockout-duration: 300000 # 5 minutes

    # Rate limiting configuration
    rate-limit:
      enabled: true
      requests-per-minute: 60
      burst-capacity: 100

  # Email service configuration
  email:
    enabled: false
    mock: true
    from: <EMAIL>
    from-name: Nexus Microservices Platform

  # RocketMQ listener control
  rocketmq:
    subscription:
      enabled: false
    user:
      enabled: true
    notification:
      enabled: true

# Enhanced logging configuration
logging:
  level:
    com.nexus.auth: DEBUG
    com.nexus.common: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE