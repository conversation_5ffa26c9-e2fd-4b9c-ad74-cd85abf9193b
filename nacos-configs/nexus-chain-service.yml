# Nexus Chain Service Configuration
# Data ID: nexus-chain-service.yml, Group: DEFAULT_GROUP

server:
  port: 8086
  servlet:
    context-path: /chain

spring:
  application:
    name: nexus-chain-service

  # Redis database allocation
  redis:
    database: 5

# Chain service configuration
nexus:
  chain:
    # Service chain configuration
    service-chain:
      enabled: true
      max-chain-length: 10
      execution-timeout: 300000 # 5 minutes
      parallel-execution: true
      max-parallel-tasks: 5
    
    # Chain execution configuration
    execution:
      retry-enabled: true
      max-retry-attempts: 3
      retry-delay: 1000 # milliseconds
      circuit-breaker-enabled: true
      circuit-breaker-threshold: 5
      circuit-breaker-timeout: 60000 # 1 minute
    
    # Chain storage configuration
    storage:
      enabled: true
      max-stored-chains: 1000
      cleanup-interval: 3600 # 1 hour
      retention-period: 604800 # 7 days
    
    # Chain monitoring configuration
    monitoring:
      metrics-enabled: true
      performance-tracking: true
      error-reporting: true
      execution-logging: true
    
    # Chain security configuration
    security:
      validation-enabled: true
      sandbox-mode: true
      resource-limits:
        max-memory: 512MB
        max-cpu-time: 300 # seconds
        max-network-requests: 100
    
    # Chain caching configuration
    cache:
      enabled: true
      chain-definition-ttl: 3600 # 1 hour
      execution-result-ttl: 1800 # 30 minutes
      user-chains-ttl: 300 # 5 minutes

  # RocketMQ listener control
  rocketmq:
    chain:
      enabled: true
    subscription:
      enabled: false
    user:
      enabled: false
    notification:
      enabled: false

# Service inter-communication configuration
service:
  auth:
    url: http://nexus-auth-service:8081
  subscription:
    url: http://nexus-subscription-service:8084
  mcp-local:
    url: http://nexus-mcp-local-service:8082
  mcp-remote:
    url: http://nexus-mcp-remote-service:8083

# Logging configuration
logging:
  level:
    com.nexus.chain: DEBUG
    org.springframework.amqp: INFO
    org.springframework.cache: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{50}] - %msg%n"
  file:
    name: logs/nexus-chain-service.log
