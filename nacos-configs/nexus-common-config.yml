spring:
  main:
    web-application-type: servlet
    allow-bean-definition-overriding: true
    allow-circular-references: true

  http:
    encoding:
      charset: UTF-8
      enabled: true
      force: true

  datasource:
    url: *****************************************************************************************************************************************************************************
    username: neondb_owner
    password: npg_kc2S7QGCPbEh
    driver-class-name: org.postgresql.Driver
    hikari:
      pool-name: NexusMicroservicesPool
      maximum-pool-size: 10
      minimum-idle: 3
      connection-timeout: 30000
      idle-timeout: 120000
      max-lifetime: 300000
      leak-detection-threshold: 15000
      connection-test-query: SELECT 1
      connection-init-sql: SET TIME ZONE 'UTC'
      validation-timeout: 5000
      keepalive-time: 60000
      auto-commit: true
      data-source-properties:
        socketTimeout: 30
        connectTimeout: 15
        loginTimeout: 20
        tcpKeepAlive: true
        ssl: true
        sslmode: require
        sslfactory: org.postgresql.ssl.NonValidatingFactory
        reWriteBatchedInserts: true
        prepareThreshold: 5
        preferQueryMode: extended
        autoReconnect: true

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    open-in-view: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQL10Dialect
        format_sql: false
        jdbc:
          batch_size: 25
          batch_versioned_data: true
        order_inserts: true
        order_updates: true
        connection:
          release_mode: after_transaction
        time_zone: UTC
        physical_naming_strategy: org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy
        implicit_naming_strategy: org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
        cache:
          use_second_level_cache: false
          use_query_cache: false
        generate_statistics: false

  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 3000ms

  # RocketMQ配置
  rocketmq:
    name-server: localhost:9876
    producer:
      group: nexus-producer-group
      send-message-timeout: 3000
      retry-times-when-send-failed: 2
      retry-times-when-send-async-failed: 2
      max-message-size: 4194304
    consumer:
      group: nexus-consumer-group
      pull-batch-size: 32
      name-server: localhost:9876

  cache:
    type: redis
    redis:
      time-to-live: 1800000
      cache-null-values: false

nexus:
  auth:
    jwt:
      secret: nexus-microservices-jwt-secret-key-2024
      expiration: 86400
      refresh-expiration: 604800

  # Email service configuration
  email:
    enabled: false
    mock: true
    from: <EMAIL>
    from-name: Nexus Microservices Platform

  # RocketMQ listener control
  rocketmq:
    subscription:
      enabled: true
    mcp:
      enabled: true
    user:
      enabled: true
    notification:
      enabled: true

  # MCP service configuration
  mcp:
    discovery:
      enabled: true
      health-check-interval: 60000
    local:
      enabled: true
      base-path: /opt/mcp-servers
    remote:
      enabled: true
      timeout: 30000

logging:
  level:
    root: INFO
    com.nexus: DEBUG
    io.grpc: INFO
  charset:
    console: UTF-8
    file: UTF-8
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,refresh
  endpoint:
    health:
      show-details: when-authorized
  health:
    defaults:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true