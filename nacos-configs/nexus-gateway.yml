server:
  port: 8080

spring:
  application:
    name: nexus-gateway
  cloud:
    gateway:
      routes:
        - id: auth-service
          uri: lb://nexus-auth-service
          predicates:
            - Path=/auth/**
          filters:
            - StripPrefix=0
        
        - id: subscription-service
          uri: lb://nexus-subscription-service
          predicates:
            - Path=/api/v1/subscriptions/**
          filters:
            - StripPrefix=0
        
        - id: mcp-local-service
          uri: lb://nexus-mcp-local-service
          predicates:
            - Path=/api/v1/mcp/local/**
          filters:
            - StripPrefix=0
        
        - id: mcp-remote-service
          uri: lb://nexus-mcp-remote-service
          predicates:
            - Path=/api/v1/mcp/remote/**
          filters:
            - StripPrefix=0
        
        - id: unified-mcp
          uri: lb://nexus-mcp-local-service
          predicates:
            - Path=/api/v1/unified-mcp/**
          filters:
            - StripPrefix=0

        - id: realtime-service
          uri: lb://nexus-realtime-service
          predicates:
            - Path=/api/v1/realtime/**
          filters:
            - StripPrefix=0

        - id: chain-service
          uri: lb://nexus-chain-service
          predicates:
            - Path=/api/v1/chains/**
          filters:
            - StripPrefix=0

        - id: market-service
          uri: lb://nexus-market-service
          predicates:
            - Path=/api/v1/market/**
          filters:
            - StripPrefix=0
      
      default-filters:
        - DedupeResponseHeader=Access-Control-Allow-Credentials Access-Control-Allow-Origin

nexus:
  gateway:
    security:
      enabled: true
      excluded-paths:
        - "/api/v1/auth/login"
        - "/api/v1/auth/register"
        - "/actuator/**"
    
    rate-limit:
      enabled: true
      requests-per-minute: 1000
      burst-capacity: 1500
    
    monitoring:
      metrics-enabled: true
      tracing-enabled: true

logging:
  file:
    name: logs/nexus-gateway.log