# Nexus Market Service Configuration
# Data ID: nexus-market-service.yml, Group: DEFAULT_GROUP

server:
  port: 8085
  servlet:
    context-path: /market

spring:
  application:
    name: nexus-market-service

  # Redis database allocation
  redis:
    database: 7

# Market service configuration
nexus:
  market:
    # Service marketplace configuration
    marketplace:
      enabled: true
      featured-services-count: 10
      popular-services-count: 20
      new-services-count: 15
      categories:
        - "AI & Machine Learning"
        - "Data Processing"
        - "Web Services"
        - "File Management"
        - "Communication"
        - "Development Tools"
        - "Analytics"
        - "Security"
    
    # Service discovery configuration
    discovery:
      enabled: true
      auto-discovery: true
      discovery-interval: 300 # 5 minutes
      health-check-enabled: true
      health-check-interval: 60 # 1 minute
    
    # Service rating configuration
    rating:
      enabled: true
      allow-anonymous-rating: false
      max-rating: 5
      min-rating: 1
      rating-cooldown: 86400 # 24 hours
    
    # Service recommendation configuration
    recommendation:
      enabled: true
      algorithm: "collaborative-filtering"
      max-recommendations: 10
      update-interval: 3600 # 1 hour
      personalization-enabled: true
    
    # Service analytics configuration
    analytics:
      enabled: true
      track-views: true
      track-downloads: true
      track-usage: true
      retention-period: 2592000 # 30 days
    
    # Service caching configuration
    cache:
      enabled: true
      service-list-ttl: 300 # 5 minutes
      service-detail-ttl: 600 # 10 minutes
      category-list-ttl: 1800 # 30 minutes
      search-results-ttl: 180 # 3 minutes

  # RocketMQ listener control
  rocketmq:
    market:
      enabled: true
    subscription:
      enabled: false
    user:
      enabled: false
    notification:
      enabled: false

# Service inter-communication configuration
service:
  auth:
    url: http://nexus-auth-service:8081
  subscription:
    url: http://nexus-subscription-service:8084
  mcp-local:
    url: http://nexus-mcp-local-service:8082
  mcp-remote:
    url: http://nexus-mcp-remote-service:8083

# Logging configuration
logging:
  level:
    com.nexus.market: DEBUG
    org.springframework.amqp: INFO
    org.springframework.cache: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{50}] - %msg%n"
  file:
    name: logs/nexus-market-service.log
