server:
  port: 8082
  servlet:
    context-path: /mcp-local

spring:
  application:
    name: nexus-mcp-local-service
  redis:
    database: 3

nexus:
  mcp:
    local:
      tools:
        enabled: true
        scan-packages:
          - "com.nexus.mcp.local.tools"
        max-concurrent-executions: 10
        execution-timeout: 30000
      
      security:
        sandbox-enabled: true
        allowed-operations:
          - "file.read"
          - "file.write"
          - "process.execute"
          - "network.request"
        blocked-paths:
          - "/etc"
          - "/sys"
          - "/proc"
          - "C:\\Windows\\System32"
      
      monitoring:
        metrics-enabled: true
        performance-tracking: true
        error-reporting: true
      
      cache:
        enabled: true
        ttl: 300
        max-size: 1000

  # RocketMQ listener control
  rocketmq:
    mcp:
      enabled: true
    subscription:
      enabled: false
    user:
      enabled: false
    notification:
      enabled: false

logging:
  file:
    name: logs/nexus-mcp-local-service.log