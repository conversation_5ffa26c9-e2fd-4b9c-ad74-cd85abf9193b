server:
  port: 8083
  servlet:
    context-path: /mcp-remote

spring:
  application:
    name: nexus-mcp-remote-service
  redis:
    database: 4

nexus:
  mcp:
    remote:
      providers:
        enabled: true
        discovery-enabled: true
        health-check-interval: 60
        connection-timeout: 10000
        read-timeout: 30000
      
      routing:
        load-balancing: ROUND_ROBIN
        failover-enabled: true
        circuit-breaker-enabled: true
        retry-attempts: 3
      
      security:
        authentication-required: true
        authorization-enabled: true
        rate-limiting: true
        request-validation: true
      
      monitoring:
        metrics-enabled: true
        tracing-enabled: true
        logging-enabled: true
      
      cache:
        enabled: true
        provider-cache-ttl: 600
        response-cache-ttl: 300

  # RocketMQ listener control
  rocketmq:
    mcp:
      enabled: true
    subscription:
      enabled: false
    user:
      enabled: false
    notification:
      enabled: false

logging:
  file:
    name: logs/nexus-mcp-remote-service.log