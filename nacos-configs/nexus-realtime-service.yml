# Nexus Realtime Service Configuration
# Data ID: nexus-realtime-service.yml, Group: DEFAULT_GROUP

server:
  port: 8087
  servlet:
    context-path: /realtime

spring:
  application:
    name: nexus-realtime-service

  # Redis database allocation
  redis:
    database: 6

# Realtime service configuration
nexus:
  realtime:
    # WebSocket configuration
    websocket:
      enabled: true
      path: /ws/realtime
      allowed-origins: "*"
      heartbeat-interval: 30000 # 30 seconds
      max-connections: 1000

    # Messaging configuration
    messaging:
      queue-size: 10000
      message-ttl: 3600 # seconds
      batch-size: 100
      send-timeout: 5000 # milliseconds

    # Redis Stream configuration
    redis-stream:
      enabled: true
      stream-prefix: "nexus:realtime:"
      consumer-group: "nexus-realtime-group"
      max-length: 10000
      consumer-timeout: 30000

    # Subscription management configuration
    subscription:
      max-subscriptions-per-user: 50
      subscription-ttl: 86400 # 24 hours
      cleanup-interval: 3600 # 1 hour

    # Notification configuration
    notification:
      system-notifications: true
      service-status-notifications: true
      task-progress-notifications: true
      notification-levels:
        - INFO
        - WARN
        - ERROR

    # Performance configuration
    performance:
      async-pool-size: 20
      message-pool-size: 10
      connection-check-interval: 60 # seconds
      stats-collection-interval: 300 # seconds

  # RocketMQ listener control
  rocketmq:
    realtime:
      enabled: true
    subscription:
      enabled: false
    user:
      enabled: false
    notification:
      enabled: true

# Logging configuration
logging:
  file:
    name: logs/nexus-realtime-service.log
