server:
  port: 8084
  servlet:
    context-path: /subscription

spring:
  application:
    name: nexus-subscription-service
  redis:
    database: 2

nexus:
  subscription:
    plans:
      free:
        name: "Free Plan"
        price: 0
        requests-per-month: 1000
        features:
          - "Basic API Access"
          - "Community Support"
      pro:
        name: "Pro Plan"
        price: 29
        requests-per-month: 10000
        features:
          - "Full API Access"
          - "Priority Support"
          - "Advanced Analytics"
      enterprise:
        name: "Enterprise Plan"
        price: 99
        requests-per-month: 100000
        features:
          - "Unlimited API Access"
          - "24/7 Support"
          - "Custom Integration"
          - "SLA Guarantee"
    
    billing:
      currency: "USD"
      billing-cycle: "monthly"
      grace-period-days: 7
      auto-renewal: true
    
    usage:
      tracking-enabled: true
      reset-day: 1
      warning-threshold: 80
      hard-limit-enabled: true
    
    security:
      api-key-required: true
      rate-limiting: true
      default-policy: DENY
    
    rate-limit:
      enabled: true
      default-strategy: TOKEN_BUCKET
      check-interval: 60
    
    notification:
      enabled: true
      expiry-notification: true
      usage-warning-notification: true
      usage-warning-threshold: 80

  # Email service configuration
  email:
    enabled: false
    mock: true
    from: <EMAIL>
    from-name: Nexus Microservices Platform

  # RocketMQ listener control
  rocketmq:
    subscription:
      enabled: true
    user:
      enabled: false
    notification:
      enabled: true

logging:
  file:
    name: logs/nexus-subscription-service.log