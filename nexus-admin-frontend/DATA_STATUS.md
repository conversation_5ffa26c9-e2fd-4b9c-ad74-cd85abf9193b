# Nexus 管理平台数据状态说明

## 当前数据使用情况

### ✅ 真实数据（已连接后端API）
- **用户认证** - 完全使用真实API
  - 登录/登出功能
  - JWT Token 管理
  - 用户信息获取
- **WebSocket 连接** - 真实连接
  - 实时通信功能
  - 连接状态监控

### 🔄 模拟数据（待连接后端）
- **Dashboard 指标**
  - 服务指标 (`serviceMetrics`)
  - 订阅指标 (`subscriptionMetrics`) 
  - API调用指标 (`apiMetrics`)
  - 系统健康度 (`systemHealth`)
- **图表数据**
  - API调用趋势图
  - 服务状态分布图
  - 实时监控图表
- **活动日志**
  - 系统活动记录
  - 操作历史
- **MCP服务管理**
  - 服务列表
  - 服务状态
  - 工具管理
- **监控数据**
  - CPU/内存使用率
  - 网络流量
  - 连接数统计

## 模拟数据位置

### Dashboard 页面
```typescript
// 位置: src/pages/dashboard/Dashboard.vue
const serviceMetrics = ref({
  total: 12,
  running: 8,
  healthy: 7,
  error: 1
})

const subscriptionMetrics = ref({
  active: 156,
  trend: 12.5,
  total: 189
})

const apiMetrics = ref({
  totalCalls: 2847,
  trend: 8.3,
  successRate: 98.5
})
```

### 实时监控页面
```typescript
// 位置: src/pages/monitoring/Realtime.vue
const systemMetrics = ref({
  cpuUsage: 45,
  memoryUsage: 68,
  networkTraffic: 12.5
})
```

### MCP服务页面
```typescript
// 位置: src/pages/mcp/Overview.vue
const services = ref([
  {
    id: '1',
    name: 'Context7 Service',
    status: 'running',
    // ... 更多模拟数据
  }
])
```

## 后续开发计划

### 第一阶段：核心API集成
1. **服务管理API**
   - 获取服务列表
   - 服务状态监控
   - 服务控制操作

2. **监控数据API**
   - 系统指标获取
   - 实时数据推送
   - 历史数据查询

### 第二阶段：高级功能API
1. **用户管理API**
   - 用户列表管理
   - 权限控制
   - 角色管理

2. **配置管理API**
   - 系统配置
   - 服务配置
   - 主题设置同步

### 第三阶段：扩展功能
1. **日志管理API**
   - 日志查询
   - 日志分析
   - 告警管理

2. **报表统计API**
   - 数据报表
   - 趋势分析
   - 导出功能

## 如何切换到真实数据

当后端API准备就绪时，只需要：

1. **更新API配置**
   ```typescript
   // src/config/api.ts
   export const API_ENDPOINTS = {
     SERVICES: '/api/services',
     METRICS: '/api/metrics',
     MONITORING: '/api/monitoring'
   }
   ```

2. **替换模拟数据**
   ```typescript
   // 将模拟数据替换为API调用
   const serviceMetrics = await api.get('/api/metrics/services')
   ```

3. **启用实时数据**
   ```typescript
   // 通过WebSocket接收实时数据
   websocket.on('metrics:update', (data) => {
     serviceMetrics.value = data
   })
   ```

## 当前界面特性

### ✨ 企业级深色主题
- 现代深蓝渐变背景
- 毛玻璃效果卡片
- 高对比度文字
- 渐变文字效果
- 微妙动画和过渡

### 🎨 视觉增强
- 统一的色彩体系
- 企业级配色方案
- 优化的字体可读性
- 现代阴影系统
- 响应式设计

### 🚀 交互体验
- 平滑动画效果
- 悬停状态优化
- 加载状态美化
- 微交互反馈

界面现在具有完整的企业级美感和良好的用户体验，等待后端API集成即可实现完整功能。
