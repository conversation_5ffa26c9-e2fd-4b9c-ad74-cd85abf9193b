/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AboutSection: typeof import('./src/components/settings/AboutSection.vue')['default']
    AddCustomShortcutModal: typeof import('./src/components/AddCustomShortcutModal.vue')['default']
    AdvancedSettings: typeof import('./src/components/settings/AdvancedSettings.vue')['default']
    BatchOperationModal: typeof import('./src/components/BatchOperationModal.vue')['default']
    BreadcrumbNav: typeof import('./src/components/BreadcrumbNav.vue')['default']
    CreateCollectionModal: typeof import('./src/components/CreateCollectionModal.vue')['default']
    GlobalComponents: typeof import('./src/components/GlobalComponents.vue')['default']
    GlobalSearchModal: typeof import('./src/components/GlobalSearchModal.vue')['default']
    GlobalShortcuts: typeof import('./src/components/GlobalShortcuts.vue')['default']
    HelpModal: typeof import('./src/components/HelpModal.vue')['default']
    ImportSettingsModal: typeof import('./src/components/ImportSettingsModal.vue')['default']
    InterfaceSettings: typeof import('./src/components/settings/InterfaceSettings.vue')['default']
    McpCommandBuilder: typeof import('./src/components/McpCommandBuilder.vue')['default']
    McpServiceRegisterModal: typeof import('./src/components/McpServiceRegisterModal.vue')['default']
    MonitoringSettingsModal: typeof import('./src/components/MonitoringSettingsModal.vue')['default']
    NAlert: typeof import('naive-ui')['NAlert']
    NAvatar: typeof import('naive-ui')['NAvatar']
    NBadge: typeof import('naive-ui')['NBadge']
    NBreadcrumb: typeof import('naive-ui')['NBreadcrumb']
    NBreadcrumbItem: typeof import('naive-ui')['NBreadcrumbItem']
    NButton: typeof import('naive-ui')['NButton']
    NButtonGroup: typeof import('naive-ui')['NButtonGroup']
    NCheckbox: typeof import('naive-ui')['NCheckbox']
    NCheckboxGroup: typeof import('naive-ui')['NCheckboxGroup']
    NCollapse: typeof import('naive-ui')['NCollapse']
    NCollapseItem: typeof import('naive-ui')['NCollapseItem']
    NConfigProvider: typeof import('naive-ui')['NConfigProvider']
    NDataTable: typeof import('naive-ui')['NDataTable']
    NDatePicker: typeof import('naive-ui')['NDatePicker']
    NDescriptions: typeof import('naive-ui')['NDescriptions']
    NDescriptionsItem: typeof import('naive-ui')['NDescriptionsItem']
    NDialogProvider: typeof import('naive-ui')['NDialogProvider']
    NDrawer: typeof import('naive-ui')['NDrawer']
    NDrawerContent: typeof import('naive-ui')['NDrawerContent']
    NDropdown: typeof import('naive-ui')['NDropdown']
    NDynamicTags: typeof import('naive-ui')['NDynamicTags']
    NEmpty: typeof import('naive-ui')['NEmpty']
    NForm: typeof import('naive-ui')['NForm']
    NFormItem: typeof import('naive-ui')['NFormItem']
    NGrid: typeof import('naive-ui')['NGrid']
    NGridItem: typeof import('naive-ui')['NGridItem']
    NIcon: typeof import('naive-ui')['NIcon']
    NInput: typeof import('naive-ui')['NInput']
    NInputNumber: typeof import('naive-ui')['NInputNumber']
    NLoadingBar: typeof import('naive-ui')['NLoadingBar']
    NLoadingBarProvider: typeof import('naive-ui')['NLoadingBarProvider']
    NLog: typeof import('naive-ui')['NLog']
    NMenu: typeof import('naive-ui')['NMenu']
    NMessageProvider: typeof import('naive-ui')['NMessageProvider']
    NModal: typeof import('naive-ui')['NModal']
    NNotificationProvider: typeof import('naive-ui')['NNotificationProvider']
    NotificationSettings: typeof import('./src/components/settings/NotificationSettings.vue')['default']
    NP: typeof import('naive-ui')['NP']
    NPopconfirm: typeof import('naive-ui')['NPopconfirm']
    NProgress: typeof import('naive-ui')['NProgress']
    NRadio: typeof import('naive-ui')['NRadio']
    NRadioButton: typeof import('naive-ui')['NRadioButton']
    NRadioGroup: typeof import('naive-ui')['NRadioGroup']
    NSelect: typeof import('naive-ui')['NSelect']
    NSpace: typeof import('naive-ui')['NSpace']
    NSpin: typeof import('naive-ui')['NSpin']
    NStatistic: typeof import('naive-ui')['NStatistic']
    NSwitch: typeof import('naive-ui')['NSwitch']
    NTabPane: typeof import('naive-ui')['NTabPane']
    NTabs: typeof import('naive-ui')['NTabs']
    NTag: typeof import('naive-ui')['NTag']
    NText: typeof import('naive-ui')['NText']
    NTimeline: typeof import('naive-ui')['NTimeline']
    NTimelineItem: typeof import('naive-ui')['NTimelineItem']
    NTree: typeof import('naive-ui')['NTree']
    NUpload: typeof import('naive-ui')['NUpload']
    NUploadDragger: typeof import('naive-ui')['NUploadDragger']
    ProfileSettings: typeof import('./src/components/settings/ProfileSettings.vue')['default']
    RealtimeChart: typeof import('./src/components/RealtimeChart.vue')['default']
    RealtimeStatusCard: typeof import('./src/components/RealtimeStatusCard.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SaveRequestModal: typeof import('./src/components/SaveRequestModal.vue')['default']
    SecuritySettings: typeof import('./src/components/settings/SecuritySettings.vue')['default']
    ServiceDetailDrawer: typeof import('./src/components/ServiceDetailDrawer.vue')['default']
    ServiceRegisterModal: typeof import('./src/components/ServiceRegisterModal.vue')['default']
    ShortcutsHelpModal: typeof import('./src/components/ShortcutsHelpModal.vue')['default']
    SidebarMenu: typeof import('./src/components/SidebarMenu.vue')['default']
    SubscriptionCreateModal: typeof import('./src/components/SubscriptionCreateModal.vue')['default']
    SubscriptionDetailDrawer: typeof import('./src/components/SubscriptionDetailDrawer.vue')['default']
    SystemSettings: typeof import('./src/components/settings/SystemSettings.vue')['default']
    WebSocketIndicator: typeof import('./src/components/WebSocketIndicator.vue')['default']
  }
}
