<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/nexus-logo.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Nexus微服务管理平台 - 现代化的MCP服务管理界面" />
    <meta name="keywords" content="Nexus, MCP, 微服务, 管理平台" />
    <title>Nexus管理平台</title>
    <style>
      /* 预加载样式，防止闪烁 */
      #app {
        height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
      
      .loading {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 16px;
        display: flex;
        align-items: center;
        gap: 12px;
      }
      
      .loading::before {
        content: '';
        width: 20px;
        height: 20px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-top: 2px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div class="loading">
        正在加载 Nexus 管理平台...
      </div>
    </div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
