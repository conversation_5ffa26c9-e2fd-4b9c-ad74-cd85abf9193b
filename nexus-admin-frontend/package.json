{"name": "nexus-admin-frontend", "version": "1.0.0", "description": "Nexus微服务管理平台前端", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.4.15", "vue-router": "^4.2.5", "pinia": "^2.1.7", "naive-ui": "^2.38.1", "@vicons/ionicons5": "^0.12.0", "@vicons/tabler": "^0.12.0", "axios": "^1.6.7", "@tanstack/vue-query": "^5.17.19", "@vueuse/core": "^10.7.2", "@vueuse/motion": "^2.0.0", "echarts": "^5.4.3", "vue-echarts": "^6.6.1", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "monaco-editor": "^0.45.0", "sockjs-client": "^1.6.1", "@stomp/stompjs": "^7.0.0"}, "devDependencies": {"@types/node": "^20.11.5", "@types/lodash-es": "^4.17.12", "@types/sockjs-client": "^1.5.4", "@typescript-eslint/eslint-plugin": "^6.19.1", "@typescript-eslint/parser": "^6.19.1", "@vitejs/plugin-vue": "^5.0.3", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.1", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.20.1", "prettier": "^3.2.4", "typescript": "~5.3.3", "unplugin-auto-import": "^0.17.5", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.12", "vue-tsc": "^1.8.27"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}