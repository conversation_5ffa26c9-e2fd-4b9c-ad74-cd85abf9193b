<template>
  <n-config-provider
    :theme="theme"
    :theme-overrides="themeOverrides"
    :locale="locale"
    :date-locale="dateLocale"
  >
    <n-loading-bar-provider>
      <n-dialog-provider>
        <n-notification-provider>
          <n-message-provider>
            <router-view />
            <GlobalComponents />
          </n-message-provider>
        </n-notification-provider>
      </n-dialog-provider>
    </n-loading-bar-provider>
  </n-config-provider>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { zhCN, dateZhCN, enUS, dateEnUS } from 'naive-ui'
import type { GlobalTheme, GlobalThemeOverrides } from 'naive-ui'
import { darkTheme } from 'naive-ui'
import { useThemeStore } from '@/stores/theme'
import GlobalComponents from '@/components/GlobalComponents.vue'

const themeStore = useThemeStore()

// 主题配置
const theme = computed<GlobalTheme | null>(() => {
  return themeStore.isDark ? darkTheme : null
})

// 主题覆盖配置 - 企业级深色主题
const themeOverrides = computed<GlobalThemeOverrides>(() => ({
  common: {
    primaryColor: '#3b82f6',
    primaryColorHover: '#2563eb',
    primaryColorPressed: '#1d4ed8',
    primaryColorSuppl: 'rgba(59, 130, 246, 0.1)',

    successColor: '#10b981',
    successColorHover: '#059669',
    successColorSuppl: 'rgba(16, 185, 129, 0.1)',

    warningColor: '#f59e0b',
    warningColorHover: '#d97706',
    warningColorSuppl: 'rgba(245, 158, 11, 0.1)',

    errorColor: '#ef4444',
    errorColorHover: '#dc2626',
    errorColorSuppl: 'rgba(239, 68, 68, 0.1)',

    infoColor: '#06b6d4',
    infoColorHover: '#0891b2',
    infoColorSuppl: 'rgba(6, 182, 212, 0.1)',

    borderRadius: '8px',
    borderRadiusSmall: '6px',
    fontFamily: `-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif`,
    fontSize: '14px',
    lineHeight: '1.6',

    // 深色主题背景
    bodyColor: '#0f1419',
    cardColor: 'rgba(45, 55, 72, 0.8)',
    modalColor: '#1a1d29',
    popoverColor: '#2d3748',
    tableHeaderColor: 'rgba(45, 55, 72, 0.6)',

    // 文字颜色
    textColorBase: '#f8fafc',
    textColor1: '#f8fafc',
    textColor2: '#cbd5e1',
    textColor3: '#94a3b8',
    textColorDisabled: '#64748b',

    // 边框颜色
    borderColor: 'rgba(148, 163, 184, 0.2)',
    dividerColor: 'rgba(148, 163, 184, 0.1)',

    // 输入框
    inputColor: 'rgba(45, 55, 72, 0.6)',
    inputColorDisabled: 'rgba(45, 55, 72, 0.3)',

    // 阴影
    boxShadow1: '0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06)',
    boxShadow2: '0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06)',
    boxShadow3: '0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05)'
  },
  Card: {
    borderRadius: '16px',
    paddingMedium: '24px',
    color: 'rgba(45, 55, 72, 0.8)',
    borderColor: 'rgba(148, 163, 184, 0.2)'
  },
  Button: {
    borderRadius: '8px',
    fontWeight: '500',
    heightMedium: '40px',
    paddingMedium: '0 20px'
  },
  Input: {
    borderRadius: '8px',
    color: 'rgba(45, 55, 72, 0.6)',
    colorFocus: 'rgba(45, 55, 72, 0.8)',
    borderColor: 'rgba(148, 163, 184, 0.2)',
    borderColorFocus: '#3b82f6'
  },
  Select: {
    borderRadius: '8px'
  },
  DataTable: {
    borderRadius: '12px',
    thColor: 'rgba(45, 55, 72, 0.6)',
    tdColor: 'rgba(45, 55, 72, 0.4)',
    borderColor: 'rgba(148, 163, 184, 0.1)'
  },
  Modal: {
    borderRadius: '16px',
    color: '#1a1d29'
  },
  Drawer: {
    borderRadius: '16px 0 0 16px',
    color: '#1a1d29'
  },
  Menu: {
    borderRadius: '8px',
    itemColorActive: 'rgba(59, 130, 246, 0.1)',
    itemColorActiveHover: 'rgba(59, 130, 246, 0.15)',
    itemTextColorActive: '#3b82f6',
    itemIconColorActive: '#3b82f6'
  },
  Notification: {
    borderRadius: '12px',
    color: '#2d3748'
  },
  Message: {
    borderRadius: '8px',
    color: '#2d3748'
  }
}))

// 国际化配置
const locale = computed(() => {
  return themeStore.language === 'zh-CN' ? zhCN : enUS
})

const dateLocale = computed(() => {
  return themeStore.language === 'zh-CN' ? dateZhCN : dateEnUS
})

// 初始化主题
themeStore.initTheme()
</script>

<style>
/* 全局样式调整 */
.n-card {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.n-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.n-button {
  transition: all 0.3s ease;
}

.n-button:not(.n-button--disabled):hover {
  transform: translateY(-1px);
}

.n-data-table {
  border-radius: 12px;
  overflow: hidden;
}

.n-data-table-th {
  background: rgba(102, 126, 234, 0.05);
  font-weight: 600;
}

/* 自定义滚动条 */
.n-scrollbar-rail {
  right: 2px;
}

.n-scrollbar-rail--vertical {
  width: 6px;
}

.n-scrollbar-rail--horizontal {
  height: 6px;
}

/* 加载条样式 */
.n-loading-bar {
  background: linear-gradient(90deg, #667eea, #764ba2);
  height: 3px;
}

/* 通知样式 */
.n-notification {
  border-radius: 12px;
  backdrop-filter: blur(20px);
}

/* 对话框样式 */
.n-modal {
  border-radius: 16px;
}

/* 抽屉样式 */
.n-drawer {
  border-radius: 16px 0 0 16px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .n-card {
    margin: 8px;
    border-radius: 12px;
  }
  
  .n-drawer {
    border-radius: 0;
  }
}
</style>
