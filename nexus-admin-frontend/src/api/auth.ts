/**
 * 认证相关API
 */
import { httpPost, httpGet } from '@/utils/http'
import type { 
  LoginRequest, 
  RegisterRequest, 
  LoginResponse, 
  User,
  ApiResponse 
} from '@/types/api'

export const authApi = {
  // 用户登录
  login: (data: LoginRequest) => 
    httpPost<LoginResponse>('/auth/api/v1/auth/login', data),

  // 用户注册
  register: (data: RegisterRequest) => 
    httpPost<User>('/auth/api/v1/auth/register', data),

  // 验证Token
  validateToken: (token: string) => 
    httpPost<User>('/auth/api/v1/auth/validate/token', { token }),

  // 验证API密钥
  validateApiKey: (apiKey: string) => 
    httpPost<User>('/auth/api/v1/auth/validate/api-key', null, {
      params: { apiKey }
    }),

  // 刷新Token
  refreshToken: (refreshToken: string) => 
    httpPost<LoginResponse>('/auth/api/v1/auth/refresh', { refreshToken }),

  // 用户登出
  logout: () => 
    httpPost('/auth/api/v1/auth/logout'),

  // 获取用户信息
  getUserInfo: () => 
    httpGet<User>('/auth/api/v1/auth/profile'),

  // 更新用户信息
  updateProfile: (data: Partial<User>) => 
    httpPost<User>('/auth/api/v1/auth/profile', data),

  // 修改密码
  changePassword: (data: { oldPassword: string; newPassword: string }) => 
    httpPost('/auth/api/v1/auth/change-password', data),

  // 重置密码
  resetPassword: (email: string) => 
    httpPost('/auth/api/v1/auth/reset-password', { email }),

  // 生成API密钥
  generateApiKey: () => 
    httpPost<{ apiKey: string }>('/auth/api/v1/auth/generate-api-key'),

  // 撤销API密钥
  revokeApiKey: (apiKey: string) => 
    httpPost('/auth/api/v1/auth/revoke-api-key', { apiKey })
}
