/**
 * MCP服务相关API
 */
import { httpGet, httpPost } from '@/utils/http'
import type { 
  McpService,
  McpServerInfo,
  McpTool,
  McpResource,
  ToolCallRequest,
  ToolCallResponse,
  McpCommandRequest,
  McpServiceStartResult,
  SystemHealth
} from '@/types/api'

export const mcpApi = {
  // UnifiedMCP接口
  unified: {
    // 获取服务器信息
    getServerInfo: () => 
      httpGet<McpServerInfo>('/api/v1/unified-mcp/server/info'),

    // 列出所有工具
    listTools: () => 
      httpGet<{ tools: McpTool[] }>('/api/v1/unified-mcp/tools/list'),

    // 调用工具
    callTool: (request: ToolCallRequest) => 
      httpPost<ToolCallResponse>('/api/v1/unified-mcp/tools/call', request),

    // 列出所有资源
    listResources: () => 
      httpGet<{ resources: McpResource[] }>('/api/v1/unified-mcp/resources/list'),

    // 读取资源
    readResource: (uri: string) => 
      httpPost('/api/v1/unified-mcp/resources/read', { uri }),

    // 健康检查
    health: () => 
      httpGet<SystemHealth>('/api/v1/unified-mcp/health')
  },

  // 远程MCP服务管理
  remote: {
    // 获取所有远程服务
    getServices: () => 
      httpGet<McpService[]>('/api/v1/mcp/remote/services'),

    // 获取服务详情
    getService: (id: number) => 
      httpGet<McpService>(`/api/v1/mcp/remote/services/${id}`),

    // 注册远程服务
    registerService: (data: Partial<McpService>) => 
      httpPost<McpService>('/api/v1/mcp/remote/services', data),

    // 更新服务
    updateService: (id: number, data: Partial<McpService>) => 
      httpPost<McpService>(`/api/v1/mcp/remote/services/${id}`, data),

    // 删除服务
    deleteService: (id: number) => 
      httpPost(`/api/v1/mcp/remote/services/${id}/delete`),

    // 启动服务
    startService: (id: number) => 
      httpPost(`/api/v1/mcp/remote/services/${id}/start`),

    // 停止服务
    stopService: (id: number) => 
      httpPost(`/api/v1/mcp/remote/services/${id}/stop`),

    // 重启服务
    restartService: (id: number) => 
      httpPost(`/api/v1/mcp/remote/services/${id}/restart`),

    // 健康检查
    healthCheck: (id: number) => 
      httpPost(`/api/v1/mcp/remote/services/${id}/health`)
  },

  // 本地MCP服务管理
  local: {
    // 获取所有本地服务
    getServices: () => 
      httpGet<McpService[]>('/api/v1/mcp/local/services'),

    // 获取服务详情
    getService: (id: number) => 
      httpGet<McpService>(`/api/v1/mcp/local/services/${id}`),

    // 注册本地服务
    registerService: (data: Partial<McpService>) => 
      httpPost<McpService>('/api/v1/mcp/local/services', data),

    // 启动服务
    startService: (id: number) => 
      httpPost(`/api/v1/mcp/local/services/${id}/start`),

    // 停止服务
    stopService: (id: number) => 
      httpPost(`/api/v1/mcp/local/services/${id}/stop`)
  },

  // MCP命令管理
  commands: {
    // 通过命令启动服务
    startByCommand: (request: McpCommandRequest) =>
      httpPost<McpServiceStartResult>('/api/mcp/command/start', request),

    // 批量启动服务
    startBatch: (commands: string[]) =>
      httpPost<Record<string, McpServiceStartResult>>('/api/mcp/command/start-batch', { commands }),

    // 通过命令停止服务
    stopByCommand: (command: string) =>
      httpPost<{ success: boolean; message: string }>('/api/mcp/command/stop', { command }),

    // 验证命令
    validateCommand: (command: string) =>
      httpPost<{ valid: boolean; command: string; message?: string }>('/api/mcp/command/validate', { command }),

    // 预览命令解析结果
    previewCommand: (command: string) =>
      httpPost<{ config: any; command: string }>('/api/mcp/command/preview', { command }),

    // 获取命令示例
    getExamples: () =>
      httpGet<{ examples: string[]; count: number }>('/api/mcp/command/examples'),

    // 获取命令服务映射
    getMappings: () =>
      httpGet<{ mappings: Record<string, string>; count: number }>('/api/mcp/command/mappings'),

    // 获取运行状态
    getStatus: () =>
      httpGet<{ running: Record<string, any>; count: number }>('/api/mcp/command/status')
  },

  // 任务管理
  tasks: {
    // 获取任务状态
    getTaskStatus: (taskId: string) => 
      httpGet(`/api/v1/mcp/remote/tasks/${taskId}/status`),

    // 获取任务完整信息
    getTaskInfo: (taskId: string) => 
      httpGet(`/api/v1/mcp/remote/tasks/${taskId}`),

    // 取消任务
    cancelTask: (taskId: string) => 
      httpPost(`/api/v1/mcp/remote/tasks/${taskId}/cancel`),

    // 获取任务列表
    getTasks: (params?: { status?: string; limit?: number }) => 
      httpGet('/api/v1/mcp/remote/tasks', { params })
  }
}
