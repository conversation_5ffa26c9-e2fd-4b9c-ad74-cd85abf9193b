/**
 * 实时通信相关API
 */
import { httpGet, httpPost } from '@/utils/http'
import type { 
  RealtimeMessage,
  SubscriptionRequest
} from '@/types/api'

export const realtimeApi = {
  // 广播消息
  broadcast: (message: RealtimeMessage) => 
    httpPost('/api/v1/realtime/broadcast', message),

  // 发送服务状态更新
  sendServiceStatus: (serviceId: string, status: string, details?: Record<string, any>) => 
    httpPost('/api/v1/realtime/service-status', details, {
      params: { serviceId, status }
    }),

  // 发送用户消息
  sendToUser: (userId: string, message: RealtimeMessage) => 
    httpPost(`/api/v1/realtime/users/${userId}/message`, message),

  // 发送主题消息
  sendToTopic: (topic: string, message: RealtimeMessage) => 
    httpPost(`/api/v1/realtime/topics/${topic}/message`, message),

  // 订阅主题
  subscribe: (request: SubscriptionRequest) => 
    httpPost('/api/v1/realtime/subscribe', request),

  // 取消订阅
  unsubscribe: (userId: string, topic: string) => 
    httpPost('/api/v1/realtime/unsubscribe', { userId, topic }),

  // 获取连接状态
  getStatus: () => 
    httpGet<{
      activeConnections: number;
      timestamp: string;
      service: string;
      status: string;
    }>('/api/v1/realtime/status'),

  // 获取用户连接信息
  getUserConnections: (userId: string) => 
    httpGet(`/api/v1/realtime/users/${userId}/connections`),

  // 获取主题订阅信息
  getTopicSubscriptions: (topic: string) => 
    httpGet(`/api/v1/realtime/topics/${topic}/subscriptions`),

  // 获取所有活跃主题
  getActiveTopics: () => 
    httpGet<string[]>('/api/v1/realtime/topics'),

  // 获取消息历史
  getMessageHistory: (params?: {
    userId?: string;
    topic?: string;
    type?: string;
    startTime?: string;
    endTime?: string;
    limit?: number;
  }) => 
    httpGet<RealtimeMessage[]>('/api/v1/realtime/messages', { params }),

  // 健康检查
  health: () => 
    httpGet<{
      status: string;
      service: string;
      activeConnections: number;
      timestamp: string;
    }>('/api/v1/realtime/health'),

  // 获取统计信息
  getStats: () => 
    httpGet<{
      totalConnections: number;
      activeConnections: number;
      totalMessages: number;
      messagesPerSecond: number;
      topicCount: number;
      subscriptionCount: number;
      uptime: number;
    }>('/api/v1/realtime/stats'),

  // 强制断开用户连接
  disconnectUser: (userId: string) => 
    httpPost(`/api/v1/realtime/users/${userId}/disconnect`),

  // 清理过期连接
  cleanupConnections: () => 
    httpPost('/api/v1/realtime/cleanup'),

  // 发送系统通知
  sendSystemNotification: (message: string, level: 'info' | 'warning' | 'error' = 'info') => 
    httpPost('/api/v1/realtime/system-notification', {
      type: 'SYSTEM_NOTIFICATION',
      content: message,
      level,
      timestamp: new Date().toISOString()
    }),

  // 发送工具执行状态
  sendToolExecutionStatus: (data: {
    taskId: string;
    toolName: string;
    status: string;
    progress?: number;
    result?: any;
    error?: string;
  }) => 
    httpPost('/api/v1/realtime/tool-execution', {
      type: 'TOOL_EXECUTION',
      taskId: data.taskId,
      toolName: data.toolName,
      status: data.status,
      progress: data.progress,
      data: data.result,
      content: data.error,
      timestamp: new Date().toISOString()
    })
}
