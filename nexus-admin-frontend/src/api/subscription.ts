/**
 * 订阅管理相关API
 */
import { httpGet, httpPost, httpPut, httpDelete } from '@/utils/http'
import type { 
  Subscription,
  CreateSubscriptionRequest,
  PageResponse
} from '@/types/api'

export const subscriptionApi = {
  // 创建订阅
  create: (data: CreateSubscriptionRequest) => 
    httpPost<Subscription>('/api/v1/subscriptions', data),

  // 获取用户订阅列表
  getUserSubscriptions: (userId: number, params?: { 
    page?: number; 
    size?: number; 
    status?: string 
  }) => 
    httpGet<PageResponse<Subscription>>(`/api/v1/subscriptions/user/${userId}`, { params }),

  // 获取所有订阅（管理员）
  getAll: (params?: { 
    page?: number; 
    size?: number; 
    status?: string;
    userId?: number;
    serviceName?: string;
  }) => 
    httpGet<PageResponse<Subscription>>('/api/v1/subscriptions', { params }),

  // 获取订阅详情
  getById: (id: number) => 
    httpGet<Subscription>(`/api/v1/subscriptions/${id}`),

  // 更新订阅
  update: (id: number, data: Partial<Subscription>) => 
    httpPut<Subscription>(`/api/v1/subscriptions/${id}`, data),

  // 取消订阅
  cancel: (id: number) => 
    httpDelete(`/api/v1/subscriptions/${id}`),

  // 暂停订阅
  suspend: (id: number, reason?: string) => 
    httpPost(`/api/v1/subscriptions/${id}/suspend`, { reason }),

  // 恢复订阅
  resume: (id: number) => 
    httpPost(`/api/v1/subscriptions/${id}/resume`),

  // 续费订阅
  renew: (id: number, data: { endDate: string; callLimit?: number }) => 
    httpPost<Subscription>(`/api/v1/subscriptions/${id}/renew`, data),

  // 记录API调用
  recordCall: (userId: number, serviceConfigId: number) => 
    httpPost('/api/v1/subscriptions/record-call', null, {
      params: { userId, serviceConfigId }
    }),

  // 检查订阅权限
  checkPermission: (userId: number, serviceConfigId: number) => 
    httpGet<{ hasPermission: boolean; subscription?: Subscription }>('/api/v1/subscriptions/check-permission', {
      params: { userId, serviceConfigId }
    }),

  // 获取使用统计
  getUsageStats: (subscriptionId: number, params?: {
    startDate?: string;
    endDate?: string;
    granularity?: 'hour' | 'day' | 'week' | 'month';
  }) => 
    httpGet(`/api/v1/subscriptions/${subscriptionId}/usage`, { params }),

  // 获取订阅统计
  getStats: () => 
    httpGet<{
      totalSubscriptions: number;
      activeSubscriptions: number;
      expiredSubscriptions: number;
      totalRevenue: number;
      monthlyGrowth: number;
    }>('/api/v1/subscriptions/stats'),

  // 导出订阅数据
  export: (params?: {
    format?: 'csv' | 'excel';
    startDate?: string;
    endDate?: string;
    status?: string;
  }) => 
    httpGet('/api/v1/subscriptions/export', { 
      params,
      responseType: 'blob'
    }),

  // 批量操作
  batchUpdate: (ids: number[], action: 'suspend' | 'resume' | 'cancel', data?: any) => 
    httpPost('/api/v1/subscriptions/batch', { ids, action, data }),

  // 获取即将到期的订阅
  getExpiring: (days: number = 7) => 
    httpGet<Subscription[]>('/api/v1/subscriptions/expiring', {
      params: { days }
    }),

  // 发送续费提醒
  sendRenewalReminder: (subscriptionId: number) => 
    httpPost(`/api/v1/subscriptions/${subscriptionId}/reminder`),

  // 获取订阅配置
  getConfigs: () => 
    httpGet<Array<{
      id: number;
      serviceName: string;
      displayName: string;
      description: string;
      pricing: {
        model: string;
        price: number;
        currency: string;
        unit: string;
      };
      features: string[];
      limitations: {
        callLimit: number;
        rateLimit: number;
      };
    }>>('/api/v1/subscriptions/configs')
}
