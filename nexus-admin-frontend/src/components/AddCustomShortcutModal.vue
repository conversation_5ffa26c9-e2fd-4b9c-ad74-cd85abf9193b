<template>
  <n-modal
    v-model:show="showModal"
    preset="card"
    title="添加自定义快捷键"
    size="medium"
    :mask-closable="false"
    :closable="true"
    class="add-shortcut-modal"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-placement="top"
      size="medium"
    >
      <n-form-item label="快捷键名称" path="description">
        <n-input
          v-model:value="formData.description"
          placeholder="输入快捷键描述"
          clearable
        />
      </n-form-item>

      <n-form-item label="快捷键组合" path="key">
        <div class="key-input-section">
          <n-input
            v-model:value="formData.key"
            placeholder="按下快捷键组合..."
            readonly
            @keydown="handleKeyDown"
            @focus="startRecording"
            @blur="stopRecording"
          />
          <n-button
            size="small"
            @click="clearKey"
            :disabled="!formData.key"
          >
            清除
          </n-button>
        </div>
        <template #feedback>
          <span class="form-hint">
            点击输入框并按下您想要的快捷键组合
          </span>
        </template>
      </n-form-item>

      <n-form-item label="分类" path="category">
        <n-select
          v-model:value="formData.category"
          :options="categoryOptions"
          placeholder="选择分类"
        />
      </n-form-item>

      <n-form-item label="动作类型" path="actionType">
        <n-radio-group v-model:value="formData.actionType">
          <n-space direction="vertical">
            <n-radio value="navigation">
              <div class="action-option">
                <div class="option-title">页面导航</div>
                <div class="option-description">跳转到指定页面</div>
              </div>
            </n-radio>
            <n-radio value="command">
              <div class="action-option">
                <div class="option-title">执行命令</div>
                <div class="option-description">执行自定义JavaScript代码</div>
              </div>
            </n-radio>
            <n-radio value="api">
              <div class="action-option">
                <div class="option-title">API调用</div>
                <div class="option-description">调用指定的API接口</div>
              </div>
            </n-radio>
          </n-space>
        </n-radio-group>
      </n-form-item>

      <!-- 导航配置 -->
      <n-form-item
        v-if="formData.actionType === 'navigation'"
        label="目标路径"
        path="navigationPath"
      >
        <n-input
          v-model:value="formData.navigationPath"
          placeholder="/path/to/page"
        />
      </n-form-item>

      <!-- 命令配置 -->
      <n-form-item
        v-if="formData.actionType === 'command'"
        label="JavaScript代码"
        path="commandCode"
      >
        <n-input
          v-model:value="formData.commandCode"
          type="textarea"
          placeholder="console.log('Hello World');"
          :rows="4"
        />
      </n-form-item>

      <!-- API配置 -->
      <template v-if="formData.actionType === 'api'">
        <n-form-item label="API URL" path="apiUrl">
          <n-input
            v-model:value="formData.apiUrl"
            placeholder="/api/endpoint"
          />
        </n-form-item>
        
        <n-form-item label="HTTP方法" path="apiMethod">
          <n-select
            v-model:value="formData.apiMethod"
            :options="httpMethodOptions"
          />
        </n-form-item>
      </template>
    </n-form>

    <template #action>
      <n-space justify="end">
        <n-button @click="showModal = false">取消</n-button>
        <n-button type="primary" @click="handleSubmit" :loading="submitting">
          添加快捷键
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useMessage } from 'naive-ui'
import type { FormInst, FormRules } from 'naive-ui'

const props = defineProps<{
  show: boolean
}>()

const emit = defineEmits<{
  'update:show': [value: boolean]
  success: [shortcut: any]
}>()

const message = useMessage()

// 响应式数据
const formRef = ref<FormInst | null>(null)
const submitting = ref(false)
const isRecording = ref(false)

const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 表单数据
const formData = ref({
  description: '',
  key: '',
  category: 'custom',
  actionType: 'navigation',
  navigationPath: '',
  commandCode: '',
  apiUrl: '',
  apiMethod: 'GET'
})

// 选项配置
const categoryOptions = [
  { label: '自定义', value: 'custom' },
  { label: '导航', value: 'navigation' },
  { label: '界面', value: 'interface' },
  { label: '操作', value: 'actions' }
]

const httpMethodOptions = [
  { label: 'GET', value: 'GET' },
  { label: 'POST', value: 'POST' },
  { label: 'PUT', value: 'PUT' },
  { label: 'DELETE', value: 'DELETE' }
]

// 表单验证规则
const formRules: FormRules = {
  description: [
    { required: true, message: '请输入快捷键描述', trigger: 'blur' }
  ],
  key: [
    { required: true, message: '请设置快捷键组合', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  actionType: [
    { required: true, message: '请选择动作类型', trigger: 'change' }
  ],
  navigationPath: [
    {
      required: true,
      message: '请输入目标路径',
      trigger: 'blur',
      validator: () => formData.value.actionType !== 'navigation' || !!formData.value.navigationPath
    }
  ],
  commandCode: [
    {
      required: true,
      message: '请输入JavaScript代码',
      trigger: 'blur',
      validator: () => formData.value.actionType !== 'command' || !!formData.value.commandCode
    }
  ],
  apiUrl: [
    {
      required: true,
      message: '请输入API URL',
      trigger: 'blur',
      validator: () => formData.value.actionType !== 'api' || !!formData.value.apiUrl
    }
  ]
}

// 事件处理
const startRecording = () => {
  isRecording.value = true
}

const stopRecording = () => {
  isRecording.value = false
}

const handleKeyDown = (event: KeyboardEvent) => {
  if (!isRecording.value) return

  event.preventDefault()
  event.stopPropagation()

  const keys: string[] = []
  
  if (event.ctrlKey) keys.push('ctrl')
  if (event.altKey) keys.push('alt')
  if (event.shiftKey) keys.push('shift')
  if (event.metaKey) keys.push('meta')
  
  // 添加主键
  if (!['Control', 'Alt', 'Shift', 'Meta'].includes(event.key)) {
    keys.push(event.key.toLowerCase())
  }
  
  if (keys.length > 1) { // 至少需要一个修饰键
    formData.value.key = keys.join('+')
  }
}

const clearKey = () => {
  formData.value.key = ''
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    // 构建快捷键动作
    let action: () => void

    switch (formData.value.actionType) {
      case 'navigation':
        action = () => {
          // 这里应该使用router进行导航
          console.log('导航到:', formData.value.navigationPath)
        }
        break
        
      case 'command':
        action = () => {
          try {
            // 执行JavaScript代码
            eval(formData.value.commandCode)
          } catch (error) {
            console.error('执行自定义命令失败:', error)
          }
        }
        break
        
      case 'api':
        action = async () => {
          try {
            const response = await fetch(formData.value.apiUrl, {
              method: formData.value.apiMethod
            })
            console.log('API调用结果:', response)
          } catch (error) {
            console.error('API调用失败:', error)
          }
        }
        break
        
      default:
        action = () => console.log('未知动作类型')
    }

    const shortcut = {
      key: formData.value.key,
      description: formData.value.description,
      action,
      category: formData.value.category,
      enabled: true
    }

    emit('success', shortcut)
    showModal.value = false
    resetForm()

  } catch (error: any) {
    message.error('添加失败: ' + (error.message || '未知错误'))
  } finally {
    submitting.value = false
  }
}

const resetForm = () => {
  formData.value = {
    description: '',
    key: '',
    category: 'custom',
    actionType: 'navigation',
    navigationPath: '',
    commandCode: '',
    apiUrl: '',
    apiMethod: 'GET'
  }
}
</script>

<style scoped>
.add-shortcut-modal {
  max-width: 500px;
}

.key-input-section {
  display: flex;
  gap: 8px;
  align-items: center;
}

.key-input-section .n-input {
  flex: 1;
}

.form-hint {
  font-size: 12px;
  color: #999;
}

.action-option {
  padding-left: 8px;
}

.option-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}

.option-description {
  font-size: 12px;
  color: #666;
}

:deep(.n-form-item-label) {
  font-weight: 600;
}
</style>
