<template>
  <n-modal v-model:show="visible" preset="dialog" title="批量操作">
    <template #header>
      <div class="flex items-center gap-2">
        <n-icon :component="BatchIcon" />
        <span>批量操作</span>
      </div>
    </template>

    <div class="space-y-4">
      <!-- 操作类型选择 -->
      <div>
        <n-text class="block mb-2">操作类型</n-text>
        <n-select
          v-model:value="selectedOperation"
          :options="operationOptions"
          placeholder="请选择操作类型"
        />
      </div>

      <!-- 选中项目显示 -->
      <div>
        <n-text class="block mb-2">选中项目 ({{ selectedItems.length }})</n-text>
        <div class="max-h-32 overflow-y-auto border rounded p-2">
          <div v-for="item in selectedItems" :key="item.id" class="flex items-center justify-between py-1">
            <span>{{ item.name || item.title || item.id }}</span>
            <n-tag size="small" :type="getItemType(item)">
              {{ item.type || '未知' }}
            </n-tag>
          </div>
        </div>
      </div>

      <!-- 操作参数 -->
      <div v-if="selectedOperation">
        <n-text class="block mb-2">操作参数</n-text>
        <div v-if="selectedOperation === 'delete'">
          <n-alert type="warning" class="mb-2">
            此操作不可逆，请确认是否要删除选中的 {{ selectedItems.length }} 个项目？
          </n-alert>
          <n-checkbox v-model:checked="confirmDelete">
            我确认要删除这些项目
          </n-checkbox>
        </div>
        <div v-else-if="selectedOperation === 'status'">
          <n-select
            v-model:value="newStatus"
            :options="statusOptions"
            placeholder="请选择新状态"
          />
        </div>
        <div v-else-if="selectedOperation === 'tag'">
          <n-dynamic-tags v-model:value="newTags" />
        </div>
      </div>
    </div>

    <template #action>
      <div class="flex gap-2">
        <n-button @click="handleCancel">取消</n-button>
        <n-button
          type="primary"
          :disabled="!canExecute"
          :loading="executing"
          @click="handleExecute"
        >
          执行操作
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { NModal, NIcon, NText, NSelect, NTag, NAlert, NCheckbox, NDynamicTags, NButton, useMessage } from 'naive-ui'
import { BatchIcon } from '@vicons/tabler'

interface BatchItem {
  id: string | number
  name?: string
  title?: string
  type?: string
  status?: string
  [key: string]: any
}

interface Props {
  show: boolean
  items: BatchItem[]
}

interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'execute', operation: string, params: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const message = useMessage()

const visible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const selectedItems = computed(() => props.items || [])

const selectedOperation = ref<string>('')
const confirmDelete = ref(false)
const newStatus = ref<string>('')
const newTags = ref<string[]>([])
const executing = ref(false)

const operationOptions = [
  { label: '删除', value: 'delete' },
  { label: '修改状态', value: 'status' },
  { label: '添加标签', value: 'tag' },
  { label: '导出', value: 'export' }
]

const statusOptions = [
  { label: '启用', value: 'enabled' },
  { label: '禁用', value: 'disabled' },
  { label: '维护中', value: 'maintenance' }
]

const canExecute = computed(() => {
  if (!selectedOperation.value || selectedItems.value.length === 0) return false
  
  switch (selectedOperation.value) {
    case 'delete':
      return confirmDelete.value
    case 'status':
      return !!newStatus.value
    case 'tag':
      return newTags.value.length > 0
    case 'export':
      return true
    default:
      return false
  }
})

const getItemType = (item: BatchItem) => {
  switch (item.type) {
    case 'service': return 'info'
    case 'user': return 'success'
    case 'config': return 'warning'
    default: return 'default'
  }
}

const handleCancel = () => {
  visible.value = false
  resetForm()
}

const handleExecute = async () => {
  if (!canExecute.value) return

  executing.value = true
  try {
    const params = {
      operation: selectedOperation.value,
      items: selectedItems.value.map(item => item.id),
      ...(selectedOperation.value === 'status' && { status: newStatus.value }),
      ...(selectedOperation.value === 'tag' && { tags: newTags.value })
    }

    emit('execute', selectedOperation.value, params)
    message.success(`批量${operationOptions.find(op => op.value === selectedOperation.value)?.label}操作已提交`)
    visible.value = false
    resetForm()
  } catch (error) {
    message.error('操作失败：' + (error as Error).message)
  } finally {
    executing.value = false
  }
}

const resetForm = () => {
  selectedOperation.value = ''
  confirmDelete.value = false
  newStatus.value = ''
  newTags.value = []
}
</script>

<style scoped>
.space-y-4 > * + * {
  margin-top: 1rem;
}
</style>
