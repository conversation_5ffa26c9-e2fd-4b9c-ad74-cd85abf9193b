<template>
  <div class="breadcrumb-nav">
    <n-breadcrumb>
      <n-breadcrumb-item
        v-for="(item, index) in breadcrumbItems"
        :key="item.path || index"
        :clickable="!!item.path && index < breadcrumbItems.length - 1"
        @click="handleBreadcrumbClick(item)"
      >
        <template #icon>
          <n-icon v-if="item.icon" :size="16">
            <component :is="item.icon" />
          </n-icon>
        </template>
        {{ item.label }}
      </n-breadcrumb-item>
    </n-breadcrumb>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  HomeOutline,
  GridOutline,
  ServerOutline,
  ConstructOutline,
  FolderOutline,
  TerminalOutline,
  CardOutline,
  AnalyticsOutline,
  PulseOutline,
  HardwareChipOutline,
  SwapHorizontalOutline,
  FlaskOutline,
  SettingsOutline,
  PersonOutline,
  OptionsOutline
} from '@vicons/ionicons5'

const route = useRoute()
const router = useRouter()

interface BreadcrumbItem {
  label: string
  path?: string
  icon?: any
}

// 路径到面包屑的映射
const pathToBreadcrumb: Record<string, BreadcrumbItem[]> = {
  '/': [
    { label: '首页', path: '/', icon: HomeOutline },
    { label: '仪表板', icon: GridOutline }
  ],
  '/mcp/overview': [
    { label: '首页', path: '/', icon: HomeOutline },
    { label: 'MCP服务', icon: ServerOutline },
    { label: '服务总览', icon: GridOutline }
  ],
  '/mcp/tools': [
    { label: '首页', path: '/', icon: HomeOutline },
    { label: 'MCP服务', icon: ServerOutline },
    { label: '工具管理', icon: ConstructOutline }
  ],
  '/mcp/resources': [
    { label: '首页', path: '/', icon: HomeOutline },
    { label: 'MCP服务', icon: ServerOutline },
    { label: '资源管理', icon: FolderOutline }
  ],
  '/mcp/commands': [
    { label: '首页', path: '/', icon: HomeOutline },
    { label: 'MCP服务', icon: ServerOutline },
    { label: '服务控制', icon: TerminalOutline }
  ],
  '/subscriptions': [
    { label: '首页', path: '/', icon: HomeOutline },
    { label: '订阅管理', icon: CardOutline }
  ],
  '/monitoring/realtime': [
    { label: '首页', path: '/', icon: HomeOutline },
    { label: '系统监控', icon: AnalyticsOutline },
    { label: '实时监控', icon: PulseOutline }
  ],
  '/monitoring/services': [
    { label: '首页', path: '/', icon: HomeOutline },
    { label: '系统监控', icon: AnalyticsOutline },
    { label: '服务监控', icon: HardwareChipOutline }
  ],
  '/monitoring/api-calls': [
    { label: '首页', path: '/', icon: HomeOutline },
    { label: '系统监控', icon: AnalyticsOutline },
    { label: 'API监控', icon: SwapHorizontalOutline }
  ],
  '/testing': [
    { label: '首页', path: '/', icon: HomeOutline },
    { label: '接口测试', icon: FlaskOutline }
  ],
  '/settings/profile': [
    { label: '首页', path: '/', icon: HomeOutline },
    { label: '系统设置', icon: SettingsOutline },
    { label: '个人资料', icon: PersonOutline }
  ],
  '/settings/preferences': [
    { label: '首页', path: '/', icon: HomeOutline },
    { label: '系统设置', icon: SettingsOutline },
    { label: '偏好设置', icon: OptionsOutline }
  ]
}

// 计算面包屑项目
const breadcrumbItems = computed<BreadcrumbItem[]>(() => {
  const currentPath = route.path
  
  // 直接匹配
  if (pathToBreadcrumb[currentPath]) {
    return pathToBreadcrumb[currentPath]
  }
  
  // 动态路由匹配
  for (const [pattern, items] of Object.entries(pathToBreadcrumb)) {
    if (currentPath.startsWith(pattern) && pattern !== '/') {
      return items
    }
  }
  
  // 默认面包屑
  return [
    { label: '首页', path: '/', icon: HomeOutline },
    { label: '未知页面' }
  ]
})

// 处理面包屑点击
const handleBreadcrumbClick = (item: BreadcrumbItem) => {
  if (item.path) {
    router.push(item.path)
  }
}
</script>

<style scoped>
.breadcrumb-nav {
  display: flex;
  align-items: center;
  height: 100%;
}

:deep(.n-breadcrumb) {
  font-size: 14px;
}

:deep(.n-breadcrumb-item) {
  color: var(--text-secondary);
  transition: all var(--duration-normal) var(--ease-out);
}

:deep(.n-breadcrumb-item:hover) {
  color: var(--text-primary);
}

:deep(.n-breadcrumb-item:last-child) {
  color: var(--text-primary);
  font-weight: 600;
}

:deep(.n-breadcrumb-item__link) {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  transition: all var(--duration-normal) var(--ease-out);
}

:deep(.n-breadcrumb-item__link:hover) {
  background: var(--primary-color-light);
  color: var(--primary-color);
}

:deep(.n-breadcrumb-item__separator) {
  color: var(--text-quaternary);
  margin: 0 var(--space-2);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .breadcrumb-nav {
    display: none;
  }
}

@media (max-width: 480px) {
  :deep(.n-breadcrumb-item__link) {
    padding: 2px 4px;
    font-size: 12px;
  }
  
  :deep(.n-breadcrumb-item__separator) {
    margin: 0 4px;
  }
}
</style>
