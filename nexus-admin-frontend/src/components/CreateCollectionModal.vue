<template>
  <n-modal
    v-model:show="showModal"
    preset="card"
    title="创建请求集合"
    size="medium"
    :mask-closable="false"
    :closable="true"
    class="create-collection-modal"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-placement="top"
      size="medium"
    >
      <n-form-item label="集合名称" path="name">
        <n-input
          v-model:value="formData.name"
          placeholder="输入集合名称"
          clearable
        />
      </n-form-item>

      <n-form-item label="集合描述" path="description">
        <n-input
          v-model:value="formData.description"
          type="textarea"
          placeholder="输入集合描述（可选）"
          :rows="3"
          maxlength="500"
          show-count
        />
      </n-form-item>

      <n-form-item label="集合类型">
        <n-radio-group v-model:value="formData.type">
          <n-space direction="vertical">
            <n-radio value="personal">
              <div class="radio-option">
                <div class="option-title">个人集合</div>
                <div class="option-description">仅您可以访问和编辑</div>
              </div>
            </n-radio>
            <n-radio value="team">
              <div class="radio-option">
                <div class="option-title">团队集合</div>
                <div class="option-description">团队成员可以共享和协作</div>
              </div>
            </n-radio>
            <n-radio value="public">
              <div class="radio-option">
                <div class="option-title">公开集合</div>
                <div class="option-description">所有用户都可以查看</div>
              </div>
            </n-radio>
          </n-space>
        </n-radio-group>
      </n-form-item>

      <n-form-item label="标签">
        <n-dynamic-tags
          v-model:value="formData.tags"
          :max="10"
          placeholder="添加标签"
        />
      </n-form-item>

      <n-form-item label="基础配置">
        <n-space direction="vertical" style="width: 100%;">
          <n-form-item label="基础URL" label-placement="left" label-width="80px">
            <n-input
              v-model:value="formData.baseUrl"
              placeholder="https://api.example.com"
              clearable
            />
          </n-form-item>
          
          <n-form-item label="超时时间" label-placement="left" label-width="80px">
            <n-input-number
              v-model:value="formData.timeout"
              :min="1000"
              :max="60000"
              :step="1000"
              style="width: 200px;"
            >
              <template #suffix>ms</template>
            </n-input-number>
          </n-form-item>
        </n-space>
      </n-form-item>

      <n-form-item label="默认请求头">
        <div class="headers-section">
          <div
            v-for="(header, index) in formData.defaultHeaders"
            :key="index"
            class="header-item"
          >
            <n-input
              v-model:value="header.key"
              placeholder="Header名"
              style="flex: 1;"
            />
            <n-input
              v-model:value="header.value"
              placeholder="Header值"
              style="flex: 1;"
            />
            <n-button
              quaternary
              type="error"
              size="small"
              @click="removeHeader(index)"
            >
              <n-icon>
                <TrashOutline />
              </n-icon>
            </n-button>
          </div>
          
          <n-button
            dashed
            block
            @click="addHeader"
          >
            <template #icon>
              <n-icon>
                <AddOutline />
              </n-icon>
            </template>
            添加请求头
          </n-button>
        </div>
      </n-form-item>

      <n-form-item label="环境变量">
        <div class="variables-section">
          <div
            v-for="(variable, index) in formData.variables"
            :key="index"
            class="variable-item"
          >
            <n-input
              v-model:value="variable.key"
              placeholder="变量名"
              style="flex: 1;"
            />
            <n-input
              v-model:value="variable.value"
              placeholder="变量值"
              style="flex: 1;"
            />
            <n-input
              v-model:value="variable.description"
              placeholder="描述"
              style="flex: 1;"
            />
            <n-button
              quaternary
              type="error"
              size="small"
              @click="removeVariable(index)"
            >
              <n-icon>
                <TrashOutline />
              </n-icon>
            </n-button>
          </div>
          
          <n-button
            dashed
            block
            @click="addVariable"
          >
            <template #icon>
              <n-icon>
                <AddOutline />
              </n-icon>
            </template>
            添加环境变量
          </n-button>
        </div>
      </n-form-item>

      <n-form-item label="导入选项" v-if="showImportOptions">
        <n-space direction="vertical" style="width: 100%;">
          <n-upload
            :file-list="importFiles"
            :max="1"
            accept=".json,.har,.postman_collection"
            @update:file-list="handleFileChange"
            @before-upload="handleBeforeUpload"
          >
            <n-upload-dragger>
              <div style="margin-bottom: 12px;">
                <n-icon size="48" :depth="3">
                  <CloudUploadOutline />
                </n-icon>
              </div>
              <n-text style="font-size: 16px;">
                点击或拖拽文件到此区域上传
              </n-text>
              <n-p depth="3" style="margin: 8px 0 0 0;">
                支持 Postman Collection、HAR 文件或 JSON 格式
              </n-p>
            </n-upload-dragger>
          </n-upload>
          
          <n-checkbox v-model:checked="formData.mergeRequests">
            合并重复的请求
          </n-checkbox>
        </n-space>
      </n-form-item>
    </n-form>

    <template #action>
      <n-space justify="end">
        <n-button @click="toggleImportOptions">
          {{ showImportOptions ? '隐藏导入' : '从文件导入' }}
        </n-button>
        <n-button @click="showModal = false">取消</n-button>
        <n-button type="primary" @click="handleSubmit" :loading="submitting">
          创建集合
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useMessage } from 'naive-ui'
import { AddOutline, TrashOutline, CloudUploadOutline } from '@vicons/ionicons5'
import type { FormInst, FormRules, UploadFileInfo } from 'naive-ui'

const props = defineProps<{
  show: boolean
}>()

const emit = defineEmits<{
  'update:show': [value: boolean]
  success: [collection: any]
}>()

const message = useMessage()

// 响应式数据
const formRef = ref<FormInst | null>(null)
const submitting = ref(false)
const showImportOptions = ref(false)
const importFiles = ref<UploadFileInfo[]>([])

const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 表单数据
const formData = ref({
  name: '',
  description: '',
  type: 'personal',
  tags: [] as string[],
  baseUrl: '',
  timeout: 30000,
  defaultHeaders: [
    { key: 'Content-Type', value: 'application/json' },
    { key: '', value: '' }
  ],
  variables: [
    { key: '', value: '', description: '' }
  ],
  mergeRequests: true
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入集合名称', trigger: 'blur' },
    { min: 2, max: 50, message: '集合名称长度应在2-50个字符之间', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '描述不能超过500个字符', trigger: 'blur' }
  ]
}

// 方法
const addHeader = () => {
  formData.value.defaultHeaders.push({ key: '', value: '' })
}

const removeHeader = (index: number) => {
  if (formData.value.defaultHeaders.length > 1) {
    formData.value.defaultHeaders.splice(index, 1)
  }
}

const addVariable = () => {
  formData.value.variables.push({ key: '', value: '', description: '' })
}

const removeVariable = (index: number) => {
  if (formData.value.variables.length > 1) {
    formData.value.variables.splice(index, 1)
  }
}

const toggleImportOptions = () => {
  showImportOptions.value = !showImportOptions.value
  if (!showImportOptions.value) {
    importFiles.value = []
  }
}

const handleFileChange = (fileList: UploadFileInfo[]) => {
  importFiles.value = fileList
}

const handleBeforeUpload = (data: { file: UploadFileInfo; fileList: UploadFileInfo[] }) => {
  const { file } = data
  
  // 验证文件类型
  const allowedTypes = ['.json', '.har', '.postman_collection']
  const fileName = file.name || ''
  const isValidType = allowedTypes.some(type => fileName.toLowerCase().endsWith(type))
  
  if (!isValidType) {
    message.error('不支持的文件类型，请上传 JSON、HAR 或 Postman Collection 文件')
    return false
  }
  
  // 验证文件大小（10MB）
  const isValidSize = (file.file?.size || 0) < 10 * 1024 * 1024
  if (!isValidSize) {
    message.error('文件大小不能超过 10MB')
    return false
  }
  
  return false // 阻止自动上传，我们手动处理
}

const parseImportFile = async (file: File): Promise<any> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string
        const data = JSON.parse(content)
        
        // 根据文件类型解析
        if (file.name.endsWith('.postman_collection.json') || data.info?.schema?.includes('postman')) {
          // Postman Collection 格式
          resolve(parsePostmanCollection(data))
        } else if (file.name.endsWith('.har') || data.log) {
          // HAR 格式
          resolve(parseHarFile(data))
        } else {
          // 通用 JSON 格式
          resolve(data)
        }
      } catch (error) {
        reject(new Error('文件格式错误，无法解析'))
      }
    }
    
    reader.onerror = () => {
      reject(new Error('文件读取失败'))
    }
    
    reader.readAsText(file)
  })
}

const parsePostmanCollection = (data: any) => {
  // 解析 Postman Collection
  return {
    name: data.info?.name || '导入的集合',
    description: data.info?.description || '',
    requests: data.item?.map((item: any) => ({
      name: item.name,
      method: item.request?.method || 'GET',
      url: item.request?.url?.raw || item.request?.url || '',
      headers: item.request?.header || [],
      body: item.request?.body
    })) || []
  }
}

const parseHarFile = (data: any) => {
  // 解析 HAR 文件
  const entries = data.log?.entries || []
  return {
    name: '从HAR导入的集合',
    description: '从HAR文件导入的请求集合',
    requests: entries.map((entry: any) => ({
      name: entry.request?.url || '未命名请求',
      method: entry.request?.method || 'GET',
      url: entry.request?.url || '',
      headers: entry.request?.headers || [],
      body: entry.request?.postData
    }))
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    let collectionData = {
      id: Date.now(),
      name: formData.value.name,
      description: formData.value.description,
      type: formData.value.type,
      tags: formData.value.tags,
      baseUrl: formData.value.baseUrl,
      timeout: formData.value.timeout,
      defaultHeaders: formData.value.defaultHeaders.filter(h => h.key && h.value),
      variables: formData.value.variables.filter(v => v.key && v.value),
      requests: [] as any[],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    // 处理导入文件
    if (importFiles.value.length > 0 && importFiles.value[0].file) {
      try {
        const importData = await parseImportFile(importFiles.value[0].file)
        
        if (importData.requests) {
          collectionData.requests = importData.requests
        }
        
        // 如果导入数据有名称且用户没有修改默认名称，使用导入的名称
        if (importData.name && formData.value.name === '') {
          collectionData.name = importData.name
        }
        
        if (importData.description && formData.value.description === '') {
          collectionData.description = importData.description
        }
        
        message.success(`成功导入 ${collectionData.requests.length} 个请求`)
      } catch (error: any) {
        message.error('导入文件失败: ' + error.message)
        return
      }
    }

    emit('success', collectionData)
    showModal.value = false
    resetForm()

  } catch (error: any) {
    message.error('创建失败: ' + (error.message || '未知错误'))
  } finally {
    submitting.value = false
  }
}

const resetForm = () => {
  formData.value = {
    name: '',
    description: '',
    type: 'personal',
    tags: [],
    baseUrl: '',
    timeout: 30000,
    defaultHeaders: [
      { key: 'Content-Type', value: 'application/json' },
      { key: '', value: '' }
    ],
    variables: [
      { key: '', value: '', description: '' }
    ],
    mergeRequests: true
  }
  importFiles.value = []
  showImportOptions.value = false
}

// 监听模态框关闭
watch(showModal, (show) => {
  if (!show) {
    resetForm()
  }
})
</script>

<style scoped>
.create-collection-modal {
  max-width: 600px;
}

.radio-option {
  padding-left: 8px;
}

.option-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}

.option-description {
  font-size: 12px;
  color: #666;
}

.headers-section,
.variables-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.header-item,
.variable-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.n-form-item-label) {
  font-weight: 600;
}

:deep(.n-upload-dragger) {
  padding: 24px;
}
</style>
