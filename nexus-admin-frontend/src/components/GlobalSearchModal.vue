<template>
  <n-modal
    v-model:show="showModal"
    preset="card"
    title="全局搜索"
    size="large"
    :mask-closable="true"
    :closable="true"
    class="search-modal"
  >
    <div class="search-container">
      <!-- 搜索输入框 -->
      <n-input
        ref="searchInputRef"
        v-model:value="searchQuery"
        size="large"
        placeholder="搜索页面、功能、设置..."
        clearable
        @input="handleSearch"
        @keyup.enter="handleEnter"
        @keyup.up="handleArrowUp"
        @keyup.down="handleArrowDown"
      >
        <template #prefix>
          <n-icon>
            <SearchOutline />
          </n-icon>
        </template>
      </n-input>

      <!-- 搜索结果 -->
      <div v-if="searchQuery" class="search-results">
        <div v-if="loading" class="loading-container">
          <n-spin size="small" />
          <span>搜索中...</span>
        </div>

        <div v-else-if="searchResults.length === 0" class="no-results">
          <n-icon size="48" color="#ccc">
            <SearchOutline />
          </n-icon>
          <p>未找到相关结果</p>
          <p class="suggestion">尝试使用不同的关键词</p>
        </div>

        <div v-else class="results-list">
          <div
            v-for="(result, index) in searchResults"
            :key="result.id"
            class="result-item"
            :class="{ active: selectedIndex === index }"
            @click="selectResult(result)"
            @mouseenter="selectedIndex = index"
          >
            <div class="result-icon">
              <n-icon :size="20">
                <component :is="result.icon" />
              </n-icon>
            </div>
            <div class="result-content">
              <div class="result-title" v-html="highlightText(result.title, searchQuery)"></div>
              <div class="result-description">{{ result.description }}</div>
              <div class="result-path">{{ result.path }}</div>
            </div>
            <div class="result-type">
              <n-tag size="small" :type="getResultTypeColor(result.type)">
                {{ result.type }}
              </n-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 搜索建议 -->
      <div v-else class="search-suggestions">
        <div class="suggestion-section">
          <h4>快速导航</h4>
          <div class="suggestion-grid">
            <div
              v-for="suggestion in quickNavSuggestions"
              :key="suggestion.id"
              class="suggestion-item"
              @click="selectResult(suggestion)"
            >
              <n-icon :size="18">
                <component :is="suggestion.icon" />
              </n-icon>
              <span>{{ suggestion.title }}</span>
            </div>
          </div>
        </div>

        <div class="suggestion-section">
          <h4>最近访问</h4>
          <div class="recent-list">
            <div
              v-for="recent in recentItems"
              :key="recent.id"
              class="recent-item"
              @click="selectResult(recent)"
            >
              <n-icon :size="16">
                <component :is="recent.icon" />
              </n-icon>
              <span>{{ recent.title }}</span>
              <span class="recent-time">{{ formatTime(recent.lastVisit) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 快捷键提示 -->
      <div class="shortcuts-hint">
        <span><kbd>↑</kbd><kbd>↓</kbd> 导航</span>
        <span><kbd>Enter</kbd> 选择</span>
        <span><kbd>Esc</kbd> 关闭</span>
      </div>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { SearchOutline, GridOutline, ServerOutline, CardOutline, AnalyticsOutline, FlaskOutline } from '@vicons/ionicons5'
import { useDebounceFn } from '@vueuse/core'

interface SearchResult {
  id: string
  title: string
  description: string
  path: string
  type: 'page' | 'feature' | 'setting' | 'api'
  icon: any
  lastVisit?: number
}

const props = defineProps<{
  show: boolean
}>()

const emit = defineEmits<{
  'update:show': [value: boolean]
}>()

const router = useRouter()

// 响应式数据
const searchInputRef = ref()
const searchQuery = ref('')
const loading = ref(false)
const selectedIndex = ref(0)

// 计算属性
const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 搜索数据源
const allSearchItems: SearchResult[] = [
  { id: '1', title: '仪表板', description: '系统概览和统计信息', path: '/', type: 'page', icon: GridOutline },
  { id: '2', title: 'MCP服务总览', description: '查看所有MCP服务状态', path: '/mcp/overview', type: 'page', icon: ServerOutline },
  { id: '3', title: '工具管理', description: '管理MCP工具和调用', path: '/mcp/tools', type: 'feature', icon: ServerOutline },
  { id: '4', title: '资源管理', description: '管理MCP资源和访问', path: '/mcp/resources', type: 'feature', icon: ServerOutline },
  { id: '5', title: '服务控制', description: '启动、停止MCP服务', path: '/mcp/commands', type: 'feature', icon: ServerOutline },
  { id: '6', title: '订阅管理', description: '管理用户订阅和权限', path: '/subscriptions', type: 'page', icon: CardOutline },
  { id: '7', title: '实时监控', description: '实时系统状态监控', path: '/monitoring/realtime', type: 'page', icon: AnalyticsOutline },
  { id: '8', title: '服务监控', description: '服务健康状态监控', path: '/monitoring/services', type: 'page', icon: AnalyticsOutline },
  { id: '9', title: 'API监控', description: 'API调用统计和监控', path: '/monitoring/api-calls', type: 'page', icon: AnalyticsOutline },
  { id: '10', title: '接口测试', description: 'API接口测试工具', path: '/testing', type: 'feature', icon: FlaskOutline }
]

// 搜索结果
const searchResults = ref<SearchResult[]>([])

// 快速导航建议
const quickNavSuggestions = computed(() => [
  { id: 'quick-1', title: '仪表板', path: '/', icon: GridOutline },
  { id: 'quick-2', title: 'MCP服务', path: '/mcp/overview', icon: ServerOutline },
  { id: 'quick-3', title: '订阅管理', path: '/subscriptions', icon: CardOutline },
  { id: 'quick-4', title: '实时监控', path: '/monitoring/realtime', icon: AnalyticsOutline }
])

// 最近访问项目（模拟数据）
const recentItems = ref<SearchResult[]>([
  { id: 'recent-1', title: '工具管理', path: '/mcp/tools', icon: ServerOutline, lastVisit: Date.now() - 300000 },
  { id: 'recent-2', title: '接口测试', path: '/testing', icon: FlaskOutline, lastVisit: Date.now() - 600000 }
])

// 防抖搜索
const debouncedSearch = useDebounceFn((query: string) => {
  if (!query.trim()) {
    searchResults.value = []
    return
  }

  loading.value = true
  
  // 模拟搜索延迟
  setTimeout(() => {
    const filtered = allSearchItems.filter(item => 
      item.title.toLowerCase().includes(query.toLowerCase()) ||
      item.description.toLowerCase().includes(query.toLowerCase())
    )
    
    searchResults.value = filtered
    selectedIndex.value = 0
    loading.value = false
  }, 200)
}, 300)

// 搜索处理
const handleSearch = (value: string) => {
  debouncedSearch(value)
}

// 键盘导航
const handleArrowUp = () => {
  if (selectedIndex.value > 0) {
    selectedIndex.value--
  }
}

const handleArrowDown = () => {
  if (selectedIndex.value < searchResults.value.length - 1) {
    selectedIndex.value++
  }
}

const handleEnter = () => {
  if (searchResults.value.length > 0) {
    selectResult(searchResults.value[selectedIndex.value])
  }
}

// 选择结果
const selectResult = (result: SearchResult) => {
  router.push(result.path)
  showModal.value = false
  searchQuery.value = ''
  
  // 添加到最近访问
  const existingIndex = recentItems.value.findIndex(item => item.path === result.path)
  if (existingIndex >= 0) {
    recentItems.value.splice(existingIndex, 1)
  }
  recentItems.value.unshift({ ...result, lastVisit: Date.now() })
  if (recentItems.value.length > 5) {
    recentItems.value.pop()
  }
}

// 高亮文本
const highlightText = (text: string, query: string) => {
  if (!query) return text
  const regex = new RegExp(`(${query})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

// 获取结果类型颜色
const getResultTypeColor = (type: string) => {
  const colors: Record<string, any> = {
    page: 'info',
    feature: 'success',
    setting: 'warning',
    api: 'error'
  }
  return colors[type] || 'default'
}

// 格式化时间
const formatTime = (timestamp: number) => {
  const diff = Date.now() - timestamp
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(minutes / 60)
  
  if (hours > 0) return `${hours}小时前`
  if (minutes > 0) return `${minutes}分钟前`
  return '刚刚'
}

// 监听模态框显示状态
watch(showModal, async (show) => {
  if (show) {
    await nextTick()
    searchInputRef.value?.focus()
  } else {
    searchQuery.value = ''
    searchResults.value = []
    selectedIndex.value = 0
  }
})
</script>

<style scoped>
.search-modal {
  max-width: 600px;
}

.search-container {
  padding: 8px 0;
}

.search-results {
  margin-top: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 32px;
  color: #666;
}

.no-results {
  text-align: center;
  padding: 32px;
  color: #666;
}

.no-results p {
  margin: 8px 0;
}

.suggestion {
  font-size: 14px;
  color: #999;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.result-item:hover,
.result-item.active {
  background: rgba(102, 126, 234, 0.1);
}

.result-icon {
  flex-shrink: 0;
  color: #667eea;
}

.result-content {
  flex: 1;
  min-width: 0;
}

.result-title {
  font-weight: 600;
  margin-bottom: 2px;
}

.result-title :deep(mark) {
  background: #667eea;
  color: white;
  padding: 1px 2px;
  border-radius: 2px;
}

.result-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 2px;
}

.result-path {
  font-size: 12px;
  color: #999;
}

.result-type {
  flex-shrink: 0;
}

.search-suggestions {
  margin-top: 16px;
}

.suggestion-section {
  margin-bottom: 24px;
}

.suggestion-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.suggestion-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 8px;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #eee;
}

.suggestion-item:hover {
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
}

.recent-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.recent-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.recent-item:hover {
  background: rgba(102, 126, 234, 0.1);
}

.recent-time {
  margin-left: auto;
  font-size: 12px;
  color: #999;
}

.shortcuts-hint {
  display: flex;
  gap: 16px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #eee;
  font-size: 12px;
  color: #666;
}

.shortcuts-hint kbd {
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 3px;
  padding: 2px 6px;
  font-family: monospace;
  font-size: 11px;
}
</style>
