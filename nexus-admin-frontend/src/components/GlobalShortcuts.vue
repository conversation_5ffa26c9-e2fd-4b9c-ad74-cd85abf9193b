<template>
  <!-- 全局快捷键处理组件 -->
  <div style="display: none;"></div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useThemeStore } from '@/stores/theme'
import { useMessage } from 'naive-ui'

const router = useRouter()
const themeStore = useThemeStore()
const message = useMessage()

// 快捷键映射
const shortcuts = {
  // 主题切换 Ctrl+Shift+T
  'ctrl+shift+t': () => {
    themeStore.toggleTheme()
    message.info(`已切换到${themeStore.isDark ? '暗色' : '亮色'}主题`)
  },
  
  // 侧边栏切换 Ctrl+B
  'ctrl+b': () => {
    themeStore.toggleSidebar()
  },
  
  // 导航快捷键
  'ctrl+1': () => router.push('/'),
  'ctrl+2': () => router.push('/mcp/overview'),
  'ctrl+3': () => router.push('/subscriptions'),
  'ctrl+4': () => router.push('/monitoring/realtime'),
  'ctrl+5': () => router.push('/testing'),
  
  // 搜索 Ctrl+K
  'ctrl+k': (event: KeyboardEvent) => {
    event.preventDefault()
    // 触发全局搜索
    const searchEvent = new CustomEvent('global-search')
    window.dispatchEvent(searchEvent)
  },
  
  // 刷新 F5 或 Ctrl+R
  'f5': () => window.location.reload(),
  'ctrl+r': (event: KeyboardEvent) => {
    event.preventDefault()
    window.location.reload()
  },
  
  // 帮助 F1
  'f1': (event: KeyboardEvent) => {
    event.preventDefault()
    // 显示帮助对话框
    const helpEvent = new CustomEvent('show-help')
    window.dispatchEvent(helpEvent)
  }
}

// 键盘事件处理
const handleKeyDown = (event: KeyboardEvent) => {
  const key = event.key.toLowerCase()
  const ctrl = event.ctrlKey
  const shift = event.shiftKey
  const alt = event.altKey
  
  // 构建快捷键字符串
  let shortcut = ''
  if (ctrl) shortcut += 'ctrl+'
  if (shift) shortcut += 'shift+'
  if (alt) shortcut += 'alt+'
  shortcut += key
  
  // 查找并执行对应的快捷键处理函数
  const handler = shortcuts[shortcut as keyof typeof shortcuts]
  if (handler) {
    event.preventDefault()
    handler(event)
  }
}

// 组件挂载时注册事件监听
onMounted(() => {
  document.addEventListener('keydown', handleKeyDown)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown)
})

// 暴露快捷键信息供其他组件使用
defineExpose({
  shortcuts: {
    '主题切换': 'Ctrl + Shift + T',
    '侧边栏切换': 'Ctrl + B',
    '仪表板': 'Ctrl + 1',
    'MCP服务': 'Ctrl + 2',
    '订阅管理': 'Ctrl + 3',
    '实时监控': 'Ctrl + 4',
    '接口测试': 'Ctrl + 5',
    '全局搜索': 'Ctrl + K',
    '刷新页面': 'F5 / Ctrl + R',
    '帮助': 'F1'
  }
})
</script>
