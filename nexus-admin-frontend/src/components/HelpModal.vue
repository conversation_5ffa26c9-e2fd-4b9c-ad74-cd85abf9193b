<template>
  <n-modal
    v-model:show="showModal"
    preset="card"
    title="帮助中心"
    size="large"
    :mask-closable="true"
    :closable="true"
    class="help-modal"
  >
    <div class="help-container">
      <!-- 帮助导航 -->
      <div class="help-nav">
        <n-menu
          v-model:value="activeSection"
          mode="vertical"
          :options="helpSections"
          @update:value="handleSectionChange"
        />
      </div>

      <!-- 帮助内容 -->
      <div class="help-content">
        <!-- 快速开始 -->
        <div v-if="activeSection === 'quick-start'" class="content-section">
          <h2>快速开始</h2>
          <div class="quick-start-grid">
            <div class="quick-item">
              <n-icon size="32" color="#667eea">
                <GridOutline />
              </n-icon>
              <h3>查看仪表板</h3>
              <p>从仪表板开始，了解系统整体状态和关键指标</p>
              <n-button text type="primary" @click="navigateTo('/')">
                前往仪表板
              </n-button>
            </div>
            
            <div class="quick-item">
              <n-icon size="32" color="#667eea">
                <ServerOutline />
              </n-icon>
              <h3>管理MCP服务</h3>
              <p>注册、启动和管理您的MCP服务</p>
              <n-button text type="primary" @click="navigateTo('/mcp/overview')">
                查看MCP服务
              </n-button>
            </div>
            
            <div class="quick-item">
              <n-icon size="32" color="#667eea">
                <FlaskOutline />
              </n-icon>
              <h3>测试API接口</h3>
              <p>使用内置工具测试和调试API接口</p>
              <n-button text type="primary" @click="navigateTo('/testing')">
                开始测试
              </n-button>
            </div>
          </div>
        </div>

        <!-- 快捷键 -->
        <div v-if="activeSection === 'shortcuts'" class="content-section">
          <h2>快捷键</h2>
          <div class="shortcuts-list">
            <div v-for="(shortcut, key) in shortcuts" :key="key" class="shortcut-item">
              <div class="shortcut-keys">
                <kbd v-for="k in shortcut.split(' + ')" :key="k">{{ k }}</kbd>
              </div>
              <div class="shortcut-description">{{ key }}</div>
            </div>
          </div>
        </div>

        <!-- 功能介绍 -->
        <div v-if="activeSection === 'features'" class="content-section">
          <h2>功能介绍</h2>
          <div class="features-list">
            <div class="feature-item">
              <h3>MCP服务管理</h3>
              <p>统一管理本地和远程MCP服务，支持服务注册、启动、停止、健康检查等操作。</p>
              <ul>
                <li>服务总览：查看所有服务状态</li>
                <li>工具管理：管理和调用MCP工具</li>
                <li>资源管理：访问和管理MCP资源</li>
                <li>服务控制：通过命令行启动服务</li>
              </ul>
            </div>
            
            <div class="feature-item">
              <h3>订阅管理</h3>
              <p>管理用户订阅，控制服务访问权限和使用限制。</p>
              <ul>
                <li>订阅创建和管理</li>
                <li>使用量统计和限制</li>
                <li>权限控制</li>
                <li>到期提醒</li>
              </ul>
            </div>
            
            <div class="feature-item">
              <h3>实时监控</h3>
              <p>实时监控系统状态、服务健康度和API调用情况。</p>
              <ul>
                <li>实时数据展示</li>
                <li>服务健康监控</li>
                <li>API调用统计</li>
                <li>告警通知</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- 常见问题 -->
        <div v-if="activeSection === 'faq'" class="content-section">
          <h2>常见问题</h2>
          <n-collapse>
            <n-collapse-item title="如何注册新的MCP服务？" name="1">
              <p>您可以通过以下步骤注册新的MCP服务：</p>
              <ol>
                <li>进入"MCP服务"页面</li>
                <li>点击"注册服务"按钮</li>
                <li>填写服务信息（名称、端点、类型等）</li>
                <li>保存并启动服务</li>
              </ol>
            </n-collapse-item>
            
            <n-collapse-item title="如何使用接口测试工具？" name="2">
              <p>接口测试工具提供了完整的API测试功能：</p>
              <ol>
                <li>进入"接口测试"页面</li>
                <li>选择HTTP方法和输入URL</li>
                <li>设置请求头和参数</li>
                <li>点击发送查看响应结果</li>
              </ol>
            </n-collapse-item>
            
            <n-collapse-item title="如何查看实时监控数据？" name="3">
              <p>实时监控提供多维度的系统监控：</p>
              <ol>
                <li>进入"系统监控"页面</li>
                <li>选择要查看的监控类型</li>
                <li>数据会自动实时更新</li>
                <li>可以设置告警阈值</li>
              </ol>
            </n-collapse-item>
          </n-collapse>
        </div>

        <!-- 联系支持 -->
        <div v-if="activeSection === 'support'" class="content-section">
          <h2>联系支持</h2>
          <div class="support-options">
            <div class="support-item">
              <n-icon size="24" color="#667eea">
                <MailOutline />
              </n-icon>
              <div>
                <h3>邮件支持</h3>
                <p>发送邮件到 <EMAIL></p>
                <p>我们会在24小时内回复</p>
              </div>
            </div>
            
            <div class="support-item">
              <n-icon size="24" color="#667eea">
                <ChatbubbleOutline />
              </n-icon>
              <div>
                <h3>在线客服</h3>
                <p>工作日 9:00-18:00</p>
                <n-button type="primary" size="small">开始对话</n-button>
              </div>
            </div>
            
            <div class="support-item">
              <n-icon size="24" color="#667eea">
                <DocumentTextOutline />
              </n-icon>
              <div>
                <h3>文档中心</h3>
                <p>查看详细的技术文档</p>
                <n-button text type="primary" size="small">访问文档</n-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import {
  GridOutline,
  ServerOutline,
  FlaskOutline,
  MailOutline,
  ChatbubbleOutline,
  DocumentTextOutline
} from '@vicons/ionicons5'

const props = defineProps<{
  show: boolean
}>()

const emit = defineEmits<{
  'update:show': [value: boolean]
}>()

const router = useRouter()

// 响应式数据
const activeSection = ref('quick-start')

// 计算属性
const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 帮助章节
const helpSections = [
  { label: '快速开始', key: 'quick-start' },
  { label: '快捷键', key: 'shortcuts' },
  { label: '功能介绍', key: 'features' },
  { label: '常见问题', key: 'faq' },
  { label: '联系支持', key: 'support' }
]

// 快捷键列表
const shortcuts = {
  '主题切换': 'Ctrl + Shift + T',
  '侧边栏切换': 'Ctrl + B',
  '仪表板': 'Ctrl + 1',
  'MCP服务': 'Ctrl + 2',
  '订阅管理': 'Ctrl + 3',
  '实时监控': 'Ctrl + 4',
  '接口测试': 'Ctrl + 5',
  '全局搜索': 'Ctrl + K',
  '刷新页面': 'F5',
  '帮助': 'F1'
}

// 事件处理
const handleSectionChange = (key: string) => {
  activeSection.value = key
}

const navigateTo = (path: string) => {
  router.push(path)
  showModal.value = false
}
</script>

<style scoped>
.help-modal {
  max-width: 800px;
}

.help-container {
  display: flex;
  gap: 24px;
  min-height: 500px;
}

.help-nav {
  width: 200px;
  flex-shrink: 0;
}

.help-content {
  flex: 1;
  overflow-y: auto;
}

.content-section h2 {
  margin: 0 0 24px 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.quick-start-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.quick-item {
  text-align: center;
  padding: 24px;
  border: 1px solid #eee;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.quick-item:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.quick-item h3 {
  margin: 16px 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}

.quick-item p {
  margin: 0 0 16px 0;
  color: #666;
  font-size: 14px;
}

.shortcuts-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.shortcut-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.shortcut-keys {
  display: flex;
  gap: 4px;
}

.shortcut-keys kbd {
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 4px 8px;
  font-family: monospace;
  font-size: 12px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.shortcut-description {
  font-weight: 500;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.feature-item h3 {
  margin: 0 0 8px 0;
  color: #667eea;
  font-size: 18px;
  font-weight: 600;
}

.feature-item p {
  margin: 0 0 12px 0;
  color: #666;
  line-height: 1.6;
}

.feature-item ul {
  margin: 0;
  padding-left: 20px;
  color: #666;
}

.feature-item li {
  margin-bottom: 4px;
}

.support-options {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.support-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  border: 1px solid #eee;
  border-radius: 12px;
}

.support-item h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
}

.support-item p {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .help-container {
    flex-direction: column;
  }
  
  .help-nav {
    width: 100%;
  }
  
  .quick-start-grid {
    grid-template-columns: 1fr;
  }
  
  .shortcut-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
