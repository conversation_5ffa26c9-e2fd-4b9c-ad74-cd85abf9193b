<template>
  <n-modal
    v-model:show="showModal"
    preset="card"
    title="导入设置配置"
    size="medium"
    :mask-closable="false"
    :closable="true"
    class="import-settings-modal"
  >
    <div class="import-content">
      <n-upload
        :file-list="fileList"
        :max="1"
        accept=".json"
        @before-upload="handleBeforeUpload"
        @update:file-list="handleFileChange"
        :show-file-list="false"
      >
        <n-upload-dragger>
          <div style="margin-bottom: 12px;">
            <n-icon size="48" :depth="3">
              <CloudUploadOutline />
            </n-icon>
          </div>
          <n-text style="font-size: 16px;">
            点击或拖拽配置文件到此区域上传
          </n-text>
          <n-p depth="3" style="margin: 8px 0 0 0;">
            支持 JSON 格式的配置文件
          </n-p>
        </n-upload-dragger>
      </n-upload>

      <div v-if="previewData" class="preview-section">
        <h4>配置预览</h4>
        <n-scrollbar style="max-height: 300px;">
          <pre class="config-preview">{{ JSON.stringify(previewData, null, 2) }}</pre>
        </n-scrollbar>
        
        <div class="import-options">
          <n-checkbox-group v-model:value="selectedSections">
            <n-space direction="vertical">
              <n-checkbox
                v-for="section in availableSections"
                :key="section.key"
                :value="section.key"
                :label="section.label"
              />
            </n-space>
          </n-checkbox-group>
        </div>
      </div>
    </div>

    <template #action>
      <n-space justify="end">
        <n-button @click="showModal = false">取消</n-button>
        <n-button
          type="primary"
          @click="handleImport"
          :loading="importing"
          :disabled="!previewData"
        >
          导入配置
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useMessage } from 'naive-ui'
import { CloudUploadOutline } from '@vicons/ionicons5'
import type { UploadFileInfo } from 'naive-ui'

const props = defineProps<{
  show: boolean
}>()

const emit = defineEmits<{
  'update:show': [value: boolean]
  success: [settings: any]
}>()

const message = useMessage()

// 响应式数据
const importing = ref(false)
const fileList = ref<UploadFileInfo[]>([])
const previewData = ref<any>(null)
const selectedSections = ref<string[]>([])

const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 可用的配置节
const availableSections = computed(() => {
  if (!previewData.value?.settings) return []
  
  const sections = []
  const settings = previewData.value.settings
  
  if (settings.profile) {
    sections.push({ key: 'profile', label: '个人资料' })
  }
  if (settings.interface) {
    sections.push({ key: 'interface', label: '界面设置' })
  }
  if (settings.notifications) {
    sections.push({ key: 'notifications', label: '通知设置' })
  }
  if (settings.security) {
    sections.push({ key: 'security', label: '安全设置' })
  }
  if (settings.system) {
    sections.push({ key: 'system', label: '系统配置' })
  }
  if (settings.advanced) {
    sections.push({ key: 'advanced', label: '高级设置' })
  }
  
  return sections
})

// 事件处理
const handleBeforeUpload = (data: { file: UploadFileInfo; fileList: UploadFileInfo[] }) => {
  const { file } = data
  
  // 验证文件类型
  if (!file.name?.endsWith('.json')) {
    message.error('请选择 JSON 格式的配置文件')
    return false
  }
  
  // 验证文件大小（1MB）
  if ((file.file?.size || 0) > 1024 * 1024) {
    message.error('文件大小不能超过 1MB')
    return false
  }
  
  // 读取文件内容
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const content = e.target?.result as string
      const data = JSON.parse(content)
      
      // 验证配置文件格式
      if (!data.settings) {
        message.error('无效的配置文件格式')
        return
      }
      
      previewData.value = data
      selectedSections.value = availableSections.value.map(s => s.key)
      
    } catch (error) {
      message.error('配置文件格式错误，请检查 JSON 格式')
      previewData.value = null
    }
  }
  
  reader.onerror = () => {
    message.error('文件读取失败')
  }
  
  reader.readAsText(file.file!)
  
  return false // 阻止自动上传
}

const handleFileChange = (newFileList: UploadFileInfo[]) => {
  fileList.value = newFileList
  
  if (newFileList.length === 0) {
    previewData.value = null
    selectedSections.value = []
  }
}

const handleImport = async () => {
  if (!previewData.value || selectedSections.value.length === 0) {
    message.warning('请选择要导入的配置节')
    return
  }

  importing.value = true
  
  try {
    // 构建要导入的设置
    const importSettings: any = {}
    
    selectedSections.value.forEach(section => {
      if (previewData.value.settings[section]) {
        importSettings[section] = previewData.value.settings[section]
      }
    })
    
    emit('success', importSettings)
    showModal.value = false
    resetModal()
    
  } catch (error: any) {
    message.error('导入失败: ' + error.message)
  } finally {
    importing.value = false
  }
}

const resetModal = () => {
  fileList.value = []
  previewData.value = null
  selectedSections.value = []
}
</script>

<style scoped>
.import-settings-modal {
  max-width: 600px;
}

.import-content {
  padding: 8px 0;
}

.preview-section {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.preview-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.config-preview {
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 16px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #333;
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}

.import-options {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

:deep(.n-upload-dragger) {
  padding: 32px;
}
</style>
