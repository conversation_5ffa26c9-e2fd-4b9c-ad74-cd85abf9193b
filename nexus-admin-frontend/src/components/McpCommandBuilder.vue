<template>
  <div class="mcp-command-builder">
    <div class="builder-header">
      <h3>MCP服务命令构建器</h3>
      <p>选择模板或自定义命令来启动MCP服务</p>
    </div>

    <!-- 命令模板选择 -->
    <div class="template-section">
      <h4>常用服务模板</h4>
      <div class="template-grid">
        <div 
          v-for="template in commandTemplates" 
          :key="template.id"
          class="template-card"
          :class="{ active: selectedTemplate?.id === template.id }"
          @click="selectTemplate(template)"
        >
          <div class="template-icon">
            <n-icon size="24" :color="template.color">
              <component :is="template.icon" />
            </n-icon>
          </div>
          <div class="template-info">
            <div class="template-name">{{ template.name }}</div>
            <div class="template-desc">{{ template.description }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 命令编辑器 -->
    <div class="command-section">
      <h4>命令配置</h4>
      
      <!-- 命令输入 -->
      <div class="command-input">
        <n-input
          v-model:value="command"
          type="textarea"
          placeholder="输入MCP服务启动命令，例如：npx -y @modelcontextprotocol/server-filesystem /path/to/files"
          :rows="3"
          @input="onCommandChange"
        />
      </div>

      <!-- 参数配置 -->
      <div v-if="selectedTemplate && selectedTemplate.params.length > 0" class="params-section">
        <h5>参数配置</h5>
        <div class="params-grid">
          <div v-for="param in selectedTemplate.params" :key="param.key" class="param-item">
            <label>{{ param.label }}</label>
            <n-input
              v-model:value="paramValues[param.key]"
              :placeholder="param.placeholder"
              @input="updateCommand"
            />
            <span v-if="param.description" class="param-desc">{{ param.description }}</span>
          </div>
        </div>
      </div>

      <!-- 服务名称 -->
      <div class="service-name-section">
        <label>服务名称（可选）</label>
        <n-input
          v-model:value="serviceName"
          placeholder="自定义服务名称，留空则自动生成"
        />
      </div>
    </div>

    <!-- 命令预览和验证 -->
    <div class="preview-section">
      <div class="preview-header">
        <h4>命令预览</h4>
        <div class="preview-actions">
          <n-button size="small" @click="validateCommand" :loading="validating">
            验证命令
          </n-button>
          <n-button size="small" @click="previewCommand" :loading="previewing">
            预览配置
          </n-button>
        </div>
      </div>
      
      <div class="command-preview">
        <pre>{{ command || '请输入或选择命令模板' }}</pre>
      </div>

      <!-- 验证结果 -->
      <div v-if="validationResult" class="validation-result">
        <n-alert 
          :type="validationResult.valid ? 'success' : 'error'"
          :title="validationResult.valid ? '命令验证通过' : '命令验证失败'"
        >
          {{ validationResult.message || (validationResult.valid ? '命令格式正确，可以启动服务' : '请检查命令格式') }}
        </n-alert>
      </div>

      <!-- 配置预览 -->
      <div v-if="configPreview" class="config-preview">
        <h5>解析后的配置</h5>
        <pre>{{ JSON.stringify(configPreview, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import {
  FolderOutline,
  GitBranchOutline,
  TimeOutline,
  CloudDownloadOutline,
  TerminalOutline,
  CodeOutline,
  ServerOutline
} from '@vicons/ionicons5'
import { mcpApi } from '@/api/mcp'
import { useMessage } from 'naive-ui'

interface CommandTemplate {
  id: string
  name: string
  description: string
  command: string
  icon: any
  color: string
  params: Array<{
    key: string
    label: string
    placeholder: string
    description?: string
    required?: boolean
  }>
}

interface ValidationResult {
  valid: boolean
  command: string
  message?: string
}

const message = useMessage()

// 响应式数据
const command = ref('')
const serviceName = ref('')
const selectedTemplate = ref<CommandTemplate | null>(null)
const paramValues = reactive<Record<string, string>>({})
const validating = ref(false)
const previewing = ref(false)
const validationResult = ref<ValidationResult | null>(null)
const configPreview = ref<any>(null)

// 命令模板
const commandTemplates: CommandTemplate[] = [
  {
    id: 'filesystem',
    name: '文件系统服务',
    description: '提供文件和目录操作功能',
    command: 'npx -y @modelcontextprotocol/server-filesystem',
    icon: FolderOutline,
    color: '#52c41a',
    params: [
      {
        key: 'path',
        label: '允许访问的路径',
        placeholder: '/home/<USER>/workspace',
        description: '指定MCP服务可以访问的文件系统路径',
        required: true
      }
    ]
  },
  {
    id: 'git',
    name: 'Git仓库服务',
    description: 'Git仓库管理和操作',
    command: 'uvx mcp-server-git',
    icon: GitBranchOutline,
    color: '#1890ff',
    params: [
      {
        key: 'repository',
        label: '仓库路径',
        placeholder: '/path/to/git/repo',
        description: 'Git仓库的本地路径',
        required: true
      }
    ]
  },
  {
    id: 'sqlite',
    name: 'SQLite数据库',
    description: 'SQLite数据库查询和操作',
    command: 'uvx mcp-server-sqlite',
    icon: ServerOutline,
    color: '#722ed1',
    params: [
      {
        key: 'db-path',
        label: '数据库文件路径',
        placeholder: '/path/to/database.db',
        description: 'SQLite数据库文件的路径',
        required: true
      }
    ]
  },
  {
    id: 'memory',
    name: '内存服务',
    description: '提供内存存储和检索功能',
    command: 'npx -y @modelcontextprotocol/server-memory',
    icon: ServerOutline,
    color: '#fa8c16',
    params: []
  },
  {
    id: 'time',
    name: '时间服务',
    description: '提供时间和日期相关功能',
    command: 'npx -y @modelcontextprotocol/server-time',
    icon: TimeOutline,
    color: '#13c2c2',
    params: []
  },
  {
    id: 'fetch',
    name: '网页抓取服务',
    description: '网页内容获取和处理',
    command: 'npx -y @modelcontextprotocol/server-fetch',
    icon: CloudDownloadOutline,
    color: '#eb2f96',
    params: []
  },
  {
    id: 'custom-python',
    name: 'Python自定义服务',
    description: '自定义Python MCP服务',
    command: 'python',
    icon: CodeOutline,
    color: '#faad14',
    params: [
      {
        key: 'script',
        label: 'Python脚本路径',
        placeholder: 'mcp_server.py',
        description: 'Python MCP服务脚本文件',
        required: true
      },
      {
        key: 'args',
        label: '额外参数',
        placeholder: '--port 8080 --config config.json',
        description: '传递给Python脚本的额外参数'
      }
    ]
  },
  {
    id: 'custom-node',
    name: 'Node.js自定义服务',
    description: '自定义Node.js MCP服务',
    command: 'node',
    icon: TerminalOutline,
    color: '#52c41a',
    params: [
      {
        key: 'script',
        label: 'Node.js脚本路径',
        placeholder: 'server.js',
        description: 'Node.js MCP服务脚本文件',
        required: true
      },
      {
        key: 'args',
        label: '额外参数',
        placeholder: '--port 8080 --config config.json',
        description: '传递给Node.js脚本的额外参数'
      }
    ]
  }
]

// 计算属性
const emit = defineEmits<{
  commandChange: [command: string, serviceName?: string]
  templateSelect: [template: CommandTemplate]
}>()

// 方法
const selectTemplate = (template: CommandTemplate) => {
  selectedTemplate.value = template
  
  // 清空之前的参数值
  Object.keys(paramValues).forEach(key => {
    delete paramValues[key]
  })
  
  // 设置默认命令
  command.value = template.command
  
  // 如果没有参数，直接更新命令
  if (template.params.length === 0) {
    updateCommand()
  }
  
  emit('templateSelect', template)
}

const updateCommand = () => {
  if (!selectedTemplate.value) return
  
  let cmd = selectedTemplate.value.command
  
  // 根据模板类型构建命令
  if (selectedTemplate.value.id === 'filesystem') {
    const path = paramValues.path
    if (path) {
      cmd = `${cmd} "${path}"`
    }
  } else if (selectedTemplate.value.id === 'git') {
    const repo = paramValues.repository
    if (repo) {
      cmd = `${cmd} --repository "${repo}"`
    }
  } else if (selectedTemplate.value.id === 'sqlite') {
    const dbPath = paramValues['db-path']
    if (dbPath) {
      cmd = `${cmd} --db-path "${dbPath}"`
    }
  } else if (selectedTemplate.value.id === 'custom-python') {
    const script = paramValues.script
    const args = paramValues.args
    if (script) {
      cmd = `${cmd} "${script}"`
      if (args) {
        cmd = `${cmd} ${args}`
      }
    }
  } else if (selectedTemplate.value.id === 'custom-node') {
    const script = paramValues.script
    const args = paramValues.args
    if (script) {
      cmd = `${cmd} "${script}"`
      if (args) {
        cmd = `${cmd} ${args}`
      }
    }
  }
  
  command.value = cmd
  onCommandChange()
}

const onCommandChange = () => {
  // 清空之前的验证结果
  validationResult.value = null
  configPreview.value = null
  
  emit('commandChange', command.value, serviceName.value)
}

const validateCommand = async () => {
  if (!command.value.trim()) {
    message.warning('请输入命令')
    return
  }
  
  validating.value = true
  try {
    const result = await mcpApi.commands.validateCommand(command.value)
    validationResult.value = result.data
    
    if (result.data.valid) {
      message.success('命令验证通过')
    } else {
      message.error('命令验证失败')
    }
  } catch (error: any) {
    message.error('验证失败: ' + (error.message || '未知错误'))
    validationResult.value = {
      valid: false,
      command: command.value,
      message: error.message || '验证请求失败'
    }
  } finally {
    validating.value = false
  }
}

const previewCommand = async () => {
  if (!command.value.trim()) {
    message.warning('请输入命令')
    return
  }
  
  previewing.value = true
  try {
    const result = await mcpApi.commands.previewCommand(command.value)
    configPreview.value = result.data.config
    message.success('配置预览生成成功')
  } catch (error: any) {
    message.error('预览失败: ' + (error.message || '未知错误'))
  } finally {
    previewing.value = false
  }
}

// 监听服务名称变化
watch(serviceName, () => {
  emit('commandChange', command.value, serviceName.value)
})

// 暴露方法给父组件
defineExpose({
  validateCommand,
  previewCommand,
  getCommand: () => command.value,
  getServiceName: () => serviceName.value,
  reset: () => {
    command.value = ''
    serviceName.value = ''
    selectedTemplate.value = null
    Object.keys(paramValues).forEach(key => delete paramValues[key])
    validationResult.value = null
    configPreview.value = null
  }
})
</script>

<style scoped>
.mcp-command-builder {
  padding: 0;
}

.builder-header {
  margin-bottom: 24px;
}

.builder-header h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.builder-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.template-section {
  margin-bottom: 32px;
}

.template-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 12px;
}

.template-card {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.template-card:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.template-card.active {
  border-color: #1890ff;
  background: #f6ffed;
}

.template-icon {
  margin-right: 12px;
  flex-shrink: 0;
}

.template-info {
  flex: 1;
}

.template-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.template-desc {
  font-size: 12px;
  color: #666;
}

.command-section {
  margin-bottom: 32px;
}

.command-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.command-input {
  margin-bottom: 20px;
}

.params-section {
  margin-bottom: 20px;
}

.params-section h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.params-grid {
  display: grid;
  gap: 16px;
}

.param-item label {
  display: block;
  margin-bottom: 4px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.param-desc {
  display: block;
  margin-top: 4px;
  font-size: 12px;
  color: #999;
}

.service-name-section label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.preview-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 24px;
}

.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.preview-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.preview-actions {
  display: flex;
  gap: 8px;
}

.command-preview {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
}

.command-preview pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.4;
  color: #333;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.validation-result {
  margin-bottom: 16px;
}

.config-preview {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
}

.config-preview h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.config-preview pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #333;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 300px;
  overflow-y: auto;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .template-grid {
    grid-template-columns: 1fr;
  }
  
  .preview-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
</style>
