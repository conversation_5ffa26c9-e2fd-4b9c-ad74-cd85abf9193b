<template>
  <n-modal
    v-model:show="showModal"
    preset="card"
    title="启动MCP服务"
    size="large"
    :mask-closable="false"
    :closable="true"
    class="mcp-register-modal"
  >
    <div class="register-content">
      <!-- 注册方式选择 -->
      <div class="register-mode">
        <n-radio-group v-model:value="registerMode" @update:value="onModeChange">
          <n-radio-button value="command">
            <n-icon style="margin-right: 6px;">
              <TerminalOutline />
            </n-icon>
            命令启动
          </n-radio-button>
          <n-radio-button value="manual">
            <n-icon style="margin-right: 6px;">
              <SettingsOutline />
            </n-icon>
            手动配置
          </n-radio-button>
        </n-radio-group>
      </div>

      <!-- 命令方式注册 -->
      <div v-if="registerMode === 'command'" class="command-register">
        <McpCommandBuilder
          ref="commandBuilderRef"
          @command-change="onCommandChange"
          @template-select="onTemplateSelect"
        />
      </div>

      <!-- 手动配置方式 -->
      <div v-else class="manual-register">
        <n-alert type="info" style="margin-bottom: 16px;">
          手动配置适用于已经运行的远程MCP服务，或需要自定义配置的服务
        </n-alert>
        
        <n-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-placement="top"
          size="medium"
        >
          <n-grid :cols="2" :x-gap="16">
            <n-grid-item>
              <n-form-item label="服务名称" path="serviceName">
                <n-input
                  v-model:value="formData.serviceName"
                  placeholder="请输入服务名称"
                />
              </n-form-item>
            </n-grid-item>
            
            <n-grid-item>
              <n-form-item label="显示名称" path="displayName">
                <n-input
                  v-model:value="formData.displayName"
                  placeholder="请输入显示名称"
                />
              </n-form-item>
            </n-grid-item>
          </n-grid>
          
          <n-form-item label="服务描述" path="description">
            <n-input
              v-model:value="formData.description"
              type="textarea"
              placeholder="请输入服务描述"
              :rows="2"
            />
          </n-form-item>
          
          <n-grid :cols="2" :x-gap="16">
            <n-grid-item>
              <n-form-item label="服务类型" path="serviceType">
                <n-select
                  v-model:value="formData.serviceType"
                  :options="serviceTypeOptions"
                  placeholder="请选择服务类型"
                />
              </n-form-item>
            </n-grid-item>
            
            <n-grid-item>
              <n-form-item label="协议类型" path="protocolType">
                <n-select
                  v-model:value="formData.protocolType"
                  :options="protocolTypeOptions"
                  placeholder="请选择协议类型"
                />
              </n-form-item>
            </n-grid-item>
          </n-grid>
          
          <n-form-item label="服务端点" path="endpoint">
            <n-input
              v-model:value="formData.endpoint"
              placeholder="请输入服务端点URL，例如：http://localhost:8080"
            />
          </n-form-item>

          <!-- 高级配置 -->
          <n-collapse>
            <n-collapse-item title="高级配置" name="advanced">
              <n-grid :cols="2" :x-gap="16">
                <n-grid-item>
                  <n-form-item label="优先级">
                    <n-input-number
                      v-model:value="formData.priority"
                      :min="0"
                      :max="100"
                      placeholder="0-100"
                    />
                  </n-form-item>
                </n-grid-item>
                
                <n-grid-item>
                  <n-form-item label="权重">
                    <n-input-number
                      v-model:value="formData.weight"
                      :min="1"
                      :max="1000"
                      placeholder="1-1000"
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>

              <n-grid :cols="2" :x-gap="16">
                <n-grid-item>
                  <n-form-item label="最大并发请求">
                    <n-input-number
                      v-model:value="formData.maxConcurrentRequests"
                      :min="1"
                      :max="100"
                      placeholder="1-100"
                    />
                  </n-form-item>
                </n-grid-item>
                
                <n-grid-item>
                  <n-form-item label="请求超时(ms)">
                    <n-input-number
                      v-model:value="formData.requestTimeout"
                      :min="1000"
                      :max="300000"
                      :step="1000"
                      placeholder="1000-300000"
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>

              <n-form-item label="启用缓存">
                <n-switch v-model:value="formData.cacheEnabled" />
              </n-form-item>

              <n-form-item v-if="formData.cacheEnabled" label="缓存TTL(秒)">
                <n-input-number
                  v-model:value="formData.cacheTtl"
                  :min="60"
                  :max="3600"
                  :step="60"
                  placeholder="60-3600"
                />
              </n-form-item>
            </n-collapse-item>
          </n-collapse>
        </n-form>
      </div>
    </div>
    
    <template #action>
      <n-space justify="end">
        <n-button @click="handleCancel">取消</n-button>
        <n-button 
          v-if="registerMode === 'command'" 
          @click="validateAndPreview" 
          :loading="validating"
        >
          验证命令
        </n-button>
        <n-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ registerMode === 'command' ? '启动服务' : '注册服务' }}
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import { useMessage } from 'naive-ui'
import {
  TerminalOutline,
  SettingsOutline
} from '@vicons/ionicons5'
import { mcpApi } from '@/api/mcp'
import McpCommandBuilder from './McpCommandBuilder.vue'
import type { FormInst, FormRules } from 'naive-ui'

interface Props {
  show: boolean
}

interface Emits {
  'update:show': [value: boolean]
  success: []
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const message = useMessage()

// 响应式数据
const registerMode = ref<'command' | 'manual'>('command')
const commandBuilderRef = ref<InstanceType<typeof McpCommandBuilder> | null>(null)
const formRef = ref<FormInst | null>(null)
const submitting = ref(false)
const validating = ref(false)

const currentCommand = ref('')
const currentServiceName = ref('')

const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 手动配置表单数据
const formData = reactive({
  serviceName: '',
  displayName: '',
  description: '',
  serviceType: 'WEB_API',
  protocolType: 'HTTP',
  endpoint: '',
  priority: 0,
  weight: 100,
  maxConcurrentRequests: 10,
  requestTimeout: 30000,
  cacheEnabled: true,
  cacheTtl: 300
})

// 选项数据
const serviceTypeOptions = [
  { label: 'Web API服务', value: 'WEB_API' },
  { label: 'gRPC服务', value: 'GRPC_SERVICE' },
  { label: 'WebSocket服务', value: 'WEBSOCKET_SERVICE' },
  { label: '数据库服务', value: 'DATABASE_SERVICE' },
  { label: '文件服务', value: 'FILE_SERVICE' },
  { label: '搜索服务', value: 'SEARCH_SERVICE' },
  { label: 'AI服务', value: 'AI_SERVICE' },
  { label: '自定义服务', value: 'CUSTOM' }
]

const protocolTypeOptions = [
  { label: 'HTTP协议', value: 'HTTP' },
  { label: 'HTTPS协议', value: 'HTTPS' },
  { label: 'gRPC协议', value: 'GRPC' },
  { label: 'WebSocket协议', value: 'WEBSOCKET' },
  { label: 'TCP协议', value: 'TCP' },
  { label: 'UDP协议', value: 'UDP' }
]

// 表单验证规则
const formRules: FormRules = {
  serviceName: [
    { required: true, message: '请输入服务名称', trigger: 'blur' },
    { min: 2, max: 50, message: '服务名称长度应在2-50个字符之间', trigger: 'blur' }
  ],
  displayName: [
    { required: true, message: '请输入显示名称', trigger: 'blur' }
  ],
  endpoint: [
    { required: true, message: '请输入服务端点', trigger: 'blur' },
    { pattern: /^https?:\/\/.+/, message: '请输入有效的HTTP/HTTPS URL', trigger: 'blur' }
  ]
}

// 事件处理
const onModeChange = (mode: 'command' | 'manual') => {
  registerMode.value = mode
}

const onCommandChange = (command: string, serviceName?: string) => {
  currentCommand.value = command
  currentServiceName.value = serviceName || ''
}

const onTemplateSelect = (template: any) => {
  // 模板选择处理
  console.log('Selected template:', template)
}

const validateAndPreview = async () => {
  if (!commandBuilderRef.value) return
  
  validating.value = true
  try {
    await commandBuilderRef.value.validateCommand()
    await commandBuilderRef.value.previewCommand()
  } finally {
    validating.value = false
  }
}

const handleSubmit = async () => {
  submitting.value = true
  
  try {
    if (registerMode.value === 'command') {
      // 命令方式启动
      if (!currentCommand.value.trim()) {
        message.warning('请输入或选择启动命令')
        return
      }

      const request = {
        command: currentCommand.value,
        serviceName: currentServiceName.value || undefined
      }

      const result = await mcpApi.commands.startByCommand(request)
      
      if (result.data.success) {
        message.success(`服务启动成功: ${result.data.serviceName}`)
        emit('success')
        showModal.value = false
        resetForm()
      } else {
        message.error(`服务启动失败: ${result.data.message}`)
      }
    } else {
      // 手动配置方式
      if (!formRef.value) return
      
      await formRef.value.validate()
      
      const serviceData = {
        serviceName: formData.serviceName,
        displayName: formData.displayName,
        description: formData.description,
        serviceType: formData.serviceType,
        endpoint: formData.endpoint,
        protocolType: formData.protocolType,
        priority: formData.priority,
        weight: formData.weight,
        maxConcurrentRequests: formData.maxConcurrentRequests,
        requestTimeout: formData.requestTimeout,
        cacheEnabled: formData.cacheEnabled,
        cacheTtl: formData.cacheTtl
      }

      await mcpApi.remote.registerService(serviceData)
      message.success('服务注册成功')
      emit('success')
      showModal.value = false
      resetForm()
    }
  } catch (error: any) {
    message.error(error.message || '操作失败')
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  showModal.value = false
  resetForm()
}

const resetForm = () => {
  // 重置命令构建器
  if (commandBuilderRef.value) {
    commandBuilderRef.value.reset()
  }
  
  // 重置表单数据
  Object.assign(formData, {
    serviceName: '',
    displayName: '',
    description: '',
    serviceType: 'WEB_API',
    protocolType: 'HTTP',
    endpoint: '',
    priority: 0,
    weight: 100,
    maxConcurrentRequests: 10,
    requestTimeout: 30000,
    cacheEnabled: true,
    cacheTtl: 300
  })
  
  currentCommand.value = ''
  currentServiceName.value = ''
}
</script>

<style scoped>
.mcp-register-modal {
  max-width: 900px;
}

.register-content {
  padding: 0;
}

.register-mode {
  margin-bottom: 24px;
  text-align: center;
}

.command-register {
  min-height: 400px;
}

.manual-register {
  min-height: 300px;
}

:deep(.n-form-item-label) {
  font-weight: 600;
}

:deep(.n-collapse-item__header) {
  font-weight: 600;
}
</style>
