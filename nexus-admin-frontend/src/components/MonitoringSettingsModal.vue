<template>
  <n-modal
    v-model:show="showModal"
    preset="card"
    title="监控设置"
    size="large"
    :mask-closable="false"
    :closable="true"
    class="settings-modal"
  >
    <n-tabs type="line" animated>
      <!-- 刷新设置 -->
      <n-tab-pane name="refresh" tab="刷新设置">
        <n-form
          :model="settings.refresh"
          label-placement="left"
          label-width="120px"
        >
          <n-form-item label="自动刷新">
            <n-switch v-model:value="settings.refresh.enabled" />
          </n-form-item>
          
          <n-form-item label="刷新间隔" v-if="settings.refresh.enabled">
            <n-input-number
              v-model:value="settings.refresh.interval"
              :min="1"
              :max="300"
              style="width: 200px;"
            >
              <template #suffix>秒</template>
            </n-input-number>
          </n-form-item>
          
          <n-form-item label="图表刷新">
            <n-switch v-model:value="settings.refresh.charts" />
          </n-form-item>
          
          <n-form-item label="指标刷新">
            <n-switch v-model:value="settings.refresh.metrics" />
          </n-form-item>
          
          <n-form-item label="日志刷新">
            <n-switch v-model:value="settings.refresh.logs" />
          </n-form-item>
        </n-form>
      </n-tab-pane>

      <!-- 告警设置 -->
      <n-tab-pane name="alerts" tab="告警设置">
        <div class="alerts-section">
          <div class="section-header">
            <h4>系统指标告警</h4>
            <n-button size="small" @click="addAlert">
              <template #icon>
                <n-icon>
                  <AddOutline />
                </n-icon>
              </template>
              添加告警
            </n-button>
          </div>
          
          <div class="alerts-list">
            <div
              v-for="(alert, index) in settings.alerts"
              :key="index"
              class="alert-item"
            >
              <div class="alert-config">
                <n-grid :cols="4" :x-gap="12">
                  <n-grid-item>
                    <n-select
                      v-model:value="alert.metric"
                      :options="metricOptions"
                      placeholder="选择指标"
                    />
                  </n-grid-item>
                  
                  <n-grid-item>
                    <n-select
                      v-model:value="alert.condition"
                      :options="conditionOptions"
                      placeholder="条件"
                    />
                  </n-grid-item>
                  
                  <n-grid-item>
                    <n-input-number
                      v-model:value="alert.threshold"
                      placeholder="阈值"
                      :min="0"
                      :max="100"
                    />
                  </n-grid-item>
                  
                  <n-grid-item>
                    <n-space>
                      <n-switch v-model:value="alert.enabled" size="small" />
                      <n-button
                        quaternary
                        type="error"
                        size="small"
                        @click="removeAlert(index)"
                      >
                        <n-icon>
                          <TrashOutline />
                        </n-icon>
                      </n-button>
                    </n-space>
                  </n-grid-item>
                </n-grid>
              </div>
              
              <div class="alert-actions">
                <n-checkbox-group v-model:value="alert.actions">
                  <n-space>
                    <n-checkbox value="notification" label="桌面通知" />
                    <n-checkbox value="email" label="邮件通知" />
                    <n-checkbox value="webhook" label="Webhook" />
                  </n-space>
                </n-checkbox-group>
              </div>
            </div>
          </div>
        </div>
      </n-tab-pane>

      <!-- 显示设置 -->
      <n-tab-pane name="display" tab="显示设置">
        <n-form
          :model="settings.display"
          label-placement="left"
          label-width="120px"
        >
          <n-form-item label="主题模式">
            <n-radio-group v-model:value="settings.display.theme">
              <n-space>
                <n-radio value="light">亮色</n-radio>
                <n-radio value="dark">暗色</n-radio>
                <n-radio value="auto">自动</n-radio>
              </n-space>
            </n-radio-group>
          </n-form-item>
          
          <n-form-item label="图表动画">
            <n-switch v-model:value="settings.display.animations" />
          </n-form-item>
          
          <n-form-item label="数据密度">
            <n-radio-group v-model:value="settings.display.density">
              <n-space>
                <n-radio value="compact">紧凑</n-radio>
                <n-radio value="normal">正常</n-radio>
                <n-radio value="comfortable">舒适</n-radio>
              </n-space>
            </n-radio-group>
          </n-form-item>
          
          <n-form-item label="显示网格">
            <n-switch v-model:value="settings.display.showGrid" />
          </n-form-item>
          
          <n-form-item label="显示图例">
            <n-switch v-model:value="settings.display.showLegend" />
          </n-form-item>
          
          <n-form-item label="数据点数量">
            <n-input-number
              v-model:value="settings.display.dataPoints"
              :min="10"
              :max="200"
              style="width: 200px;"
            />
          </n-form-item>
        </n-form>
      </n-tab-pane>

      <!-- 数据设置 -->
      <n-tab-pane name="data" tab="数据设置">
        <n-form
          :model="settings.data"
          label-placement="left"
          label-width="120px"
        >
          <n-form-item label="数据保留">
            <n-select
              v-model:value="settings.data.retention"
              :options="retentionOptions"
              style="width: 200px;"
            />
          </n-form-item>
          
          <n-form-item label="采样间隔">
            <n-select
              v-model:value="settings.data.sampling"
              :options="samplingOptions"
              style="width: 200px;"
            />
          </n-form-item>
          
          <n-form-item label="数据压缩">
            <n-switch v-model:value="settings.data.compression" />
          </n-form-item>
          
          <n-form-item label="离线缓存">
            <n-switch v-model:value="settings.data.offlineCache" />
          </n-form-item>
          
          <n-form-item label="导出格式">
            <n-checkbox-group v-model:value="settings.data.exportFormats">
              <n-space>
                <n-checkbox value="csv" label="CSV" />
                <n-checkbox value="json" label="JSON" />
                <n-checkbox value="excel" label="Excel" />
              </n-space>
            </n-checkbox-group>
          </n-form-item>
        </n-form>
      </n-tab-pane>

      <!-- 通知设置 -->
      <n-tab-pane name="notifications" tab="通知设置">
        <n-form
          :model="settings.notifications"
          label-placement="left"
          label-width="120px"
        >
          <n-form-item label="桌面通知">
            <n-switch v-model:value="settings.notifications.desktop" />
          </n-form-item>
          
          <n-form-item label="声音提醒">
            <n-switch v-model:value="settings.notifications.sound" />
          </n-form-item>
          
          <n-form-item label="邮件通知">
            <n-switch v-model:value="settings.notifications.email" />
          </n-form-item>
          
          <n-form-item label="邮件地址" v-if="settings.notifications.email">
            <n-input
              v-model:value="settings.notifications.emailAddress"
              placeholder="输入邮件地址"
              style="width: 300px;"
            />
          </n-form-item>
          
          <n-form-item label="Webhook URL">
            <n-input
              v-model:value="settings.notifications.webhookUrl"
              placeholder="输入Webhook URL"
              style="width: 300px;"
            />
          </n-form-item>
          
          <n-form-item label="通知频率">
            <n-select
              v-model:value="settings.notifications.frequency"
              :options="frequencyOptions"
              style="width: 200px;"
            />
          </n-form-item>
        </n-form>
      </n-tab-pane>
    </n-tabs>

    <template #action>
      <n-space justify="end">
        <n-button @click="resetSettings">重置</n-button>
        <n-button @click="showModal = false">取消</n-button>
        <n-button type="primary" @click="saveSettings">保存设置</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useMessage } from 'naive-ui'
import { AddOutline, TrashOutline } from '@vicons/ionicons5'

const props = defineProps<{
  show: boolean
}>()

const emit = defineEmits<{
  'update:show': [value: boolean]
  update: [settings: any]
}>()

const message = useMessage()

const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 默认设置
const defaultSettings = {
  refresh: {
    enabled: true,
    interval: 30,
    charts: true,
    metrics: true,
    logs: true
  },
  alerts: [
    {
      metric: 'cpu',
      condition: 'greater',
      threshold: 80,
      enabled: true,
      actions: ['notification']
    }
  ],
  display: {
    theme: 'auto',
    animations: true,
    density: 'normal',
    showGrid: true,
    showLegend: true,
    dataPoints: 50
  },
  data: {
    retention: '7d',
    sampling: '1m',
    compression: true,
    offlineCache: true,
    exportFormats: ['csv', 'json']
  },
  notifications: {
    desktop: true,
    sound: false,
    email: false,
    emailAddress: '',
    webhookUrl: '',
    frequency: 'immediate'
  }
}

const settings = ref(JSON.parse(JSON.stringify(defaultSettings)))

// 选项配置
const metricOptions = [
  { label: 'CPU使用率', value: 'cpu' },
  { label: '内存使用率', value: 'memory' },
  { label: '网络流量', value: 'network' },
  { label: '磁盘使用率', value: 'disk' },
  { label: 'API响应时间', value: 'response_time' },
  { label: '错误率', value: 'error_rate' }
]

const conditionOptions = [
  { label: '大于', value: 'greater' },
  { label: '小于', value: 'less' },
  { label: '等于', value: 'equal' }
]

const retentionOptions = [
  { label: '1天', value: '1d' },
  { label: '3天', value: '3d' },
  { label: '7天', value: '7d' },
  { label: '30天', value: '30d' },
  { label: '90天', value: '90d' }
]

const samplingOptions = [
  { label: '10秒', value: '10s' },
  { label: '30秒', value: '30s' },
  { label: '1分钟', value: '1m' },
  { label: '5分钟', value: '5m' },
  { label: '15分钟', value: '15m' }
]

const frequencyOptions = [
  { label: '立即', value: 'immediate' },
  { label: '每5分钟', value: '5m' },
  { label: '每15分钟', value: '15m' },
  { label: '每小时', value: '1h' },
  { label: '每天', value: '1d' }
]

// 方法
const addAlert = () => {
  settings.value.alerts.push({
    metric: '',
    condition: 'greater',
    threshold: 80,
    enabled: true,
    actions: ['notification']
  })
}

const removeAlert = (index: number) => {
  settings.value.alerts.splice(index, 1)
}

const resetSettings = () => {
  settings.value = JSON.parse(JSON.stringify(defaultSettings))
  message.info('设置已重置')
}

const saveSettings = () => {
  // 保存到本地存储
  localStorage.setItem('nexus_monitoring_settings', JSON.stringify(settings.value))
  
  emit('update', settings.value)
  message.success('设置已保存')
  showModal.value = false
}

// 加载保存的设置
const loadSettings = () => {
  try {
    const saved = localStorage.getItem('nexus_monitoring_settings')
    if (saved) {
      settings.value = { ...defaultSettings, ...JSON.parse(saved) }
    }
  } catch (error) {
    console.error('加载监控设置失败:', error)
  }
}

// 监听模态框显示
watch(showModal, (show) => {
  if (show) {
    loadSettings()
  }
})
</script>

<style scoped>
.settings-modal {
  max-width: 800px;
}

.alerts-section {
  padding: 16px 0;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.section-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.alert-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  background: #fafafa;
}

.alert-config {
  margin-bottom: 12px;
}

.alert-actions {
  padding-top: 12px;
  border-top: 1px solid #e0e0e0;
}

:deep(.n-form-item-label) {
  font-weight: 600;
}

:deep(.n-tabs-tab) {
  font-weight: 500;
}
</style>
