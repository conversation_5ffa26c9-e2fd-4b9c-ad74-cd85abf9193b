<template>
  <div class="realtime-chart">
    <div class="chart-header">
      <h3 class="chart-title">{{ title }}</h3>
      <div class="chart-controls">
        <n-button-group size="small">
          <n-button 
            v-for="period in timePeriods" 
            :key="period.value"
            :type="selectedPeriod === period.value ? 'primary' : 'default'"
            @click="selectedPeriod = period.value"
          >
            {{ period.label }}
          </n-button>
        </n-button-group>
        
        <n-button 
          quaternary 
          circle 
          size="small" 
          :loading="isRefreshing"
          @click="refreshData"
        >
          <n-icon>
            <RefreshOutline />
          </n-icon>
        </n-button>
      </div>
    </div>

    <div class="chart-container" :style="{ height: `${height}px` }">
      <div v-if="loading" class="chart-loading">
        <n-spin size="large" />
        <p>加载数据中...</p>
      </div>
      
      <div v-else-if="error" class="chart-error">
        <n-icon size="48" color="#ff6b6b">
          <WarningOutline />
        </n-icon>
        <p>{{ error }}</p>
        <n-button size="small" @click="refreshData">重试</n-button>
      </div>
      
      <v-chart 
        v-else
        ref="chartRef"
        :option="chartOption"
        :autoresize="true"
        class="chart"
      />
    </div>

    <div v-if="showStats" class="chart-stats">
      <div class="stat-item">
        <span class="stat-label">当前值</span>
        <span class="stat-value">{{ formatValue(currentValue) }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">平均值</span>
        <span class="stat-value">{{ formatValue(averageValue) }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">最大值</span>
        <span class="stat-value">{{ formatValue(maxValue) }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">趋势</span>
        <span class="stat-value" :class="trendClass">
          <n-icon>
            <component :is="trendIcon" />
          </n-icon>
          {{ trendText }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import { RefreshOutline, WarningOutline, TrendingUpOutline, TrendingDownOutline, RemoveOutline } from '@vicons/ionicons5'

// 注册ECharts组件
use([
  CanvasRenderer,
  LineChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent
])

interface ChartDataPoint {
  timestamp: number
  value: number
  label?: string
}

interface TimePeriod {
  label: string
  value: string
  duration: number // 毫秒
}

const props = withDefaults(defineProps<{
  title: string
  data: ChartDataPoint[]
  type?: 'line' | 'bar'
  height?: number
  showStats?: boolean
  color?: string
  loading?: boolean
  error?: string
  autoRefresh?: boolean
  refreshInterval?: number
}>(), {
  type: 'line',
  height: 300,
  showStats: true,
  color: '#667eea',
  autoRefresh: true,
  refreshInterval: 30000 // 30秒
})

const emit = defineEmits<{
  refresh: []
}>()

// 响应式数据
const chartRef = ref()
const selectedPeriod = ref('1h')
const isRefreshing = ref(false)

// 时间周期选项
const timePeriods: TimePeriod[] = [
  { label: '15分钟', value: '15m', duration: 15 * 60 * 1000 },
  { label: '1小时', value: '1h', duration: 60 * 60 * 1000 },
  { label: '6小时', value: '6h', duration: 6 * 60 * 60 * 1000 },
  { label: '24小时', value: '24h', duration: 24 * 60 * 60 * 1000 }
]

// 过滤数据
const filteredData = computed(() => {
  const period = timePeriods.find(p => p.value === selectedPeriod.value)
  if (!period) return props.data

  const cutoffTime = Date.now() - period.duration
  return props.data.filter(point => point.timestamp >= cutoffTime)
})

// 图表配置
const chartOption = computed(() => {
  const data = filteredData.value
  
  return {
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: 'transparent',
      textStyle: {
        color: '#fff'
      },
      formatter: (params: any) => {
        const point = params[0]
        const time = new Date(point.axisValue).toLocaleTimeString()
        return `${time}<br/>${point.seriesName}: ${formatValue(point.value)}`
      }
    },
    xAxis: {
      type: 'category',
      data: data.map(point => point.timestamp),
      axisLabel: {
        formatter: (value: number) => {
          return new Date(value).toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
          })
        }
      },
      axisLine: {
        lineStyle: {
          color: '#e0e0e0'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#e0e0e0'
        }
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0'
        }
      }
    },
    series: [
      {
        name: props.title,
        type: props.type,
        data: data.map(point => point.value),
        smooth: props.type === 'line',
        lineStyle: {
          color: props.color,
          width: 2
        },
        itemStyle: {
          color: props.color
        },
        areaStyle: props.type === 'line' ? {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: props.color + '40' },
              { offset: 1, color: props.color + '10' }
            ]
          }
        } : undefined
      }
    ]
  }
})

// 统计数据
const currentValue = computed(() => {
  const data = filteredData.value
  return data.length > 0 ? data[data.length - 1].value : 0
})

const averageValue = computed(() => {
  const data = filteredData.value
  if (data.length === 0) return 0
  const sum = data.reduce((acc, point) => acc + point.value, 0)
  return sum / data.length
})

const maxValue = computed(() => {
  const data = filteredData.value
  return data.length > 0 ? Math.max(...data.map(point => point.value)) : 0
})

// 趋势计算
const trend = computed(() => {
  const data = filteredData.value
  if (data.length < 2) return 0
  
  const recent = data.slice(-5) // 最近5个数据点
  if (recent.length < 2) return 0
  
  const firstValue = recent[0].value
  const lastValue = recent[recent.length - 1].value
  
  return ((lastValue - firstValue) / firstValue) * 100
})

const trendIcon = computed(() => {
  if (trend.value > 5) return TrendingUpOutline
  if (trend.value < -5) return TrendingDownOutline
  return RemoveOutline
})

const trendClass = computed(() => {
  if (trend.value > 5) return 'trend-up'
  if (trend.value < -5) return 'trend-down'
  return 'trend-stable'
})

const trendText = computed(() => {
  const abs = Math.abs(trend.value)
  if (abs < 1) return '稳定'
  return `${abs.toFixed(1)}%`
})

// 格式化数值
const formatValue = (value: number) => {
  if (value >= 1000000) {
    return (value / 1000000).toFixed(1) + 'M'
  }
  if (value >= 1000) {
    return (value / 1000).toFixed(1) + 'K'
  }
  return value.toFixed(0)
}

// 刷新数据
const refreshData = async () => {
  isRefreshing.value = true
  emit('refresh')
  
  // 模拟刷新延迟
  setTimeout(() => {
    isRefreshing.value = false
  }, 1000)
}

// 自动刷新
let refreshTimer: NodeJS.Timeout | null = null

const startAutoRefresh = () => {
  if (!props.autoRefresh) return
  
  refreshTimer = setInterval(() => {
    refreshData()
  }, props.refreshInterval)
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 生命周期
onMounted(() => {
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})

// 监听自动刷新设置变化
watch(() => props.autoRefresh, (newValue) => {
  if (newValue) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
})
</script>

<style scoped>
.realtime-chart {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.chart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.chart-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chart-container {
  position: relative;
  width: 100%;
}

.chart-loading,
.chart-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #666;
}

.chart-loading p,
.chart-error p {
  margin: 12px 0;
}

.chart {
  width: 100%;
  height: 100%;
}

.chart-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.stat-value {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.trend-up {
  color: #52c41a;
}

.trend-down {
  color: #ff4d4f;
}

.trend-stable {
  color: #666;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .chart-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
