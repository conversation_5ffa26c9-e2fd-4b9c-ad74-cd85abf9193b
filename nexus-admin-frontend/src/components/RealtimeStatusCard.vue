<template>
  <div class="status-card glass-card" :class="statusClass">
    <div class="card-header">
      <div class="card-icon">
        <n-icon :size="iconSize" :color="iconColor">
          <component :is="icon" />
        </n-icon>
      </div>
      
      <div class="card-actions">
        <n-button 
          v-if="showRefresh"
          quaternary 
          circle 
          size="small"
          :loading="refreshing"
          @click="handleRefresh"
        >
          <n-icon>
            <RefreshOutline />
          </n-icon>
        </n-button>
        
        <n-dropdown 
          v-if="actions.length > 0"
          :options="actions"
          @select="handleActionSelect"
        >
          <n-button quaternary circle size="small">
            <n-icon>
              <EllipsisVerticalOutline />
            </n-icon>
          </n-button>
        </n-dropdown>
      </div>
    </div>

    <div class="card-content">
      <div class="main-value">
        <span class="value-number">{{ formattedValue }}</span>
        <span v-if="unit" class="value-unit">{{ unit }}</span>
      </div>
      
      <div class="card-title">{{ title }}</div>
      
      <div v-if="description" class="card-description">
        {{ description }}
      </div>
      
      <div v-if="showTrend" class="trend-indicator">
        <n-icon :size="16" :color="trendColor">
          <component :is="trendIcon" />
        </n-icon>
        <span class="trend-text" :style="{ color: trendColor }">
          {{ trendText }}
        </span>
      </div>
    </div>

    <div v-if="showProgress" class="progress-section">
      <n-progress 
        :percentage="progressPercentage"
        :color="progressColor"
        :show-indicator="false"
        :height="4"
      />
      <div class="progress-text">
        {{ progressText }}
      </div>
    </div>

    <div v-if="showChart" class="mini-chart">
      <v-chart 
        :option="miniChartOption"
        :autoresize="true"
        style="height: 60px;"
      />
    </div>

    <div v-if="tags.length > 0" class="card-tags">
      <n-tag 
        v-for="tag in tags" 
        :key="tag.label"
        :type="tag.type || 'default'"
        size="small"
      >
        {{ tag.label }}
      </n-tag>
    </div>

    <!-- 状态指示器 -->
    <div class="status-indicator" :class="statusIndicatorClass">
      <div class="status-dot"></div>
    </div>

    <!-- 加载遮罩 -->
    <div v-if="loading" class="loading-overlay">
      <n-spin size="small" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart } from 'echarts/charts'
import { GridComponent } from 'echarts/components'
import VChart from 'vue-echarts'
import {
  RefreshOutline,
  EllipsisVerticalOutline,
  TrendingUpOutline,
  TrendingDownOutline,
  RemoveOutline
} from '@vicons/ionicons5'

// 注册ECharts组件
use([CanvasRenderer, LineChart, GridComponent])

interface Tag {
  label: string
  type?: 'default' | 'primary' | 'info' | 'success' | 'warning' | 'error'
}

interface Action {
  label: string
  key: string
  icon?: any
}

interface ChartDataPoint {
  timestamp: number
  value: number
}

const props = withDefaults(defineProps<{
  title: string
  value: number | string
  unit?: string
  description?: string
  icon: any
  iconSize?: number
  iconColor?: string
  status?: 'success' | 'warning' | 'error' | 'info' | 'default'
  loading?: boolean
  refreshing?: boolean
  showRefresh?: boolean
  showTrend?: boolean
  trend?: number
  showProgress?: boolean
  progress?: number
  progressMax?: number
  progressText?: string
  showChart?: boolean
  chartData?: ChartDataPoint[]
  chartColor?: string
  tags?: Tag[]
  actions?: Action[]
}>(), {
  iconSize: 24,
  iconColor: '#667eea',
  status: 'default',
  loading: false,
  refreshing: false,
  showRefresh: false,
  showTrend: false,
  trend: 0,
  showProgress: false,
  progress: 0,
  progressMax: 100,
  progressText: '',
  showChart: false,
  chartData: () => [],
  chartColor: '#667eea',
  tags: () => [],
  actions: () => []
})

const emit = defineEmits<{
  refresh: []
  action: [key: string]
}>()

// 计算属性
const formattedValue = computed(() => {
  if (typeof props.value === 'string') return props.value
  
  const num = props.value
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toLocaleString()
})

const statusClass = computed(() => ({
  [`status-${props.status}`]: true,
  'loading': props.loading
}))

const statusIndicatorClass = computed(() => ({
  [`indicator-${props.status}`]: true
}))

const trendIcon = computed(() => {
  if (props.trend > 0) return TrendingUpOutline
  if (props.trend < 0) return TrendingDownOutline
  return RemoveOutline
})

const trendColor = computed(() => {
  if (props.trend > 0) return '#52c41a'
  if (props.trend < 0) return '#ff4d4f'
  return '#666'
})

const trendText = computed(() => {
  const abs = Math.abs(props.trend)
  if (abs < 0.1) return '稳定'
  const sign = props.trend > 0 ? '+' : ''
  return `${sign}${props.trend.toFixed(1)}%`
})

const progressPercentage = computed(() => {
  return Math.min((props.progress / props.progressMax) * 100, 100)
})

const progressColor = computed(() => {
  const percentage = progressPercentage.value
  if (percentage >= 90) return '#ff4d4f'
  if (percentage >= 70) return '#faad14'
  return '#52c41a'
})

const miniChartOption = computed(() => {
  if (!props.showChart || props.chartData.length === 0) return {}
  
  return {
    grid: {
      left: 0,
      right: 0,
      top: 0,
      bottom: 0
    },
    xAxis: {
      type: 'category',
      data: props.chartData.map(point => point.timestamp),
      show: false
    },
    yAxis: {
      type: 'value',
      show: false
    },
    series: [
      {
        type: 'line',
        data: props.chartData.map(point => point.value),
        smooth: true,
        symbol: 'none',
        lineStyle: {
          color: props.chartColor,
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: props.chartColor + '40' },
              { offset: 1, color: props.chartColor + '10' }
            ]
          }
        }
      }
    ]
  }
})

// 事件处理
const handleRefresh = () => {
  emit('refresh')
}

const handleActionSelect = (key: string) => {
  emit('action', key)
}
</script>

<style scoped>
.status-card {
  position: relative;
  padding: var(--space-6);
  border-radius: var(--radius-2xl);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  transition: all var(--duration-normal) var(--ease-out);
  overflow: hidden;
}

.status-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  opacity: 0.6;
}

.status-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl), var(--shadow-glow);
  border-color: var(--primary-color-light);
}

.status-card.loading {
  pointer-events: none;
  opacity: 0.7;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
  position: relative;
  z-index: 1;
}

.card-icon {
  padding: var(--space-2);
  border-radius: var(--radius-lg);
  background: var(--primary-color-light);
  backdrop-filter: blur(8px);
  border: 1px solid var(--primary-color-light);
  transition: all var(--duration-normal) var(--ease-out);
  position: relative;
}

.card-icon::before {
  content: '';
  position: absolute;
  inset: 0;
  background: var(--gradient-primary);
  opacity: 0.1;
  border-radius: inherit;
  z-index: -1;
}

.card-actions {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.card-content {
  margin-bottom: var(--space-4);
  position: relative;
  z-index: 1;
}

.main-value {
  display: flex;
  align-items: baseline;
  gap: var(--space-1);
  margin-bottom: var(--space-2);
}

.value-number {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.value-unit {
  font-size: 14px;
  color: var(--text-tertiary);
  font-weight: 500;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.card-description {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.4;
}

.trend-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 8px;
}

.trend-text {
  font-size: 14px;
  font-weight: 500;
}

.progress-section {
  margin-bottom: 16px;
}

.progress-text {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  text-align: right;
}

.mini-chart {
  margin-bottom: 16px;
}

.card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.status-indicator {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 8px;
  height: 8px;
}

.status-dot {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.indicator-success .status-dot {
  background: var(--success-color);
}

.indicator-warning .status-dot {
  background: var(--warning-color);
}

.indicator-error .status-dot {
  background: var(--error-color);
}

.indicator-info .status-dot {
  background: var(--info-color);
}

.indicator-default .status-dot {
  background: var(--text-quaternary);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 currentColor;
    opacity: 1;
  }
  70% {
    box-shadow: 0 0 0 4px transparent;
    opacity: 0;
  }
  100% {
    box-shadow: 0 0 0 0 transparent;
    opacity: 0;
  }
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-overlay);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-2xl);
}

/* 状态样式 */
.status-success {
  border-left: 4px solid var(--success-color);
}

.status-success::before {
  background: linear-gradient(90deg, transparent, var(--success-color), transparent);
}

.status-warning {
  border-left: 4px solid var(--warning-color);
}

.status-warning::before {
  background: linear-gradient(90deg, transparent, var(--warning-color), transparent);
}

.status-error {
  border-left: 4px solid var(--error-color);
}

.status-error::before {
  background: linear-gradient(90deg, transparent, var(--error-color), transparent);
}

.status-info {
  border-left: 4px solid var(--info-color);
}

.status-info::before {
  background: linear-gradient(90deg, transparent, var(--info-color), transparent);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .status-card {
    padding: 16px;
  }
  
  .value-number {
    font-size: 24px;
  }
  
  .card-title {
    font-size: 14px;
  }
  
  .card-description {
    font-size: 12px;
  }
}
</style>
