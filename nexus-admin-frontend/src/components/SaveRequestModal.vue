<template>
  <n-modal
    v-model:show="showModal"
    preset="card"
    title="保存请求"
    size="medium"
    :mask-closable="false"
    :closable="true"
    class="save-request-modal"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-placement="top"
      size="medium"
    >
      <n-form-item label="请求名称" path="name">
        <n-input
          v-model:value="formData.name"
          placeholder="输入请求名称"
          clearable
        />
      </n-form-item>

      <n-form-item label="请求描述" path="description">
        <n-input
          v-model:value="formData.description"
          type="textarea"
          placeholder="输入请求描述（可选）"
          :rows="3"
          maxlength="300"
          show-count
        />
      </n-form-item>

      <n-form-item label="保存到集合" path="collectionId">
        <div class="collection-selection">
          <n-select
            v-model:value="formData.collectionId"
            :options="collectionOptions"
            placeholder="选择集合"
            filterable
          />
          
          <n-button @click="showCreateCollection = true">
            <template #icon>
              <n-icon>
                <AddOutline />
              </n-icon>
            </template>
            新建集合
          </n-button>
        </div>
      </n-form-item>

      <n-form-item label="请求预览">
        <div class="request-preview">
          <div class="preview-header">
            <n-tag :type="getMethodType(request.method)">
              {{ request.method }}
            </n-tag>
            <span class="preview-url">{{ request.url || '未设置URL' }}</span>
          </div>
          
          <div class="preview-details">
            <n-collapse>
              <n-collapse-item title="请求详情" name="details">
                <div class="detail-section">
                  <h5>参数 ({{ enabledParams.length }})</h5>
                  <div v-if="enabledParams.length === 0" class="empty-hint">
                    无参数
                  </div>
                  <div v-else class="params-list">
                    <div
                      v-for="param in enabledParams"
                      :key="param.key"
                      class="param-row"
                    >
                      <span class="param-key">{{ param.key }}</span>
                      <span class="param-value">{{ param.value }}</span>
                    </div>
                  </div>
                </div>
                
                <div class="detail-section">
                  <h5>请求头 ({{ enabledHeaders.length }})</h5>
                  <div v-if="enabledHeaders.length === 0" class="empty-hint">
                    无自定义请求头
                  </div>
                  <div v-else class="headers-list">
                    <div
                      v-for="header in enabledHeaders"
                      :key="header.key"
                      class="header-row"
                    >
                      <span class="header-key">{{ header.key }}</span>
                      <span class="header-value">{{ header.value }}</span>
                    </div>
                  </div>
                </div>
                
                <div v-if="request.bodyType !== 'none'" class="detail-section">
                  <h5>请求体</h5>
                  <div class="body-preview">
                    <n-tag size="small">{{ getBodyTypeLabel(request.bodyType) }}</n-tag>
                    <div class="body-content">
                      <pre v-if="request.bodyType === 'json' || request.bodyType === 'raw'">{{ request.body || '空' }}</pre>
                      <div v-else-if="request.bodyType === 'form'" class="form-data-preview">
                        <div
                          v-for="item in enabledFormData"
                          :key="item.key"
                          class="form-item-preview"
                        >
                          <span class="form-key">{{ item.key }}</span>
                          <span class="form-value">{{ item.value }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div v-if="request.auth.type" class="detail-section">
                  <h5>认证</h5>
                  <n-tag>{{ getAuthTypeLabel(request.auth.type) }}</n-tag>
                </div>
              </n-collapse-item>
            </n-collapse>
          </div>
        </div>
      </n-form-item>

      <n-form-item label="保存选项">
        <n-space direction="vertical">
          <n-checkbox v-model:checked="formData.saveAsTemplate">
            保存为模板（可复用的请求模板）
          </n-checkbox>
          
          <n-checkbox v-model:checked="formData.includeAuth">
            包含认证信息
          </n-checkbox>
          
          <n-checkbox v-model:checked="formData.autoGenerateTests">
            自动生成基础测试用例
          </n-checkbox>
        </n-space>
      </n-form-item>

      <n-form-item label="标签">
        <n-dynamic-tags
          v-model:value="formData.tags"
          :max="5"
          placeholder="添加标签"
        />
      </n-form-item>
    </n-form>

    <template #action>
      <n-space justify="end">
        <n-button @click="showModal = false">取消</n-button>
        <n-button @click="saveAsCopy">另存为副本</n-button>
        <n-button type="primary" @click="handleSubmit" :loading="submitting">
          保存请求
        </n-button>
      </n-space>
    </template>

    <!-- 创建集合对话框 -->
    <CreateCollectionModal
      v-model:show="showCreateCollection"
      @success="handleCollectionCreated"
    />
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useMessage } from 'naive-ui'
import { AddOutline } from '@vicons/ionicons5'
import CreateCollectionModal from './CreateCollectionModal.vue'
import type { FormInst, FormRules } from 'naive-ui'

const props = defineProps<{
  show: boolean
  request: any
  collections: any[]
}>()

const emit = defineEmits<{
  'update:show': [value: boolean]
  success: [data: any]
}>()

const message = useMessage()

// 响应式数据
const formRef = ref<FormInst | null>(null)
const submitting = ref(false)
const showCreateCollection = ref(false)

const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 表单数据
const formData = ref({
  name: '',
  description: '',
  collectionId: null as number | null,
  saveAsTemplate: false,
  includeAuth: true,
  autoGenerateTests: false,
  tags: [] as string[]
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入请求名称', trigger: 'blur' },
    { min: 2, max: 100, message: '请求名称长度应在2-100个字符之间', trigger: 'blur' }
  ],
  collectionId: [
    { required: true, type: 'number', message: '请选择集合', trigger: 'change' }
  ]
}

// 计算属性
const collectionOptions = computed(() => {
  return props.collections.map(collection => ({
    label: collection.name,
    value: collection.id
  }))
})

const enabledParams = computed(() => {
  return props.request.params?.filter((p: any) => p.enabled && p.key) || []
})

const enabledHeaders = computed(() => {
  return props.request.headers?.filter((h: any) => h.enabled && h.key) || []
})

const enabledFormData = computed(() => {
  return props.request.formData?.filter((f: any) => f.enabled && f.key) || []
})

// 工具函数
const getMethodType = (method: string) => {
  const typeMap: Record<string, any> = {
    GET: 'success',
    POST: 'info',
    PUT: 'warning',
    DELETE: 'error',
    PATCH: 'default'
  }
  return typeMap[method] || 'default'
}

const getBodyTypeLabel = (type: string) => {
  const labelMap: Record<string, string> = {
    json: 'JSON',
    form: '表单数据',
    raw: '原始数据',
    none: '无'
  }
  return labelMap[type] || type
}

const getAuthTypeLabel = (type: string) => {
  const labelMap: Record<string, string> = {
    bearer: 'Bearer Token',
    basic: 'Basic Auth',
    apikey: 'API Key',
    none: '无认证'
  }
  return labelMap[type] || type
}

// 生成默认请求名称
const generateDefaultName = () => {
  const method = props.request.method || 'GET'
  const url = props.request.url || ''
  
  if (url) {
    try {
      const urlObj = new URL(url)
      const path = urlObj.pathname
      const segments = path.split('/').filter(Boolean)
      const lastSegment = segments[segments.length - 1] || 'root'
      return `${method} ${lastSegment}`
    } catch {
      return `${method} 请求`
    }
  }
  
  return `${method} 请求`
}

// 事件处理
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    const requestData = {
      id: Date.now(),
      name: formData.value.name,
      description: formData.value.description,
      collectionId: formData.value.collectionId,
      method: props.request.method,
      url: props.request.url,
      params: enabledParams.value,
      headers: enabledHeaders.value,
      bodyType: props.request.bodyType,
      body: props.request.body,
      formData: enabledFormData.value,
      auth: formData.value.includeAuth ? props.request.auth : { type: null },
      tags: formData.value.tags,
      isTemplate: formData.value.saveAsTemplate,
      hasTests: formData.value.autoGenerateTests,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    // 如果自动生成测试用例
    if (formData.value.autoGenerateTests) {
      requestData.tests = generateBasicTests(requestData)
    }

    emit('success', requestData)
    showModal.value = false
    resetForm()

  } catch (error: any) {
    message.error('保存失败: ' + (error.message || '未知错误'))
  } finally {
    submitting.value = false
  }
}

const saveAsCopy = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    // 在名称后添加 "副本"
    const copyName = formData.value.name + ' 副本'
    formData.value.name = copyName
    
    await handleSubmit()
  } catch (error) {
    // 验证失败，不执行保存
  }
}

const generateBasicTests = (request: any) => {
  const tests = []
  
  // 状态码测试
  tests.push({
    name: '状态码检查',
    script: `pm.test("状态码应为 200", function () {
    pm.response.to.have.status(200);
});`
  })
  
  // 响应时间测试
  tests.push({
    name: '响应时间检查',
    script: `pm.test("响应时间应小于 2000ms", function () {
    pm.expect(pm.response.responseTime).to.be.below(2000);
});`
  })
  
  // JSON 响应测试
  if (request.headers.some((h: any) => h.key === 'Content-Type' && h.value.includes('json'))) {
    tests.push({
      name: 'JSON 格式检查',
      script: `pm.test("响应应为有效的 JSON", function () {
    pm.response.to.be.json;
});`
    })
  }
  
  return tests
}

const handleCollectionCreated = (collection: any) => {
  formData.value.collectionId = collection.id
  message.success('集合创建成功')
}

const resetForm = () => {
  formData.value = {
    name: '',
    description: '',
    collectionId: null,
    saveAsTemplate: false,
    includeAuth: true,
    autoGenerateTests: false,
    tags: []
  }
}

// 监听模态框显示
watch(showModal, (show) => {
  if (show) {
    // 自动生成默认名称
    formData.value.name = generateDefaultName()
    
    // 如果只有一个集合，自动选择
    if (props.collections.length === 1) {
      formData.value.collectionId = props.collections[0].id
    }
  } else {
    resetForm()
  }
})
</script>

<style scoped>
.save-request-modal {
  max-width: 600px;
}

.collection-selection {
  display: flex;
  gap: 12px;
  align-items: center;
}

.collection-selection .n-select {
  flex: 1;
}

.request-preview {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  background: #fafafa;
}

.preview-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.preview-url {
  font-family: monospace;
  font-size: 14px;
  color: #333;
  word-break: break-all;
}

.detail-section {
  margin-bottom: 16px;
}

.detail-section h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.empty-hint {
  color: #999;
  font-size: 12px;
  font-style: italic;
}

.params-list,
.headers-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.param-row,
.header-row {
  display: flex;
  gap: 12px;
  font-size: 12px;
  font-family: monospace;
}

.param-key,
.header-key {
  font-weight: 600;
  color: #333;
  min-width: 120px;
}

.param-value,
.header-value {
  color: #666;
  word-break: break-all;
}

.body-preview {
  margin-top: 8px;
}

.body-content {
  margin-top: 8px;
  padding: 8px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.body-content pre {
  margin: 0;
  font-size: 12px;
  font-family: monospace;
  white-space: pre-wrap;
  word-break: break-all;
}

.form-data-preview {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.form-item-preview {
  display: flex;
  gap: 12px;
  font-size: 12px;
  font-family: monospace;
}

.form-key {
  font-weight: 600;
  color: #333;
  min-width: 120px;
}

.form-value {
  color: #666;
  word-break: break-all;
}

:deep(.n-form-item-label) {
  font-weight: 600;
}
</style>
