<template>
  <n-drawer
    v-model:show="showDrawer"
    :width="800"
    placement="right"
    class="service-detail-drawer"
  >
    <n-drawer-content
      :title="service?.displayName || service?.serviceName || '服务详情'"
      closable
    >
      <div v-if="service" class="service-detail">
        <!-- 服务状态卡片 -->
        <div class="status-section">
          <RealtimeStatusCard
            :title="service.serviceName"
            :value="service.status"
            :icon="ServerOutline"
            :status="getStatusType(service.status)"
            :description="service.description"
            :show-trend="false"
            :tags="getServiceTags(service)"
          />
        </div>

        <!-- 详细信息 -->
        <n-tabs type="line" animated>
          <!-- 基本信息 -->
          <n-tab-pane name="basic" tab="基本信息">
            <div class="info-section">
              <n-descriptions :column="2" bordered>
                <n-descriptions-item label="服务ID">
                  {{ service.id }}
                </n-descriptions-item>
                <n-descriptions-item label="服务名称">
                  {{ service.serviceName }}
                </n-descriptions-item>
                <n-descriptions-item label="显示名称">
                  {{ service.displayName }}
                </n-descriptions-item>
                <n-descriptions-item label="服务类型">
                  <n-tag :type="service.serviceType === 'LOCAL' ? 'info' : 'success'">
                    {{ service.serviceType }}
                  </n-tag>
                </n-descriptions-item>
                <n-descriptions-item label="版本">
                  {{ service.version }}
                </n-descriptions-item>
                <n-descriptions-item label="状态">
                  <n-tag :type="getStatusType(service.status)">
                    {{ getStatusText(service.status) }}
                  </n-tag>
                </n-descriptions-item>
                <n-descriptions-item label="健康状态">
                  <n-tag :type="service.isHealthy ? 'success' : 'error'">
                    {{ service.isHealthy ? '健康' : '异常' }}
                  </n-tag>
                </n-descriptions-item>
                <n-descriptions-item label="可用性">
                  <n-tag :type="service.isAvailable ? 'success' : 'warning'">
                    {{ service.isAvailable ? '可用' : '不可用' }}
                  </n-tag>
                </n-descriptions-item>
                <n-descriptions-item label="端点" v-if="service.endpoint">
                  <n-text code>{{ service.endpoint }}</n-text>
                </n-descriptions-item>
                <n-descriptions-item label="工具数量">
                  {{ service.toolCount || 0 }}
                </n-descriptions-item>
                <n-descriptions-item label="资源数量">
                  {{ service.resourceCount || 0 }}
                </n-descriptions-item>
                <n-descriptions-item label="最后健康检查">
                  {{ service.lastHealthCheck ? formatTime(service.lastHealthCheck) : '未检查' }}
                </n-descriptions-item>
                <n-descriptions-item label="创建时间">
                  {{ formatTime(service.createdAt) }}
                </n-descriptions-item>
                <n-descriptions-item label="更新时间">
                  {{ formatTime(service.updatedAt) }}
                </n-descriptions-item>
                <n-descriptions-item label="描述" :span="2">
                  {{ service.description || '无描述' }}
                </n-descriptions-item>
              </n-descriptions>
            </div>
          </n-tab-pane>

          <!-- 工具列表 -->
          <n-tab-pane name="tools" tab="工具">
            <div class="tools-section">
              <div class="section-header">
                <h3>可用工具 ({{ tools.length }})</h3>
                <n-button @click="refreshTools" :loading="toolsLoading">
                  <template #icon>
                    <n-icon>
                      <RefreshOutline />
                    </n-icon>
                  </template>
                  刷新
                </n-button>
              </div>

              <div v-if="toolsLoading" class="loading-container">
                <n-spin size="large" />
                <p>加载工具列表...</p>
              </div>

              <div v-else-if="tools.length === 0" class="empty-container">
                <n-empty description="暂无可用工具" />
              </div>

              <div v-else class="tools-grid">
                <div
                  v-for="tool in tools"
                  :key="tool.name"
                  class="tool-card"
                >
                  <div class="tool-header">
                    <h4>{{ tool.name }}</h4>
                    <n-button
                      size="small"
                      @click="testTool(tool)"
                    >
                      测试
                    </n-button>
                  </div>
                  <p class="tool-description">{{ tool.description }}</p>
                  <div class="tool-schema">
                    <n-collapse>
                      <n-collapse-item title="参数结构" name="schema">
                        <pre>{{ JSON.stringify(tool.inputSchema, null, 2) }}</pre>
                      </n-collapse-item>
                    </n-collapse>
                  </div>
                </div>
              </div>
            </div>
          </n-tab-pane>

          <!-- 资源列表 -->
          <n-tab-pane name="resources" tab="资源">
            <div class="resources-section">
              <div class="section-header">
                <h3>可用资源 ({{ resources.length }})</h3>
                <n-button @click="refreshResources" :loading="resourcesLoading">
                  <template #icon>
                    <n-icon>
                      <RefreshOutline />
                    </n-icon>
                  </template>
                  刷新
                </n-button>
              </div>

              <div v-if="resourcesLoading" class="loading-container">
                <n-spin size="large" />
                <p>加载资源列表...</p>
              </div>

              <div v-else-if="resources.length === 0" class="empty-container">
                <n-empty description="暂无可用资源" />
              </div>

              <div v-else class="resources-list">
                <div
                  v-for="resource in resources"
                  :key="resource.uri"
                  class="resource-item"
                >
                  <div class="resource-info">
                    <h4>{{ resource.name }}</h4>
                    <p class="resource-uri">{{ resource.uri }}</p>
                    <p class="resource-description">{{ resource.description }}</p>
                    <div class="resource-meta">
                      <n-tag v-if="resource.mimeType" size="small">
                        {{ resource.mimeType }}
                      </n-tag>
                    </div>
                  </div>
                  <div class="resource-actions">
                    <n-button size="small" @click="accessResource(resource)">
                      访问
                    </n-button>
                  </div>
                </div>
              </div>
            </div>
          </n-tab-pane>

          <!-- 日志 -->
          <n-tab-pane name="logs" tab="日志">
            <div class="logs-section">
              <div class="section-header">
                <h3>服务日志</h3>
                <n-space>
                  <n-button @click="refreshLogs" :loading="logsLoading">
                    <template #icon>
                      <n-icon>
                        <RefreshOutline />
                      </n-icon>
                    </template>
                    刷新
                  </n-button>
                  <n-button @click="clearLogs">
                    清空日志
                  </n-button>
                </n-space>
              </div>

              <div class="logs-container">
                <n-log
                  :log="logsContent"
                  :loading="logsLoading"
                  language="text"
                  :hljs="false"
                />
              </div>
            </div>
          </n-tab-pane>
        </n-tabs>
      </div>

      <template #footer>
        <n-space justify="end">
          <n-button @click="showDrawer = false">关闭</n-button>
          <n-button
            :type="service?.status === 'RUNNING' ? 'error' : 'success'"
            @click="toggleService"
            :loading="actionLoading"
          >
            {{ service?.status === 'RUNNING' ? '停止服务' : '启动服务' }}
          </n-button>
          <n-button type="primary" @click="editService">
            编辑配置
          </n-button>
        </n-space>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useQuery } from '@tanstack/vue-query'
import { useMessage } from 'naive-ui'
import { ServerOutline, RefreshOutline } from '@vicons/ionicons5'
import { mcpApi } from '@/api/mcp'
import RealtimeStatusCard from './RealtimeStatusCard.vue'
import type { McpService, McpTool, McpResource } from '@/types/api'

const props = defineProps<{
  show: boolean
  service: McpService | null
}>()

const emit = defineEmits<{
  'update:show': [value: boolean]
  update: []
}>()

const message = useMessage()

// 响应式数据
const actionLoading = ref(false)
const toolsLoading = ref(false)
const resourcesLoading = ref(false)
const logsLoading = ref(false)
const tools = ref<McpTool[]>([])
const resources = ref<McpResource[]>([])
const logsContent = ref('')

const showDrawer = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 获取状态类型
const getStatusType = (status: string) => {
  const statusMap: Record<string, any> = {
    RUNNING: 'success',
    STOPPED: 'default',
    ERROR: 'error',
    STARTING: 'warning',
    STOPPING: 'warning'
  }
  return statusMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    RUNNING: '运行中',
    STOPPED: '已停止',
    ERROR: '错误',
    STARTING: '启动中',
    STOPPING: '停止中'
  }
  return statusMap[status] || status
}

// 获取服务标签
const getServiceTags = (service: McpService) => {
  const tags = []
  
  tags.push({
    label: service.serviceType,
    type: service.serviceType === 'LOCAL' ? 'info' : 'success'
  })
  
  if (service.version) {
    tags.push({
      label: `v${service.version}`,
      type: 'default'
    })
  }
  
  return tags
}

// 格式化时间
const formatTime = (time: string) => {
  return new Date(time).toLocaleString()
}

// 刷新工具列表
const refreshTools = async () => {
  if (!props.service) return
  
  toolsLoading.value = true
  try {
    const response = await mcpApi.unified.listTools()
    tools.value = response.data.tools.filter(
      tool => tool.serviceName === props.service?.serviceName
    )
  } catch (error: any) {
    message.error('获取工具列表失败: ' + error.message)
  } finally {
    toolsLoading.value = false
  }
}

// 刷新资源列表
const refreshResources = async () => {
  if (!props.service) return
  
  resourcesLoading.value = true
  try {
    const response = await mcpApi.unified.listResources()
    resources.value = response.data.resources.filter(
      resource => resource.serviceName === props.service?.serviceName
    )
  } catch (error: any) {
    message.error('获取资源列表失败: ' + error.message)
  } finally {
    resourcesLoading.value = false
  }
}

// 刷新日志
const refreshLogs = async () => {
  if (!props.service) return
  
  logsLoading.value = true
  try {
    // 模拟获取日志
    await new Promise(resolve => setTimeout(resolve, 1000))
    logsContent.value = `[${new Date().toISOString()}] 服务 ${props.service.serviceName} 运行正常\n` +
                       `[${new Date().toISOString()}] 健康检查通过\n` +
                       `[${new Date().toISOString()}] 处理了 5 个请求\n`
  } catch (error: any) {
    message.error('获取日志失败: ' + error.message)
  } finally {
    logsLoading.value = false
  }
}

// 清空日志
const clearLogs = () => {
  logsContent.value = ''
  message.success('日志已清空')
}

// 测试工具
const testTool = (tool: McpTool) => {
  message.info(`测试工具: ${tool.name}`)
  // 这里可以打开工具测试对话框
}

// 访问资源
const accessResource = (resource: McpResource) => {
  message.info(`访问资源: ${resource.name}`)
  // 这里可以打开资源访问对话框
}

// 切换服务状态
const toggleService = async () => {
  if (!props.service) return
  
  actionLoading.value = true
  try {
    if (props.service.status === 'RUNNING') {
      await (props.service.serviceType === 'LOCAL'
        ? mcpApi.local.stopService(props.service.id)
        : mcpApi.remote.stopService(props.service.id))
      message.success('服务已停止')
    } else {
      await (props.service.serviceType === 'LOCAL'
        ? mcpApi.local.startService(props.service.id)
        : mcpApi.remote.startService(props.service.id))
      message.success('服务已启动')
    }
    emit('update')
  } catch (error: any) {
    message.error(error.message || '操作失败')
  } finally {
    actionLoading.value = false
  }
}

// 编辑服务
const editService = () => {
  message.info('编辑服务配置')
  // 这里可以打开编辑对话框
}

// 监听服务变化
watch(() => props.service, (service) => {
  if (service && props.show) {
    refreshTools()
    refreshResources()
    refreshLogs()
  }
}, { immediate: true })
</script>

<style scoped>
.service-detail {
  padding: 0;
}

.status-section {
  margin-bottom: 24px;
}

.info-section {
  padding: 16px 0;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #666;
}

.tools-grid {
  display: grid;
  gap: 16px;
}

.tool-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
}

.tool-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.tool-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.tool-description {
  margin: 0 0 12px 0;
  color: #666;
  font-size: 14px;
}

.tool-schema pre {
  background: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
}

.resources-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.resource-item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
}

.resource-info {
  flex: 1;
}

.resource-info h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
}

.resource-uri {
  margin: 0 0 4px 0;
  font-family: monospace;
  font-size: 12px;
  color: #666;
}

.resource-description {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 14px;
}

.resource-meta {
  display: flex;
  gap: 8px;
}

.resource-actions {
  flex-shrink: 0;
  margin-left: 16px;
}

.logs-container {
  height: 400px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}
</style>
