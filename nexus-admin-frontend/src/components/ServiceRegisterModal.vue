<template>
  <n-modal
    v-model:show="showModal"
    preset="card"
    title="注册MCP服务"
    size="large"
    :mask-closable="false"
    :closable="true"
    class="register-modal"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-placement="top"
      size="medium"
    >
      <!-- 服务类型选择 -->
      <n-form-item label="服务类型" path="serviceType">
        <n-radio-group v-model:value="formData.serviceType">
          <n-space>
            <n-radio value="LOCAL">
              <div class="radio-option">
                <n-icon size="20">
                  <DesktopOutline />
                </n-icon>
                <span>本地服务</span>
              </div>
            </n-radio>
            <n-radio value="REMOTE">
              <div class="radio-option">
                <n-icon size="20">
                  <CloudOutline />
                </n-icon>
                <span>远程服务</span>
              </div>
            </n-radio>
          </n-space>
        </n-radio-group>
      </n-form-item>

      <!-- 基本信息 -->
      <n-grid :cols="2" :x-gap="16">
        <n-grid-item>
          <n-form-item label="服务名称" path="serviceName">
            <n-input
              v-model:value="formData.serviceName"
              placeholder="输入服务名称"
              clearable
            />
          </n-form-item>
        </n-grid-item>
        
        <n-grid-item>
          <n-form-item label="显示名称" path="displayName">
            <n-input
              v-model:value="formData.displayName"
              placeholder="输入显示名称"
              clearable
            />
          </n-form-item>
        </n-grid-item>
      </n-grid>

      <n-form-item label="服务描述" path="description">
        <n-input
          v-model:value="formData.description"
          type="textarea"
          placeholder="输入服务描述"
          :rows="3"
        />
      </n-form-item>

      <!-- 远程服务配置 -->
      <template v-if="formData.serviceType === 'REMOTE'">
        <n-form-item label="服务端点" path="endpoint">
          <n-input
            v-model:value="formData.endpoint"
            placeholder="http://localhost:3000 或 ws://localhost:3000"
            clearable
          >
            <template #prefix>
              <n-icon>
                <LinkOutline />
              </n-icon>
            </template>
          </n-input>
        </n-form-item>

        <n-grid :cols="2" :x-gap="16">
          <n-grid-item>
            <n-form-item label="连接超时(秒)" path="connectionTimeout">
              <n-input-number
                v-model:value="formData.connectionTimeout"
                :min="1"
                :max="300"
                placeholder="30"
              />
            </n-form-item>
          </n-grid-item>
          
          <n-grid-item>
            <n-form-item label="请求超时(秒)" path="requestTimeout">
              <n-input-number
                v-model:value="formData.requestTimeout"
                :min="1"
                :max="300"
                placeholder="60"
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>

        <!-- 认证配置 -->
        <n-form-item label="认证方式">
          <n-select
            v-model:value="formData.authType"
            :options="authTypeOptions"
            placeholder="选择认证方式"
            clearable
          />
        </n-form-item>

        <template v-if="formData.authType === 'bearer'">
          <n-form-item label="Bearer Token" path="authToken">
            <n-input
              v-model:value="formData.authToken"
              type="password"
              placeholder="输入Bearer Token"
              show-password-on="click"
            />
          </n-form-item>
        </template>

        <template v-if="formData.authType === 'basic'">
          <n-grid :cols="2" :x-gap="16">
            <n-grid-item>
              <n-form-item label="用户名" path="authUsername">
                <n-input
                  v-model:value="formData.authUsername"
                  placeholder="输入用户名"
                />
              </n-form-item>
            </n-grid-item>
            
            <n-grid-item>
              <n-form-item label="密码" path="authPassword">
                <n-input
                  v-model:value="formData.authPassword"
                  type="password"
                  placeholder="输入密码"
                  show-password-on="click"
                />
              </n-form-item>
            </n-grid-item>
          </n-grid>
        </template>

        <template v-if="formData.authType === 'apikey'">
          <n-grid :cols="2" :x-gap="16">
            <n-grid-item>
              <n-form-item label="API Key名称" path="apiKeyName">
                <n-input
                  v-model:value="formData.apiKeyName"
                  placeholder="X-API-Key"
                />
              </n-form-item>
            </n-grid-item>
            
            <n-grid-item>
              <n-form-item label="API Key值" path="apiKeyValue">
                <n-input
                  v-model:value="formData.apiKeyValue"
                  type="password"
                  placeholder="输入API Key"
                  show-password-on="click"
                />
              </n-form-item>
            </n-grid-item>
          </n-grid>
        </template>
      </template>

      <!-- 本地服务配置 -->
      <template v-if="formData.serviceType === 'LOCAL'">
        <n-form-item label="执行命令" path="command">
          <n-input
            v-model:value="formData.command"
            placeholder="node server.js 或 python app.py"
            clearable
          >
            <template #prefix>
              <n-icon>
                <TerminalOutline />
              </n-icon>
            </template>
          </n-input>
        </n-form-item>

        <n-form-item label="工作目录" path="workingDirectory">
          <n-input
            v-model:value="formData.workingDirectory"
            placeholder="/path/to/service"
            clearable
          >
            <template #prefix>
              <n-icon>
                <FolderOutline />
              </n-icon>
            </template>
          </n-input>
        </n-form-item>

        <n-form-item label="环境变量">
          <div class="env-vars">
            <div
              v-for="(env, index) in formData.environmentVariables"
              :key="index"
              class="env-var-item"
            >
              <n-input
                v-model:value="env.key"
                placeholder="变量名"
                style="flex: 1;"
              />
              <n-input
                v-model:value="env.value"
                placeholder="变量值"
                style="flex: 1;"
              />
              <n-button
                quaternary
                type="error"
                @click="removeEnvVar(index)"
              >
                <n-icon>
                  <TrashOutline />
                </n-icon>
              </n-button>
            </div>
            
            <n-button
              dashed
              block
              @click="addEnvVar"
            >
              <template #icon>
                <n-icon>
                  <AddOutline />
                </n-icon>
              </template>
              添加环境变量
            </n-button>
          </div>
        </n-form-item>
      </template>

      <!-- 高级配置 -->
      <n-collapse>
        <n-collapse-item title="高级配置" name="advanced">
          <n-grid :cols="2" :x-gap="16">
            <n-grid-item>
              <n-form-item label="版本">
                <n-input
                  v-model:value="formData.version"
                  placeholder="1.0.0"
                />
              </n-form-item>
            </n-grid-item>
            
            <n-grid-item>
              <n-form-item label="健康检查间隔(秒)">
                <n-input-number
                  v-model:value="formData.healthCheckInterval"
                  :min="10"
                  :max="3600"
                  placeholder="60"
                />
              </n-form-item>
            </n-grid-item>
          </n-grid>

          <n-form-item>
            <n-checkbox v-model:checked="formData.autoStart">
              自动启动服务
            </n-checkbox>
          </n-form-item>

          <n-form-item>
            <n-checkbox v-model:checked="formData.enableHealthCheck">
              启用健康检查
            </n-checkbox>
          </n-form-item>
        </n-collapse-item>
      </n-collapse>
    </n-form>

    <template #action>
      <n-space justify="end">
        <n-button @click="showModal = false">取消</n-button>
        <n-button @click="testConnection" :loading="testing">
          测试连接
        </n-button>
        <n-button type="primary" @click="handleSubmit" :loading="submitting">
          注册服务
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useMessage } from 'naive-ui'
import {
  DesktopOutline,
  CloudOutline,
  LinkOutline,
  TerminalOutline,
  FolderOutline,
  AddOutline,
  TrashOutline
} from '@vicons/ionicons5'
import { mcpApi } from '@/api/mcp'
import type { FormInst, FormRules } from 'naive-ui'

const props = defineProps<{
  show: boolean
}>()

const emit = defineEmits<{
  'update:show': [value: boolean]
  success: []
}>()

const message = useMessage()

// 响应式数据
const formRef = ref<FormInst | null>(null)
const submitting = ref(false)
const testing = ref(false)

const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 表单数据
const formData = ref({
  serviceType: 'REMOTE' as 'LOCAL' | 'REMOTE',
  serviceName: '',
  displayName: '',
  description: '',
  version: '1.0.0',
  
  // 远程服务配置
  endpoint: '',
  connectionTimeout: 30,
  requestTimeout: 60,
  authType: null as string | null,
  authToken: '',
  authUsername: '',
  authPassword: '',
  apiKeyName: 'X-API-Key',
  apiKeyValue: '',
  
  // 本地服务配置
  command: '',
  workingDirectory: '',
  environmentVariables: [] as Array<{ key: string; value: string }>,
  
  // 高级配置
  autoStart: true,
  enableHealthCheck: true,
  healthCheckInterval: 60
})

// 认证方式选项
const authTypeOptions = [
  { label: '无认证', value: null },
  { label: 'Bearer Token', value: 'bearer' },
  { label: 'Basic Auth', value: 'basic' },
  { label: 'API Key', value: 'apikey' }
]

// 表单验证规则
const formRules: FormRules = {
  serviceName: [
    { required: true, message: '请输入服务名称', trigger: 'blur' },
    { min: 2, max: 50, message: '服务名称长度应在2-50个字符之间', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: '服务名称只能包含字母、数字、下划线和连字符', trigger: 'blur' }
  ],
  displayName: [
    { required: true, message: '请输入显示名称', trigger: 'blur' },
    { max: 100, message: '显示名称不能超过100个字符', trigger: 'blur' }
  ],
  endpoint: [
    {
      required: true,
      validator: (rule: any, value: string) => {
        if (formData.value.serviceType === 'REMOTE' && !value) {
          return new Error('请输入服务端点')
        }
        if (value && !/^(http|https|ws|wss):\/\/.+/.test(value)) {
          return new Error('请输入有效的URL格式')
        }
        return true
      },
      trigger: 'blur'
    }
  ],
  command: [
    {
      required: true,
      validator: (rule: any, value: string) => {
        if (formData.value.serviceType === 'LOCAL' && !value) {
          return new Error('请输入执行命令')
        }
        return true
      },
      trigger: 'blur'
    }
  ]
}

// 环境变量管理
const addEnvVar = () => {
  formData.value.environmentVariables.push({ key: '', value: '' })
}

const removeEnvVar = (index: number) => {
  formData.value.environmentVariables.splice(index, 1)
}

// 测试连接
const testConnection = async () => {
  if (formData.value.serviceType !== 'REMOTE' || !formData.value.endpoint) {
    message.warning('请先填写服务端点')
    return
  }

  testing.value = true
  try {
    // 这里可以调用测试连接的API
    await new Promise(resolve => setTimeout(resolve, 2000)) // 模拟测试
    message.success('连接测试成功')
  } catch (error: any) {
    message.error(error.message || '连接测试失败')
  } finally {
    testing.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    const serviceData = {
      ...formData.value,
      // 处理环境变量格式
      environmentVariables: formData.value.environmentVariables.reduce((acc, env) => {
        if (env.key && env.value) {
          acc[env.key] = env.value
        }
        return acc
      }, {} as Record<string, string>)
    }

    // 调用注册API
    if (formData.value.serviceType === 'LOCAL') {
      await mcpApi.local.registerService(serviceData)
    } else {
      await mcpApi.remote.registerService(serviceData)
    }

    message.success('服务注册成功')
    emit('success')
    showModal.value = false
    resetForm()

  } catch (error: any) {
    message.error(error.message || '注册失败')
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  formData.value = {
    serviceType: 'REMOTE',
    serviceName: '',
    displayName: '',
    description: '',
    version: '1.0.0',
    endpoint: '',
    connectionTimeout: 30,
    requestTimeout: 60,
    authType: null,
    authToken: '',
    authUsername: '',
    authPassword: '',
    apiKeyName: 'X-API-Key',
    apiKeyValue: '',
    command: '',
    workingDirectory: '',
    environmentVariables: [],
    autoStart: true,
    enableHealthCheck: true,
    healthCheckInterval: 60
  }
}

// 监听模态框关闭
watch(showModal, (show) => {
  if (!show) {
    resetForm()
  }
})
</script>

<style scoped>
.register-modal {
  max-width: 800px;
}

.radio-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.env-vars {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.env-var-item {
  display: flex;
  gap: 8px;
  align-items: center;
}

:deep(.n-form-item-label) {
  font-weight: 600;
}

:deep(.n-collapse-item__header) {
  font-weight: 600;
}
</style>
