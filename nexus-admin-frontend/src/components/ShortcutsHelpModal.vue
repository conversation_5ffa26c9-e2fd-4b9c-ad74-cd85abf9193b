<template>
  <n-modal
    v-model:show="showModal"
    preset="card"
    title="快捷键帮助"
    size="large"
    :mask-closable="true"
    :closable="true"
    class="shortcuts-help-modal"
  >
    <div class="shortcuts-help">
      <!-- 搜索栏 -->
      <div class="search-section">
        <n-input
          v-model:value="searchQuery"
          placeholder="搜索快捷键..."
          clearable
        >
          <template #prefix>
            <n-icon>
              <SearchOutline />
            </n-icon>
          </template>
        </n-input>
      </div>

      <!-- 快捷键统计 -->
      <div class="stats-section">
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-number">{{ stats.total }}</span>
            <span class="stat-label">总快捷键</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ stats.enabled }}</span>
            <span class="stat-label">已启用</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ stats.categories }}</span>
            <span class="stat-label">分类</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ stats.mostUsed.length }}</span>
            <span class="stat-label">常用</span>
          </div>
        </div>
      </div>

      <!-- 快捷键分类 -->
      <div class="categories-section">
        <n-tabs type="line" animated>
          <n-tab-pane
            v-for="category in filteredCategories"
            :key="category.name"
            :name="category.name"
            :tab="category.label"
          >
            <div class="shortcuts-list">
              <div
                v-for="shortcut in category.shortcuts"
                :key="shortcut.key"
                class="shortcut-item"
                :class="{ disabled: !shortcut.enabled }"
              >
                <div class="shortcut-keys">
                  <kbd
                    v-for="key in parseShortcutKey(shortcut.key)"
                    :key="key"
                    class="key"
                  >
                    {{ formatKey(key) }}
                  </kbd>
                </div>
                
                <div class="shortcut-info">
                  <div class="shortcut-description">{{ shortcut.description }}</div>
                  <div class="shortcut-category">{{ getCategoryLabel(shortcut.category) }}</div>
                </div>
                
                <div class="shortcut-actions">
                  <n-switch
                    :value="shortcut.enabled"
                    size="small"
                    @update:value="(value) => toggleShortcut(shortcut.key, value)"
                  />
                  
                  <n-button
                    quaternary
                    size="small"
                    @click="testShortcut(shortcut)"
                  >
                    测试
                  </n-button>
                </div>
              </div>
            </div>
          </n-tab-pane>
          
          <!-- 最常用快捷键 -->
          <n-tab-pane name="frequent" tab="常用">
            <div class="frequent-shortcuts">
              <div v-if="stats.mostUsed.length === 0" class="empty-state">
                <n-empty description="暂无使用记录" />
              </div>
              
              <div v-else class="frequent-list">
                <div
                  v-for="(key, index) in stats.mostUsed"
                  :key="key"
                  class="frequent-item"
                >
                  <div class="frequent-rank">{{ index + 1 }}</div>
                  <div class="frequent-keys">
                    <kbd
                      v-for="k in parseShortcutKey(key)"
                      :key="k"
                      class="key"
                    >
                      {{ formatKey(k) }}
                    </kbd>
                  </div>
                  <div class="frequent-description">
                    {{ getShortcutDescription(key) }}
                  </div>
                </div>
              </div>
            </div>
          </n-tab-pane>
          
          <!-- 自定义快捷键 -->
          <n-tab-pane name="custom" tab="自定义">
            <div class="custom-shortcuts">
              <div class="custom-header">
                <h4>自定义快捷键</h4>
                <n-button size="small" @click="showAddCustom = true">
                  <template #icon>
                    <n-icon>
                      <AddOutline />
                    </n-icon>
                  </template>
                  添加快捷键
                </n-button>
              </div>
              
              <div class="custom-list">
                <div
                  v-for="shortcut in customShortcuts"
                  :key="shortcut.key"
                  class="custom-item"
                >
                  <div class="custom-keys">
                    <kbd
                      v-for="key in parseShortcutKey(shortcut.key)"
                      :key="key"
                      class="key"
                    >
                      {{ formatKey(key) }}
                    </kbd>
                  </div>
                  
                  <div class="custom-info">
                    <div class="custom-description">{{ shortcut.description }}</div>
                    <div class="custom-category">{{ getCategoryLabel(shortcut.category) }}</div>
                  </div>
                  
                  <div class="custom-actions">
                    <n-button
                      quaternary
                      size="small"
                      @click="editCustomShortcut(shortcut)"
                    >
                      编辑
                    </n-button>
                    
                    <n-popconfirm
                      @positive-click="removeCustomShortcut(shortcut.key)"
                    >
                      <template #trigger>
                        <n-button
                          quaternary
                          type="error"
                          size="small"
                        >
                          删除
                        </n-button>
                      </template>
                      确定删除此自定义快捷键吗？
                    </n-popconfirm>
                  </div>
                </div>
              </div>
            </div>
          </n-tab-pane>
        </n-tabs>
      </div>

      <!-- 快捷键提示 -->
      <div class="tips-section">
        <n-alert type="info" title="使用提示">
          <ul>
            <li>按 <kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>?</kbd> 随时打开此帮助</li>
            <li>在输入框中时，快捷键会被禁用</li>
            <li>可以在设置中自定义快捷键</li>
            <li>某些快捷键可能与浏览器快捷键冲突</li>
          </ul>
        </n-alert>
      </div>
    </div>

    <template #action>
      <n-space justify="end">
        <n-button @click="resetAllShortcuts">重置所有</n-button>
        <n-button @click="exportShortcuts">导出配置</n-button>
        <n-button type="primary" @click="showModal = false">关闭</n-button>
      </n-space>
    </template>

    <!-- 添加自定义快捷键对话框 -->
    <AddCustomShortcutModal
      v-model:show="showAddCustom"
      @success="handleCustomShortcutAdded"
    />
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useMessage } from 'naive-ui'
import { SearchOutline, AddOutline } from '@vicons/ionicons5'
import { useGlobalShortcuts } from '@/composables/useGlobalShortcuts'
import AddCustomShortcutModal from './AddCustomShortcutModal.vue'

const props = defineProps<{
  show: boolean
}>()

const emit = defineEmits<{
  'update:show': [value: boolean]
}>()

const message = useMessage()

// 使用快捷键组合式函数
const {
  shortcuts,
  allShortcuts,
  enabledShortcuts,
  getShortcutStats,
  toggleShortcut: toggleShortcutEnabled,
  removeCustomShortcut: removeCustom,
  addCustomShortcut
} = useGlobalShortcuts()

// 响应式数据
const searchQuery = ref('')
const showAddCustom = ref(false)

const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 计算属性
const stats = computed(() => getShortcutStats())

const filteredCategories = computed(() => {
  if (!searchQuery.value) return shortcuts.value
  
  const query = searchQuery.value.toLowerCase()
  return shortcuts.value.map(category => ({
    ...category,
    shortcuts: category.shortcuts.filter(shortcut =>
      shortcut.key.toLowerCase().includes(query) ||
      shortcut.description.toLowerCase().includes(query)
    )
  })).filter(category => category.shortcuts.length > 0)
})

const customShortcuts = computed(() => {
  return allShortcuts.value.filter(shortcut => 
    shortcut.category === 'custom'
  )
})

// 工具函数
const parseShortcutKey = (key: string): string[] => {
  return key.split('+').map(k => k.trim())
}

const formatKey = (key: string): string => {
  const keyMap: Record<string, string> = {
    ctrl: 'Ctrl',
    alt: 'Alt',
    shift: 'Shift',
    meta: 'Cmd',
    ' ': 'Space',
    arrowup: '↑',
    arrowdown: '↓',
    arrowleft: '←',
    arrowright: '→',
    enter: 'Enter',
    escape: 'Esc',
    backspace: 'Backspace',
    delete: 'Del',
    tab: 'Tab'
  }
  
  return keyMap[key.toLowerCase()] || key.toUpperCase()
}

const getCategoryLabel = (category: string): string => {
  const categoryMap: Record<string, string> = {
    navigation: '导航',
    interface: '界面',
    search: '搜索',
    actions: '操作',
    help: '帮助',
    system: '系统',
    custom: '自定义'
  }
  
  return categoryMap[category] || category
}

const getShortcutDescription = (key: string): string => {
  const shortcut = allShortcuts.value.find(s => s.key === key)
  return shortcut?.description || '未知快捷键'
}

// 事件处理
const toggleShortcut = (key: string, enabled: boolean) => {
  toggleShortcutEnabled(key)
  message.success(enabled ? '快捷键已启用' : '快捷键已禁用')
}

const testShortcut = (shortcut: any) => {
  try {
    shortcut.action()
    message.success(`快捷键 ${shortcut.key} 测试成功`)
  } catch (error) {
    message.error(`快捷键 ${shortcut.key} 测试失败`)
  }
}

const editCustomShortcut = (shortcut: any) => {
  // 编辑自定义快捷键
  console.log('编辑快捷键:', shortcut)
}

const removeCustomShortcut = (key: string) => {
  removeCustom(key)
  message.success('自定义快捷键已删除')
}

const resetAllShortcuts = () => {
  // 重置所有快捷键到默认状态
  allShortcuts.value.forEach(shortcut => {
    if (shortcut.category !== 'custom') {
      shortcut.enabled = true
    }
  })
  message.success('所有快捷键已重置')
}

const exportShortcuts = () => {
  const config = {
    shortcuts: shortcuts.value,
    exportTime: new Date().toISOString(),
    version: '1.0.0'
  }
  
  const blob = new Blob([JSON.stringify(config, null, 2)], {
    type: 'application/json'
  })
  
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = 'nexus-shortcuts-config.json'
  a.click()
  
  URL.revokeObjectURL(url)
  message.success('快捷键配置已导出')
}

const handleCustomShortcutAdded = (shortcut: any) => {
  addCustomShortcut(shortcut)
  message.success('自定义快捷键已添加')
}

// 监听快捷键事件
watch(() => props.show, (show) => {
  if (show) {
    // 重置搜索
    searchQuery.value = ''
  }
})
</script>

<style scoped>
.shortcuts-help-modal {
  max-width: 900px;
}

.shortcuts-help {
  padding: 8px 0;
}

.search-section {
  margin-bottom: 24px;
}

.stats-section {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.categories-section {
  margin-bottom: 24px;
}

.shortcuts-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.shortcut-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.shortcut-item:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.shortcut-item.disabled {
  opacity: 0.5;
}

.shortcut-keys {
  display: flex;
  gap: 4px;
  min-width: 150px;
}

.key {
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 4px 8px;
  font-family: monospace;
  font-size: 12px;
  font-weight: 600;
  color: #333;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.shortcut-info {
  flex: 1;
  margin-left: 16px;
}

.shortcut-description {
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}

.shortcut-category {
  font-size: 12px;
  color: #666;
}

.shortcut-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.frequent-shortcuts {
  padding: 16px 0;
}

.frequent-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.frequent-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.frequent-rank {
  width: 24px;
  height: 24px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  margin-right: 16px;
}

.frequent-keys {
  display: flex;
  gap: 4px;
  min-width: 120px;
}

.frequent-description {
  flex: 1;
  margin-left: 16px;
  color: #333;
}

.custom-shortcuts {
  padding: 16px 0;
}

.custom-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.custom-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.custom-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.custom-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.custom-keys {
  display: flex;
  gap: 4px;
  min-width: 150px;
}

.custom-info {
  flex: 1;
  margin-left: 16px;
}

.custom-description {
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}

.custom-category {
  font-size: 12px;
  color: #666;
}

.custom-actions {
  display: flex;
  gap: 8px;
}

.tips-section {
  margin-top: 24px;
}

.tips-section ul {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.tips-section li {
  margin-bottom: 4px;
  font-size: 14px;
}

.tips-section kbd {
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 3px;
  padding: 2px 6px;
  font-family: monospace;
  font-size: 11px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .shortcut-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .shortcut-keys {
    min-width: auto;
  }
  
  .shortcut-info {
    margin-left: 0;
  }
}
</style>
