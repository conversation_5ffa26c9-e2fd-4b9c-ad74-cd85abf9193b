<template>
  <div class="sidebar-menu">
    <n-menu
      :value="activeKey"
      :collapsed="themeStore.sidebarCollapsed"
      :collapsed-width="64"
      :collapsed-icon-size="20"
      :options="menuOptions"
      :render-label="renderMenuLabel"
      :render-icon="renderMenuIcon"
      @update:value="handleMenuSelect"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { NIcon, NBadge } from 'naive-ui'
import {
  HomeOutline,
  ServerOutline,
  ConstructOutline,
  FolderOutline,
  TerminalOutline,
  CardOutline,
  AnalyticsOutline,
  PulseOutline,
  HardwareChipOutline,
  SwapHorizontalOutline,
  FlaskOutline,
  SettingsOutline,
  PersonOutline,
  OptionsOutline,
  GridOutline
} from '@vicons/ionicons5'
import { useThemeStore } from '@/stores/theme'
import type { MenuOption } from 'naive-ui'

const route = useRoute()
const router = useRouter()
const themeStore = useThemeStore()

// 当前激活的菜单项
const activeKey = computed(() => {
  const path = route.path
  
  // 精确匹配路径到菜单项
  if (path === '/') return 'dashboard'
  if (path.startsWith('/mcp/overview')) return 'mcp-overview'
  if (path.startsWith('/mcp/tools')) return 'mcp-tools'
  if (path.startsWith('/mcp/resources')) return 'mcp-resources'
  if (path.startsWith('/mcp/commands')) return 'mcp-commands'
  if (path.startsWith('/subscriptions')) return 'subscriptions'
  if (path.startsWith('/monitoring/realtime')) return 'monitoring-realtime'
  if (path.startsWith('/monitoring/services')) return 'monitoring-services'
  if (path.startsWith('/monitoring/api-calls')) return 'monitoring-api-calls'
  if (path.startsWith('/testing')) return 'testing'
  if (path.startsWith('/settings/profile')) return 'settings-profile'
  if (path.startsWith('/settings/preferences')) return 'settings-preferences'
  
  return ''
})

// 菜单选项配置
const menuOptions = computed<MenuOption[]>(() => [
  {
    label: '仪表板',
    key: 'dashboard',
    icon: HomeOutline,
    path: '/'
  },
  {
    type: 'divider',
    key: 'divider-1'
  },
  {
    label: 'MCP服务',
    key: 'mcp',
    icon: ServerOutline,
    children: [
      {
        label: '服务总览',
        key: 'mcp-overview',
        icon: GridOutline,
        path: '/mcp/overview'
      },
      {
        label: '工具管理',
        key: 'mcp-tools',
        icon: ConstructOutline,
        path: '/mcp/tools',
        badge: 'NEW'
      },
      {
        label: '资源管理',
        key: 'mcp-resources',
        icon: FolderOutline,
        path: '/mcp/resources'
      },
      {
        label: '服务控制',
        key: 'mcp-commands',
        icon: TerminalOutline,
        path: '/mcp/commands'
      }
    ]
  },
  {
    label: '订阅管理',
    key: 'subscriptions',
    icon: CardOutline,
    path: '/subscriptions'
  },
  {
    type: 'divider',
    key: 'divider-2'
  },
  {
    label: '系统监控',
    key: 'monitoring',
    icon: AnalyticsOutline,
    children: [
      {
        label: '实时监控',
        key: 'monitoring-realtime',
        icon: PulseOutline,
        path: '/monitoring/realtime'
      },
      {
        label: '服务监控',
        key: 'monitoring-services',
        icon: HardwareChipOutline,
        path: '/monitoring/services'
      },
      {
        label: 'API监控',
        key: 'monitoring-api-calls',
        icon: SwapHorizontalOutline,
        path: '/monitoring/api-calls'
      }
    ]
  },
  {
    label: '接口测试',
    key: 'testing',
    icon: FlaskOutline,
    path: '/testing'
  },
  {
    type: 'divider',
    key: 'divider-3'
  },
  {
    label: '系统设置',
    key: 'settings',
    icon: SettingsOutline,
    children: [
      {
        label: '个人资料',
        key: 'settings-profile',
        icon: PersonOutline,
        path: '/settings/profile'
      },
      {
        label: '偏好设置',
        key: 'settings-preferences',
        icon: OptionsOutline,
        path: '/settings/preferences'
      }
    ]
  }
])

// 渲染菜单图标
const renderMenuIcon = (option: MenuOption) => {
  if (option.icon) {
    return h(NIcon, { size: 18 }, {
      default: () => h(option.icon as any)
    })
  }
  return null
}

// 渲染菜单标签
const renderMenuLabel = (option: MenuOption) => {
  if (option.badge) {
    return h('div', { class: 'menu-label-with-badge' }, [
      h('span', option.label),
      h(NBadge, { 
        value: option.badge,
        size: 'small',
        type: 'success'
      })
    ])
  }
  return option.label
}

// 处理菜单选择
const handleMenuSelect = (key: string, option: MenuOption) => {
  if (option.path) {
    router.push(option.path)
  }
}
</script>

<style scoped>
.sidebar-menu {
  height: 100%;
  padding: 0 var(--space-4);
}

:deep(.n-menu) {
  background: transparent;
  color: var(--text-primary);
  border-radius: var(--radius-lg);
}

:deep(.n-menu-item) {
  margin-bottom: var(--space-1);
  border-radius: var(--radius-lg) !important;
  transition: all var(--duration-normal) var(--ease-out) !important;
}

:deep(.n-menu-item:hover) {
  background: var(--primary-color-light) !important;
  color: var(--primary-color) !important;
}

:deep(.n-menu-item.n-menu-item--selected) {
  background: var(--gradient-primary) !important;
  color: white !important;
  box-shadow: var(--shadow-glow) !important;
}

:deep(.n-menu-item-content) {
  padding: var(--space-3) var(--space-4) !important;
}

:deep(.n-menu-item-content-header) {
  font-weight: 500 !important;
}

:deep(.n-submenu-children) {
  background: rgba(45, 55, 72, 0.3) !important;
  border-radius: var(--radius-lg) !important;
  margin-top: var(--space-1) !important;
  backdrop-filter: blur(8px) !important;
}

:deep(.n-menu-item) {
  margin: 4px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

:deep(.n-menu-item:hover) {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(4px);
}

:deep(.n-menu-item.n-menu-item--selected) {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  font-weight: 600;
}

:deep(.n-menu-item.n-menu-item--selected::before) {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background: white;
  border-radius: 0 2px 2px 0;
}

:deep(.n-submenu-children) {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  margin: 4px 0;
}

:deep(.n-submenu-children .n-menu-item) {
  margin: 2px 8px;
  padding-left: 32px;
}

:deep(.n-menu-item-content) {
  padding: 12px 16px;
}

:deep(.n-menu-item-content-header) {
  display: flex;
  align-items: center;
  gap: 12px;
}

:deep(.n-menu-item-icon) {
  color: rgba(255, 255, 255, 0.8);
}

:deep(.n-menu-item.n-menu-item--selected .n-menu-item-icon) {
  color: white;
}

:deep(.n-divider) {
  margin: 16px 20px;
  background: rgba(255, 255, 255, 0.1);
}

.menu-label-with-badge {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

/* 折叠状态下的样式调整 */
:deep(.n-menu--collapsed .n-menu-item) {
  margin: 4px 8px;
  justify-content: center;
}

:deep(.n-menu--collapsed .n-menu-item-content) {
  padding: 12px 8px;
  justify-content: center;
}

:deep(.n-menu--collapsed .n-submenu) {
  position: relative;
}

:deep(.n-menu--collapsed .n-submenu-children) {
  position: absolute;
  left: 100%;
  top: 0;
  min-width: 200px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  z-index: 1000;
}

/* 响应式调整 */
@media (max-width: 768px) {
  :deep(.n-menu-item) {
    margin: 2px 8px;
  }
  
  :deep(.n-menu-item-content) {
    padding: 10px 12px;
  }
}
</style>
