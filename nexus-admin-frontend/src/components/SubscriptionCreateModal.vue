<template>
  <n-modal
    v-model:show="showModal"
    preset="card"
    title="创建订阅"
    size="large"
    :mask-closable="false"
    :closable="true"
    class="create-modal"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-placement="top"
      size="medium"
    >
      <!-- 用户选择 -->
      <n-form-item label="选择用户" path="userId">
        <n-select
          v-model:value="formData.userId"
          placeholder="搜索并选择用户"
          filterable
          remote
          :options="userOptions"
          :loading="userLoading"
          @search="searchUsers"
          clearable
        >
          <template #empty>
            <div class="empty-users">
              <n-icon size="24">
                <PersonOutline />
              </n-icon>
              <p>输入用户名或邮箱搜索用户</p>
            </div>
          </template>
        </n-select>
      </n-form-item>

      <!-- 服务选择 -->
      <n-form-item label="选择服务" path="serviceConfigId">
        <div class="service-selection">
          <n-grid :cols="1" :y-gap="12">
            <n-grid-item
              v-for="service in serviceConfigs"
              :key="service.id"
            >
              <div 
                class="service-card"
                :class="{ active: formData.serviceConfigId === service.id }"
                @click="selectService(service)"
              >
                <div class="service-header">
                  <div class="service-info">
                    <h4>{{ service.displayName }}</h4>
                    <p>{{ service.description }}</p>
                  </div>
                  <div class="service-price">
                    <span class="price">¥{{ service.pricing.price }}</span>
                    <span class="unit">/ {{ service.pricing.unit }}</span>
                  </div>
                </div>
                
                <div class="service-features">
                  <div class="features-list">
                    <div
                      v-for="feature in service.features"
                      :key="feature"
                      class="feature-item"
                    >
                      <n-icon size="14" color="#52c41a">
                        <CheckmarkOutline />
                      </n-icon>
                      <span>{{ feature }}</span>
                    </div>
                  </div>
                  
                  <div class="limitations">
                    <div class="limitation-item">
                      <span class="label">调用限制:</span>
                      <span class="value">{{ service.limitations.callLimit.toLocaleString() }} 次/月</span>
                    </div>
                    <div class="limitation-item">
                      <span class="label">频率限制:</span>
                      <span class="value">{{ service.limitations.rateLimit }} 次/分钟</span>
                    </div>
                  </div>
                </div>
              </div>
            </n-grid-item>
          </n-grid>
        </div>
      </n-form-item>

      <!-- 订阅配置 -->
      <n-grid :cols="2" :x-gap="16">
        <n-grid-item>
          <n-form-item label="调用限制" path="callLimit">
            <n-input-number
              v-model:value="formData.callLimit"
              :min="1"
              :max="1000000"
              placeholder="自定义调用限制"
              style="width: 100%;"
            >
              <template #suffix>次/月</template>
            </n-input-number>
          </n-form-item>
        </n-grid-item>
        
        <n-grid-item>
          <n-form-item label="有效期" path="endDate">
            <n-date-picker
              v-model:value="formData.endDate"
              type="date"
              placeholder="选择到期日期"
              :is-date-disabled="(date: number) => date < Date.now()"
              style="width: 100%;"
            />
          </n-form-item>
        </n-grid-item>
      </n-grid>

      <!-- 快速设置 -->
      <n-form-item label="快速设置">
        <n-button-group>
          <n-button
            v-for="preset in durationPresets"
            :key="preset.label"
            :type="selectedPreset === preset.value ? 'primary' : 'default'"
            @click="applyPreset(preset)"
          >
            {{ preset.label }}
          </n-button>
        </n-button-group>
      </n-form-item>

      <!-- 备注 -->
      <n-form-item label="备注">
        <n-input
          v-model:value="formData.notes"
          type="textarea"
          placeholder="添加订阅备注（可选）"
          :rows="3"
          maxlength="500"
          show-count
        />
      </n-form-item>

      <!-- 订阅预览 -->
      <div class="subscription-preview">
        <h4>订阅预览</h4>
        <div class="preview-content">
          <div class="preview-item">
            <span class="label">用户:</span>
            <span class="value">{{ selectedUserName || '未选择' }}</span>
          </div>
          <div class="preview-item">
            <span class="label">服务:</span>
            <span class="value">{{ selectedServiceName || '未选择' }}</span>
          </div>
          <div class="preview-item">
            <span class="label">调用限制:</span>
            <span class="value">{{ formData.callLimit?.toLocaleString() || 0 }} 次/月</span>
          </div>
          <div class="preview-item">
            <span class="label">有效期:</span>
            <span class="value">
              {{ formData.endDate ? new Date(formData.endDate).toLocaleDateString() : '未设置' }}
            </span>
          </div>
          <div class="preview-item total">
            <span class="label">预计费用:</span>
            <span class="value">¥{{ estimatedCost }}</span>
          </div>
        </div>
      </div>
    </n-form>

    <template #action>
      <n-space justify="end">
        <n-button @click="showModal = false">取消</n-button>
        <n-button type="primary" @click="handleSubmit" :loading="submitting">
          创建订阅
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useQuery } from '@tanstack/vue-query'
import { useMessage } from 'naive-ui'
import { PersonOutline, CheckmarkOutline } from '@vicons/ionicons5'
import { subscriptionApi } from '@/api/subscription'
import { authApi } from '@/api/auth'
import type { FormInst, FormRules } from 'naive-ui'
import type { CreateSubscriptionRequest } from '@/types/api'

const props = defineProps<{
  show: boolean
}>()

const emit = defineEmits<{
  'update:show': [value: boolean]
  success: []
}>()

const message = useMessage()

// 响应式数据
const formRef = ref<FormInst | null>(null)
const submitting = ref(false)
const userLoading = ref(false)
const userOptions = ref<Array<{ label: string; value: number }>>([])
const selectedPreset = ref<string | null>(null)

const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 表单数据
const formData = ref<CreateSubscriptionRequest & { endDate: number | null }>({
  userId: null as number | null,
  serviceConfigId: null as number | null,
  callLimit: 10000,
  endDate: null,
  notes: ''
})

// 获取服务配置
const { data: serviceConfigs } = useQuery({
  queryKey: ['subscription-configs'],
  queryFn: () => subscriptionApi.getConfigs().then(res => res.data)
})

// 时长预设
const durationPresets = [
  { label: '1个月', value: '1month', months: 1 },
  { label: '3个月', value: '3months', months: 3 },
  { label: '6个月', value: '6months', months: 6 },
  { label: '1年', value: '1year', months: 12 }
]

// 表单验证规则
const formRules: FormRules = {
  userId: [
    { required: true, type: 'number', message: '请选择用户', trigger: 'change' }
  ],
  serviceConfigId: [
    { required: true, type: 'number', message: '请选择服务', trigger: 'change' }
  ],
  callLimit: [
    { required: true, type: 'number', message: '请设置调用限制', trigger: 'blur' },
    { type: 'number', min: 1, message: '调用限制不能少于1次', trigger: 'blur' }
  ],
  endDate: [
    { required: true, type: 'number', message: '请选择到期日期', trigger: 'change' }
  ]
}

// 计算属性
const selectedUserName = computed(() => {
  const option = userOptions.value.find(opt => opt.value === formData.value.userId)
  return option?.label || ''
})

const selectedServiceName = computed(() => {
  const service = serviceConfigs.value?.find(s => s.id === formData.value.serviceConfigId)
  return service?.displayName || ''
})

const selectedService = computed(() => {
  return serviceConfigs.value?.find(s => s.id === formData.value.serviceConfigId)
})

const estimatedCost = computed(() => {
  if (!selectedService.value || !formData.value.endDate) return 0
  
  const service = selectedService.value
  const months = Math.ceil((formData.value.endDate - Date.now()) / (30 * 24 * 60 * 60 * 1000))
  
  if (service.pricing.model === 'monthly') {
    return (service.pricing.price * months).toFixed(2)
  }
  
  return service.pricing.price.toFixed(2)
})

// 搜索用户
const searchUsers = async (query: string) => {
  if (!query || query.length < 2) {
    userOptions.value = []
    return
  }
  
  userLoading.value = true
  try {
    // 这里应该调用用户搜索API
    // 模拟搜索结果
    await new Promise(resolve => setTimeout(resolve, 500))
    userOptions.value = [
      { label: `${query} (<EMAIL>)`, value: 1 },
      { label: `${query}2 (<EMAIL>)`, value: 2 }
    ]
  } catch (error) {
    console.error('搜索用户失败:', error)
  } finally {
    userLoading.value = false
  }
}

// 选择服务
const selectService = (service: any) => {
  formData.value.serviceConfigId = service.id
  formData.value.callLimit = service.limitations.callLimit
}

// 应用预设
const applyPreset = (preset: any) => {
  selectedPreset.value = preset.value
  const endDate = new Date()
  endDate.setMonth(endDate.getMonth() + preset.months)
  formData.value.endDate = endDate.getTime()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    const subscriptionData: CreateSubscriptionRequest = {
      userId: formData.value.userId!,
      serviceConfigId: formData.value.serviceConfigId!,
      callLimit: formData.value.callLimit,
      endDate: new Date(formData.value.endDate!).toISOString(),
      notes: formData.value.notes
    }

    await subscriptionApi.create(subscriptionData)
    
    message.success('订阅创建成功')
    emit('success')
    showModal.value = false
    resetForm()

  } catch (error: any) {
    message.error(error.message || '创建失败')
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  formData.value = {
    userId: null,
    serviceConfigId: null,
    callLimit: 10000,
    endDate: null,
    notes: ''
  }
  userOptions.value = []
  selectedPreset.value = null
}

// 监听模态框关闭
watch(showModal, (show) => {
  if (!show) {
    resetForm()
  }
})

// 组件挂载时设置默认到期时间
onMounted(() => {
  const defaultEndDate = new Date()
  defaultEndDate.setMonth(defaultEndDate.getMonth() + 1)
  formData.value.endDate = defaultEndDate.getTime()
})
</script>

<style scoped>
.create-modal {
  max-width: 800px;
}

.empty-users {
  text-align: center;
  padding: 20px;
  color: #666;
}

.empty-users p {
  margin: 8px 0 0 0;
  font-size: 14px;
}

.service-selection {
  max-height: 400px;
  overflow-y: auto;
}

.service-card {
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.service-card:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.service-card.active {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.service-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.service-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.service-price {
  text-align: right;
}

.price {
  font-size: 20px;
  font-weight: 700;
  color: #667eea;
}

.unit {
  font-size: 12px;
  color: #666;
}

.service-features {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 20px;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #333;
}

.limitations {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.limitation-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.limitation-item .label {
  color: #666;
}

.limitation-item .value {
  color: #333;
  font-weight: 500;
}

.subscription-preview {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
}

.subscription-preview h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.preview-item .label {
  color: #666;
}

.preview-item .value {
  color: #333;
  font-weight: 500;
}

.preview-item.total {
  border-top: 1px solid #e0e0e0;
  padding-top: 8px;
  margin-top: 4px;
  font-weight: 600;
}

.preview-item.total .value {
  color: #667eea;
  font-size: 16px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .service-header {
    flex-direction: column;
    gap: 12px;
  }
  
  .service-features {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .limitations {
    border-top: 1px solid #e0e0e0;
    padding-top: 12px;
  }
}
</style>
