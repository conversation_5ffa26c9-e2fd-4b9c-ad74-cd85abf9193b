<template>
  <n-drawer
    v-model:show="showDrawer"
    :width="800"
    placement="right"
    class="subscription-detail-drawer"
  >
    <n-drawer-content
      :title="`订阅详情 - ${subscription?.serviceDisplayName || ''}`"
      closable
    >
      <div v-if="subscription" class="subscription-detail">
        <!-- 状态卡片 -->
        <div class="status-section">
          <RealtimeStatusCard
            :title="subscription.serviceDisplayName"
            :value="getStatusText(subscription.status)"
            :icon="CardOutline"
            :status="getStatusType(subscription.status)"
            :description="`用户: ${subscription.username || `用户${subscription.userId}`}`"
            :show-progress="true"
            :progress="subscription.usedCalls"
            :progress-max="subscription.callLimit"
            :progress-text="`${subscription.usedCalls}/${subscription.callLimit} 次调用`"
            :tags="getSubscriptionTags(subscription)"
          />
        </div>

        <!-- 详细信息 -->
        <n-tabs type="line" animated>
          <!-- 基本信息 -->
          <n-tab-pane name="basic" tab="基本信息">
            <div class="info-section">
              <n-descriptions :column="2" bordered>
                <n-descriptions-item label="订阅ID">
                  {{ subscription.id }}
                </n-descriptions-item>
                <n-descriptions-item label="用户ID">
                  {{ subscription.userId }}
                </n-descriptions-item>
                <n-descriptions-item label="用户名">
                  {{ subscription.username || '未知用户' }}
                </n-descriptions-item>
                <n-descriptions-item label="服务名称">
                  {{ subscription.serviceDisplayName }}
                </n-descriptions-item>
                <n-descriptions-item label="服务标识">
                  <n-text code>{{ subscription.serviceName }}</n-text>
                </n-descriptions-item>
                <n-descriptions-item label="状态">
                  <n-tag :type="getStatusType(subscription.status)">
                    {{ getStatusText(subscription.status) }}
                  </n-tag>
                </n-descriptions-item>
                <n-descriptions-item label="开始日期">
                  {{ formatDate(subscription.startDate) }}
                </n-descriptions-item>
                <n-descriptions-item label="结束日期">
                  {{ formatDate(subscription.endDate) }}
                </n-descriptions-item>
                <n-descriptions-item label="调用限制">
                  {{ subscription.callLimit.toLocaleString() }} 次/月
                </n-descriptions-item>
                <n-descriptions-item label="已使用">
                  {{ subscription.usedCalls.toLocaleString() }} 次
                </n-descriptions-item>
                <n-descriptions-item label="剩余调用">
                  {{ subscription.remainingCalls.toLocaleString() }} 次
                </n-descriptions-item>
                <n-descriptions-item label="使用率">
                  {{ ((subscription.usedCalls / subscription.callLimit) * 100).toFixed(1) }}%
                </n-descriptions-item>
                <n-descriptions-item label="创建时间">
                  {{ formatDateTime(subscription.createdAt) }}
                </n-descriptions-item>
                <n-descriptions-item label="更新时间">
                  {{ formatDateTime(subscription.updatedAt) }}
                </n-descriptions-item>
                <n-descriptions-item label="备注" :span="2">
                  {{ subscription.notes || '无备注' }}
                </n-descriptions-item>
              </n-descriptions>
            </div>
          </n-tab-pane>

          <!-- 使用统计 -->
          <n-tab-pane name="usage" tab="使用统计">
            <div class="usage-section">
              <div class="section-header">
                <h3>使用情况统计</h3>
                <n-button @click="refreshUsageStats" :loading="usageLoading">
                  <template #icon>
                    <n-icon>
                      <RefreshOutline />
                    </n-icon>
                  </template>
                  刷新
                </n-button>
              </div>

              <!-- 使用量图表 -->
              <div class="usage-chart">
                <RealtimeChart
                  title="API调用趋势"
                  :data="usageChartData"
                  :height="300"
                  type="line"
                  color="#667eea"
                  :loading="usageLoading"
                  @refresh="refreshUsageStats"
                />
              </div>

              <!-- 统计数据 -->
              <div class="usage-stats">
                <n-grid :cols="4" :x-gap="16">
                  <n-grid-item>
                    <n-statistic label="今日调用" :value="todayUsage" />
                  </n-grid-item>
                  <n-grid-item>
                    <n-statistic label="本周调用" :value="weekUsage" />
                  </n-grid-item>
                  <n-grid-item>
                    <n-statistic label="本月调用" :value="monthUsage" />
                  </n-grid-item>
                  <n-grid-item>
                    <n-statistic label="平均每日" :value="avgDailyUsage" />
                  </n-grid-item>
                </n-grid>
              </div>
            </div>
          </n-tab-pane>

          <!-- 操作历史 -->
          <n-tab-pane name="history" tab="操作历史">
            <div class="history-section">
              <div class="section-header">
                <h3>操作历史</h3>
                <n-button @click="refreshHistory" :loading="historyLoading">
                  <template #icon>
                    <n-icon>
                      <RefreshOutline />
                    </n-icon>
                  </template>
                  刷新
                </n-button>
              </div>

              <div v-if="historyLoading" class="loading-container">
                <n-spin size="large" />
                <p>加载历史记录...</p>
              </div>

              <div v-else-if="operationHistory.length === 0" class="empty-container">
                <n-empty description="暂无操作历史" />
              </div>

              <n-timeline v-else>
                <n-timeline-item
                  v-for="item in operationHistory"
                  :key="item.id"
                  :type="getHistoryItemType(item.action)"
                >
                  <template #header>
                    <div class="history-header">
                      <span class="action">{{ getActionText(item.action) }}</span>
                      <span class="time">{{ formatDateTime(item.timestamp) }}</span>
                    </div>
                  </template>
                  <div class="history-content">
                    <p>{{ item.description }}</p>
                    <div v-if="item.details" class="history-details">
                      <n-collapse>
                        <n-collapse-item title="详细信息" name="details">
                          <pre>{{ JSON.stringify(item.details, null, 2) }}</pre>
                        </n-collapse-item>
                      </n-collapse>
                    </div>
                  </div>
                </n-timeline-item>
              </n-timeline>
            </div>
          </n-tab-pane>

          <!-- 续费管理 -->
          <n-tab-pane name="renewal" tab="续费管理">
            <div class="renewal-section">
              <h3>续费管理</h3>
              
              <div class="renewal-info">
                <n-alert
                  v-if="isExpiringSoon"
                  type="warning"
                  title="订阅即将到期"
                  :description="`此订阅将在 ${daysUntilExpiry} 天后到期，请及时续费以避免服务中断。`"
                />
                
                <n-alert
                  v-else-if="subscription.status === 'EXPIRED'"
                  type="error"
                  title="订阅已过期"
                  description="此订阅已过期，请续费以恢复服务。"
                />
                
                <n-alert
                  v-else
                  type="info"
                  title="订阅状态正常"
                  :description="`订阅有效期至 ${formatDate(subscription.endDate)}`"
                />
              </div>

              <div class="renewal-form">
                <n-form :model="renewalData" label-placement="left" label-width="100px">
                  <n-form-item label="续费时长">
                    <n-select
                      v-model:value="renewalData.duration"
                      :options="renewalOptions"
                      placeholder="选择续费时长"
                    />
                  </n-form-item>
                  
                  <n-form-item label="调用限制">
                    <n-input-number
                      v-model:value="renewalData.callLimit"
                      :min="1"
                      :max="1000000"
                      placeholder="调整调用限制"
                      style="width: 100%;"
                    >
                      <template #suffix>次/月</template>
                    </n-input-number>
                  </n-form-item>
                  
                  <n-form-item label="新到期日期">
                    <n-text>{{ newEndDate }}</n-text>
                  </n-form-item>
                  
                  <n-form-item label="续费费用">
                    <n-text type="primary" style="font-size: 18px; font-weight: 600;">
                      ¥{{ renewalCost }}
                    </n-text>
                  </n-form-item>
                  
                  <n-form-item>
                    <n-button
                      type="primary"
                      @click="handleRenewal"
                      :loading="renewalLoading"
                      :disabled="!renewalData.duration"
                    >
                      确认续费
                    </n-button>
                  </n-form-item>
                </n-form>
              </div>
            </div>
          </n-tab-pane>
        </n-tabs>
      </div>

      <template #footer>
        <n-space justify="end">
          <n-button @click="showDrawer = false">关闭</n-button>
          <n-button
            v-if="subscription?.status === 'SUSPENDED'"
            type="success"
            @click="resumeSubscription"
            :loading="actionLoading"
          >
            恢复订阅
          </n-button>
          <n-button
            v-else-if="subscription?.status === 'ACTIVE'"
            type="warning"
            @click="suspendSubscription"
            :loading="actionLoading"
          >
            暂停订阅
          </n-button>
          <n-popconfirm
            @positive-click="cancelSubscription"
          >
            <template #trigger>
              <n-button type="error" :loading="actionLoading">
                取消订阅
              </n-button>
            </template>
            确定要取消此订阅吗？此操作不可撤销。
          </n-popconfirm>
        </n-space>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useMessage } from 'naive-ui'
import { CardOutline, RefreshOutline } from '@vicons/ionicons5'
import { subscriptionApi } from '@/api/subscription'
import RealtimeStatusCard from './RealtimeStatusCard.vue'
import RealtimeChart from './RealtimeChart.vue'
import type { Subscription } from '@/types/api'

const props = defineProps<{
  show: boolean
  subscription: Subscription | null
}>()

const emit = defineEmits<{
  'update:show': [value: boolean]
  update: []
}>()

const message = useMessage()

// 响应式数据
const actionLoading = ref(false)
const usageLoading = ref(false)
const historyLoading = ref(false)
const renewalLoading = ref(false)

const usageChartData = ref<Array<{ timestamp: number; value: number }>>([])
const operationHistory = ref<Array<any>>([])

const renewalData = ref({
  duration: null as string | null,
  callLimit: 10000
})

const showDrawer = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 续费选项
const renewalOptions = [
  { label: '1个月', value: '1month' },
  { label: '3个月', value: '3months' },
  { label: '6个月', value: '6months' },
  { label: '1年', value: '1year' }
]

// 计算属性
const isExpiringSoon = computed(() => {
  if (!props.subscription) return false
  const daysLeft = Math.ceil((new Date(props.subscription.endDate).getTime() - Date.now()) / (24 * 60 * 60 * 1000))
  return daysLeft <= 7 && daysLeft > 0 && props.subscription.status === 'ACTIVE'
})

const daysUntilExpiry = computed(() => {
  if (!props.subscription) return 0
  return Math.ceil((new Date(props.subscription.endDate).getTime() - Date.now()) / (24 * 60 * 60 * 1000))
})

const newEndDate = computed(() => {
  if (!props.subscription || !renewalData.value.duration) return ''
  
  const currentEnd = new Date(props.subscription.endDate)
  const newEnd = new Date(currentEnd)
  
  switch (renewalData.value.duration) {
    case '1month':
      newEnd.setMonth(newEnd.getMonth() + 1)
      break
    case '3months':
      newEnd.setMonth(newEnd.getMonth() + 3)
      break
    case '6months':
      newEnd.setMonth(newEnd.getMonth() + 6)
      break
    case '1year':
      newEnd.setFullYear(newEnd.getFullYear() + 1)
      break
  }
  
  return newEnd.toLocaleDateString()
})

const renewalCost = computed(() => {
  if (!renewalData.value.duration) return '0.00'
  
  const baseCost = 100 // 基础价格
  const multiplier = {
    '1month': 1,
    '3months': 2.7,
    '6months': 5.4,
    '1year': 10
  }[renewalData.value.duration] || 1
  
  return (baseCost * multiplier).toFixed(2)
})

// 模拟统计数据
const todayUsage = ref(Math.floor(Math.random() * 100))
const weekUsage = ref(Math.floor(Math.random() * 500))
const monthUsage = ref(Math.floor(Math.random() * 2000))
const avgDailyUsage = ref(Math.floor(Math.random() * 50))

// 工具函数
const getStatusType = (status: string) => {
  const statusMap: Record<string, any> = {
    ACTIVE: 'success',
    EXPIRED: 'error',
    SUSPENDED: 'warning',
    CANCELLED: 'default'
  }
  return statusMap[status] || 'default'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    ACTIVE: '活跃',
    EXPIRED: '已过期',
    SUSPENDED: '已暂停',
    CANCELLED: '已取消'
  }
  return statusMap[status] || status
}

const getSubscriptionTags = (subscription: Subscription) => {
  const tags = []
  
  if (subscription.status === 'ACTIVE' && isExpiringSoon.value) {
    tags.push({ label: '即将到期', type: 'warning' })
  }
  
  const usageRate = (subscription.usedCalls / subscription.callLimit) * 100
  if (usageRate > 90) {
    tags.push({ label: '使用量高', type: 'error' })
  } else if (usageRate > 70) {
    tags.push({ label: '使用量中等', type: 'warning' })
  }
  
  return tags
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString()
}

const formatDateTime = (date: string) => {
  return new Date(date).toLocaleString()
}

const getHistoryItemType = (action: string) => {
  const typeMap: Record<string, any> = {
    CREATE: 'success',
    SUSPEND: 'warning',
    RESUME: 'info',
    CANCEL: 'error',
    RENEW: 'success'
  }
  return typeMap[action] || 'default'
}

const getActionText = (action: string) => {
  const actionMap: Record<string, string> = {
    CREATE: '创建订阅',
    SUSPEND: '暂停订阅',
    RESUME: '恢复订阅',
    CANCEL: '取消订阅',
    RENEW: '续费订阅'
  }
  return actionMap[action] || action
}

// 事件处理
const refreshUsageStats = async () => {
  if (!props.subscription) return
  
  usageLoading.value = true
  try {
    // 模拟获取使用统计
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const now = Date.now()
    usageChartData.value = Array.from({ length: 30 }, (_, i) => ({
      timestamp: now - (29 - i) * 24 * 60 * 60 * 1000,
      value: Math.floor(Math.random() * 100)
    }))
  } catch (error: any) {
    message.error('获取使用统计失败: ' + error.message)
  } finally {
    usageLoading.value = false
  }
}

const refreshHistory = async () => {
  if (!props.subscription) return
  
  historyLoading.value = true
  try {
    // 模拟获取操作历史
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    operationHistory.value = [
      {
        id: 1,
        action: 'CREATE',
        description: '订阅创建成功',
        timestamp: props.subscription.createdAt,
        details: { userId: props.subscription.userId }
      }
    ]
  } catch (error: any) {
    message.error('获取操作历史失败: ' + error.message)
  } finally {
    historyLoading.value = false
  }
}

const handleRenewal = async () => {
  if (!props.subscription || !renewalData.value.duration) return
  
  renewalLoading.value = true
  try {
    const endDate = new Date()
    const months = {
      '1month': 1,
      '3months': 3,
      '6months': 6,
      '1year': 12
    }[renewalData.value.duration] || 1
    
    endDate.setMonth(endDate.getMonth() + months)
    
    await subscriptionApi.renew(props.subscription.id, {
      endDate: endDate.toISOString(),
      callLimit: renewalData.value.callLimit
    })
    
    message.success('续费成功')
    emit('update')
  } catch (error: any) {
    message.error(error.message || '续费失败')
  } finally {
    renewalLoading.value = false
  }
}

const suspendSubscription = async () => {
  if (!props.subscription) return
  
  actionLoading.value = true
  try {
    await subscriptionApi.suspend(props.subscription.id, '手动暂停')
    message.success('订阅已暂停')
    emit('update')
  } catch (error: any) {
    message.error(error.message || '暂停失败')
  } finally {
    actionLoading.value = false
  }
}

const resumeSubscription = async () => {
  if (!props.subscription) return
  
  actionLoading.value = true
  try {
    await subscriptionApi.resume(props.subscription.id)
    message.success('订阅已恢复')
    emit('update')
  } catch (error: any) {
    message.error(error.message || '恢复失败')
  } finally {
    actionLoading.value = false
  }
}

const cancelSubscription = async () => {
  if (!props.subscription) return
  
  actionLoading.value = true
  try {
    await subscriptionApi.cancel(props.subscription.id)
    message.success('订阅已取消')
    emit('update')
    showDrawer.value = false
  } catch (error: any) {
    message.error(error.message || '取消失败')
  } finally {
    actionLoading.value = false
  }
}

// 监听订阅变化
watch(() => props.subscription, (subscription) => {
  if (subscription && props.show) {
    renewalData.value.callLimit = subscription.callLimit
    refreshUsageStats()
    refreshHistory()
  }
}, { immediate: true })
</script>

<style scoped>
.subscription-detail {
  padding: 0;
}

.status-section {
  margin-bottom: 24px;
}

.info-section {
  padding: 16px 0;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #666;
}

.usage-chart {
  margin-bottom: 24px;
}

.usage-stats {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action {
  font-weight: 600;
}

.time {
  font-size: 12px;
  color: #666;
}

.history-content p {
  margin: 0 0 8px 0;
  color: #666;
}

.history-details pre {
  background: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
}

.renewal-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.renewal-info {
  margin-bottom: 24px;
}

.renewal-form {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}
</style>
