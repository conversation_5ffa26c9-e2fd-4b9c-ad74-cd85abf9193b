<template>
  <Teleport to="body">
    <Transition name="slide-up">
      <div v-if="showIndicator" class="websocket-indicator" :class="statusClass">
        <div class="indicator-content">
          <n-icon :size="16" :class="iconClass">
            <component :is="statusIcon" />
          </n-icon>
          <span class="status-text">{{ statusText }}</span>
          <n-button
            v-if="status === 'error' || status === 'disconnected'"
            text
            size="small"
            @click="reconnect"
          >
            重连
          </n-button>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import {
  WifiOutline,
  SyncOutline,
  WarningOutline,
  CloseOutline
} from '@vicons/ionicons5'
import { useWebSocket } from '@/composables/useWebSocket'

const { status, connect } = useWebSocket()

// 显示指示器的条件
const showIndicator = ref(false)

// 状态样式类
const statusClass = computed(() => ({
  'status-connected': status.value === 'connected',
  'status-connecting': status.value === 'connecting' || status.value === 'reconnecting',
  'status-disconnected': status.value === 'disconnected',
  'status-error': status.value === 'error'
}))

// 状态图标
const statusIcon = computed(() => {
  switch (status.value) {
    case 'connected':
      return WifiOutline
    case 'connecting':
    case 'reconnecting':
      return SyncOutline
    case 'disconnected':
      return CloseOutline
    case 'error':
      return WarningOutline
    default:
      return CloseOutline
  }
})

// 图标样式类
const iconClass = computed(() => ({
  'spin': status.value === 'connecting' || status.value === 'reconnecting'
}))

// 状态文本
const statusText = computed(() => {
  switch (status.value) {
    case 'connected':
      return '实时连接已建立'
    case 'connecting':
      return '正在连接...'
    case 'reconnecting':
      return '正在重连...'
    case 'disconnected':
      return '实时连接已断开'
    case 'error':
      return '连接出现错误'
    default:
      return '未知状态'
  }
})

// 重连方法
const reconnect = () => {
  connect()
}

// 监听状态变化，控制指示器显示
watch(status, (newStatus, oldStatus) => {
  if (newStatus === 'connected') {
    // 连接成功时显示2秒后隐藏
    showIndicator.value = true
    setTimeout(() => {
      if (status.value === 'connected') {
        showIndicator.value = false
      }
    }, 2000)
  } else {
    // 其他状态时立即显示
    showIndicator.value = true
  }
}, { immediate: true })

// 连接状态变为已连接时，延迟隐藏指示器
watch(() => status.value === 'connected', (isConnected) => {
  if (isConnected) {
    setTimeout(() => {
      if (status.value === 'connected') {
        showIndicator.value = false
      }
    }, 2000)
  }
})
</script>

<style scoped>
.websocket-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  padding: 12px 16px;
  border-radius: 8px;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.status-connected {
  background: rgba(72, 219, 251, 0.1);
  color: #48dbfb;
  border-color: rgba(72, 219, 251, 0.3);
}

.status-connecting {
  background: rgba(254, 202, 87, 0.1);
  color: #feca57;
  border-color: rgba(254, 202, 87, 0.3);
}

.status-disconnected {
  background: rgba(255, 107, 107, 0.1);
  color: #ff6b6b;
  border-color: rgba(255, 107, 107, 0.3);
}

.status-error {
  background: rgba(255, 107, 107, 0.1);
  color: #ff6b6b;
  border-color: rgba(255, 107, 107, 0.3);
}

.indicator-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-text {
  white-space: nowrap;
}

.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(-20px);
}

.slide-up-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .websocket-indicator {
    top: 10px;
    right: 10px;
    left: 10px;
    padding: 10px 12px;
    font-size: 13px;
  }
  
  .indicator-content {
    justify-content: center;
  }
}
</style>
