<template>
  <div class="about-section">
    <div class="section-header">
      <h2>关于</h2>
      <p>系统信息和版本详情</p>
    </div>

    <!-- 系统信息 -->
    <div class="info-card">
      <div class="app-logo">
        <div class="logo-icon">
          <n-icon size="48" color="#667eea">
            <ServerOutline />
          </n-icon>
        </div>
        <div class="app-info">
          <h3>Nexus Admin Dashboard</h3>
          <p>MCP服务管理平台</p>
        </div>
      </div>

      <div class="version-info">
        <div class="info-item">
          <span class="info-label">版本</span>
          <span class="info-value">{{ systemInfo.version }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">构建时间</span>
          <span class="info-value">{{ systemInfo.buildTime }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">环境</span>
          <span class="info-value">{{ systemInfo.environment }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">Node.js版本</span>
          <span class="info-value">{{ systemInfo.nodeVersion }}</span>
        </div>
      </div>
    </div>

    <!-- 技术栈 -->
    <div class="tech-stack">
      <h3>技术栈</h3>
      <div class="tech-grid">
        <div class="tech-item">
          <n-icon size="24" color="#4fc08d">
            <LogoVue />
          </n-icon>
          <span>Vue 3</span>
        </div>
        <div class="tech-item">
          <n-icon size="24" color="#3178c6">
            <CodeSlashOutline />
          </n-icon>
          <span>TypeScript</span>
        </div>
        <div class="tech-item">
          <n-icon size="24" color="#646cff">
            <FlashOutline />
          </n-icon>
          <span>Vite</span>
        </div>
        <div class="tech-item">
          <n-icon size="24" color="#36ad6a">
            <LeafOutline />
          </n-icon>
          <span>Naive UI</span>
        </div>
      </div>
    </div>

    <!-- 系统状态 -->
    <div class="system-status">
      <h3>系统状态</h3>
      <div class="status-grid">
        <div class="status-item">
          <span class="status-label">运行时间</span>
          <span class="status-value">{{ formatUptime(systemStatus.uptime) }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">内存使用</span>
          <span class="status-value">{{ systemStatus.memoryUsage }}%</span>
        </div>
        <div class="status-item">
          <span class="status-label">活跃用户</span>
          <span class="status-value">{{ systemStatus.activeUsers }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">API调用</span>
          <span class="status-value">{{ systemStatus.apiCalls.toLocaleString() }}</span>
        </div>
      </div>
    </div>

    <!-- 链接 -->
    <div class="links-section">
      <h3>相关链接</h3>
      <div class="links-grid">
        <n-button text tag="a" href="#" target="_blank">
          <template #icon>
            <n-icon>
              <DocumentTextOutline />
            </n-icon>
          </template>
          用户手册
        </n-button>
        
        <n-button text tag="a" href="#" target="_blank">
          <template #icon>
            <n-icon>
              <LogoGithub />
            </n-icon>
          </template>
          GitHub仓库
        </n-button>
        
        <n-button text tag="a" href="#" target="_blank">
          <template #icon>
            <n-icon>
              <BugOutline />
            </n-icon>
          </template>
          问题反馈
        </n-button>
        
        <n-button text tag="a" href="#" target="_blank">
          <template #icon>
            <n-icon>
              <MailOutline />
            </n-icon>
          </template>
          联系我们
        </n-button>
      </div>
    </div>

    <!-- 许可证 -->
    <div class="license-section">
      <h3>许可证</h3>
      <p>
        本软件基于 MIT 许可证发布。详细信息请参阅项目根目录下的 LICENSE 文件。
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  ServerOutline,
  LogoVue,
  CodeSlashOutline,
  FlashOutline,
  LeafOutline,
  DocumentTextOutline,
  LogoGithub,
  BugOutline,
  MailOutline
} from '@vicons/ionicons5'

// 系统信息
const systemInfo = ref({
  version: '1.0.0',
  buildTime: '2024-01-20 14:30:00',
  environment: 'Production',
  nodeVersion: '18.17.0'
})

// 系统状态
const systemStatus = ref({
  uptime: 86400000, // 毫秒
  memoryUsage: 45,
  activeUsers: 23,
  apiCalls: 15847
})

// 格式化运行时间
const formatUptime = (uptime: number) => {
  const days = Math.floor(uptime / (24 * 60 * 60 * 1000))
  const hours = Math.floor((uptime % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000))
  const minutes = Math.floor((uptime % (60 * 60 * 1000)) / (60 * 1000))
  
  if (days > 0) {
    return `${days}天 ${hours}小时`
  } else if (hours > 0) {
    return `${hours}小时 ${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}
</script>

<style scoped>
.about-section {
  max-width: 800px;
}

.section-header {
  margin-bottom: 32px;
}

.section-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.section-header p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.info-card {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
}

.app-logo {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.app-info h3 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.app-info p {
  margin: 0;
  color: #666;
}

.version-info {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-label {
  color: #666;
  font-size: 14px;
}

.info-value {
  color: #333;
  font-weight: 600;
  font-size: 14px;
}

.tech-stack,
.system-status,
.links-section,
.license-section {
  margin-bottom: 32px;
}

.tech-stack h3,
.system-status h3,
.links-section h3,
.license-section h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.tech-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.tech-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
}

.tech-item span {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.status-label {
  color: #666;
  font-size: 14px;
}

.status-value {
  color: #333;
  font-weight: 600;
  font-size: 16px;
}

.links-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.license-section p {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .version-info {
    grid-template-columns: 1fr;
  }
  
  .tech-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .status-grid {
    grid-template-columns: 1fr;
  }
  
  .links-grid {
    grid-template-columns: 1fr;
  }
  
  .app-logo {
    flex-direction: column;
    text-align: center;
  }
}
</style>
