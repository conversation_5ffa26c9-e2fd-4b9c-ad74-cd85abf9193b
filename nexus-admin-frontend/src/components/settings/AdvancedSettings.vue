<template>
  <div class="advanced-settings">
    <div class="section-header">
      <h2>高级设置</h2>
      <p>开发者选项和实验性功能</p>
    </div>

    <n-form
      :model="formData"
      label-placement="top"
      size="medium"
    >
      <!-- 实验性功能 -->
      <div class="settings-group">
        <h3>实验性功能</h3>
        
        <div class="form-grid">
          <n-form-item label="启用实验性功能">
            <n-switch
              v-model:value="formData.experimentalFeatures"
              @update:value="handleChange"
            />
            <template #feedback>
              <span class="form-hint">启用可能不稳定的新功能</span>
            </template>
          </n-form-item>

          <n-form-item label="开发者模式">
            <n-switch
              v-model:value="formData.developerMode"
              @update:value="handleChange"
            />
            <template #feedback>
              <span class="form-hint">显示开发者工具和调试信息</span>
            </template>
          </n-form-item>
        </div>
      </div>

      <!-- 性能设置 -->
      <div class="settings-group">
        <h3>性能设置</h3>
        
        <div class="form-grid">
          <n-form-item label="API速率限制">
            <n-input-number
              v-model:value="formData.apiRateLimit"
              :min="100"
              :max="10000"
              style="width: 200px;"
              @update:value="handleChange"
            >
              <template #suffix>次/小时</template>
            </n-input-number>
          </n-form-item>

          <n-form-item label="缓存大小">
            <n-input-number
              v-model:value="formData.cacheSize"
              :min="64"
              :max="2048"
              style="width: 200px;"
              @update:value="handleChange"
            >
              <template #suffix>MB</template>
            </n-input-number>
          </n-form-item>
        </div>
      </div>

      <!-- 自定义代码 -->
      <div class="settings-group">
        <h3>自定义代码</h3>
        
        <n-form-item label="自定义CSS">
          <n-input
            v-model:value="formData.customCSS"
            type="textarea"
            placeholder="/* 在这里输入自定义CSS代码 */"
            :rows="6"
            @input="handleChange"
          />
          <template #feedback>
            <span class="form-hint">
              自定义CSS将应用到整个应用程序。请谨慎使用。
            </span>
          </template>
        </n-form-item>

        <n-form-item label="自定义JavaScript">
          <n-input
            v-model:value="formData.customJS"
            type="textarea"
            placeholder="// 在这里输入自定义JavaScript代码"
            :rows="6"
            @input="handleChange"
          />
          <template #feedback>
            <span class="form-hint">
              自定义JavaScript将在页面加载时执行。请确保代码安全。
            </span>
          </template>
        </n-form-item>
      </div>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps<{
  settings: any
}>()

const emit = defineEmits<{
  'update:settings': [value: any]
  change: []
}>()

// 表单数据
const formData = ref({
  experimentalFeatures: false,
  developerMode: false,
  apiRateLimit: 1000,
  cacheSize: 256,
  customCSS: '',
  customJS: ''
})

// 事件处理
const handleChange = () => {
  emit('update:settings', formData.value)
  emit('change')
}

// 监听props变化
watch(() => props.settings, (newSettings) => {
  if (newSettings) {
    formData.value = { ...formData.value, ...newSettings }
  }
}, { immediate: true, deep: true })
</script>

<style scoped>
.advanced-settings {
  max-width: 800px;
}

.section-header {
  margin-bottom: 32px;
}

.section-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.section-header p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.settings-group {
  margin-bottom: 40px;
  padding-bottom: 32px;
  border-bottom: 1px solid #f0f0f0;
}

.settings-group:last-child {
  border-bottom: none;
}

.settings-group h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.form-hint {
  font-size: 12px;
  color: #999;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
</style>
