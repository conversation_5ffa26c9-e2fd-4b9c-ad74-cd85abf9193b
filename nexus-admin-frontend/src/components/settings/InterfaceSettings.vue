<template>
  <div class="interface-settings">
    <div class="section-header">
      <h2>界面设置</h2>
      <p>自定义界面外观和交互体验</p>
    </div>

    <n-form
      :model="formData"
      label-placement="top"
      size="medium"
    >
      <!-- 主题设置 -->
      <div class="settings-group">
        <h3>主题设置</h3>
        
        <n-form-item label="主题模式">
          <n-radio-group v-model:value="formData.theme" @update:value="handleChange">
            <n-space direction="vertical">
              <n-radio value="light">
                <div class="theme-option">
                  <div class="theme-preview light-theme"></div>
                  <div class="theme-info">
                    <div class="theme-name">浅色主题</div>
                    <div class="theme-description">适合白天使用的明亮界面</div>
                  </div>
                </div>
              </n-radio>
              
              <n-radio value="dark">
                <div class="theme-option">
                  <div class="theme-preview dark-theme"></div>
                  <div class="theme-info">
                    <div class="theme-name">深色主题</div>
                    <div class="theme-description">适合夜间使用的暗色界面</div>
                  </div>
                </div>
              </n-radio>
              
              <n-radio value="auto">
                <div class="theme-option">
                  <div class="theme-preview auto-theme"></div>
                  <div class="theme-info">
                    <div class="theme-name">自动切换</div>
                    <div class="theme-description">根据系统设置自动切换主题</div>
                  </div>
                </div>
              </n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item>

        <n-form-item label="主色调">
          <div class="color-picker-section">
            <div class="preset-colors">
              <div
                v-for="color in presetColors"
                :key="color.value"
                class="color-option"
                :class="{ active: formData.primaryColor === color.value }"
                :style="{ backgroundColor: color.value }"
                @click="selectColor(color.value)"
              >
                <n-icon v-if="formData.primaryColor === color.value" color="white">
                  <CheckmarkOutline />
                </n-icon>
              </div>
            </div>
            
            <n-color-picker
              v-model:value="formData.primaryColor"
              :modes="['hex']"
              @update:value="handleChange"
            />
          </div>
        </n-form-item>
      </div>

      <!-- 布局设置 -->
      <div class="settings-group">
        <h3>布局设置</h3>
        
        <div class="form-grid">
          <n-form-item label="侧边栏">
            <n-switch
              v-model:value="formData.sidebarCollapsed"
              @update:value="handleChange"
            >
              <template #checked>展开</template>
              <template #unchecked>收起</template>
            </n-switch>
          </n-form-item>

          <n-form-item label="面包屑导航">
            <n-switch
              v-model:value="formData.showBreadcrumb"
              @update:value="handleChange"
            />
          </n-form-item>

          <n-form-item label="页面过渡动画">
            <n-switch
              v-model:value="formData.showPageTransition"
              @update:value="handleChange"
            />
          </n-form-item>

          <n-form-item label="显示网格线">
            <n-switch
              v-model:value="formData.showGrid"
              @update:value="handleChange"
            />
          </n-form-item>
        </div>

        <n-form-item label="内容密度">
          <n-radio-group v-model:value="formData.density" @update:value="handleChange">
            <n-space>
              <n-radio value="compact">紧凑</n-radio>
              <n-radio value="normal">正常</n-radio>
              <n-radio value="comfortable">舒适</n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item>
      </div>

      <!-- 字体设置 -->
      <div class="settings-group">
        <h3>字体设置</h3>
        
        <div class="form-grid">
          <n-form-item label="字体大小">
            <n-select
              v-model:value="formData.fontSize"
              :options="fontSizeOptions"
              @update:value="handleChange"
            />
          </n-form-item>

          <n-form-item label="字体族">
            <n-select
              v-model:value="formData.fontFamily"
              :options="fontFamilyOptions"
              @update:value="handleChange"
            />
          </n-form-item>
        </div>

        <n-form-item label="字体预览">
          <div class="font-preview" :style="fontPreviewStyle">
            <div class="preview-title">Nexus Admin Dashboard</div>
            <div class="preview-text">这是一个字体预览示例，包含中文和English文本。</div>
            <div class="preview-numbers">0123456789</div>
          </div>
        </n-form-item>
      </div>

      <!-- 动画设置 -->
      <div class="settings-group">
        <h3>动画设置</h3>
        
        <div class="form-grid">
          <n-form-item label="启用动画">
            <n-switch
              v-model:value="formData.enableAnimations"
              @update:value="handleChange"
            />
          </n-form-item>

          <n-form-item label="动画速度">
            <n-select
              v-model:value="formData.animationSpeed"
              :options="animationSpeedOptions"
              :disabled="!formData.enableAnimations"
              @update:value="handleChange"
            />
          </n-form-item>
        </div>

        <n-form-item label="减少动画" v-if="formData.enableAnimations">
          <n-switch
            v-model:value="formData.reduceMotion"
            @update:value="handleChange"
          />
          <template #feedback>
            <span class="form-hint">为运动敏感用户减少动画效果</span>
          </template>
        </n-form-item>
      </div>

      <!-- 可访问性设置 -->
      <div class="settings-group">
        <h3>可访问性</h3>
        
        <div class="form-grid">
          <n-form-item label="高对比度">
            <n-switch
              v-model:value="formData.highContrast"
              @update:value="handleChange"
            />
          </n-form-item>

          <n-form-item label="焦点指示器">
            <n-switch
              v-model:value="formData.focusIndicator"
              @update:value="handleChange"
            />
          </n-form-item>

          <n-form-item label="键盘导航">
            <n-switch
              v-model:value="formData.keyboardNavigation"
              @update:value="handleChange"
            />
          </n-form-item>

          <n-form-item label="屏幕阅读器支持">
            <n-switch
              v-model:value="formData.screenReader"
              @update:value="handleChange"
            />
          </n-form-item>
        </div>
      </div>

      <!-- 自定义CSS -->
      <div class="settings-group">
        <h3>自定义样式</h3>
        
        <n-form-item label="自定义CSS">
          <n-input
            v-model:value="formData.customCSS"
            type="textarea"
            placeholder="/* 在这里输入自定义CSS代码 */"
            :rows="8"
            @input="handleChange"
          />
          <template #feedback>
            <span class="form-hint">
              自定义CSS将应用到整个应用程序。请谨慎使用，错误的CSS可能影响界面显示。
            </span>
          </template>
        </n-form-item>
      </div>

      <!-- 重置选项 -->
      <div class="settings-group">
        <h3>重置选项</h3>
        
        <div class="reset-options">
          <n-button @click="resetToDefaults">
            重置为默认设置
          </n-button>
          
          <n-button @click="resetColors">
            重置颜色设置
          </n-button>
          
          <n-button @click="clearCustomCSS">
            清除自定义CSS
          </n-button>
        </div>
      </div>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useMessage } from 'naive-ui'
import { CheckmarkOutline } from '@vicons/ionicons5'

const props = defineProps<{
  settings: any
}>()

const emit = defineEmits<{
  'update:settings': [value: any]
  change: []
}>()

const message = useMessage()

// 表单数据
const formData = ref({
  theme: 'auto',
  primaryColor: '#667eea',
  sidebarCollapsed: false,
  showBreadcrumb: true,
  showPageTransition: true,
  showGrid: false,
  density: 'normal',
  fontSize: 'medium',
  fontFamily: 'system',
  enableAnimations: true,
  animationSpeed: 'normal',
  reduceMotion: false,
  highContrast: false,
  focusIndicator: true,
  keyboardNavigation: true,
  screenReader: false,
  customCSS: ''
})

// 预设颜色
const presetColors = [
  { name: '默认蓝', value: '#667eea' },
  { name: '科技蓝', value: '#1890ff' },
  { name: '成功绿', value: '#52c41a' },
  { name: '警告橙', value: '#faad14' },
  { name: '错误红', value: '#ff4d4f' },
  { name: '紫色', value: '#722ed1' },
  { name: '青色', value: '#13c2c2' },
  { name: '粉色', value: '#eb2f96' }
]

// 字体大小选项
const fontSizeOptions = [
  { label: '小号 (12px)', value: 'small' },
  { label: '中号 (14px)', value: 'medium' },
  { label: '大号 (16px)', value: 'large' },
  { label: '特大 (18px)', value: 'extra-large' }
]

// 字体族选项
const fontFamilyOptions = [
  { label: '系统默认', value: 'system' },
  { label: 'Helvetica', value: 'helvetica' },
  { label: 'Arial', value: 'arial' },
  { label: 'Times New Roman', value: 'times' },
  { label: 'Courier New', value: 'courier' },
  { label: '微软雅黑', value: 'microsoft-yahei' },
  { label: '苹方', value: 'pingfang' }
]

// 动画速度选项
const animationSpeedOptions = [
  { label: '慢速', value: 'slow' },
  { label: '正常', value: 'normal' },
  { label: '快速', value: 'fast' },
  { label: '极快', value: 'very-fast' }
]

// 计算属性
const fontPreviewStyle = computed(() => {
  const fontSizeMap = {
    small: '12px',
    medium: '14px',
    large: '16px',
    'extra-large': '18px'
  }

  const fontFamilyMap = {
    system: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    helvetica: 'Helvetica, Arial, sans-serif',
    arial: 'Arial, sans-serif',
    times: '"Times New Roman", Times, serif',
    courier: '"Courier New", Courier, monospace',
    'microsoft-yahei': '"Microsoft YaHei", sans-serif',
    pingfang: 'PingFang SC, sans-serif'
  }

  return {
    fontSize: fontSizeMap[formData.value.fontSize as keyof typeof fontSizeMap],
    fontFamily: fontFamilyMap[formData.value.fontFamily as keyof typeof fontFamilyMap]
  }
})

// 事件处理
const handleChange = () => {
  emit('update:settings', formData.value)
  emit('change')
}

const selectColor = (color: string) => {
  formData.value.primaryColor = color
  handleChange()
}

const resetToDefaults = () => {
  formData.value = {
    theme: 'auto',
    primaryColor: '#667eea',
    sidebarCollapsed: false,
    showBreadcrumb: true,
    showPageTransition: true,
    showGrid: false,
    density: 'normal',
    fontSize: 'medium',
    fontFamily: 'system',
    enableAnimations: true,
    animationSpeed: 'normal',
    reduceMotion: false,
    highContrast: false,
    focusIndicator: true,
    keyboardNavigation: true,
    screenReader: false,
    customCSS: ''
  }
  handleChange()
  message.success('已重置为默认设置')
}

const resetColors = () => {
  formData.value.primaryColor = '#667eea'
  handleChange()
  message.success('已重置颜色设置')
}

const clearCustomCSS = () => {
  formData.value.customCSS = ''
  handleChange()
  message.success('已清除自定义CSS')
}

// 监听props变化
watch(() => props.settings, (newSettings) => {
  if (newSettings) {
    formData.value = { ...formData.value, ...newSettings }
  }
}, { immediate: true, deep: true })
</script>

<style scoped>
.interface-settings {
  max-width: 800px;
}

.section-header {
  margin-bottom: 32px;
}

.section-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.section-header p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.settings-group {
  margin-bottom: 40px;
  padding-bottom: 32px;
  border-bottom: 1px solid #f0f0f0;
}

.settings-group:last-child {
  border-bottom: none;
}

.settings-group h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.theme-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.theme-preview {
  width: 40px;
  height: 24px;
  border-radius: 6px;
  border: 2px solid #e0e0e0;
}

.light-theme {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.dark-theme {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

.auto-theme {
  background: linear-gradient(135deg, #ffffff 0%, #ffffff 50%, #2c3e50 50%, #2c3e50 100%);
}

.theme-info {
  flex: 1;
}

.theme-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}

.theme-description {
  font-size: 12px;
  color: #666;
}

.color-picker-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.preset-colors {
  display: flex;
  gap: 8px;
}

.color-option {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.color-option:hover {
  transform: scale(1.1);
}

.color-option.active {
  border-color: #333;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.font-preview {
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}

.preview-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.preview-text {
  margin-bottom: 8px;
  color: #666;
  line-height: 1.5;
}

.preview-numbers {
  font-family: monospace;
  color: #999;
}

.form-hint {
  font-size: 12px;
  color: #999;
}

.reset-options {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .color-picker-section {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .reset-options {
    flex-direction: column;
  }
  
  .reset-options .n-button {
    width: 100%;
  }
}
</style>
