<template>
  <div class="notification-settings">
    <div class="section-header">
      <h2>通知设置</h2>
      <p>管理系统通知和提醒方式</p>
    </div>

    <n-form
      :model="formData"
      label-placement="top"
      size="medium"
    >
      <!-- 通知方式 -->
      <div class="settings-group">
        <h3>通知方式</h3>
        
        <div class="form-grid">
          <n-form-item label="桌面通知">
            <n-switch
              v-model:value="formData.desktop"
              @update:value="handleChange"
            />
            <template #feedback>
              <span class="form-hint">在浏览器中显示桌面通知</span>
            </template>
          </n-form-item>

          <n-form-item label="邮件通知">
            <n-switch
              v-model:value="formData.email"
              @update:value="handleChange"
            />
            <template #feedback>
              <span class="form-hint">发送邮件通知到注册邮箱</span>
            </template>
          </n-form-item>

          <n-form-item label="声音提醒">
            <n-switch
              v-model:value="formData.sound"
              @update:value="handleChange"
            />
            <template #feedback>
              <span class="form-hint">播放提示音</span>
            </template>
          </n-form-item>
        </div>
      </div>

      <!-- 通知类型 -->
      <div class="settings-group">
        <h3>通知类型</h3>
        
        <div class="notification-types">
          <n-form-item label="服务警报">
            <n-switch
              v-model:value="formData.serviceAlerts"
              @update:value="handleChange"
            />
            <template #feedback>
              <span class="form-hint">服务状态变更和异常警报</span>
            </template>
          </n-form-item>

          <n-form-item label="系统更新">
            <n-switch
              v-model:value="formData.systemUpdates"
              @update:value="handleChange"
            />
            <template #feedback>
              <span class="form-hint">系统版本更新和维护通知</span>
            </template>
          </n-form-item>

          <n-form-item label="安全警报">
            <n-switch
              v-model:value="formData.securityAlerts"
              @update:value="handleChange"
            />
            <template #feedback>
              <span class="form-hint">安全相关的重要通知</span>
            </template>
          </n-form-item>
        </div>
      </div>

      <!-- 通知频率 -->
      <div class="settings-group">
        <h3>通知频率</h3>
        
        <n-form-item label="通知频率">
          <n-radio-group v-model:value="formData.frequency" @update:value="handleChange">
            <n-space direction="vertical">
              <n-radio value="immediate">立即通知</n-radio>
              <n-radio value="hourly">每小时汇总</n-radio>
              <n-radio value="daily">每日汇总</n-radio>
              <n-radio value="weekly">每周汇总</n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item>
      </div>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps<{
  settings: any
}>()

const emit = defineEmits<{
  'update:settings': [value: any]
  change: []
}>()

// 表单数据
const formData = ref({
  desktop: true,
  email: false,
  sound: true,
  serviceAlerts: true,
  systemUpdates: true,
  securityAlerts: true,
  frequency: 'immediate'
})

// 事件处理
const handleChange = () => {
  emit('update:settings', formData.value)
  emit('change')
}

// 监听props变化
watch(() => props.settings, (newSettings) => {
  if (newSettings) {
    formData.value = { ...formData.value, ...newSettings }
  }
}, { immediate: true, deep: true })
</script>

<style scoped>
.notification-settings {
  max-width: 800px;
}

.section-header {
  margin-bottom: 32px;
}

.section-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.section-header p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.settings-group {
  margin-bottom: 40px;
  padding-bottom: 32px;
  border-bottom: 1px solid #f0f0f0;
}

.settings-group:last-child {
  border-bottom: none;
}

.settings-group h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.notification-types {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-hint {
  font-size: 12px;
  color: #999;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
</style>
