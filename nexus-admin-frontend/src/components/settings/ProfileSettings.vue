<template>
  <div class="profile-settings">
    <div class="section-header">
      <h2>个人资料</h2>
      <p>管理您的个人信息和账户设置</p>
    </div>

    <n-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-placement="top"
      size="medium"
    >
      <!-- 头像设置 -->
      <div class="avatar-section">
        <n-form-item label="头像">
          <div class="avatar-upload">
            <n-avatar
              :size="80"
              :src="formData.avatar"
              :fallback-src="defaultAvatar"
              class="avatar-preview"
            >
              {{ formData.displayName?.charAt(0) || formData.username?.charAt(0) || 'U' }}
            </n-avatar>
            
            <div class="avatar-actions">
              <n-upload
                :file-list="avatarFileList"
                :max="1"
                accept="image/*"
                @before-upload="handleAvatarUpload"
                @update:file-list="handleAvatarFileChange"
                :show-file-list="false"
              >
                <n-button size="small">
                  <template #icon>
                    <n-icon>
                      <CameraOutline />
                    </n-icon>
                  </template>
                  更换头像
                </n-button>
              </n-upload>
              
              <n-button
                size="small"
                quaternary
                @click="removeAvatar"
                v-if="formData.avatar"
              >
                移除头像
              </n-button>
            </div>
          </div>
        </n-form-item>
      </div>

      <!-- 基本信息 -->
      <div class="form-grid">
        <n-form-item label="用户名" path="username">
          <n-input
            v-model:value="formData.username"
            placeholder="输入用户名"
            :disabled="!canEditUsername"
            @input="handleChange"
          >
            <template #suffix>
              <n-icon v-if="!canEditUsername">
                <LockClosedOutline />
              </n-icon>
            </template>
          </n-input>
          <template #feedback>
            <span v-if="!canEditUsername" class="form-hint">
              用户名不可修改，如需更改请联系管理员
            </span>
          </template>
        </n-form-item>

        <n-form-item label="显示名称" path="displayName">
          <n-input
            v-model:value="formData.displayName"
            placeholder="输入显示名称"
            @input="handleChange"
          />
        </n-form-item>

        <n-form-item label="邮箱地址" path="email">
          <n-input
            v-model:value="formData.email"
            placeholder="输入邮箱地址"
            @input="handleChange"
          >
            <template #suffix>
              <n-tag
                v-if="emailVerified"
                type="success"
                size="small"
                :bordered="false"
              >
                已验证
              </n-tag>
              <n-tag
                v-else
                type="warning"
                size="small"
                :bordered="false"
              >
                未验证
              </n-tag>
            </template>
          </n-input>
          <template #feedback>
            <div v-if="!emailVerified" class="email-verification">
              <span class="verification-hint">邮箱未验证</span>
              <n-button
                text
                type="primary"
                size="small"
                @click="sendVerificationEmail"
                :loading="sendingVerification"
              >
                发送验证邮件
              </n-button>
            </div>
          </template>
        </n-form-item>

        <n-form-item label="手机号码" path="phone">
          <n-input
            v-model:value="formData.phone"
            placeholder="输入手机号码"
            @input="handleChange"
          />
        </n-form-item>
      </div>

      <!-- 地区设置 -->
      <div class="form-grid">
        <n-form-item label="时区" path="timezone">
          <n-select
            v-model:value="formData.timezone"
            :options="timezoneOptions"
            placeholder="选择时区"
            filterable
            @update:value="handleChange"
          />
        </n-form-item>

        <n-form-item label="语言" path="language">
          <n-select
            v-model:value="formData.language"
            :options="languageOptions"
            placeholder="选择语言"
            @update:value="handleChange"
          />
        </n-form-item>
      </div>

      <!-- 个人简介 -->
      <n-form-item label="个人简介" path="bio">
        <n-input
          v-model:value="formData.bio"
          type="textarea"
          placeholder="介绍一下自己..."
          :rows="4"
          maxlength="500"
          show-count
          @input="handleChange"
        />
      </n-form-item>

      <!-- 社交链接 -->
      <div class="social-links-section">
        <h3>社交链接</h3>
        <div class="social-links">
          <n-form-item label="GitHub">
            <n-input
              v-model:value="formData.socialLinks.github"
              placeholder="https://github.com/username"
              @input="handleChange"
            >
              <template #prefix>
                <n-icon>
                  <LogoGithub />
                </n-icon>
              </template>
            </n-input>
          </n-form-item>

          <n-form-item label="LinkedIn">
            <n-input
              v-model:value="formData.socialLinks.linkedin"
              placeholder="https://linkedin.com/in/username"
              @input="handleChange"
            >
              <template #prefix>
                <n-icon>
                  <LogoLinkedin />
                </n-icon>
              </template>
            </n-input>
          </n-form-item>

          <n-form-item label="Twitter">
            <n-input
              v-model:value="formData.socialLinks.twitter"
              placeholder="https://twitter.com/username"
              @input="handleChange"
            >
              <template #prefix>
                <n-icon>
                  <LogoTwitter />
                </n-icon>
              </template>
            </n-input>
          </n-form-item>

          <n-form-item label="个人网站">
            <n-input
              v-model:value="formData.socialLinks.website"
              placeholder="https://yourwebsite.com"
              @input="handleChange"
            >
              <template #prefix>
                <n-icon>
                  <GlobeOutline />
                </n-icon>
              </template>
            </n-input>
          </n-form-item>
        </div>
      </div>

      <!-- 账户统计 -->
      <div class="account-stats">
        <h3>账户统计</h3>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-label">注册时间</span>
            <span class="stat-value">{{ formatDate(accountStats.createdAt) }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">最后登录</span>
            <span class="stat-value">{{ formatDate(accountStats.lastLogin) }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">登录次数</span>
            <span class="stat-value">{{ accountStats.loginCount }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">账户状态</span>
            <n-tag :type="getAccountStatusType(accountStats.status)">
              {{ getAccountStatusText(accountStats.status) }}
            </n-tag>
          </div>
        </div>
      </div>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useMessage } from 'naive-ui'
import {
  CameraOutline,
  LockClosedOutline,
  LogoGithub,
  LogoLinkedin,
  LogoTwitter,
  GlobeOutline
} from '@vicons/ionicons5'
import type { FormInst, FormRules, UploadFileInfo } from 'naive-ui'

const props = defineProps<{
  settings: any
}>()

const emit = defineEmits<{
  'update:settings': [value: any]
  change: []
}>()

const message = useMessage()

// 响应式数据
const formRef = ref<FormInst | null>(null)
const sendingVerification = ref(false)
const avatarFileList = ref<UploadFileInfo[]>([])

// 表单数据
const formData = ref({
  username: '',
  email: '',
  displayName: '',
  phone: '',
  avatar: '',
  timezone: 'Asia/Shanghai',
  language: 'zh-CN',
  bio: '',
  socialLinks: {
    github: '',
    linkedin: '',
    twitter: '',
    website: ''
  }
})

// 账户统计数据
const accountStats = ref({
  createdAt: '2024-01-15T08:00:00Z',
  lastLogin: '2024-01-20T14:30:00Z',
  loginCount: 156,
  status: 'active'
})

// 配置选项
const canEditUsername = ref(false)
const emailVerified = ref(false)
const defaultAvatar = '/default-avatar.png'

// 时区选项
const timezoneOptions = [
  { label: '北京时间 (UTC+8)', value: 'Asia/Shanghai' },
  { label: '东京时间 (UTC+9)', value: 'Asia/Tokyo' },
  { label: '纽约时间 (UTC-5)', value: 'America/New_York' },
  { label: '伦敦时间 (UTC+0)', value: 'Europe/London' },
  { label: '洛杉矶时间 (UTC-8)', value: 'America/Los_Angeles' }
]

// 语言选项
const languageOptions = [
  { label: '简体中文', value: 'zh-CN' },
  { label: '繁体中文', value: 'zh-TW' },
  { label: 'English', value: 'en-US' },
  { label: '日本語', value: 'ja-JP' },
  { label: '한국어', value: 'ko-KR' }
]

// 表单验证规则
const formRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度应在3-20个字符之间', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ],
  displayName: [
    { required: true, message: '请输入显示名称', trigger: 'blur' },
    { max: 50, message: '显示名称不能超过50个字符', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码', trigger: 'blur' }
  ]
}

// 工具函数
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const getAccountStatusType = (status: string) => {
  const typeMap: Record<string, any> = {
    active: 'success',
    inactive: 'warning',
    suspended: 'error',
    pending: 'info'
  }
  return typeMap[status] || 'default'
}

const getAccountStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    active: '正常',
    inactive: '未激活',
    suspended: '已暂停',
    pending: '待审核'
  }
  return textMap[status] || status
}

// 事件处理
const handleChange = () => {
  emit('update:settings', formData.value)
  emit('change')
}

const handleAvatarUpload = (data: { file: UploadFileInfo; fileList: UploadFileInfo[] }) => {
  const { file } = data
  
  // 验证文件类型
  if (!file.file?.type.startsWith('image/')) {
    message.error('请选择图片文件')
    return false
  }
  
  // 验证文件大小（2MB）
  if ((file.file?.size || 0) > 2 * 1024 * 1024) {
    message.error('图片大小不能超过 2MB')
    return false
  }
  
  // 创建预览URL
  const reader = new FileReader()
  reader.onload = (e) => {
    formData.value.avatar = e.target?.result as string
    handleChange()
  }
  reader.readAsDataURL(file.file!)
  
  return false // 阻止自动上传
}

const handleAvatarFileChange = (fileList: UploadFileInfo[]) => {
  avatarFileList.value = fileList
}

const removeAvatar = () => {
  formData.value.avatar = ''
  avatarFileList.value = []
  handleChange()
}

const sendVerificationEmail = async () => {
  if (!formData.value.email) {
    message.warning('请先输入邮箱地址')
    return
  }

  sendingVerification.value = true
  try {
    // 模拟发送验证邮件
    await new Promise(resolve => setTimeout(resolve, 2000))
    message.success('验证邮件已发送，请查收')
  } catch (error) {
    message.error('发送验证邮件失败')
  } finally {
    sendingVerification.value = false
  }
}

// 监听props变化
watch(() => props.settings, (newSettings) => {
  if (newSettings) {
    formData.value = { ...formData.value, ...newSettings }
  }
}, { immediate: true, deep: true })
</script>

<style scoped>
.profile-settings {
  max-width: 800px;
}

.section-header {
  margin-bottom: 32px;
}

.section-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.section-header p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.avatar-section {
  margin-bottom: 32px;
}

.avatar-upload {
  display: flex;
  align-items: center;
  gap: 16px;
}

.avatar-preview {
  flex-shrink: 0;
}

.avatar-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.form-hint {
  font-size: 12px;
  color: #999;
}

.email-verification {
  display: flex;
  align-items: center;
  gap: 8px;
}

.verification-hint {
  font-size: 12px;
  color: #faad14;
}

.social-links-section {
  margin: 32px 0;
}

.social-links-section h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.social-links {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.account-stats {
  margin-top: 32px;
  padding-top: 32px;
  border-top: 1px solid #f0f0f0;
}

.account-stats h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.stat-value {
  font-size: 14px;
  color: #333;
  font-weight: 600;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .social-links {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .avatar-upload {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
