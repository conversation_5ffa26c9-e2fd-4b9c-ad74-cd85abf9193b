<template>
  <div class="security-settings">
    <div class="section-header">
      <h2>安全设置</h2>
      <p>管理账户安全和访问控制</p>
    </div>

    <n-form
      :model="formData"
      label-placement="top"
      size="medium"
    >
      <!-- 双因子认证 -->
      <div class="settings-group">
        <h3>双因子认证</h3>
        
        <n-form-item label="启用双因子认证">
          <n-switch
            v-model:value="formData.twoFactorEnabled"
            @update:value="handleChange"
          />
          <template #feedback>
            <span class="form-hint">使用手机应用生成验证码进行二次验证</span>
          </template>
        </n-form-item>
      </div>

      <!-- 会话管理 -->
      <div class="settings-group">
        <h3>会话管理</h3>
        
        <div class="form-grid">
          <n-form-item label="会话超时时间">
            <n-input-number
              v-model:value="formData.sessionTimeout"
              :min="5"
              :max="480"
              style="width: 200px;"
              @update:value="handleChange"
            >
              <template #suffix>分钟</template>
            </n-input-number>
          </n-form-item>

          <n-form-item label="密码有效期">
            <n-input-number
              v-model:value="formData.passwordExpiry"
              :min="30"
              :max="365"
              style="width: 200px;"
              @update:value="handleChange"
            >
              <template #suffix>天</template>
            </n-input-number>
          </n-form-item>
        </div>
      </div>

      <!-- 登录安全 -->
      <div class="settings-group">
        <h3>登录安全</h3>
        
        <n-form-item label="登录通知">
          <n-switch
            v-model:value="formData.loginNotifications"
            @update:value="handleChange"
          />
          <template #feedback>
            <span class="form-hint">新设备登录时发送通知</span>
          </template>
        </n-form-item>
      </div>

      <!-- API密钥管理 -->
      <div class="settings-group">
        <h3>API密钥管理</h3>
        
        <n-form-item label="API密钥有效期">
          <n-input-number
            v-model:value="formData.apiKeyExpiry"
            :min="30"
            :max="730"
            style="width: 200px;"
            @update:value="handleChange"
          >
            <template #suffix>天</template>
          </n-input-number>
        </n-form-item>
      </div>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps<{
  settings: any
}>()

const emit = defineEmits<{
  'update:settings': [value: any]
  change: []
}>()

// 表单数据
const formData = ref({
  twoFactorEnabled: false,
  sessionTimeout: 30,
  passwordExpiry: 90,
  loginNotifications: true,
  ipWhitelist: [],
  apiKeyExpiry: 365
})

// 事件处理
const handleChange = () => {
  emit('update:settings', formData.value)
  emit('change')
}

// 监听props变化
watch(() => props.settings, (newSettings) => {
  if (newSettings) {
    formData.value = { ...formData.value, ...newSettings }
  }
}, { immediate: true, deep: true })
</script>

<style scoped>
.security-settings {
  max-width: 800px;
}

.section-header {
  margin-bottom: 32px;
}

.section-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.section-header p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.settings-group {
  margin-bottom: 40px;
  padding-bottom: 32px;
  border-bottom: 1px solid #f0f0f0;
}

.settings-group:last-child {
  border-bottom: none;
}

.settings-group h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.form-hint {
  font-size: 12px;
  color: #999;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
</style>
