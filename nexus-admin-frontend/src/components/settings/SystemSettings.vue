<template>
  <div class="system-settings">
    <div class="section-header">
      <h2>系统配置</h2>
      <p>管理系统运行参数和维护设置</p>
    </div>

    <n-form
      :model="formData"
      label-placement="top"
      size="medium"
    >
      <!-- 日志设置 -->
      <div class="settings-group">
        <h3>日志设置</h3>
        
        <div class="form-grid">
          <n-form-item label="日志级别">
            <n-select
              v-model:value="formData.logLevel"
              :options="logLevelOptions"
              @update:value="handleChange"
            />
          </n-form-item>

          <n-form-item label="最大日志大小">
            <n-input-number
              v-model:value="formData.maxLogSize"
              :min="10"
              :max="1000"
              style="width: 200px;"
              @update:value="handleChange"
            >
              <template #suffix>MB</template>
            </n-input-number>
          </n-form-item>
        </div>
      </div>

      <!-- 备份设置 -->
      <div class="settings-group">
        <h3>备份设置</h3>
        
        <div class="form-grid">
          <n-form-item label="启用自动备份">
            <n-switch
              v-model:value="formData.backupEnabled"
              @update:value="handleChange"
            />
          </n-form-item>

          <n-form-item label="备份间隔" v-if="formData.backupEnabled">
            <n-input-number
              v-model:value="formData.backupInterval"
              :min="1"
              :max="168"
              style="width: 200px;"
              @update:value="handleChange"
            >
              <template #suffix>小时</template>
            </n-input-number>
          </n-form-item>
        </div>
      </div>

      <!-- 系统模式 -->
      <div class="settings-group">
        <h3>系统模式</h3>
        
        <div class="form-grid">
          <n-form-item label="维护模式">
            <n-switch
              v-model:value="formData.maintenanceMode"
              @update:value="handleChange"
            />
            <template #feedback>
              <span class="form-hint">启用后将阻止普通用户访问</span>
            </template>
          </n-form-item>

          <n-form-item label="调试模式">
            <n-switch
              v-model:value="formData.debugMode"
              @update:value="handleChange"
            />
            <template #feedback>
              <span class="form-hint">启用详细的调试信息输出</span>
            </template>
          </n-form-item>
        </div>
      </div>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps<{
  settings: any
}>()

const emit = defineEmits<{
  'update:settings': [value: any]
  change: []
}>()

// 表单数据
const formData = ref({
  logLevel: 'info',
  maxLogSize: 100,
  backupEnabled: true,
  backupInterval: 24,
  maintenanceMode: false,
  debugMode: false
})

// 日志级别选项
const logLevelOptions = [
  { label: 'ERROR', value: 'error' },
  { label: 'WARN', value: 'warn' },
  { label: 'INFO', value: 'info' },
  { label: 'DEBUG', value: 'debug' },
  { label: 'TRACE', value: 'trace' }
]

// 事件处理
const handleChange = () => {
  emit('update:settings', formData.value)
  emit('change')
}

// 监听props变化
watch(() => props.settings, (newSettings) => {
  if (newSettings) {
    formData.value = { ...formData.value, ...newSettings }
  }
}, { immediate: true, deep: true })
</script>

<style scoped>
.system-settings {
  max-width: 800px;
}

.section-header {
  margin-bottom: 32px;
}

.section-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.section-header p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.settings-group {
  margin-bottom: 40px;
  padding-bottom: 32px;
  border-bottom: 1px solid #f0f0f0;
}

.settings-group:last-child {
  border-bottom: none;
}

.settings-group h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.form-hint {
  font-size: 12px;
  color: #999;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
</style>
