/**
 * 全局快捷键管理组合式函数
 */
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useThemeStore } from '@/stores/theme'
import { useAuthStore } from '@/stores/auth'

interface ShortcutAction {
  key: string
  description: string
  action: () => void
  category: string
  enabled: boolean
}

interface ShortcutCategory {
  name: string
  label: string
  shortcuts: ShortcutAction[]
}

export function useGlobalShortcuts() {
  const router = useRouter()
  const themeStore = useThemeStore()
  const authStore = useAuthStore()

  // 快捷键状态
  const isEnabled = ref(true)
  const pressedKeys = ref<Set<string>>(new Set())
  const shortcutHistory = ref<string[]>([])

  // 快捷键配置
  const shortcuts = ref<ShortcutCategory[]>([
    {
      name: 'navigation',
      label: '导航',
      shortcuts: [
        {
          key: 'ctrl+1',
          description: '跳转到仪表板',
          action: () => router.push('/'),
          category: 'navigation',
          enabled: true
        },
        {
          key: 'ctrl+2',
          description: '跳转到MCP服务',
          action: () => router.push('/mcp/overview'),
          category: 'navigation',
          enabled: true
        },
        {
          key: 'ctrl+3',
          description: '跳转到订阅管理',
          action: () => router.push('/subscriptions'),
          category: 'navigation',
          enabled: true
        },
        {
          key: 'ctrl+4',
          description: '跳转到实时监控',
          action: () => router.push('/monitoring/realtime'),
          category: 'navigation',
          enabled: true
        },
        {
          key: 'ctrl+5',
          description: '跳转到接口测试',
          action: () => router.push('/testing'),
          category: 'navigation',
          enabled: true
        },
        {
          key: 'ctrl+shift+s',
          description: '跳转到系统设置',
          action: () => router.push('/settings/profile'),
          category: 'navigation',
          enabled: true
        }
      ]
    },
    {
      name: 'interface',
      label: '界面',
      shortcuts: [
        {
          key: 'ctrl+b',
          description: '切换侧边栏',
          action: () => themeStore.toggleSidebar(),
          category: 'interface',
          enabled: true
        },
        {
          key: 'ctrl+shift+t',
          description: '切换主题',
          action: () => themeStore.toggleTheme(),
          category: 'interface',
          enabled: true
        },
        {
          key: 'ctrl+shift+f',
          description: '切换全屏',
          action: () => toggleFullscreen(),
          category: 'interface',
          enabled: true
        },
        {
          key: 'ctrl+shift+r',
          description: '重置布局',
          action: () => resetLayout(),
          category: 'interface',
          enabled: true
        }
      ]
    },
    {
      name: 'search',
      label: '搜索',
      shortcuts: [
        {
          key: 'ctrl+k',
          description: '打开全局搜索',
          action: () => triggerGlobalSearch(),
          category: 'search',
          enabled: true
        },
        {
          key: 'ctrl+shift+k',
          description: '打开命令面板',
          action: () => triggerCommandPalette(),
          category: 'search',
          enabled: true
        },
        {
          key: 'ctrl+f',
          description: '页面内搜索',
          action: () => triggerPageSearch(),
          category: 'search',
          enabled: true
        }
      ]
    },
    {
      name: 'actions',
      label: '操作',
      shortcuts: [
        {
          key: 'ctrl+n',
          description: '新建项目',
          action: () => triggerNewItem(),
          category: 'actions',
          enabled: true
        },
        {
          key: 'ctrl+s',
          description: '保存当前内容',
          action: () => triggerSave(),
          category: 'actions',
          enabled: true
        },
        {
          key: 'ctrl+shift+n',
          description: '新建集合',
          action: () => triggerNewCollection(),
          category: 'actions',
          enabled: true
        },
        {
          key: 'f5',
          description: '刷新页面',
          action: () => window.location.reload(),
          category: 'actions',
          enabled: true
        }
      ]
    },
    {
      name: 'help',
      label: '帮助',
      shortcuts: [
        {
          key: 'f1',
          description: '打开帮助',
          action: () => triggerHelp(),
          category: 'help',
          enabled: true
        },
        {
          key: 'ctrl+shift+?',
          description: '显示快捷键列表',
          action: () => triggerShortcutHelp(),
          category: 'help',
          enabled: true
        }
      ]
    },
    {
      name: 'system',
      label: '系统',
      shortcuts: [
        {
          key: 'ctrl+shift+l',
          description: '退出登录',
          action: () => handleLogout(),
          category: 'system',
          enabled: true
        },
        {
          key: 'ctrl+shift+d',
          description: '开发者工具',
          action: () => toggleDevTools(),
          category: 'system',
          enabled: true
        }
      ]
    }
  ])

  // 获取所有快捷键的扁平列表
  const allShortcuts = computed(() => {
    return shortcuts.value.flatMap(category => category.shortcuts)
  })

  // 获取启用的快捷键
  const enabledShortcuts = computed(() => {
    return allShortcuts.value.filter(shortcut => shortcut.enabled)
  })

  // 快捷键映射
  const shortcutMap = computed(() => {
    const map = new Map<string, ShortcutAction>()
    enabledShortcuts.value.forEach(shortcut => {
      map.set(normalizeKey(shortcut.key), shortcut)
    })
    return map
  })

  // 标准化按键组合
  const normalizeKey = (key: string): string => {
    return key.toLowerCase()
      .replace(/\s+/g, '')
      .split('+')
      .sort((a, b) => {
        const order = ['ctrl', 'alt', 'shift', 'meta']
        const aIndex = order.indexOf(a)
        const bIndex = order.indexOf(b)
        if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex
        if (aIndex !== -1) return -1
        if (bIndex !== -1) return 1
        return a.localeCompare(b)
      })
      .join('+')
  }

  // 获取当前按键组合
  const getCurrentKeyCombo = (): string => {
    const keys: string[] = []
    
    if (pressedKeys.value.has('Control')) keys.push('ctrl')
    if (pressedKeys.value.has('Alt')) keys.push('alt')
    if (pressedKeys.value.has('Shift')) keys.push('shift')
    if (pressedKeys.value.has('Meta')) keys.push('meta')
    
    // 添加非修饰键
    for (const key of pressedKeys.value) {
      if (!['Control', 'Alt', 'Shift', 'Meta'].includes(key)) {
        keys.push(key.toLowerCase())
      }
    }
    
    return keys.join('+')
  }

  // 键盘事件处理
  const handleKeyDown = (event: KeyboardEvent) => {
    if (!isEnabled.value) return

    // 忽略在输入框中的按键
    const target = event.target as HTMLElement
    if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {
      return
    }

    pressedKeys.value.add(event.key)
    
    const keyCombo = getCurrentKeyCombo()
    const shortcut = shortcutMap.value.get(keyCombo)
    
    if (shortcut) {
      event.preventDefault()
      event.stopPropagation()
      
      // 记录快捷键使用历史
      shortcutHistory.value.unshift(keyCombo)
      if (shortcutHistory.value.length > 50) {
        shortcutHistory.value = shortcutHistory.value.slice(0, 50)
      }
      
      // 执行快捷键动作
      try {
        shortcut.action()
      } catch (error) {
        console.error('快捷键执行失败:', error)
      }
    }
  }

  const handleKeyUp = (event: KeyboardEvent) => {
    pressedKeys.value.delete(event.key)
  }

  // 快捷键动作实现
  const toggleFullscreen = () => {
    if (document.fullscreenElement) {
      document.exitFullscreen()
    } else {
      document.documentElement.requestFullscreen()
    }
  }

  const resetLayout = () => {
    themeStore.resetLayout()
    window.dispatchEvent(new CustomEvent('layout-reset'))
  }

  const triggerGlobalSearch = () => {
    window.dispatchEvent(new CustomEvent('global-search'))
  }

  const triggerCommandPalette = () => {
    window.dispatchEvent(new CustomEvent('command-palette'))
  }

  const triggerPageSearch = () => {
    // 触发浏览器默认搜索
    const searchEvent = new KeyboardEvent('keydown', {
      key: 'f',
      ctrlKey: true,
      bubbles: true
    })
    document.dispatchEvent(searchEvent)
  }

  const triggerNewItem = () => {
    window.dispatchEvent(new CustomEvent('new-item'))
  }

  const triggerSave = () => {
    window.dispatchEvent(new CustomEvent('save-current'))
  }

  const triggerNewCollection = () => {
    window.dispatchEvent(new CustomEvent('new-collection'))
  }

  const triggerHelp = () => {
    window.dispatchEvent(new CustomEvent('show-help'))
  }

  const triggerShortcutHelp = () => {
    window.dispatchEvent(new CustomEvent('show-shortcuts'))
  }

  const handleLogout = async () => {
    try {
      await authStore.logout()
      router.push('/login')
    } catch (error) {
      console.error('退出登录失败:', error)
    }
  }

  const toggleDevTools = () => {
    // 在开发环境中打开开发者工具
    if (import.meta.env.DEV) {
      console.log('开发者工具快捷键触发')
    }
  }

  // 快捷键管理方法
  const enableShortcut = (key: string) => {
    const shortcut = allShortcuts.value.find(s => s.key === key)
    if (shortcut) {
      shortcut.enabled = true
    }
  }

  const disableShortcut = (key: string) => {
    const shortcut = allShortcuts.value.find(s => s.key === key)
    if (shortcut) {
      shortcut.enabled = false
    }
  }

  const toggleShortcut = (key: string) => {
    const shortcut = allShortcuts.value.find(s => s.key === key)
    if (shortcut) {
      shortcut.enabled = !shortcut.enabled
    }
  }

  const addCustomShortcut = (shortcut: Omit<ShortcutAction, 'enabled'>) => {
    const category = shortcuts.value.find(c => c.name === shortcut.category)
    if (category) {
      category.shortcuts.push({ ...shortcut, enabled: true })
    }
  }

  const removeCustomShortcut = (key: string) => {
    shortcuts.value.forEach(category => {
      const index = category.shortcuts.findIndex(s => s.key === key)
      if (index !== -1) {
        category.shortcuts.splice(index, 1)
      }
    })
  }

  // 获取快捷键统计
  const getShortcutStats = () => {
    return {
      total: allShortcuts.value.length,
      enabled: enabledShortcuts.value.length,
      disabled: allShortcuts.value.length - enabledShortcuts.value.length,
      categories: shortcuts.value.length,
      mostUsed: shortcutHistory.value.slice(0, 5)
    }
  }

  // 生命周期管理
  onMounted(() => {
    document.addEventListener('keydown', handleKeyDown)
    document.addEventListener('keyup', handleKeyUp)
    
    // 清除按键状态（防止页面切换时按键状态残留）
    window.addEventListener('blur', () => {
      pressedKeys.value.clear()
    })
  })

  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeyDown)
    document.removeEventListener('keyup', handleKeyUp)
  })

  return {
    // 状态
    isEnabled,
    shortcuts,
    shortcutHistory,
    
    // 计算属性
    allShortcuts,
    enabledShortcuts,
    
    // 方法
    enableShortcut,
    disableShortcut,
    toggleShortcut,
    addCustomShortcut,
    removeCustomShortcut,
    getShortcutStats,
    
    // 控制
    enable: () => { isEnabled.value = true },
    disable: () => { isEnabled.value = false },
    toggle: () => { isEnabled.value = !isEnabled.value }
  }
}

export default useGlobalShortcuts
