/**
 * 实时数据管理组合式函数
 */
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useWebSocket, TOPICS, type TopicType } from './useWebSocket'
import { useRealtimeStore } from '@/stores/realtime'
import type { RealtimeMessage } from '@/types/api'

export function useRealtimeData() {
  const { subscribe, unsubscribe, isConnected, sendMessage } = useWebSocket()
  const realtimeStore = useRealtimeStore()

  // 订阅管理
  const subscriptions = ref<Set<TopicType>>(new Set())

  // 订阅主题
  const subscribeToTopic = (topic: TopicType) => {
    if (subscriptions.value.has(topic)) return

    const subscription = subscribe(topic, (message: RealtimeMessage) => {
      realtimeStore.addMessage(message)
    })

    if (subscription) {
      subscriptions.value.add(topic)
      console.log(`已订阅主题: ${topic}`)
    }
  }

  // 取消订阅主题
  const unsubscribeFromTopic = (topic: TopicType) => {
    if (!subscriptions.value.has(topic)) return

    unsubscribe(topic)
    subscriptions.value.delete(topic)
    console.log(`已取消订阅主题: ${topic}`)
  }

  // 订阅所有主题
  const subscribeToAllTopics = () => {
    Object.values(TOPICS).forEach(topic => {
      subscribeToTopic(topic)
    })
  }

  // 取消所有订阅
  const unsubscribeFromAllTopics = () => {
    subscriptions.value.forEach(topic => {
      unsubscribeFromTopic(topic)
    })
  }

  // 发送消息到特定主题
  const sendToTopic = (topic: string, message: RealtimeMessage) => {
    return sendMessage(`/app/topic/${topic}`, message)
  }

  // 发送广播消息
  const broadcast = (message: RealtimeMessage) => {
    return sendMessage('/app/broadcast', message)
  }

  // 清理函数
  onUnmounted(() => {
    unsubscribeFromAllTopics()
  })

  return {
    // 状态
    isConnected,
    subscriptions: computed(() => Array.from(subscriptions.value)),
    
    // 方法
    subscribeToTopic,
    unsubscribeFromTopic,
    subscribeToAllTopics,
    unsubscribeFromAllTopics,
    sendToTopic,
    broadcast
  }
}

// 服务状态监控
export function useServiceMonitoring() {
  const { subscribeToTopic } = useRealtimeData()
  const realtimeStore = useRealtimeStore()

  // 自动订阅服务状态更新
  onMounted(() => {
    subscribeToTopic(TOPICS.SERVICE_STATUS)
  })

  return {
    serviceStatuses: computed(() => realtimeStore.serviceStatuses),
    activeServicesCount: computed(() => realtimeStore.activeServicesCount)
  }
}

// API调用监控
export function useApiCallMonitoring() {
  const { subscribeToTopic } = useRealtimeData()
  const realtimeStore = useRealtimeStore()

  onMounted(() => {
    subscribeToTopic(TOPICS.API_CALLS)
  })

  return {
    apiCallStats: computed(() => realtimeStore.apiCallStats),
    apiSuccessRate: computed(() => realtimeStore.apiSuccessRate),
    recentApiCalls: computed(() => realtimeStore.apiCallStats.recentCalls)
  }
}

// 工具执行监控
export function useToolExecutionMonitoring() {
  const { subscribeToTopic } = useRealtimeData()
  const realtimeStore = useRealtimeStore()

  onMounted(() => {
    subscribeToTopic(TOPICS.TOOL_EXECUTION)
  })

  return {
    toolExecutions: computed(() => realtimeStore.toolExecutions),
    activeExecutions: computed(() => realtimeStore.activeToolExecutions),
    
    // 获取特定工具执行状态
    getExecutionStatus: (taskId: string) => {
      return computed(() => realtimeStore.toolExecutions[taskId])
    }
  }
}

// 订阅更新监控
export function useSubscriptionMonitoring() {
  const { subscribeToTopic } = useRealtimeData()
  const realtimeStore = useRealtimeStore()

  onMounted(() => {
    subscribeToTopic(TOPICS.SUBSCRIPTION_UPDATES)
  })

  return {
    subscriptionUpdates: computed(() => realtimeStore.subscriptionUpdates),
    recentUpdates: computed(() => realtimeStore.subscriptionUpdates.slice(0, 10))
  }
}

// 系统告警监控
export function useSystemAlerts() {
  const { subscribeToTopic } = useRealtimeData()
  const realtimeStore = useRealtimeStore()

  onMounted(() => {
    subscribeToTopic(TOPICS.SYSTEM_ALERTS)
  })

  const acknowledgeAlert = (alertId: string) => {
    realtimeStore.acknowledgeAlert(alertId)
  }

  const acknowledgeAllAlerts = () => {
    realtimeStore.acknowledgeAllAlerts()
  }

  return {
    systemAlerts: computed(() => realtimeStore.systemAlerts),
    unacknowledgedAlerts: computed(() => realtimeStore.unacknowledgedAlerts),
    acknowledgeAlert,
    acknowledgeAllAlerts
  }
}

// 用户通知管理
export function useUserNotifications() {
  const { subscribeToTopic } = useRealtimeData()
  const realtimeStore = useRealtimeStore()

  onMounted(() => {
    subscribeToTopic(TOPICS.USER_NOTIFICATIONS)
  })

  const markAsRead = (messageId: string) => {
    realtimeStore.markNotificationAsRead(messageId)
  }

  const markAllAsRead = () => {
    realtimeStore.markAllNotificationsAsRead()
  }

  return {
    notifications: computed(() => realtimeStore.notifications),
    unreadCount: computed(() => realtimeStore.unreadNotificationsCount),
    markAsRead,
    markAllAsRead
  }
}

// 实时活动监控
export function useRealtimeActivities() {
  const realtimeStore = useRealtimeStore()

  return {
    recentActivities: computed(() => realtimeStore.recentActivities),
    connectionUptime: computed(() => realtimeStore.connectionUptime),
    isConnected: computed(() => realtimeStore.isConnected)
  }
}

// 实时数据清理
export function useDataCleanup() {
  const realtimeStore = useRealtimeStore()

  // 定期清理旧数据
  const startCleanupTimer = () => {
    return setInterval(() => {
      realtimeStore.clearOldData()
    }, 5 * 60 * 1000) // 每5分钟清理一次
  }

  let cleanupTimer: NodeJS.Timeout | null = null

  onMounted(() => {
    cleanupTimer = startCleanupTimer()
  })

  onUnmounted(() => {
    if (cleanupTimer) {
      clearInterval(cleanupTimer)
    }
  })

  return {
    clearOldData: () => realtimeStore.clearOldData(),
    resetAllData: () => realtimeStore.reset()
  }
}
