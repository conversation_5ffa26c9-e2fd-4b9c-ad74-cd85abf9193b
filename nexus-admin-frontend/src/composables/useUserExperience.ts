/**
 * 用户体验优化组合式函数
 */
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useMessage, useLoadingBar } from 'naive-ui'

interface UserAction {
  id: string
  type: 'click' | 'navigation' | 'search' | 'form' | 'error'
  target: string
  timestamp: number
  duration?: number
  metadata?: Record<string, any>
}

interface PerformanceMetric {
  name: string
  value: number
  timestamp: number
  type: 'navigation' | 'interaction' | 'resource'
}

interface UserPreference {
  key: string
  value: any
  category: 'interface' | 'behavior' | 'accessibility'
  timestamp: number
}

export function useUserExperience() {
  const router = useRouter()
  const route = useRoute()
  const message = useMessage()
  const loadingBar = useLoadingBar()

  // 用户行为追踪
  const userActions = ref<UserAction[]>([])
  const currentSession = ref({
    id: generateSessionId(),
    startTime: Date.now(),
    pageViews: 0,
    interactions: 0,
    errors: 0
  })

  // 性能监控
  const performanceMetrics = ref<PerformanceMetric[]>([])
  const pageLoadTimes = ref<Record<string, number>>({})

  // 用户偏好
  const userPreferences = ref<UserPreference[]>([])

  // 错误追踪
  const errorLog = ref<Array<{
    id: string
    type: 'javascript' | 'network' | 'user'
    message: string
    stack?: string
    timestamp: number
    url: string
    userAgent: string
  }>>([])

  // 页面可见性
  const isPageVisible = ref(!document.hidden)
  const pageVisibilityTime = ref(Date.now())

  // 网络状态
  const isOnline = ref(navigator.onLine)
  const connectionType = ref<string>('unknown')

  // 计算属性
  const sessionDuration = computed(() => {
    return Date.now() - currentSession.value.startTime
  })

  const averagePageLoadTime = computed(() => {
    const times = Object.values(pageLoadTimes.value)
    if (times.length === 0) return 0
    return times.reduce((sum, time) => sum + time, 0) / times.length
  })

  const userEngagementScore = computed(() => {
    const session = currentSession.value
    const duration = sessionDuration.value / 1000 / 60 // 分钟
    const interactionRate = session.interactions / Math.max(session.pageViews, 1)
    const errorRate = session.errors / Math.max(session.interactions, 1)
    
    // 简单的参与度评分算法
    let score = 0
    score += Math.min(duration * 10, 50) // 时长贡献，最多50分
    score += Math.min(interactionRate * 30, 30) // 交互率贡献，最多30分
    score += Math.min(session.pageViews * 2, 20) // 页面浏览贡献，最多20分
    score -= errorRate * 20 // 错误率扣分
    
    return Math.max(0, Math.min(100, score))
  })

  // 工具函数
  const generateSessionId = (): string => {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  const generateActionId = (): string => {
    return `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // 用户行为追踪
  const trackUserAction = (
    type: UserAction['type'],
    target: string,
    metadata?: Record<string, any>
  ) => {
    const action: UserAction = {
      id: generateActionId(),
      type,
      target,
      timestamp: Date.now(),
      metadata
    }

    userActions.value.push(action)
    currentSession.value.interactions++

    // 保持最近1000个行为记录
    if (userActions.value.length > 1000) {
      userActions.value = userActions.value.slice(-500)
    }

    // 发送到分析服务（模拟）
    sendAnalytics('user_action', action)
  }

  // 页面导航追踪
  const trackPageNavigation = (from: string, to: string) => {
    const startTime = Date.now()
    
    trackUserAction('navigation', to, { from, to })
    currentSession.value.pageViews++

    // 记录页面加载时间
    nextTick(() => {
      const loadTime = Date.now() - startTime
      pageLoadTimes.value[to] = loadTime
      
      recordPerformanceMetric('page_load_time', loadTime, 'navigation')
    })
  }

  // 性能指标记录
  const recordPerformanceMetric = (
    name: string,
    value: number,
    type: PerformanceMetric['type'] = 'interaction'
  ) => {
    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      type
    }

    performanceMetrics.value.push(metric)

    // 保持最近500个指标
    if (performanceMetrics.value.length > 500) {
      performanceMetrics.value = performanceMetrics.value.slice(-250)
    }
  }

  // 错误追踪
  const trackError = (
    type: 'javascript' | 'network' | 'user',
    message: string,
    stack?: string
  ) => {
    const error = {
      id: generateActionId(),
      type,
      message,
      stack,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent
    }

    errorLog.value.push(error)
    currentSession.value.errors++

    // 显示用户友好的错误消息
    if (type === 'network') {
      message.error('网络连接异常，请检查网络设置')
    } else if (type === 'user') {
      message.warning(message)
    }

    // 发送错误报告
    sendAnalytics('error', error)
  }

  // 用户偏好管理
  const setUserPreference = (
    key: string,
    value: any,
    category: UserPreference['category'] = 'interface'
  ) => {
    const preference: UserPreference = {
      key,
      value,
      category,
      timestamp: Date.now()
    }

    const existingIndex = userPreferences.value.findIndex(p => p.key === key)
    if (existingIndex >= 0) {
      userPreferences.value[existingIndex] = preference
    } else {
      userPreferences.value.push(preference)
    }

    // 保存到本地存储
    localStorage.setItem('nexus_user_preferences', JSON.stringify(userPreferences.value))
  }

  const getUserPreference = (key: string, defaultValue?: any) => {
    const preference = userPreferences.value.find(p => p.key === key)
    return preference ? preference.value : defaultValue
  }

  // 加载保存的偏好
  const loadUserPreferences = () => {
    try {
      const saved = localStorage.getItem('nexus_user_preferences')
      if (saved) {
        userPreferences.value = JSON.parse(saved)
      }
    } catch (error) {
      console.error('加载用户偏好失败:', error)
    }
  }

  // 分析数据发送（模拟）
  const sendAnalytics = (event: string, data: any) => {
    // 在实际应用中，这里会发送到分析服务
    if (import.meta.env.DEV) {
      console.log(`Analytics: ${event}`, data)
    }
  }

  // 页面性能监控
  const monitorPagePerformance = () => {
    if ('performance' in window) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      
      if (navigation) {
        recordPerformanceMetric('dns_lookup', navigation.domainLookupEnd - navigation.domainLookupStart, 'navigation')
        recordPerformanceMetric('tcp_connect', navigation.connectEnd - navigation.connectStart, 'navigation')
        recordPerformanceMetric('request_response', navigation.responseEnd - navigation.requestStart, 'navigation')
        recordPerformanceMetric('dom_parse', navigation.domContentLoadedEventEnd - navigation.responseEnd, 'navigation')
        recordPerformanceMetric('resource_load', navigation.loadEventEnd - navigation.domContentLoadedEventEnd, 'navigation')
      }
    }
  }

  // 用户体验优化建议
  const getUXSuggestions = () => {
    const suggestions: string[] = []
    
    // 基于性能指标的建议
    if (averagePageLoadTime.value > 3000) {
      suggestions.push('页面加载时间较长，建议优化资源加载')
    }
    
    // 基于错误率的建议
    const errorRate = currentSession.value.errors / Math.max(currentSession.value.interactions, 1)
    if (errorRate > 0.1) {
      suggestions.push('错误率较高，建议检查系统稳定性')
    }
    
    // 基于参与度的建议
    if (userEngagementScore.value < 30) {
      suggestions.push('用户参与度较低，建议优化界面交互')
    }
    
    return suggestions
  }

  // 事件监听器
  const setupEventListeners = () => {
    // 页面可见性变化
    document.addEventListener('visibilitychange', () => {
      isPageVisible.value = !document.hidden
      pageVisibilityTime.value = Date.now()
      
      if (document.hidden) {
        trackUserAction('navigation', 'page_hidden')
      } else {
        trackUserAction('navigation', 'page_visible')
      }
    })

    // 网络状态变化
    window.addEventListener('online', () => {
      isOnline.value = true
      message.success('网络连接已恢复')
    })

    window.addEventListener('offline', () => {
      isOnline.value = false
      message.warning('网络连接已断开')
    })

    // 全局错误捕获
    window.addEventListener('error', (event) => {
      trackError('javascript', event.message, event.error?.stack)
    })

    window.addEventListener('unhandledrejection', (event) => {
      trackError('javascript', event.reason?.message || 'Unhandled Promise Rejection')
    })

    // 获取网络连接信息
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      connectionType.value = connection.effectiveType || 'unknown'
      
      connection.addEventListener('change', () => {
        connectionType.value = connection.effectiveType || 'unknown'
      })
    }
  }

  // 生命周期管理
  onMounted(() => {
    loadUserPreferences()
    setupEventListeners()
    monitorPagePerformance()
    
    // 路由变化监听
    router.afterEach((to, from) => {
      trackPageNavigation(from.path, to.path)
    })
  })

  onUnmounted(() => {
    // 发送会话结束事件
    sendAnalytics('session_end', {
      sessionId: currentSession.value.id,
      duration: sessionDuration.value,
      pageViews: currentSession.value.pageViews,
      interactions: currentSession.value.interactions,
      errors: currentSession.value.errors,
      engagementScore: userEngagementScore.value
    })
  })

  return {
    // 状态
    currentSession: computed(() => currentSession.value),
    sessionDuration,
    userEngagementScore,
    isPageVisible,
    isOnline,
    connectionType,
    
    // 性能指标
    performanceMetrics: computed(() => performanceMetrics.value),
    averagePageLoadTime,
    
    // 用户行为
    userActions: computed(() => userActions.value),
    errorLog: computed(() => errorLog.value),
    
    // 方法
    trackUserAction,
    trackError,
    recordPerformanceMetric,
    setUserPreference,
    getUserPreference,
    getUXSuggestions,
    
    // 工具方法
    sendAnalytics
  }
}

export default useUserExperience
