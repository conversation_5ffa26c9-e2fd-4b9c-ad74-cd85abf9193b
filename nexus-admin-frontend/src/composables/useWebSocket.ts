/**
 * WebSocket实时通信组合式函数
 */
import { ref, reactive, onUnmounted, watch } from 'vue'
import { Client } from '@stomp/stompjs'
import SockJS from 'sockjs-client'
import type { RealtimeMessage, SubscriptionRequest } from '@/types/api'
import { useAuthStore } from '@/stores/auth'
import { useMessage } from 'naive-ui'

// WebSocket连接状态
export type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error' | 'reconnecting'

// 订阅主题枚举
export const TOPICS = {
  SERVICE_STATUS: 'service.status',
  API_CALLS: 'api.calls',
  TOOL_EXECUTION: 'tool.execution',
  SUBSCRIPTION_UPDATES: 'subscription.updates',
  SYSTEM_ALERTS: 'system.alerts',
  USER_NOTIFICATIONS: 'user.notifications'
} as const

export type TopicType = typeof TOPICS[keyof typeof TOPICS]

interface WebSocketState {
  status: ConnectionStatus
  client: Client | null
  subscriptions: Map<string, any>
  messageHandlers: Map<string, Array<(message: RealtimeMessage) => void>>
  reconnectAttempts: number
  lastError: string | null
}

export function useWebSocket() {
  const authStore = useAuthStore()
  const message = useMessage()

  const state = reactive<WebSocketState>({
    status: 'disconnected',
    client: null,
    subscriptions: new Map(),
    messageHandlers: new Map(),
    reconnectAttempts: 0,
    lastError: null
  })

  const isConnected = computed(() => state.status === 'connected')
  const isConnecting = computed(() => state.status === 'connecting' || state.status === 'reconnecting')

  // 连接WebSocket
  const connect = () => {
    if (state.client?.connected || isConnecting.value) {
      return
    }

    if (!authStore.isAuthenticated || !authStore.user) {
      console.warn('用户未认证，无法建立WebSocket连接')
      return
    }

    state.status = state.reconnectAttempts > 0 ? 'reconnecting' : 'connecting'
    state.lastError = null

    try {
      // 创建STOMP客户端
      state.client = new Client({
        webSocketFactory: () => new SockJS(`${import.meta.env.VITE_WS_BASE_URL}/realtime/ws/realtime?userId=${authStore.user!.id}`),
        connectHeaders: {
          Authorization: `Bearer ${authStore.token}`,
          'X-User-ID': authStore.user!.id.toString()
        },
        debug: (str) => {
          console.log('[WebSocket Debug]', str)
        },
        reconnectDelay: 5000,
        heartbeatIncoming: 4000,
        heartbeatOutgoing: 4000,
        onConnect: (frame) => {
          console.log('WebSocket连接成功:', frame)
          state.status = 'connected'
          state.reconnectAttempts = 0
          state.lastError = null
          
          // 重新订阅之前的主题
          resubscribeAll()
          
          message.success('实时连接已建立')
        },
        onDisconnect: (frame) => {
          console.log('WebSocket连接断开:', frame)
          state.status = 'disconnected'
          state.subscriptions.clear()
        },
        onStompError: (frame) => {
          console.error('WebSocket STOMP错误:', frame)
          state.status = 'error'
          state.lastError = frame.headers.message || '连接错误'
          
          message.error(`WebSocket连接错误: ${state.lastError}`)
        },
        onWebSocketError: (error) => {
          console.error('WebSocket错误:', error)
          state.status = 'error'
          state.lastError = '网络连接错误'
          
          // 自动重连
          if (state.reconnectAttempts < 5) {
            state.reconnectAttempts++
            setTimeout(() => {
              console.log(`尝试重连 (${state.reconnectAttempts}/5)`)
              connect()
            }, 5000 * state.reconnectAttempts)
          } else {
            message.error('WebSocket连接失败，请刷新页面重试')
          }
        }
      })

      state.client.activate()
    } catch (error) {
      console.error('WebSocket连接失败:', error)
      state.status = 'error'
      state.lastError = '连接初始化失败'
    }
  }

  // 断开连接
  const disconnect = () => {
    if (state.client) {
      state.client.deactivate()
      state.client = null
    }
    state.status = 'disconnected'
    state.subscriptions.clear()
    state.messageHandlers.clear()
    state.reconnectAttempts = 0
  }

  // 订阅主题
  const subscribe = (topic: TopicType, handler: (message: RealtimeMessage) => void) => {
    if (!state.client?.connected) {
      console.warn('WebSocket未连接，无法订阅主题:', topic)
      return null
    }

    const destination = `/topic/${topic}`
    
    try {
      const subscription = state.client.subscribe(destination, (message) => {
        try {
          const data: RealtimeMessage = JSON.parse(message.body)
          console.log(`[WebSocket] 收到消息 [${topic}]:`, data)
          handler(data)
        } catch (error) {
          console.error('解析WebSocket消息失败:', error)
        }
      })

      state.subscriptions.set(topic, subscription)
      
      // 保存消息处理器用于重连时恢复
      if (!state.messageHandlers.has(topic)) {
        state.messageHandlers.set(topic, [])
      }
      state.messageHandlers.get(topic)!.push(handler)

      console.log(`订阅主题成功: ${topic}`)
      return subscription
    } catch (error) {
      console.error(`订阅主题失败 [${topic}]:`, error)
      return null
    }
  }

  // 取消订阅
  const unsubscribe = (topic: TopicType) => {
    const subscription = state.subscriptions.get(topic)
    if (subscription) {
      subscription.unsubscribe()
      state.subscriptions.delete(topic)
      state.messageHandlers.delete(topic)
      console.log(`取消订阅: ${topic}`)
    }
  }

  // 发送消息
  const sendMessage = (destination: string, message: any) => {
    if (!state.client?.connected) {
      console.warn('WebSocket未连接，无法发送消息')
      return false
    }

    try {
      state.client.publish({
        destination,
        body: JSON.stringify(message)
      })
      console.log(`发送消息到 ${destination}:`, message)
      return true
    } catch (error) {
      console.error('发送消息失败:', error)
      return false
    }
  }

  // 发送心跳
  const sendHeartbeat = () => {
    sendMessage('/app/message', {
      type: 'PING',
      timestamp: new Date().toISOString()
    })
  }

  // 重新订阅所有主题
  const resubscribeAll = () => {
    for (const [topic, handlers] of state.messageHandlers.entries()) {
      for (const handler of handlers) {
        subscribe(topic as TopicType, handler)
      }
    }
  }

  // 监听认证状态变化
  watch(
    () => authStore.isAuthenticated,
    (isAuth) => {
      if (isAuth) {
        connect()
      } else {
        disconnect()
      }
    },
    { immediate: true }
  )

  // 组件卸载时清理
  onUnmounted(() => {
    disconnect()
  })

  return {
    // 状态
    status: readonly(ref(state.status)),
    isConnected,
    isConnecting,
    lastError: readonly(ref(state.lastError)),
    
    // 方法
    connect,
    disconnect,
    subscribe,
    unsubscribe,
    sendMessage,
    sendHeartbeat
  }
}
