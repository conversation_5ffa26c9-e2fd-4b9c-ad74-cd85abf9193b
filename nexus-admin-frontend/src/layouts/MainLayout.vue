<template>
  <div class="main-layout" :class="{ 'sidebar-collapsed': themeStore.sidebarCollapsed }">
    <!-- 侧边栏 -->
    <aside class="sidebar glass-card">
      <div class="sidebar-header">
        <div class="logo-container" @click="$router.push('/')">
          <div class="logo-icon glow-effect">
            <n-icon size="32" color="#3b82f6">
              <ServerOutline />
            </n-icon>
          </div>
          <Transition name="fade">
            <div v-show="!themeStore.sidebarCollapsed" class="logo-text gradient-text">
              Nexus
            </div>
          </Transition>
        </div>
        
        <n-button
          quaternary
          circle
          size="small"
          class="collapse-btn glass-button"
          @click="themeStore.toggleSidebar()"
        >
          <n-icon>
            <MenuOutline />
          </n-icon>
        </n-button>
      </div>

      <div class="sidebar-content">
        <SidebarMenu />
      </div>

      <div class="sidebar-footer">
        <div class="user-info" @click="showUserMenu = !showUserMenu">
          <n-avatar
            round
            size="small"
            :src="userAvatar"
            :fallback-src="defaultAvatar"
          >
            {{ userInitials }}
          </n-avatar>
          <Transition name="fade">
            <div v-show="!themeStore.sidebarCollapsed" class="user-details">
              <div class="username">{{ authStore.userDisplayName }}</div>
              <div class="user-role">{{ authStore.user?.role }}</div>
            </div>
          </Transition>
        </div>
      </div>
    </aside>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 顶部导航栏 -->
      <header class="header glass-card">
        <div class="header-left">
          <BreadcrumbNav />
        </div>
        
        <div class="header-right">
          <!-- 搜索框 -->
          <div class="search-container">
            <n-input
              v-model:value="searchQuery"
              placeholder="搜索..."
              size="medium"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <n-icon>
                  <SearchOutline />
                </n-icon>
              </template>
            </n-input>
          </div>

          <!-- 通知 -->
          <n-dropdown
            :options="notificationOptions"
            @select="handleNotificationSelect"
          >
            <n-button quaternary circle>
              <n-badge :value="unreadCount" :max="99">
                <n-icon size="20">
                  <NotificationsOutline />
                </n-icon>
              </n-badge>
            </n-button>
          </n-dropdown>

          <!-- 主题切换 -->
          <n-button quaternary circle @click="themeStore.toggleTheme()">
            <n-icon size="20">
              <component :is="themeIcon" />
            </n-icon>
          </n-button>

          <!-- 用户菜单 -->
          <n-dropdown
            :options="userMenuOptions"
            @select="handleUserMenuSelect"
          >
            <n-button quaternary circle>
              <n-avatar
                round
                size="small"
                :src="userAvatar"
                :fallback-src="defaultAvatar"
              >
                {{ userInitials }}
              </n-avatar>
            </n-button>
          </n-dropdown>
        </div>
      </header>

      <!-- 页面内容 -->
      <main class="page-content">
        <router-view v-slot="{ Component, route }">
          <Transition name="page-transition" mode="out-in">
            <component :is="Component" :key="route.path" />
          </Transition>
        </router-view>
      </main>
    </div>

    <!-- 全局搜索对话框 -->
    <GlobalSearchModal v-model:show="showGlobalSearch" />
    
    <!-- 帮助对话框 -->
    <HelpModal v-model:show="showHelp" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { 
  ServerOutline, 
  MenuOutline, 
  SearchOutline, 
  NotificationsOutline,
  SunnyOutline,
  MoonOutline,
  SettingsOutline,
  LogOutOutline,
  PersonOutline
} from '@vicons/ionicons5'
import { useAuthStore } from '@/stores/auth'
import { useThemeStore } from '@/stores/theme'
import SidebarMenu from '@/components/SidebarMenu.vue'
import BreadcrumbNav from '@/components/BreadcrumbNav.vue'
import GlobalSearchModal from '@/components/GlobalSearchModal.vue'
import HelpModal from '@/components/HelpModal.vue'

const router = useRouter()
const authStore = useAuthStore()
const themeStore = useThemeStore()

// 状态
const searchQuery = ref('')
const showGlobalSearch = ref(false)
const showHelp = ref(false)
const showUserMenu = ref(false)
const unreadCount = ref(3) // 模拟未读通知数量

// 计算属性
const userInitials = computed(() => {
  const user = authStore.user
  if (!user) return 'U'
  return user.username.charAt(0).toUpperCase()
})

const userAvatar = computed(() => {
  // 这里可以返回用户头像URL
  return ''
})

const defaultAvatar = computed(() => {
  // 默认头像
  return `https://api.dicebear.com/7.x/initials/svg?seed=${authStore.userDisplayName}`
})

const themeIcon = computed(() => {
  return themeStore.isDark ? SunnyOutline : MoonOutline
})

// 通知选项
const notificationOptions = computed(() => [
  {
    label: '系统通知',
    key: 'system',
    children: [
      { label: 'MCP服务状态更新', key: 'mcp-status' },
      { label: '订阅即将到期', key: 'subscription-expiry' },
      { label: '系统维护通知', key: 'maintenance' }
    ]
  },
  {
    label: '标记全部已读',
    key: 'mark-all-read'
  }
])

// 用户菜单选项
const userMenuOptions = computed(() => [
  {
    label: '个人资料',
    key: 'profile',
    icon: () => h(PersonOutline)
  },
  {
    label: '偏好设置',
    key: 'preferences',
    icon: () => h(SettingsOutline)
  },
  {
    type: 'divider',
    key: 'divider'
  },
  {
    label: '退出登录',
    key: 'logout',
    icon: () => h(LogOutOutline)
  }
])

// 事件处理
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    // 执行搜索逻辑
    console.log('搜索:', searchQuery.value)
  }
}

const handleNotificationSelect = (key: string) => {
  switch (key) {
    case 'mark-all-read':
      unreadCount.value = 0
      break
    default:
      console.log('通知选择:', key)
  }
}

const handleUserMenuSelect = async (key: string) => {
  switch (key) {
    case 'profile':
      router.push('/settings/profile')
      break
    case 'preferences':
      router.push('/settings/preferences')
      break
    case 'logout':
      await authStore.logout()
      router.push('/login')
      break
  }
}

// 全局事件监听
onMounted(() => {
  // 监听全局搜索事件
  window.addEventListener('global-search', () => {
    showGlobalSearch.value = true
  })
  
  // 监听帮助事件
  window.addEventListener('show-help', () => {
    showHelp.value = true
  })
})
</script>

<style scoped>
.main-layout {
  display: flex;
  height: 100vh;
  background: var(--gradient-bg);
  position: relative;
  overflow: hidden;
}

.sidebar {
  width: 280px;
  height: 100vh;
  position: relative;
  z-index: 10;
  transition: all var(--duration-normal) var(--ease-out);
  animation: slideInLeft var(--duration-slow) var(--ease-out);
}

.sidebar::before {
  content: '';
  position: absolute;
  top: var(--space-4);
  left: var(--space-4);
  right: var(--space-4);
  bottom: var(--space-4);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-3xl);
  box-shadow: var(--glass-shadow);
  z-index: -1;
}

.sidebar > * {
  position: relative;
  z-index: 1;
}

.sidebar-collapsed .sidebar {
  width: 80px;
}

.sidebar-header {
  padding: var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--border-secondary);
  margin: var(--space-4) var(--space-4) 0 var(--space-4);
}

.logo-container {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-out);
}

.logo-container:hover {
  transform: scale(1.05);
}

.logo-icon {
  position: relative;
  border-radius: var(--radius-lg);
  padding: var(--space-2);
  background: rgba(59, 130, 246, 0.1);
  transition: all var(--duration-normal) var(--ease-out);
}

.logo-icon:hover {
  background: rgba(59, 130, 246, 0.2);
  box-shadow: var(--shadow-glow);
}

.logo-text {
  font-size: 20px;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.5px;
}

.glass-button {
  background: rgba(148, 163, 184, 0.1) !important;
  border: 1px solid rgba(148, 163, 184, 0.2) !important;
  backdrop-filter: blur(8px);
  transition: all var(--duration-normal) var(--ease-out) !important;
}

.glass-button:hover {
  background: rgba(148, 163, 184, 0.2) !important;
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 12px rgba(59, 130, 246, 0.3) !important;
}

.collapse-btn {
  color: rgba(255, 255, 255, 0.8);
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
}

.sidebar-footer {
  padding: 16px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.user-info:hover {
  background: rgba(255, 255, 255, 0.1);
}

.user-details {
  flex: 1;
  min-width: 0;
}

.username {
  font-size: 14px;
  font-weight: 600;
  color: white;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-role {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin: var(--space-4) var(--space-4) var(--space-4) 0;
  overflow: hidden;
  position: relative;
  animation: slideInRight var(--duration-slow) var(--ease-out);
}

.header {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--space-6);
  margin-bottom: var(--space-4);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-card);
  position: relative;
  overflow: hidden;
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  opacity: 0.6;
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-container {
  width: 300px;
}

.page-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 4px;
}

/* 页面切换动画 */
.page-transition-enter-active,
.page-transition-leave-active {
  transition: all 0.3s ease;
}

.page-transition-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.page-transition-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

/* 淡入淡出动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 动画关键帧 */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .search-container {
    width: 200px;
  }
}

@media (max-width: 768px) {
  .main-layout {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    height: auto;
    margin: 0;
    border-radius: 0;
    order: 2;
  }
  
  .main-content {
    margin: 0;
    order: 1;
  }
  
  .header {
    margin-bottom: 0;
    border-radius: 0;
  }
  
  .search-container {
    display: none;
  }
}
</style>
