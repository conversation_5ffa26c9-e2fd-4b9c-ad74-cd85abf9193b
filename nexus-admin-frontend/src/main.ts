/**
 * 应用程序入口文件
 */
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { VueQueryPlugin } from '@tanstack/vue-query'
import App from './App.vue'
import router from './router'

// 样式导入
import './styles/main.css'

// 创建应用实例
const app = createApp(App)

// 安装插件
app.use(createPinia())
app.use(router)
app.use(VueQueryPlugin, {
  queryClientConfig: {
    defaultOptions: {
      queries: {
        staleTime: 1000 * 60 * 5, // 5分钟
        cacheTime: 1000 * 60 * 10, // 10分钟
        retry: 2,
        refetchOnWindowFocus: false
      }
    }
  }
})

// 挂载应用
app.mount('#app')

// 初始化auth store
import useAuthStore from './stores/auth'
const authStore = useAuthStore()
authStore.restoreUserFromStorage()

// 开发环境下的调试信息
if (import.meta.env.DEV) {
  console.log('🚀 Nexus管理平台启动成功')
  console.log('📦 Vue版本:', app.version)
  console.log('🌐 API地址:', import.meta.env.VITE_API_BASE_URL)
  console.log('🔌 WebSocket地址:', import.meta.env.VITE_WS_BASE_URL)
}
