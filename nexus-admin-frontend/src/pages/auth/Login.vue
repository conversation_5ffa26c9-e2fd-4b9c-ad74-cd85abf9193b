<template>
  <div class="login-container">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
    </div>

    <!-- 登录卡片 -->
    <div class="login-card glass-card">
      <!-- Logo和标题 -->
      <div class="login-header">
        <div class="logo">
          <n-icon size="48" color="#667eea">
            <ServerOutline />
          </n-icon>
        </div>
        <h1 class="title">Nexus管理平台</h1>
        <p class="subtitle">现代化的MCP服务管理界面</p>
      </div>

      <!-- 登录表单 -->
      <n-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        size="large"
        :show-label="false"
        @submit.prevent="handleLogin"
      >
        <n-form-item path="usernameOrEmail">
          <n-input
            v-model:value="formData.usernameOrEmail"
            placeholder="用户名或邮箱"
            :input-props="{ autocomplete: 'username' }"
          >
            <template #prefix>
              <n-icon>
                <PersonOutline />
              </n-icon>
            </template>
          </n-input>
        </n-form-item>

        <n-form-item path="password">
          <n-input
            v-model:value="formData.password"
            type="password"
            placeholder="密码"
            show-password-on="click"
            :input-props="{ autocomplete: 'current-password' }"
            @keyup.enter="handleLogin"
          >
            <template #prefix>
              <n-icon>
                <LockClosedOutline />
              </n-icon>
            </template>
          </n-input>
        </n-form-item>

        <div class="form-options">
          <n-checkbox v-model:checked="rememberMe">
            记住我
          </n-checkbox>
          <n-button text type="primary" @click="showForgotPassword = true">
            忘记密码？
          </n-button>
        </div>

        <n-button
          type="primary"
          size="large"
          block
          :loading="loading"
          :disabled="!isFormValid"
          class="login-button gradient-button"
          @click="handleLogin"
        >
          {{ loading ? '登录中...' : '登录' }}
        </n-button>
      </n-form>

      <!-- 注册链接 -->
      <div class="register-link">
        <span>还没有账户？</span>
        <n-button text type="primary" @click="$router.push('/register')">
          立即注册
        </n-button>
      </div>

      <!-- 版本信息 -->
      <div class="version-info">
        <span>版本 {{ version }}</span>
      </div>
    </div>

    <!-- 忘记密码对话框 -->
    <n-modal v-model:show="showForgotPassword" preset="dialog" title="重置密码">
      <template #default>
        <n-form ref="resetFormRef" :model="resetForm" :rules="resetRules">
          <n-form-item label="邮箱地址" path="email">
            <n-input
              v-model:value="resetForm.email"
              placeholder="请输入注册邮箱"
              type="email"
            />
          </n-form-item>
        </n-form>
      </template>
      <template #action>
        <n-space>
          <n-button @click="showForgotPassword = false">取消</n-button>
          <n-button type="primary" :loading="resetLoading" @click="handleResetPassword">
            发送重置邮件
          </n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useMessage } from 'naive-ui'
import { ServerOutline, PersonOutline, LockClosedOutline } from '@vicons/ionicons5'
import { useAuthStore } from '@/stores/auth'
import type { LoginRequest } from '@/types/api'
import type { FormInst, FormRules } from 'naive-ui'

const router = useRouter()
const route = useRoute()
const message = useMessage()
const authStore = useAuthStore()

// 表单引用
const formRef = ref<FormInst | null>(null)
const resetFormRef = ref<FormInst | null>(null)

// 表单数据
const formData = ref<LoginRequest>({
  usernameOrEmail: '',
  password: ''
})

// 重置密码表单
const resetForm = ref({
  email: ''
})

// 状态
const loading = ref(false)
const resetLoading = ref(false)
const rememberMe = ref(false)
const showForgotPassword = ref(false)
const version = ref(import.meta.env.VITE_APP_VERSION || '1.0.0')

// 表单验证规则
const formRules: FormRules = {
  usernameOrEmail: [
    { required: true, message: '请输入用户名或邮箱', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

const resetRules: FormRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ]
}

// 计算属性
const isFormValid = computed(() => {
  return formData.value.usernameOrEmail.trim() !== '' && 
         formData.value.password.trim() !== ''
})

// 处理登录
const handleLogin = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    await authStore.login(formData.value)
    
    message.success('登录成功！')
    
    // 跳转到目标页面或首页
    const redirect = route.query.redirect as string || '/'
    await router.push(redirect)
    
  } catch (error: any) {
    console.error('登录失败:', error)
    message.error(error.message || '登录失败，请检查用户名和密码')
  } finally {
    loading.value = false
  }
}

// 处理重置密码
const handleResetPassword = async () => {
  if (!resetFormRef.value) return

  try {
    await resetFormRef.value.validate()
    resetLoading.value = true

    // 这里调用重置密码API
    // await authApi.resetPassword(resetForm.value.email)
    
    message.success('重置邮件已发送，请查收邮箱')
    showForgotPassword.value = false
    resetForm.value.email = ''
    
  } catch (error: any) {
    message.error(error.message || '发送重置邮件失败')
  } finally {
    resetLoading.value = false
  }
}

// 组件挂载时的处理
onMounted(() => {
  // 如果已经登录，直接跳转
  if (authStore.isAuthenticated) {
    router.push('/')
  }
  
  // 从本地存储恢复记住的用户名
  const rememberedUsername = localStorage.getItem('nexus_remembered_username')
  if (rememberedUsername) {
    formData.value.usernameOrEmail = rememberedUsername
    rememberMe.value = true
  }
})

// 监听记住我选项
watch(rememberMe, (newValue) => {
  if (newValue && formData.value.usernameOrEmail) {
    localStorage.setItem('nexus_remembered_username', formData.value.usernameOrEmail)
  } else {
    localStorage.removeItem('nexus_remembered_username')
  }
})

// 监听用户名变化
watch(() => formData.value.usernameOrEmail, (newValue) => {
  if (rememberMe.value && newValue) {
    localStorage.setItem('nexus_remembered_username', newValue)
  }
})
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--gradient-primary);
  position: relative;
  overflow: hidden;
  padding: 20px;
}

.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.login-card {
  width: 100%;
  max-width: 400px;
  padding: 40px;
  position: relative;
  z-index: 1;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.logo {
  margin-bottom: 16px;
}

.title {
  font-size: 28px;
  font-weight: 700;
  color: white;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.subtitle {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.login-button {
  margin-bottom: 24px;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
}

.register-link {
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.version-info {
  text-align: center;
  margin-top: 24px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

/* 响应式调整 */
@media (max-width: 480px) {
  .login-card {
    padding: 24px;
    margin: 16px;
  }
  
  .title {
    font-size: 24px;
  }
  
  .decoration-circle {
    display: none;
  }
}
</style>
