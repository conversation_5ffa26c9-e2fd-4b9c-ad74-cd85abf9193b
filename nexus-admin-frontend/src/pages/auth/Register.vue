<template>
  <div class="register-container">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
    </div>

    <!-- 注册卡片 -->
    <div class="register-card glass-card">
      <!-- Logo和标题 -->
      <div class="register-header">
        <div class="logo">
          <n-icon size="48" color="#667eea">
            <ServerOutline />
          </n-icon>
        </div>
        <h1 class="title">创建账户</h1>
        <p class="subtitle">加入Nexus管理平台</p>
      </div>

      <!-- 注册表单 -->
      <n-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        size="large"
        :show-label="false"
        @submit.prevent="handleRegister"
      >
        <n-form-item path="username">
          <n-input
            v-model:value="formData.username"
            placeholder="用户名"
            :input-props="{ autocomplete: 'username' }"
          >
            <template #prefix>
              <n-icon>
                <PersonOutline />
              </n-icon>
            </template>
          </n-input>
        </n-form-item>

        <n-form-item path="email">
          <n-input
            v-model:value="formData.email"
            placeholder="邮箱地址"
            type="email"
            :input-props="{ autocomplete: 'email' }"
          >
            <template #prefix>
              <n-icon>
                <MailOutline />
              </n-icon>
            </template>
          </n-input>
        </n-form-item>

        <n-form-item path="password">
          <n-input
            v-model:value="formData.password"
            type="password"
            placeholder="密码"
            show-password-on="click"
            :input-props="{ autocomplete: 'new-password' }"
          >
            <template #prefix>
              <n-icon>
                <LockClosedOutline />
              </n-icon>
            </template>
          </n-input>
        </n-form-item>

        <n-form-item path="confirmPassword">
          <n-input
            v-model:value="formData.confirmPassword"
            type="password"
            placeholder="确认密码"
            show-password-on="click"
            :input-props="{ autocomplete: 'new-password' }"
            @keyup.enter="handleRegister"
          >
            <template #prefix>
              <n-icon>
                <LockClosedOutline />
              </n-icon>
            </template>
          </n-input>
        </n-form-item>

        <!-- 密码强度指示器 -->
        <div v-if="formData.password" class="password-strength">
          <div class="strength-label">密码强度：</div>
          <div class="strength-bar">
            <div 
              class="strength-fill" 
              :class="passwordStrength.class"
              :style="{ width: passwordStrength.width }"
            ></div>
          </div>
          <div class="strength-text" :class="passwordStrength.class">
            {{ passwordStrength.text }}
          </div>
        </div>

        <div class="form-options">
          <n-checkbox v-model:checked="agreeTerms">
            我同意
            <n-button text type="primary" @click="showTerms = true">
              服务条款
            </n-button>
            和
            <n-button text type="primary" @click="showPrivacy = true">
              隐私政策
            </n-button>
          </n-checkbox>
        </div>

        <n-button
          type="primary"
          size="large"
          block
          :loading="loading"
          :disabled="!isFormValid"
          class="register-button gradient-button"
          @click="handleRegister"
        >
          {{ loading ? '注册中...' : '创建账户' }}
        </n-button>
      </n-form>

      <!-- 登录链接 -->
      <div class="login-link">
        <span>已有账户？</span>
        <n-button text type="primary" @click="$router.push('/login')">
          立即登录
        </n-button>
      </div>
    </div>

    <!-- 服务条款对话框 -->
    <n-modal v-model:show="showTerms" preset="dialog" title="服务条款">
      <div class="terms-content">
        <p>欢迎使用Nexus管理平台。在使用我们的服务之前，请仔细阅读以下条款：</p>
        <ol>
          <li>用户应当合法使用本平台提供的服务</li>
          <li>禁止进行任何可能损害系统安全的行为</li>
          <li>用户数据将受到严格保护</li>
          <li>平台保留在必要时暂停或终止服务的权利</li>
        </ol>
      </div>
      <template #action>
        <n-button type="primary" @click="showTerms = false">我已阅读</n-button>
      </template>
    </n-modal>

    <!-- 隐私政策对话框 -->
    <n-modal v-model:show="showPrivacy" preset="dialog" title="隐私政策">
      <div class="privacy-content">
        <p>我们重视您的隐私，承诺保护您的个人信息：</p>
        <ul>
          <li>我们只收集必要的用户信息</li>
          <li>不会向第三方泄露您的个人数据</li>
          <li>使用加密技术保护数据传输</li>
          <li>您有权查看、修改或删除个人信息</li>
        </ul>
      </div>
      <template #action>
        <n-button type="primary" @click="showPrivacy = false">我已阅读</n-button>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import { ServerOutline, PersonOutline, MailOutline, LockClosedOutline } from '@vicons/ionicons5'
import { useAuthStore } from '@/stores/auth'
import type { RegisterRequest } from '@/types/api'
import type { FormInst, FormRules } from 'naive-ui'

const router = useRouter()
const message = useMessage()
const authStore = useAuthStore()

// 表单引用
const formRef = ref<FormInst | null>(null)

// 表单数据
const formData = ref<RegisterRequest & { confirmPassword: string }>({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  role: 'USER'
})

// 状态
const loading = ref(false)
const agreeTerms = ref(false)
const showTerms = ref(false)
const showPrivacy = ref(false)

// 表单验证规则
const formRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度应在3-50个字符之间', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: '用户名只能包含字母、数字、下划线和连字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 100, message: '密码长度应在6-100个字符之间', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string) => {
        return value === formData.value.password
      },
      message: '两次输入的密码不一致',
      trigger: 'blur'
    }
  ]
}

// 密码强度计算
const passwordStrength = computed(() => {
  const password = formData.value.password
  if (!password) return { width: '0%', class: '', text: '' }

  let score = 0
  
  // 长度检查
  if (password.length >= 8) score += 1
  if (password.length >= 12) score += 1
  
  // 复杂度检查
  if (/[a-z]/.test(password)) score += 1
  if (/[A-Z]/.test(password)) score += 1
  if (/[0-9]/.test(password)) score += 1
  if (/[^a-zA-Z0-9]/.test(password)) score += 1

  if (score <= 2) {
    return { width: '33%', class: 'weak', text: '弱' }
  } else if (score <= 4) {
    return { width: '66%', class: 'medium', text: '中等' }
  } else {
    return { width: '100%', class: 'strong', text: '强' }
  }
})

// 表单有效性检查
const isFormValid = computed(() => {
  return formData.value.username.trim() !== '' &&
         formData.value.email.trim() !== '' &&
         formData.value.password.trim() !== '' &&
         formData.value.confirmPassword.trim() !== '' &&
         formData.value.password === formData.value.confirmPassword &&
         agreeTerms.value
})

// 处理注册
const handleRegister = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    const { confirmPassword, ...registerData } = formData.value
    await authStore.register(registerData)
    
    message.success('注册成功！正在跳转...')
    
    // 注册成功后跳转到首页
    setTimeout(() => {
      router.push('/')
    }, 1000)
    
  } catch (error: any) {
    console.error('注册失败:', error)
    message.error(error.message || '注册失败，请稍后重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--gradient-primary);
  position: relative;
  overflow: hidden;
  padding: 20px;
}

.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  right: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  bottom: 60%;
  left: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  top: 20%;
  right: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.register-card {
  width: 100%;
  max-width: 420px;
  padding: 40px;
  position: relative;
  z-index: 1;
}

.register-header {
  text-align: center;
  margin-bottom: 32px;
}

.logo {
  margin-bottom: 16px;
}

.title {
  font-size: 28px;
  font-weight: 700;
  color: white;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.subtitle {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

.password-strength {
  margin-bottom: 16px;
  font-size: 12px;
}

.strength-label {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 4px;
}

.strength-bar {
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 4px;
}

.strength-fill {
  height: 100%;
  transition: all 0.3s ease;
}

.strength-fill.weak {
  background: #ff6b6b;
}

.strength-fill.medium {
  background: #feca57;
}

.strength-fill.strong {
  background: #48dbfb;
}

.strength-text {
  text-align: right;
}

.strength-text.weak {
  color: #ff6b6b;
}

.strength-text.medium {
  color: #feca57;
}

.strength-text.strong {
  color: #48dbfb;
}

.form-options {
  margin-bottom: 24px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.register-button {
  margin-bottom: 24px;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
}

.login-link {
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.terms-content,
.privacy-content {
  max-height: 400px;
  overflow-y: auto;
  padding: 16px 0;
}

.terms-content ol,
.privacy-content ul {
  padding-left: 20px;
}

.terms-content li,
.privacy-content li {
  margin-bottom: 8px;
  line-height: 1.6;
}

/* 响应式调整 */
@media (max-width: 480px) {
  .register-card {
    padding: 24px;
    margin: 16px;
  }
  
  .title {
    font-size: 24px;
  }
  
  .decoration-circle {
    display: none;
  }
}
</style>
