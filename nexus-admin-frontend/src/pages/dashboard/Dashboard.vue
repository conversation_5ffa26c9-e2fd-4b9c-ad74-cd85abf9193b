<template>
  <div class="dashboard">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-content">
        <h1 class="welcome-title">
          欢迎回来，{{ authStore.userDisplayName }}！
        </h1>
        <p class="welcome-subtitle">
          {{ currentTimeGreeting }} 这里是您的Nexus管理平台概览
        </p>
      </div>
      
      <div class="welcome-actions">
        <n-button type="primary" @click="$router.push('/mcp/overview')">
          <template #icon>
            <n-icon>
              <ServerOutline />
            </n-icon>
          </template>
          管理MCP服务
        </n-button>
        
        <n-button @click="$router.push('/testing')">
          <template #icon>
            <n-icon>
              <FlaskOutline />
            </n-icon>
          </template>
          接口测试
        </n-button>
      </div>
    </div>

    <!-- 核心指标卡片 -->
    <div class="metrics-grid">
      <RealtimeStatusCard
        title="MCP服务"
        :value="serviceMetrics.total"
        :icon="ServerOutline"
        status="info"
        :show-progress="true"
        :progress="serviceMetrics.running"
        :progress-max="serviceMetrics.total"
        :progress-text="`${serviceMetrics.running}/${serviceMetrics.total} 运行中`"
        description="已注册的MCP服务总数"
        :actions="[
          { label: '查看详情', key: 'view-services' },
          { label: '添加服务', key: 'add-service' }
        ]"
        @action="handleServiceAction"
      />
      
      <RealtimeStatusCard
        title="活跃订阅"
        :value="subscriptionMetrics.active"
        :icon="CardOutline"
        status="success"
        :show-trend="true"
        :trend="subscriptionMetrics.trend"
        description="当前活跃的用户订阅"
        :chart-data="subscriptionChartData"
        :show-chart="true"
        chart-color="#52c41a"
        @action="handleSubscriptionAction"
      />
      
      <RealtimeStatusCard
        title="API调用"
        :value="apiMetrics.totalCalls"
        :icon="SwapHorizontalOutline"
        status="info"
        :show-trend="true"
        :trend="apiMetrics.trend"
        description="今日API调用总数"
        :chart-data="apiCallChartData"
        :show-chart="true"
        chart-color="#1890ff"
      />
      
      <RealtimeStatusCard
        title="系统健康度"
        :value="`${systemHealth.score}%`"
        :icon="PulseOutline"
        :status="getHealthStatus(systemHealth.score)"
        :show-progress="true"
        :progress="systemHealth.score"
        :progress-max="100"
        description="系统整体健康状况"
      />
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <div class="charts-grid">
        <!-- API调用趋势 -->
        <div class="chart-card">
          <RealtimeChart
            title="API调用趋势"
            :data="apiTrendData"
            :height="300"
            type="line"
            color="#667eea"
            :auto-refresh="true"
            @refresh="refreshApiTrend"
          />
        </div>
        
        <!-- 服务状态分布 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>服务状态分布</h3>
            <n-button quaternary circle size="small" @click="refreshServiceStatus">
              <n-icon>
                <RefreshOutline />
              </n-icon>
            </n-button>
          </div>
          <div class="service-status-chart">
            <v-chart :option="serviceStatusOption" :autoresize="true" style="height: 300px;" />
          </div>
        </div>
      </div>
      
      <!-- 系统活动时间线 -->
      <div class="activity-section">
        <div class="section-header">
          <h3>系统活动</h3>
          <n-space>
            <n-button size="small" @click="refreshActivities">
              <template #icon>
                <n-icon>
                  <RefreshOutline />
                </n-icon>
              </template>
              刷新
            </n-button>
            <n-button size="small" @click="$router.push('/monitoring/realtime')">
              查看更多
            </n-button>
          </n-space>
        </div>
        
        <div class="activity-timeline">
          <n-timeline>
            <n-timeline-item
              v-for="activity in recentActivities.slice(0, 8)"
              :key="activity.id"
              :type="getActivityType(activity.type)"
              :time="formatRelativeTime(activity.timestamp)"
            >
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-description">{{ activity.content }}</div>
              </div>
            </n-timeline-item>
          </n-timeline>
        </div>
      </div>
    </div>

    <!-- 快速操作区域 -->
    <div class="quick-actions-section">
      <h3>快速操作</h3>
      <div class="quick-actions-grid">
        <div class="quick-action-card" @click="$router.push('/mcp/tools')">
          <n-icon size="32" color="#667eea">
            <ConstructOutline />
          </n-icon>
          <h4>工具管理</h4>
          <p>管理和调用MCP工具</p>
        </div>
        
        <div class="quick-action-card" @click="$router.push('/subscriptions')">
          <n-icon size="32" color="#52c41a">
            <CardOutline />
          </n-icon>
          <h4>订阅管理</h4>
          <p>管理用户订阅和权限</p>
        </div>
        
        <div class="quick-action-card" @click="$router.push('/monitoring/realtime')">
          <n-icon size="32" color="#faad14">
            <AnalyticsOutline />
          </n-icon>
          <h4>实时监控</h4>
          <p>查看系统实时状态</p>
        </div>
        
        <div class="quick-action-card" @click="$router.push('/settings/profile')">
          <n-icon size="32" color="#722ed1">
            <SettingsOutline />
          </n-icon>
          <h4>系统设置</h4>
          <p>配置系统参数</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { use } from 'echarts/core'
import { PieChart } from 'echarts/charts'
import { TitleComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import VChart from 'vue-echarts'
import {
  ServerOutline,
  FlaskOutline,
  CardOutline,
  SwapHorizontalOutline,
  PulseOutline,
  RefreshOutline,
  ConstructOutline,
  AnalyticsOutline,
  SettingsOutline
} from '@vicons/ionicons5'
import { useAuthStore } from '@/stores/auth'
import { useRealtimeActivities, useServiceMonitoring, useApiCallMonitoring } from '@/composables/useRealtimeData'
import RealtimeStatusCard from '@/components/RealtimeStatusCard.vue'
import RealtimeChart from '@/components/RealtimeChart.vue'

// 注册ECharts组件
use([CanvasRenderer, PieChart, TitleComponent, TooltipComponent, LegendComponent])

const router = useRouter()
const authStore = useAuthStore()

// 实时数据
const { recentActivities } = useRealtimeActivities()
const { serviceStatuses, activeServicesCount } = useServiceMonitoring()
const { apiCallStats, apiSuccessRate } = useApiCallMonitoring()

// 响应式数据
const serviceMetrics = ref({
  total: 12,
  running: 8,
  healthy: 7,
  error: 1
})

const subscriptionMetrics = ref({
  active: 156,
  trend: 12.5,
  total: 189
})

const apiMetrics = ref({
  totalCalls: 2847,
  trend: 8.3,
  successRate: 98.5
})

const systemHealth = ref({
  score: 94,
  status: 'healthy'
})

// 时间问候语
const currentTimeGreeting = computed(() => {
  const hour = new Date().getHours()
  if (hour < 6) return '深夜好'
  if (hour < 12) return '上午好'
  if (hour < 18) return '下午好'
  return '晚上好'
})

// 图表数据
const subscriptionChartData = computed(() => {
  const now = Date.now()
  return Array.from({ length: 7 }, (_, i) => ({
    timestamp: now - (6 - i) * 24 * 60 * 60 * 1000,
    value: Math.floor(Math.random() * 20) + 140
  }))
})

const apiCallChartData = computed(() => {
  const now = Date.now()
  return Array.from({ length: 24 }, (_, i) => ({
    timestamp: now - (23 - i) * 60 * 60 * 1000,
    value: Math.floor(Math.random() * 200) + 50
  }))
})

const apiTrendData = ref<Array<{ timestamp: number; value: number }>>([])

// 服务状态饼图配置
const serviceStatusOption = computed(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '服务状态',
      type: 'pie',
      radius: '50%',
      data: [
        { value: serviceMetrics.value.running, name: '运行中', itemStyle: { color: '#52c41a' } },
        { value: serviceMetrics.value.total - serviceMetrics.value.running - serviceMetrics.value.error, name: '已停止', itemStyle: { color: '#d9d9d9' } },
        { value: serviceMetrics.value.error, name: '错误', itemStyle: { color: '#ff4d4f' } }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
}))

// 工具函数
const getHealthStatus = (score: number) => {
  if (score >= 90) return 'success'
  if (score >= 70) return 'warning'
  return 'error'
}

const getActivityType = (type: string) => {
  const typeMap: Record<string, any> = {
    SERVICE_STATUS: 'info',
    API_CALL: 'success',
    TOOL_EXECUTION: 'warning',
    SUBSCRIPTION_UPDATE: 'info',
    SYSTEM_ALERT: 'error'
  }
  return typeMap[type] || 'default'
}

const formatRelativeTime = (timestamp: string) => {
  const now = Date.now()
  const time = new Date(timestamp).getTime()
  const diff = now - time
  
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  const days = Math.floor(diff / 86400000)
  
  if (days > 0) return `${days}天前`
  if (hours > 0) return `${hours}小时前`
  if (minutes > 0) return `${minutes}分钟前`
  return '刚刚'
}

// 事件处理
const handleServiceAction = (key: string) => {
  switch (key) {
    case 'view-services':
      router.push('/mcp/overview')
      break
    case 'add-service':
      // 触发添加服务事件
      break
  }
}

const handleSubscriptionAction = (key: string) => {
  router.push('/subscriptions')
}

const refreshApiTrend = () => {
  const now = Date.now()
  apiTrendData.value = Array.from({ length: 48 }, (_, i) => ({
    timestamp: now - (47 - i) * 30 * 60 * 1000, // 30分钟间隔
    value: Math.floor(Math.random() * 100) + 50
  }))
}

const refreshServiceStatus = () => {
  // 刷新服务状态数据
  serviceMetrics.value = {
    total: Math.floor(Math.random() * 5) + 10,
    running: Math.floor(Math.random() * 8) + 6,
    healthy: Math.floor(Math.random() * 7) + 5,
    error: Math.floor(Math.random() * 2)
  }
}

const refreshActivities = () => {
  // 刷新活动数据
  console.log('刷新系统活动')
}

// 生命周期
onMounted(() => {
  refreshApiTrend()
})
</script>

<style scoped>
.dashboard {
  padding: var(--space-6);
  max-width: 1400px;
  margin: 0 auto;
  min-height: 100%;
  position: relative;
}

.welcome-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-8);
  padding: var(--space-8);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-3xl);
  box-shadow: var(--glass-shadow);
  color: var(--text-primary);
  position: relative;
  overflow: hidden;
}

.welcome-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  opacity: 0.1;
  z-index: -1;
}

.welcome-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  opacity: 0.8;
}

.welcome-title {
  margin: 0 0 var(--space-2) 0;
  font-size: 32px;
  font-weight: 700;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: none;
  position: relative;
  z-index: 1;
}

.welcome-subtitle {
  margin: 0;
  font-size: 16px;
  color: var(--text-secondary);
  position: relative;
  z-index: 1;
}

.welcome-actions {
  display: flex;
  gap: var(--space-3);
  position: relative;
  z-index: 1;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.metrics-grid > * {
  animation: slideInUp var(--duration-slow) var(--ease-out);
}

.metrics-grid > *:nth-child(1) { animation-delay: 0.1s; }
.metrics-grid > *:nth-child(2) { animation-delay: 0.2s; }
.metrics-grid > *:nth-child(3) { animation-delay: 0.3s; }
.metrics-grid > *:nth-child(4) { animation-delay: 0.4s; }

.charts-section {
  margin-bottom: var(--space-8);
}

.charts-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--space-6);
  margin-bottom: var(--space-6);
}

.chart-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
  box-shadow: var(--glass-shadow);
  position: relative;
  overflow: hidden;
  transition: all var(--duration-normal) var(--ease-out);
}

.chart-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  opacity: 0.6;
}

.chart-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl), var(--shadow-glow);
  border-color: var(--primary-color-light);
}

.chart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
  position: relative;
  z-index: 1;
}

.chart-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.activity-section {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
  box-shadow: var(--glass-shadow);
  position: relative;
  overflow: hidden;
  transition: all var(--duration-normal) var(--ease-out);
}

.activity-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  opacity: 0.6;
}

.activity-section:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl), var(--shadow-glow);
  border-color: var(--primary-color-light);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  position: relative;
  z-index: 1;
}

.activity-timeline {
  max-height: 400px;
  overflow-y: auto;
  position: relative;
  z-index: 1;
}

.activity-content {
  padding-left: var(--space-2);
}

.activity-title {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.activity-description {
  color: var(--text-secondary);
  font-size: 14px;
}

.quick-actions-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.quick-actions-section h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.quick-action-card {
  text-align: center;
  padding: 24px;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-action-card:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
}

.quick-action-card h4 {
  margin: 12px 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.quick-action-card p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

/* 动画关键帧 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .charts-grid {
    grid-template-columns: 1fr;
  }

  .metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }
}

@media (max-width: 768px) {
  .dashboard {
    padding: 16px;
  }
  
  .welcome-section {
    flex-direction: column;
    text-align: center;
    gap: 20px;
    padding: 24px;
  }
  
  .welcome-title {
    font-size: 24px;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .quick-actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .quick-actions-grid {
    grid-template-columns: 1fr;
  }
}
</style>
