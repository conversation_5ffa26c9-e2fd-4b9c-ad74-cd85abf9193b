<template>
  <div class="api-test-page">
    <div class="page-header">
      <h1>MCP API 测试页面</h1>
      <p>测试前端与后端MCP服务的API对接情况</p>
    </div>

    <div class="test-sections">
      <!-- 本地MCP服务测试 -->
      <div class="test-section">
        <h2>本地MCP服务测试</h2>
        <div class="test-actions">
          <n-button type="primary" @click="testLocalServices" :loading="localLoading">
            测试获取本地服务
          </n-button>
        </div>
        <div class="test-result">
          <h3>测试结果:</h3>
          <pre>{{ localResult }}</pre>
        </div>
      </div>

      <!-- 远程MCP服务测试 -->
      <div class="test-section">
        <h2>远程MCP服务测试</h2>
        <div class="test-actions">
          <n-button type="primary" @click="testRemoteServices" :loading="remoteLoading">
            测试获取远程服务
          </n-button>
        </div>
        <div class="test-result">
          <h3>测试结果:</h3>
          <pre>{{ remoteResult }}</pre>
        </div>
      </div>

      <!-- 网关健康检查 -->
      <div class="test-section">
        <h2>网关健康检查</h2>
        <div class="test-actions">
          <n-button type="primary" @click="testGatewayHealth" :loading="healthLoading">
            测试网关健康状态
          </n-button>
        </div>
        <div class="test-result">
          <h3>测试结果:</h3>
          <pre>{{ healthResult }}</pre>
        </div>
      </div>

      <!-- API响应时间测试 -->
      <div class="test-section">
        <h2>API响应时间测试</h2>
        <div class="test-actions">
          <n-button type="primary" @click="testApiPerformance" :loading="perfLoading">
            测试API性能
          </n-button>
        </div>
        <div class="test-result">
          <h3>测试结果:</h3>
          <pre>{{ perfResult }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { mcpApi } from '@/api/mcp'
import { useMessage } from 'naive-ui'

const message = useMessage()

// 响应式数据
const localLoading = ref(false)
const remoteLoading = ref(false)
const healthLoading = ref(false)
const perfLoading = ref(false)

const localResult = ref('')
const remoteResult = ref('')
const healthResult = ref('')
const perfResult = ref('')

// 测试本地MCP服务
const testLocalServices = async () => {
  localLoading.value = true
  try {
    const startTime = Date.now()
    const result = await mcpApi.local.getServices()
    const endTime = Date.now()
    
    localResult.value = JSON.stringify({
      success: true,
      responseTime: `${endTime - startTime}ms`,
      data: result.data,
      message: result.message,
      timestamp: result.timestamp
    }, null, 2)
    
    message.success('本地MCP服务测试成功')
  } catch (error: any) {
    localResult.value = JSON.stringify({
      success: false,
      error: error.message || error,
      timestamp: new Date().toISOString()
    }, null, 2)
    
    message.error('本地MCP服务测试失败')
  } finally {
    localLoading.value = false
  }
}

// 测试远程MCP服务
const testRemoteServices = async () => {
  remoteLoading.value = true
  try {
    const startTime = Date.now()
    const result = await mcpApi.remote.getServices()
    const endTime = Date.now()
    
    remoteResult.value = JSON.stringify({
      success: true,
      responseTime: `${endTime - startTime}ms`,
      data: result.data,
      message: result.message,
      timestamp: result.timestamp
    }, null, 2)
    
    message.success('远程MCP服务测试成功')
  } catch (error: any) {
    remoteResult.value = JSON.stringify({
      success: false,
      error: error.message || error,
      timestamp: new Date().toISOString()
    }, null, 2)
    
    message.error('远程MCP服务测试失败')
  } finally {
    remoteLoading.value = false
  }
}

// 测试网关健康状态
const testGatewayHealth = async () => {
  healthLoading.value = true
  try {
    const startTime = Date.now()
    
    // 测试多个端点
    const tests = await Promise.allSettled([
      mcpApi.local.getServices(),
      mcpApi.remote.getServices()
    ])
    
    const endTime = Date.now()
    
    healthResult.value = JSON.stringify({
      success: true,
      responseTime: `${endTime - startTime}ms`,
      tests: {
        localMcp: tests[0].status === 'fulfilled' ? 'SUCCESS' : 'FAILED',
        remoteMcp: tests[1].status === 'fulfilled' ? 'SUCCESS' : 'FAILED'
      },
      gateway: 'HEALTHY',
      timestamp: new Date().toISOString()
    }, null, 2)
    
    message.success('网关健康检查完成')
  } catch (error: any) {
    healthResult.value = JSON.stringify({
      success: false,
      error: error.message || error,
      timestamp: new Date().toISOString()
    }, null, 2)
    
    message.error('网关健康检查失败')
  } finally {
    healthLoading.value = false
  }
}

// 测试API性能
const testApiPerformance = async () => {
  perfLoading.value = true
  try {
    const results = []
    
    // 连续测试5次
    for (let i = 0; i < 5; i++) {
      const startTime = Date.now()
      await mcpApi.local.getServices()
      const endTime = Date.now()
      results.push(endTime - startTime)
    }
    
    const avgTime = results.reduce((a, b) => a + b, 0) / results.length
    const minTime = Math.min(...results)
    const maxTime = Math.max(...results)
    
    perfResult.value = JSON.stringify({
      success: true,
      performance: {
        averageResponseTime: `${avgTime.toFixed(2)}ms`,
        minResponseTime: `${minTime}ms`,
        maxResponseTime: `${maxTime}ms`,
        allResults: results.map(t => `${t}ms`)
      },
      timestamp: new Date().toISOString()
    }, null, 2)
    
    message.success('API性能测试完成')
  } catch (error: any) {
    perfResult.value = JSON.stringify({
      success: false,
      error: error.message || error,
      timestamp: new Date().toISOString()
    }, null, 2)
    
    message.error('API性能测试失败')
  } finally {
    perfLoading.value = false
  }
}
</script>

<style scoped>
.api-test-page {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 32px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: #333;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.test-sections {
  display: grid;
  gap: 24px;
}

.test-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.test-section h2 {
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.test-actions {
  margin-bottom: 16px;
}

.test-result {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.test-result h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.test-result pre {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  font-size: 12px;
  line-height: 1.5;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
