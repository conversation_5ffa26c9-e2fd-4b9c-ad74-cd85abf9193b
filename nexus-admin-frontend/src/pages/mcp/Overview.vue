<template>
  <div class="mcp-overview">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">MCP服务总览</h1>
        <p class="page-description">管理和监控所有MCP服务的状态和性能</p>
      </div>
      
      <div class="header-actions">
        <n-dropdown :options="quickStartOptions" @select="handleQuickStart">
          <n-button type="primary">
            <template #icon>
              <n-icon>
                <AddOutline />
              </n-icon>
            </template>
            启动服务
            <template #suffix>
              <n-icon>
                <ChevronDownOutline />
              </n-icon>
            </template>
          </n-button>
        </n-dropdown>

        <n-button @click="showBatchModal = true">
          <template #icon>
            <n-icon>
              <LayersOutline />
            </n-icon>
          </template>
          批量操作
        </n-button>

        <n-button @click="refreshServices">
          <template #icon>
            <n-icon>
              <RefreshOutline />
            </n-icon>
          </template>
          刷新
        </n-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <RealtimeStatusCard
        title="总服务数"
        :value="totalServices"
        :icon="ServerOutline"
        status="info"
        :show-trend="true"
        :trend="servicesTrend"
        description="已注册的MCP服务总数"
      />
      
      <RealtimeStatusCard
        title="运行中"
        :value="runningServices"
        :icon="PlayCircleOutline"
        status="success"
        :show-progress="true"
        :progress="runningServices"
        :progress-max="totalServices"
        :progress-text="`${runningServices}/${totalServices}`"
        description="当前正在运行的服务"
      />
      
      <RealtimeStatusCard
        title="健康服务"
        :value="healthyServices"
        :icon="CheckmarkCircleOutline"
        status="success"
        :show-trend="true"
        :trend="healthTrend"
        description="健康检查通过的服务"
      />
      
      <RealtimeStatusCard
        title="错误服务"
        :value="errorServices"
        :icon="AlertCircleOutline"
        :status="errorServices > 0 ? 'error' : 'success'"
        :show-chart="true"
        :chart-data="errorChartData"
        chart-color="#ff4d4f"
        description="存在错误的服务数量"
      />
    </div>

    <!-- 服务列表 -->
    <div class="services-section">
      <div class="section-header">
        <h2>服务列表</h2>
        <div class="section-actions">
          <n-input
            v-model:value="searchQuery"
            placeholder="搜索服务..."
            clearable
            style="width: 300px;"
          >
            <template #prefix>
              <n-icon>
                <SearchOutline />
              </n-icon>
            </template>
          </n-input>
          
          <n-select
            v-model:value="statusFilter"
            placeholder="状态筛选"
            :options="statusOptions"
            clearable
            style="width: 150px;"
          />
          
          <n-select
            v-model:value="typeFilter"
            placeholder="类型筛选"
            :options="typeOptions"
            clearable
            style="width: 150px;"
          />
        </div>
      </div>

      <n-data-table
        :columns="tableColumns"
        :data="filteredServices"
        :loading="loading"
        :pagination="paginationConfig"
        :row-key="(row: McpService) => row.id"
        striped
        size="medium"
        class="services-table"
      />
    </div>

    <!-- MCP服务注册对话框 -->
    <McpServiceRegisterModal
      v-model:show="showRegisterModal"
      @success="handleRegisterSuccess"
    />

    <!-- 服务详情抽屉 -->
    <ServiceDetailDrawer
      v-model:show="showDetailDrawer"
      :service="selectedService"
      @update="handleServiceUpdate"
    />

    <!-- 批量操作对话框 -->
    <n-modal v-model:show="showBatchModal" preset="card" title="批量操作" size="large">
      <div class="batch-operations">
        <n-tabs v-model:value="batchTab" type="line">
          <n-tab-pane name="start" tab="批量启动">
            <div class="batch-start">
              <n-alert type="info" style="margin-bottom: 16px;">
                输入多个启动命令，每行一个，支持npx、uvx、python等标准命令
              </n-alert>

              <n-input
                v-model:value="batchCommands"
                type="textarea"
                placeholder="npx -y @modelcontextprotocol/server-filesystem /path/to/files&#10;uvx mcp-server-git --repository /path/to/repo&#10;npx -y @modelcontextprotocol/server-memory"
                :rows="8"
              />

              <div style="margin-top: 16px; text-align: right;">
                <n-button type="primary" @click="handleBatchStart" :loading="batchStarting">
                  批量启动
                </n-button>
              </div>
            </div>
          </n-tab-pane>

          <n-tab-pane name="manage" tab="批量管理">
            <div class="batch-manage">
              <n-alert type="warning" style="margin-bottom: 16px;">
                选择要操作的服务，然后执行批量操作
              </n-alert>

              <n-data-table
                v-model:checked-row-keys="selectedServiceIds"
                :columns="batchTableColumns"
                :data="filteredServices"
                :row-key="(row: McpService) => row.id"
                size="small"
                max-height="300"
              />

              <div style="margin-top: 16px; display: flex; gap: 8px;">
                <n-button
                  type="success"
                  @click="batchOperation('start')"
                  :loading="batchOperating"
                  :disabled="selectedServiceIds.length === 0"
                >
                  批量启动
                </n-button>
                <n-button
                  type="warning"
                  @click="batchOperation('stop')"
                  :loading="batchOperating"
                  :disabled="selectedServiceIds.length === 0"
                >
                  批量停止
                </n-button>
                <n-button
                  type="error"
                  @click="batchOperation('delete')"
                  :loading="batchOperating"
                  :disabled="selectedServiceIds.length === 0"
                >
                  批量删除
                </n-button>
              </div>
            </div>
          </n-tab-pane>
        </n-tabs>
      </div>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useQuery } from '@tanstack/vue-query'
import { useMessage } from 'naive-ui'
import {
  ServerOutline,
  AddOutline,
  RefreshOutline,
  SearchOutline,
  PlayCircleOutline,
  CheckmarkCircleOutline,
  AlertCircleOutline,
  SettingsOutline,
  TrashOutline,
  PlayOutline,
  StopOutline,
  ChevronDownOutline,
  LayersOutline,
  TerminalOutline,
  CodeOutline
} from '@vicons/ionicons5'
import { mcpApi } from '@/api/mcp'
import { useServiceMonitoring } from '@/composables/useRealtimeData'
import RealtimeStatusCard from '@/components/RealtimeStatusCard.vue'
import McpServiceRegisterModal from '@/components/McpServiceRegisterModal.vue'
import ServiceDetailDrawer from '@/components/ServiceDetailDrawer.vue'
import type { McpService } from '@/types/api'
import type { DataTableColumns } from 'naive-ui'

const message = useMessage()

// 响应式数据
const searchQuery = ref('')
const statusFilter = ref<string | null>(null)
const typeFilter = ref<string | null>(null)
const showRegisterModal = ref(false)
const showDetailDrawer = ref(false)
const selectedService = ref<McpService | null>(null)

// 批量操作相关
const showBatchModal = ref(false)
const batchTab = ref('start')
const batchCommands = ref('')
const batchStarting = ref(false)
const batchOperating = ref(false)
const selectedServiceIds = ref<number[]>([])

// 快速启动选项
const quickStartOptions = [
  {
    label: '命令启动',
    key: 'command',
    icon: () => h('n-icon', {}, h(TerminalOutline))
  },
  {
    label: '手动注册',
    key: 'manual',
    icon: () => h('n-icon', {}, h(SettingsOutline))
  },
  {
    type: 'divider',
    key: 'divider1'
  },
  {
    label: '文件系统服务',
    key: 'filesystem',
    icon: () => h('n-icon', {}, h(CodeOutline))
  },
  {
    label: 'Git仓库服务',
    key: 'git',
    icon: () => h('n-icon', {}, h(CodeOutline))
  },
  {
    label: '内存服务',
    key: 'memory',
    icon: () => h('n-icon', {}, h(ServerOutline))
  }
]

// 实时监控
const { serviceStatuses } = useServiceMonitoring()

// 查询服务列表
const {
  data: services,
  isLoading: loading,
  refetch: refreshServices
} = useQuery({
  queryKey: ['mcp-services'],
  queryFn: async () => {
    const [localServices, remoteServices] = await Promise.all([
      mcpApi.local.getServices(),
      mcpApi.remote.getServices()
    ])
    return [...localServices.data, ...remoteServices.data]
  },
  refetchInterval: 30000 // 30秒自动刷新
})

// 计算属性
const totalServices = computed(() => services.value?.length || 0)

const runningServices = computed(() => 
  services.value?.filter(s => s.status === 'RUNNING').length || 0
)

const healthyServices = computed(() => 
  services.value?.filter(s => s.isHealthy).length || 0
)

const errorServices = computed(() => 
  services.value?.filter(s => s.status === 'ERROR').length || 0
)

const servicesTrend = computed(() => {
  // 模拟趋势计算
  return Math.random() * 10 - 5
})

const healthTrend = computed(() => {
  // 模拟健康趋势
  return Math.random() * 5
})

const errorChartData = computed(() => {
  // 模拟错误数据图表
  const now = Date.now()
  return Array.from({ length: 20 }, (_, i) => ({
    timestamp: now - (19 - i) * 60000,
    value: Math.floor(Math.random() * 5)
  }))
})

// 筛选选项
const statusOptions = [
  { label: '运行中', value: 'RUNNING' },
  { label: '已停止', value: 'STOPPED' },
  { label: '错误', value: 'ERROR' },
  { label: '启动中', value: 'STARTING' },
  { label: '停止中', value: 'STOPPING' }
]

const typeOptions = [
  { label: '本地服务', value: 'LOCAL' },
  { label: '远程服务', value: 'REMOTE' }
]

// 过滤后的服务列表
const filteredServices = computed(() => {
  if (!services.value) return []
  
  return services.value.filter(service => {
    const matchesSearch = !searchQuery.value || 
      service.serviceName.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      service.displayName.toLowerCase().includes(searchQuery.value.toLowerCase())
    
    const matchesStatus = !statusFilter.value || service.status === statusFilter.value
    const matchesType = !typeFilter.value || service.serviceType === typeFilter.value
    
    return matchesSearch && matchesStatus && matchesType
  })
})

// 分页配置
const paginationConfig = {
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  showQuickJumper: true
}

// 批量操作表格列配置
const batchTableColumns = [
  {
    type: 'selection'
  },
  {
    title: '服务名称',
    key: 'serviceName',
    render: (row: McpService) => row.displayName || row.serviceName
  },
  {
    title: '类型',
    key: 'serviceType',
    width: 100,
    render: (row: McpService) => h('n-tag', {
      type: row.serviceType === 'LOCAL' ? 'info' : 'success',
      size: 'small'
    }, row.serviceType)
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row: McpService) => {
      const statusConfig = {
        RUNNING: { type: 'success', text: '运行中' },
        STOPPED: { type: 'default', text: '已停止' },
        ERROR: { type: 'error', text: '错误' },
        STARTING: { type: 'warning', text: '启动中' },
        STOPPING: { type: 'warning', text: '停止中' }
      }
      const config = statusConfig[row.status] || { type: 'default', text: row.status }

      return h('n-tag', {
        type: config.type,
        size: 'small'
      }, config.text)
    }
  }
]

// 表格列配置
const tableColumns: DataTableColumns<McpService> = [
  {
    title: '服务信息',
    key: 'serviceName',
    width: 250,
    render: (row) => h('div', { class: 'service-name-cell' }, [
      h('div', { class: 'service-name' }, [
        h('span', row.displayName || row.serviceName),
        // 显示实例标识
        row.metadata?.instanceId && h('n-tag', {
          size: 'tiny',
          type: 'info',
          style: 'margin-left: 8px;'
        }, `#${row.metadata.instanceId}`)
      ]),
      h('div', { class: 'service-id' }, `ID: ${row.id}`),
      // 显示启动命令（如果有）
      row.metadata?.startCommand && h('div', {
        class: 'service-command',
        title: row.metadata.startCommand
      }, `命令: ${row.metadata.startCommand.length > 30 ?
        row.metadata.startCommand.substring(0, 30) + '...' :
        row.metadata.startCommand}`)
    ])
  },
  {
    title: '类型/来源',
    key: 'serviceType',
    width: 120,
    render: (row) => h('div', { class: 'service-type-cell' }, [
      h('n-tag', {
        type: row.serviceType === 'LOCAL' ? 'info' : 'success',
        size: 'small'
      }, row.serviceType),
      // 显示启动方式
      row.metadata?.startMethod && h('div', {
        class: 'start-method',
        style: 'margin-top: 4px; font-size: 11px; color: #999;'
      }, row.metadata.startMethod === 'command' ? '命令启动' : '手动注册')
    ])
  },
  {
    title: '状态',
    key: 'status',
    width: 120,
    render: (row) => {
      const statusConfig = {
        RUNNING: { type: 'success', text: '运行中' },
        STOPPED: { type: 'default', text: '已停止' },
        ERROR: { type: 'error', text: '错误' },
        STARTING: { type: 'warning', text: '启动中' },
        STOPPING: { type: 'warning', text: '停止中' }
      }
      const config = statusConfig[row.status] || { type: 'default', text: row.status }
      
      return h('n-tag', {
        type: config.type,
        size: 'small'
      }, config.text)
    }
  },
  {
    title: '健康状态',
    key: 'isHealthy',
    width: 100,
    render: (row) => h('n-tag', {
      type: row.isHealthy ? 'success' : 'error',
      size: 'small'
    }, row.isHealthy ? '健康' : '异常')
  },
  {
    title: '工具/资源',
    key: 'counts',
    width: 120,
    render: (row) => h('div', { class: 'counts-cell' }, [
      h('span', `${row.toolCount || 0} 工具`),
      h('br'),
      h('span', `${row.resourceCount || 0} 资源`)
    ])
  },
  {
    title: '最后检查',
    key: 'lastHealthCheck',
    width: 150,
    render: (row) => row.lastHealthCheck 
      ? new Date(row.lastHealthCheck).toLocaleString()
      : '-'
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    render: (row) => h('div', { class: 'action-buttons' }, [
      h('n-button', {
        size: 'small',
        quaternary: true,
        onClick: () => viewServiceDetail(row)
      }, { default: () => '详情' }),
      
      h('n-button', {
        size: 'small',
        quaternary: true,
        type: row.status === 'RUNNING' ? 'error' : 'success',
        onClick: () => toggleService(row)
      }, { 
        default: () => row.status === 'RUNNING' ? '停止' : '启动',
        icon: () => h('n-icon', {}, 
          row.status === 'RUNNING' ? h(StopOutline) : h(PlayOutline)
        )
      }),
      
      h('n-button', {
        size: 'small',
        quaternary: true,
        onClick: () => configureService(row)
      }, {
        default: () => '配置',
        icon: () => h('n-icon', {}, h(SettingsOutline))
      }),
      
      h('n-popconfirm', {
        onPositiveClick: () => deleteService(row)
      }, {
        default: () => '确定删除此服务吗？',
        trigger: () => h('n-button', {
          size: 'small',
          quaternary: true,
          type: 'error'
        }, {
          default: () => '删除',
          icon: () => h('n-icon', {}, h(TrashOutline))
        })
      })
    ])
  }
]

// 事件处理
const viewServiceDetail = (service: McpService) => {
  selectedService.value = service
  showDetailDrawer.value = true
}

const toggleService = async (service: McpService) => {
  try {
    if (service.status === 'RUNNING') {
      await (service.serviceType === 'LOCAL' 
        ? mcpApi.local.stopService(service.id)
        : mcpApi.remote.stopService(service.id))
      message.success(`服务 ${service.serviceName} 已停止`)
    } else {
      await (service.serviceType === 'LOCAL'
        ? mcpApi.local.startService(service.id)
        : mcpApi.remote.startService(service.id))
      message.success(`服务 ${service.serviceName} 已启动`)
    }
    refreshServices()
  } catch (error: any) {
    message.error(error.message || '操作失败')
  }
}

const configureService = (service: McpService) => {
  // 跳转到服务配置页面
  console.log('配置服务:', service)
}

const deleteService = async (service: McpService) => {
  try {
    await (service.serviceType === 'LOCAL'
      ? mcpApi.local.deleteService?.(service.id)
      : mcpApi.remote.deleteService(service.id))
    message.success(`服务 ${service.serviceName} 已删除`)
    refreshServices()
  } catch (error: any) {
    message.error(error.message || '删除失败')
  }
}

const handleRegisterSuccess = () => {
  message.success('服务注册成功')
  refreshServices()
}

const handleServiceUpdate = () => {
  refreshServices()
}

// 快速启动处理
const handleQuickStart = (key: string) => {
  if (key === 'command' || key === 'manual') {
    showRegisterModal.value = true
    return
  }

  // 预设命令快速启动
  const commandMap = {
    filesystem: 'npx -y @modelcontextprotocol/server-filesystem /tmp/allowed-files',
    git: 'uvx mcp-server-git --repository /tmp/demo-repo',
    memory: 'npx -y @modelcontextprotocol/server-memory'
  }

  const command = commandMap[key]
  if (command) {
    quickStartService(command)
  }
}

// 快速启动服务
const quickStartService = async (command: string) => {
  try {
    const result = await mcpApi.commands.startByCommand({ command })
    if (result.data.success) {
      message.success(`服务启动成功: ${result.data.serviceName}`)
      refreshServices()
    } else {
      message.error(`服务启动失败: ${result.data.message}`)
    }
  } catch (error: any) {
    message.error(error.message || '启动失败')
  }
}

// 批量启动服务
const handleBatchStart = async () => {
  if (!batchCommands.value.trim()) {
    message.warning('请输入启动命令')
    return
  }

  const commands = batchCommands.value
    .split('\n')
    .map(cmd => cmd.trim())
    .filter(cmd => cmd.length > 0)

  if (commands.length === 0) {
    message.warning('请输入有效的启动命令')
    return
  }

  batchStarting.value = true
  try {
    const result = await mcpApi.commands.startBatch(commands)

    let successCount = 0
    let failCount = 0

    Object.values(result.data).forEach((res: any) => {
      if (res.success) {
        successCount++
      } else {
        failCount++
      }
    })

    message.success(`批量启动完成: ${successCount} 成功, ${failCount} 失败`)
    refreshServices()
    showBatchModal.value = false
    batchCommands.value = ''
  } catch (error: any) {
    message.error(error.message || '批量启动失败')
  } finally {
    batchStarting.value = false
  }
}

// 批量操作
const batchOperation = async (operation: 'start' | 'stop' | 'delete') => {
  if (selectedServiceIds.value.length === 0) {
    message.warning('请选择要操作的服务')
    return
  }

  batchOperating.value = true
  try {
    const promises = selectedServiceIds.value.map(async (id) => {
      const service = services.value?.find(s => s.id === id)
      if (!service) return

      try {
        if (operation === 'start') {
          await (service.serviceType === 'LOCAL'
            ? mcpApi.local.startService(id)
            : mcpApi.remote.startService(id))
        } else if (operation === 'stop') {
          await (service.serviceType === 'LOCAL'
            ? mcpApi.local.stopService(id)
            : mcpApi.remote.stopService(id))
        } else if (operation === 'delete') {
          await (service.serviceType === 'LOCAL'
            ? mcpApi.local.deleteService?.(id)
            : mcpApi.remote.deleteService(id))
        }
        return { success: true, id }
      } catch (error) {
        return { success: false, id, error }
      }
    })

    const results = await Promise.all(promises)
    const successCount = results.filter(r => r?.success).length
    const failCount = results.filter(r => r && !r.success).length

    const operationText = {
      start: '启动',
      stop: '停止',
      delete: '删除'
    }[operation]

    message.success(`批量${operationText}完成: ${successCount} 成功, ${failCount} 失败`)
    refreshServices()
    selectedServiceIds.value = []
    showBatchModal.value = false
  } catch (error: any) {
    message.error(error.message || '批量操作失败')
  } finally {
    batchOperating.value = false
  }
}

// 生命周期
onMounted(() => {
  refreshServices()
})
</script>

<style scoped>
.mcp-overview {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 32px;
}

.header-content h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: #333;
}

.page-description {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.services-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.section-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.section-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.services-table {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.service-name-cell) {
  .service-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 2px;
    display: flex;
    align-items: center;
  }

  .service-id {
    font-size: 12px;
    color: #999;
    margin-bottom: 2px;
  }

  .service-command {
    font-size: 11px;
    color: #666;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    background: #f5f5f5;
    padding: 2px 6px;
    border-radius: 3px;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

:deep(.service-type-cell) {
  .start-method {
    font-size: 11px;
    color: #999;
  }
}

:deep(.counts-cell) {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

:deep(.action-buttons) {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.batch-operations {
  padding: 0;
}

.batch-start,
.batch-manage {
  padding: 16px 0;
}

.batch-start .n-input {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.batch-manage .n-data-table {
  border-radius: 8px;
  overflow: hidden;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }
}

@media (max-width: 768px) {
  .mcp-overview {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .section-actions {
    width: 100%;
    flex-direction: column;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
</style>
