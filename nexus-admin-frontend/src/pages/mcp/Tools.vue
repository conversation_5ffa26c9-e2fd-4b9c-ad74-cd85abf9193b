<template>
  <div class="tools-page">
    <div class="page-header">
      <h1>MCP 工具管理</h1>
      <p>管理和配置MCP工具集</p>
    </div>

    <n-card>
      <div class="tools-content">
        <n-empty description="MCP工具功能开发中...">
          <template #extra>
            <n-button size="small">刷新</n-button>
          </template>
        </n-empty>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { NCard, NEmpty, NButton } from 'naive-ui'
</script>

<style scoped>
.tools-page {
  padding: 1rem;
}

.page-header {
  margin-bottom: 1rem;
}

.page-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
}

.page-header p {
  margin: 0;
  color: #666;
}

.tools-content {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
