<template>
  <div class="api-calls-page">
    <div class="page-header">
      <h1>API 调用监控</h1>
      <p>实时监控API调用情况和性能指标</p>
    </div>

    <n-card>
      <div class="api-calls-content">
        <n-empty description="API调用监控功能开发中...">
          <template #extra>
            <n-button size="small">刷新</n-button>
          </template>
        </n-empty>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { NCard, NEmpty, NButton } from 'naive-ui'
</script>

<style scoped>
.api-calls-page {
  padding: 1rem;
}

.page-header {
  margin-bottom: 1rem;
}

.page-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
}

.page-header p {
  margin: 0;
  color: #666;
}

.api-calls-content {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
