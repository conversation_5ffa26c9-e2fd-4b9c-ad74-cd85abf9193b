<template>
  <div class="realtime-monitoring">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">实时监控</h1>
        <p class="page-description">实时监控系统状态、服务健康度和性能指标</p>
      </div>
      
      <div class="header-actions">
        <n-space>
          <n-tag :type="connectionStatus.type" size="large">
            <template #icon>
              <n-icon>
                <component :is="connectionStatus.icon" />
              </n-icon>
            </template>
            {{ connectionStatus.text }}
          </n-tag>
          
          <n-button @click="refreshAllData">
            <template #icon>
              <n-icon>
                <RefreshOutline />
              </n-icon>
            </template>
            刷新数据
          </n-button>
          
          <n-button @click="showSettings = true">
            <template #icon>
              <n-icon>
                <SettingsOutline />
              </n-icon>
            </template>
            监控设置
          </n-button>
        </n-space>
      </div>
    </div>

    <!-- 实时指标卡片 -->
    <div class="metrics-grid">
      <RealtimeStatusCard
        title="系统负载"
        :value="systemMetrics.cpuUsage"
        unit="%"
        :icon="HardwareChipOutline"
        :status="getMetricStatus(systemMetrics.cpuUsage, 80, 90)"
        :show-progress="true"
        :progress="systemMetrics.cpuUsage"
        :progress-max="100"
        description="CPU使用率"
        :show-chart="true"
        :chart-data="cpuChartData"
        chart-color="#ff6b6b"
      />
      
      <RealtimeStatusCard
        title="内存使用"
        :value="systemMetrics.memoryUsage"
        unit="%"
        :icon="HardwareChipOutline"
        :status="getMetricStatus(systemMetrics.memoryUsage, 75, 85)"
        :show-progress="true"
        :progress="systemMetrics.memoryUsage"
        :progress-max="100"
        description="内存使用率"
        :show-chart="true"
        :chart-data="memoryChartData"
        chart-color="#feca57"
      />
      
      <RealtimeStatusCard
        title="网络流量"
        :value="systemMetrics.networkTraffic"
        unit="MB/s"
        :icon="WifiOutline"
        status="info"
        :show-trend="true"
        :trend="networkTrend"
        description="网络吞吐量"
        :show-chart="true"
        :chart-data="networkChartData"
        chart-color="#48dbfb"
      />
      
      <RealtimeStatusCard
        title="活跃连接"
        :value="connectionMetrics.activeConnections"
        :icon="LinkOutline"
        status="success"
        :show-trend="true"
        :trend="connectionTrend"
        description="WebSocket连接数"
      />
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <div class="charts-grid">
        <!-- API响应时间 -->
        <div class="chart-container">
          <RealtimeChart
            title="API响应时间"
            :data="responseTimeData"
            :height="300"
            type="line"
            color="#667eea"
            :auto-refresh="true"
            @refresh="refreshResponseTime"
          />
        </div>
        
        <!-- 错误率统计 -->
        <div class="chart-container">
          <RealtimeChart
            title="错误率统计"
            :data="errorRateData"
            :height="300"
            type="line"
            color="#ff4d4f"
            :auto-refresh="true"
            @refresh="refreshErrorRate"
          />
        </div>
      </div>
      
      <!-- 服务健康状态 -->
      <div class="health-section">
        <div class="section-header">
          <h3>服务健康状态</h3>
          <n-space>
            <n-select
              v-model:value="healthFilter"
              :options="healthFilterOptions"
              placeholder="筛选状态"
              style="width: 150px;"
              clearable
            />
            <n-button @click="refreshServiceHealth">
              <template #icon>
                <n-icon>
                  <RefreshOutline />
                </n-icon>
              </template>
              刷新
            </n-button>
          </n-space>
        </div>
        
        <div class="health-grid">
          <div
            v-for="service in filteredServices"
            :key="service.id"
            class="health-card"
            :class="getHealthCardClass(service.health)"
          >
            <div class="health-header">
              <div class="service-info">
                <h4>{{ service.name }}</h4>
                <span class="service-type">{{ service.type }}</span>
              </div>
              <div class="health-status">
                <n-icon :size="20" :color="getHealthColor(service.health)">
                  <component :is="getHealthIcon(service.health)" />
                </n-icon>
              </div>
            </div>
            
            <div class="health-metrics">
              <div class="metric-item">
                <span class="metric-label">响应时间</span>
                <span class="metric-value">{{ service.responseTime }}ms</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">成功率</span>
                <span class="metric-value">{{ service.successRate }}%</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">最后检查</span>
                <span class="metric-value">{{ formatRelativeTime(service.lastCheck) }}</span>
              </div>
            </div>
            
            <div class="health-actions">
              <n-button size="small" @click="viewServiceDetail(service)">
                详情
              </n-button>
              <n-button size="small" @click="testService(service)">
                测试
              </n-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 实时日志 -->
    <div class="logs-section">
      <div class="section-header">
        <h3>实时日志</h3>
        <n-space>
          <n-select
            v-model:value="logLevel"
            :options="logLevelOptions"
            placeholder="日志级别"
            style="width: 120px;"
          />
          <n-button @click="clearLogs">清空日志</n-button>
          <n-button @click="pauseLogs">
            {{ logsPaused ? '恢复' : '暂停' }}
          </n-button>
        </n-space>
      </div>
      
      <div class="logs-container">
        <div
          v-for="log in filteredLogs"
          :key="log.id"
          class="log-entry"
          :class="getLogClass(log.level)"
        >
          <span class="log-time">{{ formatLogTime(log.timestamp) }}</span>
          <span class="log-level">{{ log.level }}</span>
          <span class="log-service">{{ log.service }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>

    <!-- 监控设置对话框 -->
    <MonitoringSettingsModal
      v-model:show="showSettings"
      @update="handleSettingsUpdate"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  RefreshOutline,
  SettingsOutline,
  HardwareChipOutline,
  WifiOutline,
  LinkOutline,
  CheckmarkCircleOutline,
  WarningOutline,
  CloseCircleOutline
} from '@vicons/ionicons5'
import { useRealtimeData, useSystemAlerts } from '@/composables/useRealtimeData'
import RealtimeStatusCard from '@/components/RealtimeStatusCard.vue'
import RealtimeChart from '@/components/RealtimeChart.vue'
import MonitoringSettingsModal from '@/components/MonitoringSettingsModal.vue'

// 实时数据
const { isConnected, subscribeToAllTopics } = useRealtimeData()
const { systemAlerts } = useSystemAlerts()

// 响应式数据
const showSettings = ref(false)
const healthFilter = ref<string | null>(null)
const logLevel = ref('ALL')
const logsPaused = ref(false)

// 系统指标
const systemMetrics = ref({
  cpuUsage: 45,
  memoryUsage: 68,
  networkTraffic: 12.5,
  diskUsage: 34
})

const connectionMetrics = ref({
  activeConnections: 156,
  totalConnections: 1247,
  avgResponseTime: 245
})

// 图表数据
const cpuChartData = ref<Array<{ timestamp: number; value: number }>>([])
const memoryChartData = ref<Array<{ timestamp: number; value: number }>>([])
const networkChartData = ref<Array<{ timestamp: number; value: number }>>([])
const responseTimeData = ref<Array<{ timestamp: number; value: number }>>([])
const errorRateData = ref<Array<{ timestamp: number; value: number }>>([])

// 服务健康数据
const services = ref([
  {
    id: 1,
    name: 'Auth Service',
    type: 'LOCAL',
    health: 'healthy',
    responseTime: 45,
    successRate: 99.8,
    lastCheck: Date.now() - 30000
  },
  {
    id: 2,
    name: 'MCP Gateway',
    type: 'REMOTE',
    health: 'warning',
    responseTime: 156,
    successRate: 97.2,
    lastCheck: Date.now() - 60000
  },
  {
    id: 3,
    name: 'Subscription Service',
    type: 'LOCAL',
    health: 'healthy',
    responseTime: 78,
    successRate: 99.5,
    lastCheck: Date.now() - 45000
  }
])

// 实时日志
const logs = ref<Array<{
  id: number
  timestamp: number
  level: string
  service: string
  message: string
}>>([])

// 计算属性
const connectionStatus = computed(() => {
  if (isConnected.value) {
    return {
      type: 'success',
      text: '实时连接',
      icon: CheckmarkCircleOutline
    }
  } else {
    return {
      type: 'error',
      text: '连接断开',
      icon: CloseCircleOutline
    }
  }
})

const networkTrend = computed(() => Math.random() * 10 - 5)
const connectionTrend = computed(() => Math.random() * 15 - 7.5)

const healthFilterOptions = [
  { label: '全部', value: null },
  { label: '健康', value: 'healthy' },
  { label: '警告', value: 'warning' },
  { label: '错误', value: 'error' }
]

const logLevelOptions = [
  { label: '全部', value: 'ALL' },
  { label: 'ERROR', value: 'ERROR' },
  { label: 'WARN', value: 'WARN' },
  { label: 'INFO', value: 'INFO' },
  { label: 'DEBUG', value: 'DEBUG' }
]

const filteredServices = computed(() => {
  if (!healthFilter.value) return services.value
  return services.value.filter(service => service.health === healthFilter.value)
})

const filteredLogs = computed(() => {
  if (logLevel.value === 'ALL') return logs.value.slice(-100)
  return logs.value.filter(log => log.level === logLevel.value).slice(-100)
})

// 工具函数
const getMetricStatus = (value: number, warning: number, error: number) => {
  if (value >= error) return 'error'
  if (value >= warning) return 'warning'
  return 'success'
}

const getHealthCardClass = (health: string) => ({
  'health-healthy': health === 'healthy',
  'health-warning': health === 'warning',
  'health-error': health === 'error'
})

const getHealthColor = (health: string) => {
  const colors = {
    healthy: '#52c41a',
    warning: '#faad14',
    error: '#ff4d4f'
  }
  return colors[health as keyof typeof colors] || '#d9d9d9'
}

const getHealthIcon = (health: string) => {
  const icons = {
    healthy: CheckmarkCircleOutline,
    warning: WarningOutline,
    error: CloseCircleOutline
  }
  return icons[health as keyof typeof icons] || CheckmarkCircleOutline
}

const getLogClass = (level: string) => ({
  'log-error': level === 'ERROR',
  'log-warn': level === 'WARN',
  'log-info': level === 'INFO',
  'log-debug': level === 'DEBUG'
})

const formatRelativeTime = (timestamp: number) => {
  const diff = Date.now() - timestamp
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  
  if (hours > 0) return `${hours}小时前`
  if (minutes > 0) return `${minutes}分钟前`
  return '刚刚'
}

const formatLogTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString()
}

// 数据刷新函数
const generateChartData = (length: number = 20) => {
  const now = Date.now()
  return Array.from({ length }, (_, i) => ({
    timestamp: now - (length - 1 - i) * 60000,
    value: Math.floor(Math.random() * 100)
  }))
}

const refreshAllData = () => {
  refreshSystemMetrics()
  refreshResponseTime()
  refreshErrorRate()
  refreshServiceHealth()
}

const refreshSystemMetrics = () => {
  systemMetrics.value = {
    cpuUsage: Math.floor(Math.random() * 100),
    memoryUsage: Math.floor(Math.random() * 100),
    networkTraffic: Math.random() * 50,
    diskUsage: Math.floor(Math.random() * 100)
  }
  
  cpuChartData.value = generateChartData()
  memoryChartData.value = generateChartData()
  networkChartData.value = generateChartData()
}

const refreshResponseTime = () => {
  responseTimeData.value = generateChartData(30).map(item => ({
    ...item,
    value: Math.floor(Math.random() * 500) + 50
  }))
}

const refreshErrorRate = () => {
  errorRateData.value = generateChartData(30).map(item => ({
    ...item,
    value: Math.random() * 5
  }))
}

const refreshServiceHealth = () => {
  services.value.forEach(service => {
    service.responseTime = Math.floor(Math.random() * 200) + 50
    service.successRate = 95 + Math.random() * 5
    service.lastCheck = Date.now()
  })
}

const viewServiceDetail = (service: any) => {
  console.log('查看服务详情:', service)
}

const testService = (service: any) => {
  console.log('测试服务:', service)
}

const clearLogs = () => {
  logs.value = []
}

const pauseLogs = () => {
  logsPaused.value = !logsPaused.value
}

const handleSettingsUpdate = (settings: any) => {
  console.log('更新监控设置:', settings)
}

// 模拟日志生成
const generateLog = () => {
  if (logsPaused.value) return
  
  const levels = ['INFO', 'WARN', 'ERROR', 'DEBUG']
  const services = ['Auth Service', 'MCP Gateway', 'Subscription Service']
  const messages = [
    'Service started successfully',
    'Processing request',
    'Database connection established',
    'Cache miss for key',
    'Request completed',
    'Health check passed'
  ]
  
  const log = {
    id: Date.now() + Math.random(),
    timestamp: Date.now(),
    level: levels[Math.floor(Math.random() * levels.length)],
    service: services[Math.floor(Math.random() * services.length)],
    message: messages[Math.floor(Math.random() * messages.length)]
  }
  
  logs.value.push(log)
  
  // 保持日志数量在合理范围内
  if (logs.value.length > 500) {
    logs.value = logs.value.slice(-250)
  }
}

// 定时器
let metricsTimer: NodeJS.Timeout | null = null
let logsTimer: NodeJS.Timeout | null = null

// 生命周期
onMounted(() => {
  subscribeToAllTopics()
  refreshAllData()
  
  // 启动定时刷新
  metricsTimer = setInterval(refreshSystemMetrics, 5000)
  logsTimer = setInterval(generateLog, 2000)
})

onUnmounted(() => {
  if (metricsTimer) clearInterval(metricsTimer)
  if (logsTimer) clearInterval(logsTimer)
})
</script>

<style scoped>
.realtime-monitoring {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 32px;
}

.header-content h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: #333;
}

.page-description {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.charts-section {
  margin-bottom: 32px;
}

.charts-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.chart-container {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.health-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.health-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.health-card {
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.health-card.health-healthy {
  border-left: 4px solid #52c41a;
}

.health-card.health-warning {
  border-left: 4px solid #faad14;
}

.health-card.health-error {
  border-left: 4px solid #ff4d4f;
}

.health-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.service-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.service-type {
  font-size: 12px;
  color: #666;
  background: #f0f0f0;
  padding: 2px 8px;
  border-radius: 4px;
}

.health-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 16px;
}

.metric-item {
  text-align: center;
}

.metric-label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.metric-value {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.health-actions {
  display: flex;
  gap: 8px;
}

.logs-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.logs-container {
  background: #1e1e1e;
  border-radius: 8px;
  padding: 16px;
  height: 300px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-entry {
  display: flex;
  gap: 12px;
  margin-bottom: 4px;
  padding: 2px 0;
}

.log-time {
  color: #888;
  min-width: 80px;
}

.log-level {
  min-width: 50px;
  font-weight: bold;
}

.log-service {
  min-width: 120px;
  color: #4a9eff;
}

.log-message {
  color: #fff;
  flex: 1;
}

.log-error .log-level {
  color: #ff6b6b;
}

.log-warn .log-level {
  color: #feca57;
}

.log-info .log-level {
  color: #48dbfb;
}

.log-debug .log-level {
  color: #ff9ff3;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .charts-grid {
    grid-template-columns: 1fr;
  }
  
  .metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }
}

@media (max-width: 768px) {
  .realtime-monitoring {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .health-grid {
    grid-template-columns: 1fr;
  }
  
  .health-metrics {
    grid-template-columns: 1fr;
  }
}
</style>
