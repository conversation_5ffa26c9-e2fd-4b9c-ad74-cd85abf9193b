<template>
  <div class="services-page">
    <div class="page-header">
      <h1>服务监控</h1>
      <p>监控微服务状态和健康指标</p>
    </div>

    <n-card>
      <div class="services-content">
        <n-empty description="服务监控功能开发中...">
          <template #extra>
            <n-button size="small">刷新</n-button>
          </template>
        </n-empty>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { NCard, NEmpty, NButton } from 'naive-ui'
</script>

<style scoped>
.services-page {
  padding: 1rem;
}

.page-header {
  margin-bottom: 1rem;
}

.page-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
}

.page-header p {
  margin: 0;
  color: #666;
}

.services-content {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
