<template>
  <div class="preferences-page">
    <div class="page-header">
      <h1>偏好设置</h1>
      <p>个性化配置和界面偏好</p>
    </div>

    <n-card>
      <div class="preferences-content">
        <n-empty description="偏好设置功能开发中...">
          <template #extra>
            <n-button size="small">刷新</n-button>
          </template>
        </n-empty>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { NCard, NEmpty, NButton } from 'naive-ui'
</script>

<style scoped>
.preferences-page {
  padding: 1rem;
}

.page-header {
  margin-bottom: 1rem;
}

.page-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
}

.page-header p {
  margin: 0;
  color: #666;
}

.preferences-content {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
