<template>
  <div class="profile-page">
    <div class="page-header">
      <h1>个人资料</h1>
      <p>管理个人信息和账户设置</p>
    </div>

    <n-card>
      <div class="profile-content">
        <n-empty description="个人资料功能开发中...">
          <template #extra>
            <n-button size="small">刷新</n-button>
          </template>
        </n-empty>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { NCard, NEmpty, NButton } from 'naive-ui'
</script>

<style scoped>
.profile-page {
  padding: 1rem;
}

.page-header {
  margin-bottom: 1rem;
}

.page-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
}

.page-header p {
  margin: 0;
  color: #666;
}

.profile-content {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
