<template>
  <div class="settings-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">系统设置</h1>
        <p class="page-description">管理系统配置、用户偏好和安全设置</p>
      </div>
      
      <div class="header-actions">
        <n-button @click="exportSettings">
          <template #icon>
            <n-icon>
              <DownloadOutline />
            </n-icon>
          </template>
          导出配置
        </n-button>
        
        <n-button @click="importSettings">
          <template #icon>
            <n-icon>
              <CloudUploadOutline />
            </n-icon>
          </template>
          导入配置
        </n-button>
        
        <n-button type="primary" @click="saveAllSettings" :loading="saving">
          <template #icon>
            <n-icon>
              <SaveOutline />
            </n-icon>
          </template>
          保存设置
        </n-button>
      </div>
    </div>

    <div class="settings-layout">
      <!-- 左侧导航 -->
      <div class="settings-nav">
        <n-menu
          v-model:value="activeSection"
          :options="menuOptions"
          :render-label="renderMenuLabel"
          :render-icon="renderMenuIcon"
          @update:value="handleSectionChange"
        />
      </div>

      <!-- 右侧内容区域 -->
      <div class="settings-content">
        <!-- 个人资料 -->
        <div v-if="activeSection === 'profile'" class="settings-section">
          <ProfileSettings
            v-model:settings="settings.profile"
            @change="handleSettingsChange"
          />
        </div>

        <!-- 界面设置 -->
        <div v-else-if="activeSection === 'interface'" class="settings-section">
          <InterfaceSettings
            v-model:settings="settings.interface"
            @change="handleSettingsChange"
          />
        </div>

        <!-- 通知设置 -->
        <div v-else-if="activeSection === 'notifications'" class="settings-section">
          <NotificationSettings
            v-model:settings="settings.notifications"
            @change="handleSettingsChange"
          />
        </div>

        <!-- 安全设置 -->
        <div v-else-if="activeSection === 'security'" class="settings-section">
          <SecuritySettings
            v-model:settings="settings.security"
            @change="handleSettingsChange"
          />
        </div>

        <!-- 系统配置 -->
        <div v-else-if="activeSection === 'system'" class="settings-section">
          <SystemSettings
            v-model:settings="settings.system"
            @change="handleSettingsChange"
          />
        </div>

        <!-- 高级设置 -->
        <div v-else-if="activeSection === 'advanced'" class="settings-section">
          <AdvancedSettings
            v-model:settings="settings.advanced"
            @change="handleSettingsChange"
          />
        </div>

        <!-- 关于 -->
        <div v-else-if="activeSection === 'about'" class="settings-section">
          <AboutSection />
        </div>
      </div>
    </div>

    <!-- 设置变更确认对话框 -->
    <n-modal
      v-model:show="showUnsavedChanges"
      preset="dialog"
      title="未保存的更改"
      content="您有未保存的设置更改，是否要保存？"
      positive-text="保存"
      negative-text="放弃"
      @positive-click="saveAndContinue"
      @negative-click="discardAndContinue"
    />

    <!-- 导入设置对话框 -->
    <ImportSettingsModal
      v-model:show="showImportModal"
      @success="handleImportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, h, onMounted, onBeforeUnmount } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useMessage } from 'naive-ui'
import {
  PersonOutline,
  ColorPaletteOutline,
  NotificationsOutline,
  ShieldCheckmarkOutline,
  SettingsOutline,
  CodeSlashOutline,
  InformationCircleOutline,
  DownloadOutline,
  CloudUploadOutline,
  SaveOutline
} from '@vicons/ionicons5'
import { useSettingsStore } from '@/stores/settings'
import ProfileSettings from '@/components/settings/ProfileSettings.vue'
import InterfaceSettings from '@/components/settings/InterfaceSettings.vue'
import NotificationSettings from '@/components/settings/NotificationSettings.vue'
import SecuritySettings from '@/components/settings/SecuritySettings.vue'
import SystemSettings from '@/components/settings/SystemSettings.vue'
import AdvancedSettings from '@/components/settings/AdvancedSettings.vue'
import AboutSection from '@/components/settings/AboutSection.vue'
import ImportSettingsModal from '@/components/ImportSettingsModal.vue'

const router = useRouter()
const route = useRoute()
const message = useMessage()
const settingsStore = useSettingsStore()

// 响应式数据
const activeSection = ref('profile')
const saving = ref(false)
const hasUnsavedChanges = ref(false)
const showUnsavedChanges = ref(false)
const showImportModal = ref(false)
const pendingNavigation = ref<string | null>(null)

// 设置数据
const settings = ref({
  profile: {
    username: '',
    email: '',
    displayName: '',
    avatar: '',
    timezone: 'Asia/Shanghai',
    language: 'zh-CN'
  },
  interface: {
    theme: 'auto',
    primaryColor: '#667eea',
    sidebarCollapsed: false,
    showBreadcrumb: true,
    showPageTransition: true,
    density: 'normal',
    fontSize: 'medium'
  },
  notifications: {
    desktop: true,
    email: false,
    sound: true,
    serviceAlerts: true,
    systemUpdates: true,
    securityAlerts: true,
    frequency: 'immediate'
  },
  security: {
    twoFactorEnabled: false,
    sessionTimeout: 30,
    passwordExpiry: 90,
    loginNotifications: true,
    ipWhitelist: [],
    apiKeyExpiry: 365
  },
  system: {
    logLevel: 'info',
    maxLogSize: 100,
    backupEnabled: true,
    backupInterval: 24,
    maintenanceMode: false,
    debugMode: false
  },
  advanced: {
    experimentalFeatures: false,
    developerMode: false,
    apiRateLimit: 1000,
    cacheSize: 256,
    customCSS: '',
    customJS: ''
  }
})

// 菜单选项
const menuOptions = [
  {
    key: 'profile',
    label: '个人资料',
    icon: PersonOutline
  },
  {
    key: 'interface',
    label: '界面设置',
    icon: ColorPaletteOutline
  },
  {
    key: 'notifications',
    label: '通知设置',
    icon: NotificationsOutline
  },
  {
    key: 'security',
    label: '安全设置',
    icon: ShieldCheckmarkOutline
  },
  {
    key: 'system',
    label: '系统配置',
    icon: SettingsOutline
  },
  {
    key: 'advanced',
    label: '高级设置',
    icon: CodeSlashOutline
  },
  {
    key: 'about',
    label: '关于',
    icon: InformationCircleOutline
  }
]

// 渲染函数
const renderMenuLabel = ({ option }: any) => {
  return option.label
}

const renderMenuIcon = ({ option }: any) => {
  return h('n-icon', { size: 18 }, { default: () => h(option.icon) })
}

// 事件处理
const handleSectionChange = (key: string) => {
  if (hasUnsavedChanges.value) {
    pendingNavigation.value = key
    showUnsavedChanges.value = true
    return
  }
  
  activeSection.value = key
  router.replace({ query: { section: key } })
}

const handleSettingsChange = () => {
  hasUnsavedChanges.value = true
}

const saveAllSettings = async () => {
  saving.value = true
  try {
    await settingsStore.updateSettings(settings.value)
    hasUnsavedChanges.value = false
    message.success('设置保存成功')
  } catch (error: any) {
    message.error('设置保存失败: ' + error.message)
  } finally {
    saving.value = false
  }
}

const saveAndContinue = async () => {
  await saveAllSettings()
  if (pendingNavigation.value) {
    activeSection.value = pendingNavigation.value
    router.replace({ query: { section: pendingNavigation.value } })
    pendingNavigation.value = null
  }
  showUnsavedChanges.value = false
}

const discardAndContinue = () => {
  hasUnsavedChanges.value = false
  if (pendingNavigation.value) {
    activeSection.value = pendingNavigation.value
    router.replace({ query: { section: pendingNavigation.value } })
    pendingNavigation.value = null
  }
  showUnsavedChanges.value = false
  
  // 重新加载设置
  loadSettings()
}

const exportSettings = () => {
  const config = {
    settings: settings.value,
    exportTime: new Date().toISOString(),
    version: '1.0.0'
  }
  
  const blob = new Blob([JSON.stringify(config, null, 2)], {
    type: 'application/json'
  })
  
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `nexus-settings-${new Date().toISOString().split('T')[0]}.json`
  a.click()
  
  URL.revokeObjectURL(url)
  message.success('设置配置已导出')
}

const importSettings = () => {
  showImportModal.value = true
}

const handleImportSuccess = (importedSettings: any) => {
  settings.value = { ...settings.value, ...importedSettings }
  hasUnsavedChanges.value = true
  message.success('设置配置已导入，请保存以应用更改')
}

const loadSettings = async () => {
  try {
    const userSettings = await settingsStore.getSettings()
    settings.value = { ...settings.value, ...userSettings }
  } catch (error) {
    console.error('加载设置失败:', error)
  }
}

// 页面离开确认
const beforeUnloadHandler = (event: BeforeUnloadEvent) => {
  if (hasUnsavedChanges.value) {
    event.preventDefault()
    event.returnValue = '您有未保存的更改，确定要离开吗？'
    return event.returnValue
  }
}

// 生命周期
onMounted(() => {
  // 从URL参数获取初始section
  const section = route.query.section as string
  if (section && menuOptions.some(opt => opt.key === section)) {
    activeSection.value = section
  }
  
  loadSettings()
  window.addEventListener('beforeunload', beforeUnloadHandler)
})

onBeforeUnmount(() => {
  window.removeEventListener('beforeunload', beforeUnloadHandler)
})
</script>

<style scoped>
.settings-page {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 32px;
}

.header-content h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: #333;
}

.page-description {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.settings-layout {
  display: flex;
  gap: 32px;
  flex: 1;
  overflow: hidden;
}

.settings-nav {
  width: 240px;
  background: white;
  border-radius: 16px;
  padding: 20px 0;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  overflow-y: auto;
}

.settings-content {
  flex: 1;
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  overflow-y: auto;
}

.settings-section {
  max-width: 800px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .settings-layout {
    flex-direction: column;
  }
  
  .settings-nav {
    width: 100%;
    padding: 16px;
  }
  
  .settings-content {
    padding: 24px;
  }
}

@media (max-width: 768px) {
  .settings-page {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .settings-content {
    padding: 20px;
  }
}
</style>
