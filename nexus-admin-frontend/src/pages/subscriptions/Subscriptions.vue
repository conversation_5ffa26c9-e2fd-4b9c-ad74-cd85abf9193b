<template>
  <div class="subscriptions-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">订阅管理</h1>
        <p class="page-description">管理用户订阅、权限和使用情况</p>
      </div>
      
      <div class="header-actions">
        <n-button type="primary" @click="showCreateModal = true">
          <template #icon>
            <n-icon>
              <AddOutline />
            </n-icon>
          </template>
          创建订阅
        </n-button>
        
        <n-button @click="refreshSubscriptions">
          <template #icon>
            <n-icon>
              <RefreshOutline />
            </n-icon>
          </template>
          刷新
        </n-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <RealtimeStatusCard
        title="总订阅数"
        :value="totalSubscriptions"
        :icon="CardOutline"
        status="info"
        :show-trend="true"
        :trend="subscriptionsTrend"
        description="所有用户的订阅总数"
      />
      
      <RealtimeStatusCard
        title="活跃订阅"
        :value="activeSubscriptions"
        :icon="CheckmarkCircleOutline"
        status="success"
        :show-progress="true"
        :progress="activeSubscriptions"
        :progress-max="totalSubscriptions"
        :progress-text="`${activeSubscriptions}/${totalSubscriptions}`"
        description="当前活跃的订阅数量"
      />
      
      <RealtimeStatusCard
        title="即将到期"
        :value="expiringSubscriptions"
        :icon="TimeOutline"
        :status="expiringSubscriptions > 0 ? 'warning' : 'success'"
        :show-chart="true"
        :chart-data="expiringChartData"
        chart-color="#faad14"
        description="7天内即将到期的订阅"
      />
      
      <RealtimeStatusCard
        title="本月收入"
        :value="monthlyRevenue"
        unit="¥"
        :icon="TrendingUpOutline"
        status="success"
        :show-trend="true"
        :trend="revenueTrend"
        description="本月订阅收入统计"
      />
    </div>

    <!-- 订阅列表 -->
    <div class="subscriptions-section">
      <div class="section-header">
        <h2>订阅列表</h2>
        <div class="section-actions">
          <n-input
            v-model:value="searchQuery"
            placeholder="搜索用户或服务..."
            clearable
            style="width: 300px;"
          >
            <template #prefix>
              <n-icon>
                <SearchOutline />
              </n-icon>
            </template>
          </n-input>
          
          <n-select
            v-model:value="statusFilter"
            placeholder="状态筛选"
            :options="statusOptions"
            clearable
            style="width: 150px;"
          />
          
          <n-select
            v-model:value="serviceFilter"
            placeholder="服务筛选"
            :options="serviceOptions"
            clearable
            style="width: 200px;"
          />
          
          <n-date-picker
            v-model:value="dateRange"
            type="daterange"
            placeholder="选择日期范围"
            clearable
            style="width: 240px;"
          />
        </div>
      </div>

      <n-data-table
        :columns="tableColumns"
        :data="filteredSubscriptions"
        :loading="loading"
        :pagination="paginationConfig"
        :row-key="(row: Subscription) => row.id"
        striped
        size="medium"
        class="subscriptions-table"
      />
    </div>

    <!-- 创建订阅对话框 -->
    <SubscriptionCreateModal
      v-model:show="showCreateModal"
      @success="handleCreateSuccess"
    />

    <!-- 订阅详情抽屉 -->
    <SubscriptionDetailDrawer
      v-model:show="showDetailDrawer"
      :subscription="selectedSubscription"
      @update="handleSubscriptionUpdate"
    />

    <!-- 批量操作对话框 -->
    <BatchOperationModal
      v-model:show="showBatchModal"
      :selected-ids="selectedIds"
      @success="handleBatchSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useQuery } from '@tanstack/vue-query'
import { useMessage } from 'naive-ui'
import {
  CardOutline,
  AddOutline,
  RefreshOutline,
  SearchOutline,
  CheckmarkCircleOutline,
  TimeOutline,
  TrendingUpOutline,
  SettingsOutline,
  PauseOutline,
  PlayOutline,
  TrashOutline,
  EyeOutline
} from '@vicons/ionicons5'
import { subscriptionApi } from '@/api/subscription'
import { useSubscriptionMonitoring } from '@/composables/useRealtimeData'
import RealtimeStatusCard from '@/components/RealtimeStatusCard.vue'
import SubscriptionCreateModal from '@/components/SubscriptionCreateModal.vue'
import SubscriptionDetailDrawer from '@/components/SubscriptionDetailDrawer.vue'
import BatchOperationModal from '@/components/BatchOperationModal.vue'
import type { Subscription } from '@/types/api'
import type { DataTableColumns } from 'naive-ui'

const message = useMessage()

// 响应式数据
const searchQuery = ref('')
const statusFilter = ref<string | null>(null)
const serviceFilter = ref<string | null>(null)
const dateRange = ref<[number, number] | null>(null)
const showCreateModal = ref(false)
const showDetailDrawer = ref(false)
const showBatchModal = ref(false)
const selectedSubscription = ref<Subscription | null>(null)
const selectedIds = ref<number[]>([])

// 实时监控
const { subscriptionUpdates } = useSubscriptionMonitoring()

// 查询订阅列表
const {
  data: subscriptions,
  isLoading: loading,
  refetch: refreshSubscriptions
} = useQuery({
  queryKey: ['subscriptions'],
  queryFn: async () => {
    const response = await subscriptionApi.getAll()
    return response.data.content
  },
  refetchInterval: 30000
})

// 查询统计数据
const { data: stats } = useQuery({
  queryKey: ['subscription-stats'],
  queryFn: () => subscriptionApi.getStats(),
  refetchInterval: 60000
})

// 计算属性
const totalSubscriptions = computed(() => stats.value?.data.totalSubscriptions || 0)
const activeSubscriptions = computed(() => stats.value?.data.activeSubscriptions || 0)
const expiringSubscriptions = computed(() => {
  if (!subscriptions.value) return 0
  const sevenDaysFromNow = new Date()
  sevenDaysFromNow.setDate(sevenDaysFromNow.getDate() + 7)
  
  return subscriptions.value.filter(sub => 
    sub.status === 'ACTIVE' && 
    new Date(sub.endDate) <= sevenDaysFromNow
  ).length
})
const monthlyRevenue = computed(() => stats.value?.data.totalRevenue || 0)

const subscriptionsTrend = computed(() => stats.value?.data.monthlyGrowth || 0)
const revenueTrend = computed(() => Math.random() * 10 - 5) // 模拟

const expiringChartData = computed(() => {
  const now = Date.now()
  return Array.from({ length: 7 }, (_, i) => ({
    timestamp: now - (6 - i) * 24 * 60 * 60 * 1000,
    value: Math.floor(Math.random() * 5)
  }))
})

// 筛选选项
const statusOptions = [
  { label: '活跃', value: 'ACTIVE' },
  { label: '已过期', value: 'EXPIRED' },
  { label: '已暂停', value: 'SUSPENDED' },
  { label: '已取消', value: 'CANCELLED' }
]

const serviceOptions = computed(() => {
  if (!subscriptions.value) return []
  
  const services = [...new Set(subscriptions.value.map(sub => sub.serviceName))]
  return services.map(service => ({
    label: service,
    value: service
  }))
})

// 过滤后的订阅列表
const filteredSubscriptions = computed(() => {
  if (!subscriptions.value) return []
  
  return subscriptions.value.filter(subscription => {
    const matchesSearch = !searchQuery.value || 
      subscription.username?.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      subscription.serviceName.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      subscription.serviceDisplayName.toLowerCase().includes(searchQuery.value.toLowerCase())
    
    const matchesStatus = !statusFilter.value || subscription.status === statusFilter.value
    const matchesService = !serviceFilter.value || subscription.serviceName === serviceFilter.value
    
    let matchesDate = true
    if (dateRange.value) {
      const [start, end] = dateRange.value
      const subDate = new Date(subscription.createdAt).getTime()
      matchesDate = subDate >= start && subDate <= end
    }
    
    return matchesSearch && matchesStatus && matchesService && matchesDate
  })
})

// 分页配置
const paginationConfig = {
  pageSize: 15,
  showSizePicker: true,
  pageSizes: [15, 30, 50],
  showQuickJumper: true
}

// 表格列配置
const tableColumns: DataTableColumns<Subscription> = [
  {
    type: 'selection',
    multiple: true,
    onUpdateCheckedRowKeys: (keys: number[]) => {
      selectedIds.value = keys
    }
  },
  {
    title: '用户信息',
    key: 'user',
    render: (row) => h('div', { class: 'user-cell' }, [
      h('div', { class: 'username' }, row.username || `用户${row.userId}`),
      h('div', { class: 'user-id' }, `ID: ${row.userId}`)
    ])
  },
  {
    title: '服务',
    key: 'service',
    render: (row) => h('div', { class: 'service-cell' }, [
      h('div', { class: 'service-name' }, row.serviceDisplayName),
      h('div', { class: 'service-id' }, row.serviceName)
    ])
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row) => {
      const statusConfig = {
        ACTIVE: { type: 'success', text: '活跃' },
        EXPIRED: { type: 'error', text: '已过期' },
        SUSPENDED: { type: 'warning', text: '已暂停' },
        CANCELLED: { type: 'default', text: '已取消' }
      }
      const config = statusConfig[row.status] || { type: 'default', text: row.status }
      
      return h('n-tag', {
        type: config.type,
        size: 'small'
      }, config.text)
    }
  },
  {
    title: '使用情况',
    key: 'usage',
    width: 150,
    render: (row) => {
      const percentage = row.callLimit > 0 ? (row.usedCalls / row.callLimit) * 100 : 0
      return h('div', { class: 'usage-cell' }, [
        h('n-progress', {
          percentage: Math.min(percentage, 100),
          color: percentage > 90 ? '#ff4d4f' : percentage > 70 ? '#faad14' : '#52c41a',
          showIndicator: false,
          height: 6
        }),
        h('div', { class: 'usage-text' }, `${row.usedCalls}/${row.callLimit}`)
      ])
    }
  },
  {
    title: '有效期',
    key: 'period',
    width: 180,
    render: (row) => {
      const startDate = new Date(row.startDate).toLocaleDateString()
      const endDate = new Date(row.endDate).toLocaleDateString()
      const isExpiring = new Date(row.endDate).getTime() - Date.now() < 7 * 24 * 60 * 60 * 1000
      
      return h('div', { class: 'period-cell' }, [
        h('div', `${startDate} - ${endDate}`),
        isExpiring && row.status === 'ACTIVE' ? h('n-tag', {
          type: 'warning',
          size: 'small'
        }, '即将到期') : null
      ])
    }
  },
  {
    title: '创建时间',
    key: 'createdAt',
    width: 150,
    render: (row) => new Date(row.createdAt).toLocaleString()
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    render: (row) => h('div', { class: 'action-buttons' }, [
      h('n-button', {
        size: 'small',
        quaternary: true,
        onClick: () => viewSubscriptionDetail(row)
      }, {
        default: () => '详情',
        icon: () => h('n-icon', {}, h(EyeOutline))
      }),
      
      h('n-button', {
        size: 'small',
        quaternary: true,
        type: row.status === 'SUSPENDED' ? 'success' : 'warning',
        onClick: () => toggleSubscription(row)
      }, {
        default: () => row.status === 'SUSPENDED' ? '恢复' : '暂停',
        icon: () => h('n-icon', {}, 
          row.status === 'SUSPENDED' ? h(PlayOutline) : h(PauseOutline)
        )
      }),
      
      h('n-button', {
        size: 'small',
        quaternary: true,
        onClick: () => configureSubscription(row)
      }, {
        default: () => '配置',
        icon: () => h('n-icon', {}, h(SettingsOutline))
      }),
      
      h('n-popconfirm', {
        onPositiveClick: () => cancelSubscription(row)
      }, {
        default: () => '确定取消此订阅吗？',
        trigger: () => h('n-button', {
          size: 'small',
          quaternary: true,
          type: 'error'
        }, {
          default: () => '取消',
          icon: () => h('n-icon', {}, h(TrashOutline))
        })
      })
    ])
  }
]

// 事件处理
const viewSubscriptionDetail = (subscription: Subscription) => {
  selectedSubscription.value = subscription
  showDetailDrawer.value = true
}

const toggleSubscription = async (subscription: Subscription) => {
  try {
    if (subscription.status === 'SUSPENDED') {
      await subscriptionApi.resume(subscription.id)
      message.success('订阅已恢复')
    } else {
      await subscriptionApi.suspend(subscription.id, '手动暂停')
      message.success('订阅已暂停')
    }
    refreshSubscriptions()
  } catch (error: any) {
    message.error(error.message || '操作失败')
  }
}

const configureSubscription = (subscription: Subscription) => {
  // 打开配置对话框
  console.log('配置订阅:', subscription)
}

const cancelSubscription = async (subscription: Subscription) => {
  try {
    await subscriptionApi.cancel(subscription.id)
    message.success('订阅已取消')
    refreshSubscriptions()
  } catch (error: any) {
    message.error(error.message || '取消失败')
  }
}

const handleCreateSuccess = () => {
  message.success('订阅创建成功')
  refreshSubscriptions()
}

const handleSubscriptionUpdate = () => {
  refreshSubscriptions()
}

const handleBatchSuccess = () => {
  message.success('批量操作完成')
  selectedIds.value = []
  refreshSubscriptions()
}

// 生命周期
onMounted(() => {
  refreshSubscriptions()
})
</script>

<style scoped>
.subscriptions-page {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 32px;
}

.header-content h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: #333;
}

.page-description {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.subscriptions-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.section-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.section-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.subscriptions-table {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.user-cell) {
  .username {
    font-weight: 600;
    color: #333;
    margin-bottom: 2px;
  }
  
  .user-id {
    font-size: 12px;
    color: #999;
  }
}

:deep(.service-cell) {
  .service-name {
    font-weight: 500;
    color: #333;
    margin-bottom: 2px;
  }
  
  .service-id {
    font-size: 12px;
    color: #666;
  }
}

:deep(.usage-cell) {
  .usage-text {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
    text-align: center;
  }
}

:deep(.period-cell) {
  font-size: 12px;
  line-height: 1.4;
}

:deep(.action-buttons) {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }
}

@media (max-width: 768px) {
  .subscriptions-page {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .section-actions {
    width: 100%;
    flex-direction: column;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
</style>
