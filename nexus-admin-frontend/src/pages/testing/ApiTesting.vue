<template>
  <div class="api-testing">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">接口测试</h1>
        <p class="page-description">测试和调试API接口，支持多种HTTP方法和认证方式</p>
      </div>
      
      <div class="header-actions">
        <n-button @click="importCollection">
          <template #icon>
            <n-icon>
              <DownloadOutline />
            </n-icon>
          </template>
          导入集合
        </n-button>
        
        <n-button @click="exportCollection">
          <template #icon>
            <n-icon>
              <ShareOutline />
            </n-icon>
          </template>
          导出集合
        </n-button>
        
        <n-button type="primary" @click="saveRequest">
          <template #icon>
            <n-icon>
              <SaveOutline />
            </n-icon>
          </template>
          保存请求
        </n-button>
      </div>
    </div>

    <div class="testing-layout">
      <!-- 左侧请求集合 -->
      <div class="collections-panel">
        <div class="panel-header">
          <h3>请求集合</h3>
          <n-button size="small" @click="showCreateCollection = true">
            <template #icon>
              <n-icon>
                <AddOutline />
              </n-icon>
            </template>
            新建
          </n-button>
        </div>
        
        <div class="collections-tree">
          <n-tree
            :data="collectionsData"
            :render-label="renderCollectionLabel"
            :render-prefix="renderCollectionPrefix"
            :on-update:selected-keys="handleCollectionSelect"
            selectable
            expand-on-click
          />
        </div>
      </div>

      <!-- 主要测试区域 -->
      <div class="testing-main">
        <!-- 请求配置 -->
        <div class="request-section">
          <div class="request-line">
            <n-select
              v-model:value="request.method"
              :options="methodOptions"
              style="width: 120px;"
            />
            
            <n-input
              v-model:value="request.url"
              placeholder="输入请求URL"
              clearable
              style="flex: 1; margin: 0 12px;"
            >
              <template #prefix>
                <n-icon>
                  <LinkOutline />
                </n-icon>
              </template>
            </n-input>
            
            <n-button
              type="primary"
              :loading="sending"
              @click="sendRequest"
            >
              <template #icon>
                <n-icon>
                  <SendOutline />
                </n-icon>
              </template>
              发送
            </n-button>
          </div>

          <!-- 请求配置选项卡 -->
          <n-tabs type="line" animated class="request-tabs">
            <!-- 请求参数 -->
            <n-tab-pane name="params" tab="参数">
              <div class="params-section">
                <div class="params-header">
                  <span>Query参数</span>
                  <n-button size="small" @click="addParam">
                    <template #icon>
                      <n-icon>
                        <AddOutline />
                      </n-icon>
                    </template>
                    添加
                  </n-button>
                </div>
                
                <div class="params-list">
                  <div
                    v-for="(param, index) in request.params"
                    :key="index"
                    class="param-item"
                  >
                    <n-checkbox v-model:checked="param.enabled" />
                    <n-input
                      v-model:value="param.key"
                      placeholder="参数名"
                      style="flex: 1;"
                    />
                    <n-input
                      v-model:value="param.value"
                      placeholder="参数值"
                      style="flex: 1;"
                    />
                    <n-button
                      quaternary
                      type="error"
                      size="small"
                      @click="removeParam(index)"
                    >
                      <n-icon>
                        <TrashOutline />
                      </n-icon>
                    </n-button>
                  </div>
                </div>
              </div>
            </n-tab-pane>

            <!-- 请求头 -->
            <n-tab-pane name="headers" tab="请求头">
              <div class="headers-section">
                <div class="headers-header">
                  <span>HTTP Headers</span>
                  <n-button size="small" @click="addHeader">
                    <template #icon>
                      <n-icon>
                        <AddOutline />
                      </n-icon>
                    </template>
                    添加
                  </n-button>
                </div>
                
                <div class="headers-list">
                  <div
                    v-for="(header, index) in request.headers"
                    :key="index"
                    class="header-item"
                  >
                    <n-checkbox v-model:checked="header.enabled" />
                    <n-input
                      v-model:value="header.key"
                      placeholder="Header名"
                      style="flex: 1;"
                    />
                    <n-input
                      v-model:value="header.value"
                      placeholder="Header值"
                      style="flex: 1;"
                    />
                    <n-button
                      quaternary
                      type="error"
                      size="small"
                      @click="removeHeader(index)"
                    >
                      <n-icon>
                        <TrashOutline />
                      </n-icon>
                    </n-button>
                  </div>
                </div>
              </div>
            </n-tab-pane>

            <!-- 请求体 -->
            <n-tab-pane name="body" tab="请求体">
              <div class="body-section">
                <div class="body-type">
                  <n-radio-group v-model:value="request.bodyType">
                    <n-space>
                      <n-radio value="none">无</n-radio>
                      <n-radio value="json">JSON</n-radio>
                      <n-radio value="form">表单</n-radio>
                      <n-radio value="raw">原始</n-radio>
                    </n-space>
                  </n-radio-group>
                </div>
                
                <div v-if="request.bodyType === 'json'" class="json-editor">
                  <n-input
                    v-model:value="request.body"
                    type="textarea"
                    placeholder='{"key": "value"}'
                    :rows="10"
                    :autosize="{ minRows: 10, maxRows: 20 }"
                  />
                </div>
                
                <div v-else-if="request.bodyType === 'form'" class="form-data">
                  <div class="form-header">
                    <span>表单数据</span>
                    <n-button size="small" @click="addFormData">
                      <template #icon>
                        <n-icon>
                          <AddOutline />
                        </n-icon>
                      </template>
                      添加
                    </n-button>
                  </div>
                  
                  <div class="form-list">
                    <div
                      v-for="(item, index) in request.formData"
                      :key="index"
                      class="form-item"
                    >
                      <n-checkbox v-model:checked="item.enabled" />
                      <n-input
                        v-model:value="item.key"
                        placeholder="字段名"
                        style="flex: 1;"
                      />
                      <n-input
                        v-model:value="item.value"
                        placeholder="字段值"
                        style="flex: 1;"
                      />
                      <n-button
                        quaternary
                        type="error"
                        size="small"
                        @click="removeFormData(index)"
                      >
                        <n-icon>
                          <TrashOutline />
                        </n-icon>
                      </n-button>
                    </div>
                  </div>
                </div>
                
                <div v-else-if="request.bodyType === 'raw'" class="raw-body">
                  <n-input
                    v-model:value="request.body"
                    type="textarea"
                    placeholder="输入原始请求体"
                    :rows="10"
                    :autosize="{ minRows: 10, maxRows: 20 }"
                  />
                </div>
              </div>
            </n-tab-pane>

            <!-- 认证 -->
            <n-tab-pane name="auth" tab="认证">
              <div class="auth-section">
                <n-form label-placement="left" label-width="100px">
                  <n-form-item label="认证类型">
                    <n-select
                      v-model:value="request.auth.type"
                      :options="authTypeOptions"
                      placeholder="选择认证方式"
                      clearable
                    />
                  </n-form-item>
                  
                  <template v-if="request.auth.type === 'bearer'">
                    <n-form-item label="Token">
                      <n-input
                        v-model:value="request.auth.token"
                        type="password"
                        placeholder="输入Bearer Token"
                        show-password-on="click"
                      />
                    </n-form-item>
                  </template>
                  
                  <template v-if="request.auth.type === 'basic'">
                    <n-form-item label="用户名">
                      <n-input
                        v-model:value="request.auth.username"
                        placeholder="输入用户名"
                      />
                    </n-form-item>
                    <n-form-item label="密码">
                      <n-input
                        v-model:value="request.auth.password"
                        type="password"
                        placeholder="输入密码"
                        show-password-on="click"
                      />
                    </n-form-item>
                  </template>
                  
                  <template v-if="request.auth.type === 'apikey'">
                    <n-form-item label="Key名称">
                      <n-input
                        v-model:value="request.auth.keyName"
                        placeholder="X-API-Key"
                      />
                    </n-form-item>
                    <n-form-item label="Key值">
                      <n-input
                        v-model:value="request.auth.keyValue"
                        type="password"
                        placeholder="输入API Key"
                        show-password-on="click"
                      />
                    </n-form-item>
                    <n-form-item label="位置">
                      <n-radio-group v-model:value="request.auth.keyLocation">
                        <n-space>
                          <n-radio value="header">Header</n-radio>
                          <n-radio value="query">Query</n-radio>
                        </n-space>
                      </n-radio-group>
                    </n-form-item>
                  </template>
                </n-form>
              </div>
            </n-tab-pane>
          </n-tabs>
        </div>

        <!-- 响应区域 -->
        <div class="response-section">
          <div class="response-header">
            <h3>响应结果</h3>
            <div class="response-info" v-if="response">
              <n-tag :type="getStatusType(response.status)">
                {{ response.status }} {{ response.statusText }}
              </n-tag>
              <span class="response-time">{{ response.time }}ms</span>
              <span class="response-size">{{ formatSize(response.size) }}</span>
            </div>
          </div>
          
          <div v-if="!response" class="no-response">
            <n-empty description="发送请求查看响应结果" />
          </div>
          
          <div v-else class="response-content">
            <n-tabs type="line" animated>
              <n-tab-pane name="body" tab="响应体">
                <div class="response-body">
                  <div class="body-actions">
                    <n-button-group size="small">
                      <n-button
                        :type="responseView === 'formatted' ? 'primary' : 'default'"
                        @click="responseView = 'formatted'"
                      >
                        格式化
                      </n-button>
                      <n-button
                        :type="responseView === 'raw' ? 'primary' : 'default'"
                        @click="responseView = 'raw'"
                      >
                        原始
                      </n-button>
                    </n-button-group>
                    
                    <n-button size="small" @click="copyResponse">
                      <template #icon>
                        <n-icon>
                          <CopyOutline />
                        </n-icon>
                      </template>
                      复制
                    </n-button>
                  </div>
                  
                  <div class="body-content">
                    <pre v-if="responseView === 'formatted'" class="formatted-json">{{ formattedResponse }}</pre>
                    <pre v-else class="raw-text">{{ response.body }}</pre>
                  </div>
                </div>
              </n-tab-pane>
              
              <n-tab-pane name="headers" tab="响应头">
                <div class="response-headers">
                  <div
                    v-for="(value, key) in response.headers"
                    :key="key"
                    class="header-row"
                  >
                    <span class="header-key">{{ key }}:</span>
                    <span class="header-value">{{ value }}</span>
                  </div>
                </div>
              </n-tab-pane>
            </n-tabs>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建集合对话框 -->
    <CreateCollectionModal
      v-model:show="showCreateCollection"
      @success="handleCollectionCreated"
    />

    <!-- 保存请求对话框 -->
    <SaveRequestModal
      v-model:show="showSaveRequest"
      :request="request"
      :collections="collections"
      @success="handleRequestSaved"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, h } from 'vue'
import { useMessage } from 'naive-ui'
import {
  DownloadOutline,
  ShareOutline,
  SaveOutline,
  AddOutline,
  LinkOutline,
  SendOutline,
  TrashOutline,
  CopyOutline,
  FolderOutline,
  DocumentOutline
} from '@vicons/ionicons5'
import CreateCollectionModal from '@/components/CreateCollectionModal.vue'
import SaveRequestModal from '@/components/SaveRequestModal.vue'

const message = useMessage()

// 响应式数据
const sending = ref(false)
const showCreateCollection = ref(false)
const showSaveRequest = ref(false)
const responseView = ref<'formatted' | 'raw'>('formatted')

// 请求数据
const request = ref({
  method: 'GET',
  url: '',
  params: [{ key: '', value: '', enabled: true }],
  headers: [
    { key: 'Content-Type', value: 'application/json', enabled: true },
    { key: '', value: '', enabled: true }
  ],
  bodyType: 'none',
  body: '',
  formData: [{ key: '', value: '', enabled: true }],
  auth: {
    type: null as string | null,
    token: '',
    username: '',
    password: '',
    keyName: 'X-API-Key',
    keyValue: '',
    keyLocation: 'header'
  }
})

// 响应数据
const response = ref<{
  status: number
  statusText: string
  headers: Record<string, string>
  body: string
  time: number
  size: number
} | null>(null)

// 集合数据
const collections = ref([
  {
    id: 1,
    name: '默认集合',
    requests: [
      { id: 1, name: '获取用户信息', method: 'GET', url: '/api/users/me' },
      { id: 2, name: '创建订阅', method: 'POST', url: '/api/subscriptions' }
    ]
  }
])

// 选项配置
const methodOptions = [
  { label: 'GET', value: 'GET' },
  { label: 'POST', value: 'POST' },
  { label: 'PUT', value: 'PUT' },
  { label: 'DELETE', value: 'DELETE' },
  { label: 'PATCH', value: 'PATCH' },
  { label: 'HEAD', value: 'HEAD' },
  { label: 'OPTIONS', value: 'OPTIONS' }
]

const authTypeOptions = [
  { label: '无认证', value: null },
  { label: 'Bearer Token', value: 'bearer' },
  { label: 'Basic Auth', value: 'basic' },
  { label: 'API Key', value: 'apikey' }
]

// 计算属性
const collectionsData = computed(() => {
  return collections.value.map(collection => ({
    key: `collection-${collection.id}`,
    label: collection.name,
    isLeaf: false,
    children: collection.requests.map(req => ({
      key: `request-${req.id}`,
      label: req.name,
      method: req.method,
      url: req.url,
      isLeaf: true
    }))
  }))
})

const formattedResponse = computed(() => {
  if (!response.value?.body) return ''
  
  try {
    const parsed = JSON.parse(response.value.body)
    return JSON.stringify(parsed, null, 2)
  } catch {
    return response.value.body
  }
})

// 工具函数
const getStatusType = (status: number) => {
  if (status >= 200 && status < 300) return 'success'
  if (status >= 300 && status < 400) return 'info'
  if (status >= 400 && status < 500) return 'warning'
  return 'error'
}

const formatSize = (bytes: number) => {
  if (bytes < 1024) return `${bytes} B`
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`
  return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
}

// 渲染函数
const renderCollectionLabel = ({ option }: any) => {
  if (option.method) {
    return h('div', { class: 'request-label' }, [
      h('span', { class: `method-tag method-${option.method.toLowerCase()}` }, option.method),
      h('span', { class: 'request-name' }, option.label)
    ])
  }
  return option.label
}

const renderCollectionPrefix = ({ option }: any) => {
  return h('n-icon', { size: 16 }, {
    default: () => h(option.isLeaf ? DocumentOutline : FolderOutline)
  })
}

// 事件处理
const addParam = () => {
  request.value.params.push({ key: '', value: '', enabled: true })
}

const removeParam = (index: number) => {
  request.value.params.splice(index, 1)
}

const addHeader = () => {
  request.value.headers.push({ key: '', value: '', enabled: true })
}

const removeHeader = (index: number) => {
  request.value.headers.splice(index, 1)
}

const addFormData = () => {
  request.value.formData.push({ key: '', value: '', enabled: true })
}

const removeFormData = (index: number) => {
  request.value.formData.splice(index, 1)
}

const sendRequest = async () => {
  if (!request.value.url) {
    message.warning('请输入请求URL')
    return
  }

  sending.value = true
  const startTime = Date.now()

  try {
    // 模拟API请求
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000))
    
    const endTime = Date.now()
    const mockResponse = {
      status: 200,
      statusText: 'OK',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': '156',
        'Date': new Date().toUTCString()
      },
      body: JSON.stringify({
        success: true,
        message: '请求成功',
        data: {
          id: 1,
          name: 'Test User',
          email: '<EMAIL>'
        }
      }, null, 2),
      time: endTime - startTime,
      size: 156
    }

    response.value = mockResponse
    message.success('请求发送成功')

  } catch (error: any) {
    message.error('请求发送失败: ' + error.message)
  } finally {
    sending.value = false
  }
}

const copyResponse = async () => {
  if (!response.value?.body) return
  
  try {
    await navigator.clipboard.writeText(response.value.body)
    message.success('响应内容已复制到剪贴板')
  } catch {
    message.error('复制失败')
  }
}

const saveRequest = () => {
  showSaveRequest.value = true
}

const importCollection = () => {
  message.info('导入集合功能开发中')
}

const exportCollection = () => {
  message.info('导出集合功能开发中')
}

const handleCollectionSelect = (keys: string[]) => {
  const key = keys[0]
  if (key?.startsWith('request-')) {
    // 加载选中的请求
    console.log('加载请求:', key)
  }
}

const handleCollectionCreated = (collection: any) => {
  collections.value.push(collection)
  message.success('集合创建成功')
}

const handleRequestSaved = (data: any) => {
  message.success('请求保存成功')
}
</script>

<style scoped>
.api-testing {
  padding: 24px;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 24px;
}

.header-content h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: #333;
}

.page-description {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.testing-layout {
  display: flex;
  gap: 24px;
  flex: 1;
  overflow: hidden;
}

.collections-panel {
  width: 300px;
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  overflow-y: auto;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.testing-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
  overflow: hidden;
}

.request-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.request-line {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.request-tabs {
  margin-top: 16px;
}

.params-section,
.headers-section,
.form-data {
  padding: 16px 0;
}

.params-header,
.headers-header,
.form-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  font-weight: 600;
  color: #333;
}

.params-list,
.headers-list,
.form-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.param-item,
.header-item,
.form-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.body-section {
  padding: 16px 0;
}

.body-type {
  margin-bottom: 16px;
}

.json-editor,
.raw-body {
  margin-top: 16px;
}

.auth-section {
  padding: 16px 0;
}

.response-section {
  flex: 1;
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.response-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.response-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.response-info {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
}

.response-time,
.response-size {
  color: #666;
}

.no-response {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.response-content {
  flex: 1;
  overflow: hidden;
}

.response-body {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.body-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.body-content {
  flex: 1;
  overflow: auto;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.formatted-json,
.raw-text {
  margin: 0;
  padding: 16px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-all;
}

.response-headers {
  padding: 16px 0;
}

.header-row {
  display: flex;
  margin-bottom: 8px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.header-key {
  font-weight: 600;
  color: #333;
  min-width: 200px;
}

.header-value {
  color: #666;
  word-break: break-all;
}

:deep(.request-label) {
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.method-tag) {
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
  color: white;
  min-width: 40px;
  text-align: center;
}

:deep(.method-get) { background: #52c41a; }
:deep(.method-post) { background: #1890ff; }
:deep(.method-put) { background: #faad14; }
:deep(.method-delete) { background: #ff4d4f; }
:deep(.method-patch) { background: #722ed1; }
:deep(.method-head) { background: #13c2c2; }
:deep(.method-options) { background: #eb2f96; }

:deep(.request-name) {
  font-size: 13px;
  color: #333;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .testing-layout {
    flex-direction: column;
  }
  
  .collections-panel {
    width: 100%;
    height: 200px;
  }
}

@media (max-width: 768px) {
  .api-testing {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .request-line {
    flex-direction: column;
    gap: 12px;
  }
  
  .request-line .n-input {
    margin: 0 !important;
  }
}
</style>
