/**
 * 路由配置
 */
import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/pages/auth/Login.vue'),
    meta: {
      title: '登录',
      requiresAuth: false,
      hideInMenu: true
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/pages/auth/Register.vue'),
    meta: {
      title: '注册',
      requiresAuth: false,
      hideInMenu: true
    }
  },
  {
    path: '/',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: () => import('@/pages/dashboard/Dashboard.vue'),
        meta: {
          title: '仪表板',
          icon: 'dashboard-outline'
        }
      },
      {
        path: '/mcp',
        name: 'McpServices',
        meta: {
          title: 'MCP服务',
          icon: 'server-outline'
        },
        children: [
          {
            path: 'overview',
            name: 'McpOverview',
            component: () => import('@/pages/mcp/Overview.vue'),
            meta: {
              title: '服务总览',
              icon: 'grid-outline'
            }
          },
          {
            path: 'tools',
            name: 'McpTools',
            component: () => import('@/pages/mcp/Tools.vue'),
            meta: {
              title: '工具管理',
              icon: 'construct-outline'
            }
          },
          {
            path: 'resources',
            name: 'McpResources',
            component: () => import('@/pages/mcp/Resources.vue'),
            meta: {
              title: '资源管理',
              icon: 'folder-outline'
            }
          },
          {
            path: 'commands',
            name: 'McpCommands',
            component: () => import('@/pages/mcp/Commands.vue'),
            meta: {
              title: '服务控制',
              icon: 'terminal-outline'
            }
          },
          {
            path: 'api-test',
            name: 'McpApiTest',
            component: () => import('@/pages/mcp/ApiTest.vue'),
            meta: {
              title: 'API测试',
              icon: 'bug-outline'
            }
          }
        ]
      },
      {
        path: '/subscriptions',
        name: 'Subscriptions',
        component: () => import('@/pages/subscriptions/Subscriptions.vue'),
        meta: {
          title: '订阅管理',
          icon: 'card-outline'
        }
      },
      {
        path: '/monitoring',
        name: 'Monitoring',
        meta: {
          title: '系统监控',
          icon: 'analytics-outline'
        },
        children: [
          {
            path: 'realtime',
            name: 'RealtimeMonitoring',
            component: () => import('@/pages/monitoring/Realtime.vue'),
            meta: {
              title: '实时监控',
              icon: 'pulse-outline'
            }
          },
          {
            path: 'services',
            name: 'ServiceMonitoring',
            component: () => import('@/pages/monitoring/Services.vue'),
            meta: {
              title: '服务监控',
              icon: 'hardware-chip-outline'
            }
          },
          {
            path: 'api-calls',
            name: 'ApiCallMonitoring',
            component: () => import('@/pages/monitoring/ApiCalls.vue'),
            meta: {
              title: 'API监控',
              icon: 'swap-horizontal-outline'
            }
          }
        ]
      },
      {
        path: '/testing',
        name: 'ApiTesting',
        component: () => import('@/pages/testing/ApiTesting.vue'),
        meta: {
          title: '接口测试',
          icon: 'flask-outline'
        }
      },
      {
        path: '/settings',
        name: 'Settings',
        meta: {
          title: '系统设置',
          icon: 'settings-outline'
        },
        children: [
          {
            path: 'profile',
            name: 'UserProfile',
            component: () => import('@/pages/settings/Profile.vue'),
            meta: {
              title: '个人资料',
              icon: 'person-outline'
            }
          },
          {
            path: 'preferences',
            name: 'UserPreferences',
            component: () => import('@/pages/settings/Preferences.vue'),
            meta: {
              title: '偏好设置',
              icon: 'options-outline'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/pages/error/NotFound.vue'),
    meta: {
      title: '页面未找到',
      hideInMenu: true
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// LoadingBar实例
let loadingBarInstance: any = null

// 初始化LoadingBar
const initLoadingBar = () => {
  try {
    if (typeof window !== 'undefined') {
      import('naive-ui').then(({ useLoadingBar }) => {
        loadingBarInstance = useLoadingBar()
      }).catch(() => {
        // 忽略错误
      })
    }
  } catch {
    // 忽略错误
  }
}

// 安全获取LoadingBar
const getLoadingBar = () => loadingBarInstance

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const loadingBar = getLoadingBar()
  loadingBar?.start()

  const authStore = useAuthStore()

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - Nexus管理平台`
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth !== false) {
    if (!authStore.isAuthenticated) {
      // 尝试从token恢复认证状态
      if (authStore.token) {
        try {
          await authStore.validateToken()
          if (authStore.isAuthenticated) {
            next()
            return
          }
        } catch (error) {
          console.error('Token验证失败:', error)
        }
      }
      
      // 未认证，跳转到登录页
      next({
        name: 'Login',
        query: { redirect: to.fullPath }
      })
      return
    }
  }

  // 已登录用户访问登录页，重定向到首页
  if (authStore.isAuthenticated && (to.name === 'Login' || to.name === 'Register')) {
    next({ name: 'Dashboard' })
    return
  }

  next()
})

router.afterEach(() => {
  const loadingBar = getLoadingBar()
  loadingBar?.finish()
})

router.onError((error) => {
  const loadingBar = getLoadingBar()
  loadingBar?.error()
  console.error('路由错误:', error)
})

// 初始化LoadingBar
initLoadingBar()

export default router
