/**
 * 认证状态管理
 */
import { defineStore } from 'pinia'
import type { User, LoginRequest, RegisterRequest, LoginResponse } from '@/types/api'
import { httpPost } from '@/utils/http'

interface AuthState {
  user: User | null
  token: string | null
  refreshToken: string | null
  isAuthenticated: boolean
  loading: boolean
  error: string | null
}

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    user: null,
    token: localStorage.getItem('nexus_token'),
    refreshToken: localStorage.getItem('nexus_refresh_token'),
    isAuthenticated: false,
    loading: false,
    error: null
  }),

  getters: {
    isAdmin: (state) => state.user?.role === 'ADMIN',
    isUser: (state) => state.user?.role === 'USER',
    userDisplayName: (state) => state.user?.username || state.user?.email || '未知用户',
    hasPermission: (state) => (permission: string) => {
      if (!state.user) return false
      if (state.user.role === 'ADMIN') return true
      // 这里可以根据实际权限系统扩展
      return true
    }
  },

  actions: {
    // 初始化认证状态
    async initAuth() {
      if (this.token) {
        try {
          await this.validateToken()
        } catch (error) {
          this.clearAuth()
        }
      }
    },

    // 用户登录
    async login(credentials: LoginRequest): Promise<void> {
      this.loading = true
      this.error = null

      try {
        const response = await httpPost<LoginResponse>('/auth/api/v1/auth/login', credentials)
        
        if (response.success && response.data) {
          const { user, accessToken, refreshToken } = response.data
          
          this.user = user
          this.token = accessToken
          this.refreshToken = refreshToken
          this.isAuthenticated = true

          // 持久化存储
          localStorage.setItem('nexus_token', accessToken)
          localStorage.setItem('nexus_refresh_token', refreshToken)
          localStorage.setItem('nexus_user', JSON.stringify(user))

          console.log('登录成功:', user.username)
        }
      } catch (error: any) {
        this.error = error.message || '登录失败'
        throw error
      } finally {
        this.loading = false
      }
    },

    // 用户注册
    async register(userData: RegisterRequest): Promise<void> {
      this.loading = true
      this.error = null

      try {
        const response = await httpPost<User>('/auth/api/v1/auth/register', userData)
        
        if (response.success) {
          console.log('注册成功:', response.data.username)
          // 注册成功后可以自动登录
          await this.login({
            usernameOrEmail: userData.username,
            password: userData.password
          })
        }
      } catch (error: any) {
        this.error = error.message || '注册失败'
        throw error
      } finally {
        this.loading = false
      }
    },

    // 验证Token
    async validateToken(): Promise<boolean> {
      if (!this.token) return false

      try {
        const response = await httpPost<User>('/auth/api/v1/auth/validate/token', {
          token: this.token
        })

        if (response.success && response.data) {
          this.user = response.data
          this.isAuthenticated = true
          
          // 更新本地存储的用户信息
          localStorage.setItem('nexus_user', JSON.stringify(response.data))
          
          return true
        }
      } catch (error) {
        console.error('Token验证失败:', error)
        this.clearAuth()
      }

      return false
    },

    // 刷新访问令牌
    async refreshAccessToken(): Promise<void> {
      if (!this.refreshToken) {
        throw new Error('没有刷新令牌')
      }

      try {
        const response = await httpPost<LoginResponse>('/auth/api/v1/auth/refresh', {
          refreshToken: this.refreshToken
        })

        if (response.success && response.data) {
          const { accessToken, refreshToken } = response.data
          
          this.token = accessToken
          this.refreshToken = refreshToken

          // 更新本地存储
          localStorage.setItem('nexus_token', accessToken)
          localStorage.setItem('nexus_refresh_token', refreshToken)

          console.log('Token刷新成功')
        }
      } catch (error) {
        console.error('Token刷新失败:', error)
        this.clearAuth()
        throw error
      }
    },

    // 用户登出
    async logout(): Promise<void> {
      try {
        // 调用后端登出接口
        if (this.token) {
          await httpPost('/auth/api/v1/auth/logout')
        }
      } catch (error) {
        console.error('登出请求失败:', error)
      } finally {
        this.clearAuth()
      }
    },

    // 清除认证信息
    clearAuth() {
      this.user = null
      this.token = null
      this.refreshToken = null
      this.isAuthenticated = false
      this.error = null

      // 清除本地存储
      localStorage.removeItem('nexus_token')
      localStorage.removeItem('nexus_refresh_token')
      localStorage.removeItem('nexus_user')

      console.log('认证信息已清除')
    },

    // 更新用户信息
    updateUser(user: Partial<User>) {
      if (this.user) {
        this.user = { ...this.user, ...user }
        localStorage.setItem('nexus_user', JSON.stringify(this.user))
      }
    },

    // 从本地存储恢复用户信息
    restoreUserFromStorage() {
      const storedUser = localStorage.getItem('nexus_user')
      if (storedUser) {
        try {
          this.user = JSON.parse(storedUser)
          this.isAuthenticated = !!this.token
        } catch (error) {
          console.error('恢复用户信息失败:', error)
          this.clearAuth()
        }
      }
    }
  }
})

export default useAuthStore
