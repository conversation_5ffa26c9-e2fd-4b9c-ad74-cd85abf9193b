/**
 * 实时通信状态管理
 */
import { defineStore } from 'pinia'
import type { RealtimeMessage } from '@/types/api'
import { TOPICS } from '@/composables/useWebSocket'

interface RealtimeState {
  // 连接状态
  isConnected: boolean
  connectionStatus: string
  lastConnectedAt: number | null
  
  // 消息存储
  messages: RealtimeMessage[]
  notifications: RealtimeMessage[]
  
  // 服务状态
  serviceStatuses: Record<string, any>
  
  // API调用统计
  apiCallStats: {
    totalCalls: number
    successCalls: number
    errorCalls: number
    averageResponseTime: number
    recentCalls: Array<{
      timestamp: number
      method: string
      path: string
      status: number
      responseTime: number
    }>
  }
  
  // 工具执行状态
  toolExecutions: Record<string, {
    taskId: string
    toolName: string
    status: string
    progress: number
    startTime: number
    endTime?: number
    result?: any
    error?: string
  }>
  
  // 订阅更新
  subscriptionUpdates: Array<{
    userId: number
    subscriptionId: number
    action: string
    timestamp: number
    details: any
  }>
  
  // 系统告警
  systemAlerts: Array<{
    id: string
    level: 'info' | 'warning' | 'error'
    title: string
    message: string
    timestamp: number
    acknowledged: boolean
  }>
}

export const useRealtimeStore = defineStore('realtime', {
  state: (): RealtimeState => ({
    isConnected: false,
    connectionStatus: 'disconnected',
    lastConnectedAt: null,
    
    messages: [],
    notifications: [],
    
    serviceStatuses: {},
    
    apiCallStats: {
      totalCalls: 0,
      successCalls: 0,
      errorCalls: 0,
      averageResponseTime: 0,
      recentCalls: []
    },
    
    toolExecutions: {},
    
    subscriptionUpdates: [],
    
    systemAlerts: []
  }),

  getters: {
    // 连接状态相关
    connectionUptime: (state) => {
      if (!state.isConnected || !state.lastConnectedAt) return 0
      return Date.now() - state.lastConnectedAt
    },

    // 未读通知数量
    unreadNotificationsCount: (state) => {
      return state.notifications.filter(n => !n.read).length
    },

    // 活跃服务数量
    activeServicesCount: (state) => {
      return Object.values(state.serviceStatuses).filter(
        status => status.isHealthy && status.status === 'RUNNING'
      ).length
    },

    // API成功率
    apiSuccessRate: (state) => {
      const { totalCalls, successCalls } = state.apiCallStats
      return totalCalls > 0 ? (successCalls / totalCalls) * 100 : 0
    },

    // 进行中的工具执行
    activeToolExecutions: (state) => {
      return Object.values(state.toolExecutions).filter(
        execution => execution.status === 'running' || execution.status === 'pending'
      )
    },

    // 未确认的告警
    unacknowledgedAlerts: (state) => {
      return state.systemAlerts.filter(alert => !alert.acknowledged)
    },

    // 最近的系统活动
    recentActivities: (state) => {
      const activities = [
        ...state.messages.slice(-10),
        ...state.subscriptionUpdates.slice(-5).map(update => ({
          type: 'SUBSCRIPTION_UPDATE',
          timestamp: new Date(update.timestamp).toISOString(),
          content: `用户 ${update.userId} ${update.action}了订阅`,
          data: update
        }))
      ]
      
      return activities
        .sort((a, b) => new Date(b.timestamp || 0).getTime() - new Date(a.timestamp || 0).getTime())
        .slice(0, 20)
    }
  },

  actions: {
    // 设置连接状态
    setConnectionStatus(status: string, isConnected: boolean) {
      this.connectionStatus = status
      this.isConnected = isConnected
      
      if (isConnected) {
        this.lastConnectedAt = Date.now()
      }
    },

    // 添加消息
    addMessage(message: RealtimeMessage) {
      this.messages.push({
        ...message,
        timestamp: message.timestamp || new Date().toISOString()
      })
      
      // 保持消息数量在合理范围内
      if (this.messages.length > 1000) {
        this.messages = this.messages.slice(-500)
      }
      
      // 根据消息类型分发到不同的处理器
      this.handleMessageByType(message)
    },

    // 根据消息类型处理
    handleMessageByType(message: RealtimeMessage) {
      switch (message.type) {
        case 'SERVICE_STATUS':
          this.updateServiceStatus(message)
          break
        case 'API_CALL_STATS':
          this.updateApiCallStats(message)
          break
        case 'TOOL_EXECUTION':
          this.updateToolExecution(message)
          break
        case 'SUBSCRIPTION_UPDATE':
          this.addSubscriptionUpdate(message)
          break
        case 'SYSTEM_ALERT':
          this.addSystemAlert(message)
          break
        case 'USER_NOTIFICATION':
          this.addNotification(message)
          break
      }
    },

    // 更新服务状态
    updateServiceStatus(message: RealtimeMessage) {
      if (message.serviceId && message.data) {
        this.serviceStatuses[message.serviceId] = {
          ...this.serviceStatuses[message.serviceId],
          ...message.data,
          lastUpdated: Date.now()
        }
      }
    },

    // 更新API调用统计
    updateApiCallStats(message: RealtimeMessage) {
      if (message.data) {
        const stats = message.data
        this.apiCallStats = {
          ...this.apiCallStats,
          ...stats
        }
        
        // 添加最近调用记录
        if (stats.recentCall) {
          this.apiCallStats.recentCalls.unshift(stats.recentCall)
          if (this.apiCallStats.recentCalls.length > 100) {
            this.apiCallStats.recentCalls = this.apiCallStats.recentCalls.slice(0, 50)
          }
        }
      }
    },

    // 更新工具执行状态
    updateToolExecution(message: RealtimeMessage) {
      if (message.taskId) {
        const existing = this.toolExecutions[message.taskId] || {}
        
        this.toolExecutions[message.taskId] = {
          ...existing,
          taskId: message.taskId,
          toolName: message.toolName || existing.toolName,
          status: message.status || existing.status,
          progress: message.progress ?? existing.progress ?? 0,
          startTime: existing.startTime || Date.now(),
          endTime: message.status === 'completed' || message.status === 'failed' 
            ? Date.now() 
            : existing.endTime,
          result: message.data || existing.result,
          error: message.content || existing.error
        }
      }
    },

    // 添加订阅更新
    addSubscriptionUpdate(message: RealtimeMessage) {
      if (message.data) {
        this.subscriptionUpdates.unshift({
          userId: message.data.userId,
          subscriptionId: message.data.subscriptionId,
          action: message.data.action,
          timestamp: Date.now(),
          details: message.data
        })
        
        // 保持合理数量
        if (this.subscriptionUpdates.length > 200) {
          this.subscriptionUpdates = this.subscriptionUpdates.slice(0, 100)
        }
      }
    },

    // 添加系统告警
    addSystemAlert(message: RealtimeMessage) {
      const alert = {
        id: message.messageId || `alert_${Date.now()}`,
        level: message.level || 'info',
        title: message.title || '系统通知',
        message: message.content || '',
        timestamp: Date.now(),
        acknowledged: false
      }
      
      this.systemAlerts.unshift(alert)
      
      // 保持合理数量
      if (this.systemAlerts.length > 100) {
        this.systemAlerts = this.systemAlerts.slice(0, 50)
      }
    },

    // 添加用户通知
    addNotification(message: RealtimeMessage) {
      this.notifications.unshift({
        ...message,
        read: false,
        timestamp: message.timestamp || new Date().toISOString()
      })
      
      // 保持合理数量
      if (this.notifications.length > 50) {
        this.notifications = this.notifications.slice(0, 25)
      }
    },

    // 标记通知为已读
    markNotificationAsRead(messageId: string) {
      const notification = this.notifications.find(n => n.messageId === messageId)
      if (notification) {
        notification.read = true
      }
    },

    // 标记所有通知为已读
    markAllNotificationsAsRead() {
      this.notifications.forEach(notification => {
        notification.read = true
      })
    },

    // 确认告警
    acknowledgeAlert(alertId: string) {
      const alert = this.systemAlerts.find(a => a.id === alertId)
      if (alert) {
        alert.acknowledged = true
      }
    },

    // 确认所有告警
    acknowledgeAllAlerts() {
      this.systemAlerts.forEach(alert => {
        alert.acknowledged = true
      })
    },

    // 清除旧数据
    clearOldData() {
      const now = Date.now()
      const oneHourAgo = now - 60 * 60 * 1000
      const oneDayAgo = now - 24 * 60 * 60 * 1000
      
      // 清除1小时前的消息
      this.messages = this.messages.filter(
        msg => new Date(msg.timestamp || 0).getTime() > oneHourAgo
      )
      
      // 清除1天前的API调用记录
      this.apiCallStats.recentCalls = this.apiCallStats.recentCalls.filter(
        call => call.timestamp > oneDayAgo
      )
      
      // 清除已完成的工具执行（保留最近50个）
      const completedExecutions = Object.entries(this.toolExecutions)
        .filter(([_, execution]) => 
          execution.status === 'completed' || execution.status === 'failed'
        )
        .sort((a, b) => (b[1].endTime || 0) - (a[1].endTime || 0))
        .slice(50)
      
      completedExecutions.forEach(([taskId]) => {
        delete this.toolExecutions[taskId]
      })
    },

    // 重置状态
    reset() {
      this.isConnected = false
      this.connectionStatus = 'disconnected'
      this.lastConnectedAt = null
      this.messages = []
      this.notifications = []
      this.serviceStatuses = {}
      this.apiCallStats = {
        totalCalls: 0,
        successCalls: 0,
        errorCalls: 0,
        averageResponseTime: 0,
        recentCalls: []
      }
      this.toolExecutions = {}
      this.subscriptionUpdates = []
      this.systemAlerts = []
    }
  }
})

export default useRealtimeStore
