/**
 * 设置管理状态
 */
import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface UserSettings {
  profile: {
    username: string
    email: string
    displayName: string
    avatar: string
    timezone: string
    language: string
  }
  interface: {
    theme: string
    primaryColor: string
    sidebarCollapsed: boolean
    showBreadcrumb: boolean
    showPageTransition: boolean
    density: string
    fontSize: string
  }
  notifications: {
    desktop: boolean
    email: boolean
    sound: boolean
    serviceAlerts: boolean
    systemUpdates: boolean
    securityAlerts: boolean
    frequency: string
  }
  security: {
    twoFactorEnabled: boolean
    sessionTimeout: number
    passwordExpiry: number
    loginNotifications: boolean
    ipWhitelist: string[]
    apiKeyExpiry: number
  }
  system: {
    logLevel: string
    maxLogSize: number
    backupEnabled: boolean
    backupInterval: number
    maintenanceMode: boolean
    debugMode: boolean
  }
  advanced: {
    experimentalFeatures: boolean
    developerMode: boolean
    apiRateLimit: number
    cacheSize: number
    customCSS: string
    customJS: string
  }
}

export const useSettingsStore = defineStore('settings', () => {
  // 状态
  const settings = ref<UserSettings>({
    profile: {
      username: '',
      email: '',
      displayName: '',
      avatar: '',
      timezone: 'Asia/Shanghai',
      language: 'zh-CN'
    },
    interface: {
      theme: 'auto',
      primaryColor: '#667eea',
      sidebarCollapsed: false,
      showBreadcrumb: true,
      showPageTransition: true,
      density: 'normal',
      fontSize: 'medium'
    },
    notifications: {
      desktop: true,
      email: false,
      sound: true,
      serviceAlerts: true,
      systemUpdates: true,
      securityAlerts: true,
      frequency: 'immediate'
    },
    security: {
      twoFactorEnabled: false,
      sessionTimeout: 30,
      passwordExpiry: 90,
      loginNotifications: true,
      ipWhitelist: [],
      apiKeyExpiry: 365
    },
    system: {
      logLevel: 'info',
      maxLogSize: 100,
      backupEnabled: true,
      backupInterval: 24,
      maintenanceMode: false,
      debugMode: false
    },
    advanced: {
      experimentalFeatures: false,
      developerMode: false,
      apiRateLimit: 1000,
      cacheSize: 256,
      customCSS: '',
      customJS: ''
    }
  })

  // 操作
  const getSettings = async (): Promise<UserSettings> => {
    // 从本地存储加载设置
    try {
      const saved = localStorage.getItem('nexus_user_settings')
      if (saved) {
        const parsedSettings = JSON.parse(saved)
        settings.value = { ...settings.value, ...parsedSettings }
      }
    } catch (error) {
      console.error('加载用户设置失败:', error)
    }
    
    return settings.value
  }

  const updateSettings = async (newSettings: Partial<UserSettings>): Promise<void> => {
    settings.value = { ...settings.value, ...newSettings }
    
    // 保存到本地存储
    try {
      localStorage.setItem('nexus_user_settings', JSON.stringify(settings.value))
    } catch (error) {
      console.error('保存用户设置失败:', error)
      throw new Error('保存设置失败')
    }
  }

  const resetSettings = (): void => {
    settings.value = {
      profile: {
        username: '',
        email: '',
        displayName: '',
        avatar: '',
        timezone: 'Asia/Shanghai',
        language: 'zh-CN'
      },
      interface: {
        theme: 'auto',
        primaryColor: '#667eea',
        sidebarCollapsed: false,
        showBreadcrumb: true,
        showPageTransition: true,
        density: 'normal',
        fontSize: 'medium'
      },
      notifications: {
        desktop: true,
        email: false,
        sound: true,
        serviceAlerts: true,
        systemUpdates: true,
        securityAlerts: true,
        frequency: 'immediate'
      },
      security: {
        twoFactorEnabled: false,
        sessionTimeout: 30,
        passwordExpiry: 90,
        loginNotifications: true,
        ipWhitelist: [],
        apiKeyExpiry: 365
      },
      system: {
        logLevel: 'info',
        maxLogSize: 100,
        backupEnabled: true,
        backupInterval: 24,
        maintenanceMode: false,
        debugMode: false
      },
      advanced: {
        experimentalFeatures: false,
        developerMode: false,
        apiRateLimit: 1000,
        cacheSize: 256,
        customCSS: '',
        customJS: ''
      }
    }
    
    // 清除本地存储
    localStorage.removeItem('nexus_user_settings')
  }

  return {
    settings,
    getSettings,
    updateSettings,
    resetSettings
  }
})
