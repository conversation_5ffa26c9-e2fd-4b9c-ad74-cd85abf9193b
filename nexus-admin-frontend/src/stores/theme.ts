/**
 * 主题状态管理
 */
import { defineStore } from 'pinia'
import type { ThemeMode } from '@/types/common'

interface ThemeState {
  mode: ThemeMode
  language: string
  sidebarCollapsed: boolean
  primaryColor: string
}

export const useThemeStore = defineStore('theme', {
  state: (): ThemeState => ({
    mode: 'dark', // 默认使用深色主题
    language: 'zh-CN',
    sidebarCollapsed: false,
    primaryColor: '#3b82f6' // 更新为新的主色调
  }),

  getters: {
    isDark: (state) => {
      if (state.mode === 'dark') return true
      if (state.mode === 'light') return false
      // auto模式下根据系统主题
      return window.matchMedia('(prefers-color-scheme: dark)').matches
    },

    isLight: (state) => !state.isDark,

    currentTheme: (state) => state.mode,

    isZhCN: (state) => state.language === 'zh-CN'
  },

  actions: {
    // 设置主题模式
    setTheme(mode: ThemeMode) {
      this.mode = mode
      this.applyTheme()
      this.saveToStorage()
    },

    // 切换主题
    toggleTheme() {
      if (this.mode === 'light') {
        this.setTheme('dark')
      } else if (this.mode === 'dark') {
        this.setTheme('auto')
      } else {
        this.setTheme('light')
      }
    },

    // 设置语言
    setLanguage(language: string) {
      this.language = language
      this.saveToStorage()
    },

    // 切换侧边栏
    toggleSidebar() {
      this.sidebarCollapsed = !this.sidebarCollapsed
      this.saveToStorage()
    },

    // 设置侧边栏状态
    setSidebarCollapsed(collapsed: boolean) {
      this.sidebarCollapsed = collapsed
      this.saveToStorage()
    },

    // 设置主色调
    setPrimaryColor(color: string) {
      this.primaryColor = color
      this.applyPrimaryColor()
      this.saveToStorage()
    },

    // 应用主题
    applyTheme() {
      const html = document.documentElement
      
      if (this.isDark) {
        html.setAttribute('data-theme', 'dark')
        html.classList.add('dark')
      } else {
        html.setAttribute('data-theme', 'light')
        html.classList.remove('dark')
      }
    },

    // 应用主色调
    applyPrimaryColor() {
      const root = document.documentElement
      root.style.setProperty('--primary-color', this.primaryColor)
      
      // 生成相关颜色
      const hoverColor = this.adjustColor(this.primaryColor, -10)
      const pressedColor = this.adjustColor(this.primaryColor, -20)
      const supplColor = this.hexToRgba(this.primaryColor, 0.1)
      
      root.style.setProperty('--primary-color-hover', hoverColor)
      root.style.setProperty('--primary-color-pressed', pressedColor)
      root.style.setProperty('--primary-color-suppl', supplColor)
    },

    // 初始化主题
    initTheme() {
      this.loadFromStorage()
      this.applyTheme()
      this.applyPrimaryColor()
      this.watchSystemTheme()
    },

    // 监听系统主题变化
    watchSystemTheme() {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      
      const handleChange = () => {
        if (this.mode === 'auto') {
          this.applyTheme()
        }
      }
      
      mediaQuery.addEventListener('change', handleChange)
      
      // 返回清理函数
      return () => {
        mediaQuery.removeEventListener('change', handleChange)
      }
    },

    // 保存到本地存储
    saveToStorage() {
      const settings = {
        mode: this.mode,
        language: this.language,
        sidebarCollapsed: this.sidebarCollapsed,
        primaryColor: this.primaryColor
      }
      
      localStorage.setItem('nexus_theme_settings', JSON.stringify(settings))
    },

    // 从本地存储加载
    loadFromStorage() {
      try {
        const stored = localStorage.getItem('nexus_theme_settings')
        if (stored) {
          const settings = JSON.parse(stored)
          this.mode = settings.mode || 'dark'
          this.language = settings.language || 'zh-CN'
          this.sidebarCollapsed = settings.sidebarCollapsed || false
          this.primaryColor = settings.primaryColor || '#3b82f6'
        }
      } catch (error) {
        console.error('加载主题设置失败:', error)
      }
    },

    // 重置主题设置
    resetTheme() {
      this.mode = 'dark'
      this.language = 'zh-CN'
      this.sidebarCollapsed = false
      this.primaryColor = '#3b82f6'

      this.applyTheme()
      this.applyPrimaryColor()
      this.saveToStorage()
    },

    // 颜色调整工具函数
    adjustColor(color: string, amount: number): string {
      const usePound = color[0] === '#'
      const col = usePound ? color.slice(1) : color
      
      const num = parseInt(col, 16)
      let r = (num >> 16) + amount
      let g = (num >> 8 & 0x00FF) + amount
      let b = (num & 0x0000FF) + amount
      
      r = r > 255 ? 255 : r < 0 ? 0 : r
      g = g > 255 ? 255 : g < 0 ? 0 : g
      b = b > 255 ? 255 : b < 0 ? 0 : b
      
      return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0')
    },

    // 十六进制转RGBA
    hexToRgba(hex: string, alpha: number): string {
      const r = parseInt(hex.slice(1, 3), 16)
      const g = parseInt(hex.slice(3, 5), 16)
      const b = parseInt(hex.slice(5, 7), 16)
      
      return `rgba(${r}, ${g}, ${b}, ${alpha})`
    }
  }
})

export default useThemeStore
