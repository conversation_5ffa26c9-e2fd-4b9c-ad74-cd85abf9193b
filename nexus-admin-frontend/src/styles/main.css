/**
 * 主样式文件
 */

/* CSS变量定义 - 企业级深色主题 */
:root {
  /* 背景色系 */
  --bg-primary: #0f1419;
  --bg-secondary: #1a1d29;
  --bg-tertiary: #2d3748;
  --bg-elevated: #374151;
  --bg-surface: rgba(45, 55, 72, 0.8);
  --bg-overlay: rgba(15, 20, 25, 0.95);

  /* 主色调 - 科技蓝色系 */
  --primary-color: #3b82f6;
  --primary-color-hover: #2563eb;
  --primary-color-pressed: #1d4ed8;
  --primary-color-suppl: rgba(59, 130, 246, 0.1);
  --primary-color-light: rgba(59, 130, 246, 0.2);

  /* 功能色彩 */
  --success-color: #10b981;
  --success-color-hover: #059669;
  --success-light: rgba(16, 185, 129, 0.1);

  --warning-color: #f59e0b;
  --warning-color-hover: #d97706;
  --warning-light: rgba(245, 158, 11, 0.1);

  --error-color: #ef4444;
  --error-color-hover: #dc2626;
  --error-light: rgba(239, 68, 68, 0.1);

  --info-color: #06b6d4;
  --info-color-hover: #0891b2;
  --info-light: rgba(6, 182, 212, 0.1);

  /* 文字色彩 */
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-tertiary: #94a3b8;
  --text-quaternary: #64748b;
  --text-disabled: #475569;

  /* 边框色彩 */
  --border-primary: rgba(148, 163, 184, 0.2);
  --border-secondary: rgba(148, 163, 184, 0.1);
  --border-focus: var(--primary-color);
  --border-error: var(--error-color);

  /* 企业级渐变 */
  --gradient-primary: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  --gradient-secondary: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
  --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  --gradient-error: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  --gradient-surface: linear-gradient(135deg, rgba(45, 55, 72, 0.8) 0%, rgba(55, 65, 81, 0.6) 100%);
  --gradient-bg: linear-gradient(135deg, #0f1419 0%, #1a1d29 50%, #2d3748 100%);

  /* 现代阴影系统 */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);
  --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);
  --shadow-card: 0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.1);

  /* 圆角系统 */
  --radius-xs: 2px;
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  --radius-xl: 12px;
  --radius-2xl: 16px;
  --radius-3xl: 24px;
  --radius-full: 9999px;

  /* 间距系统 */
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-5: 20px;
  --space-6: 24px;
  --space-8: 32px;
  --space-10: 40px;
  --space-12: 48px;
  --space-16: 64px;
  --space-20: 80px;

  /* 动画系统 */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

  /* 毛玻璃效果 */
  --glass-bg: rgba(45, 55, 72, 0.4);
  --glass-border: rgba(148, 163, 184, 0.2);
  --glass-backdrop: blur(16px) saturate(180%);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* 浅色主题变量 */
[data-theme='light'] {
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-elevated: #ffffff;
  --bg-surface: rgba(255, 255, 255, 0.8);
  --bg-overlay: rgba(255, 255, 255, 0.95);

  --text-primary: #1e293b;
  --text-secondary: #475569;
  --text-tertiary: #64748b;
  --text-quaternary: #94a3b8;
  --text-disabled: #cbd5e1;

  --border-primary: rgba(148, 163, 184, 0.3);
  --border-secondary: rgba(148, 163, 184, 0.2);

  --glass-bg: rgba(255, 255, 255, 0.4);
  --glass-border: rgba(148, 163, 184, 0.2);
  --glass-backdrop: blur(16px) saturate(180%);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

  --gradient-bg: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%);
  --gradient-surface: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.6) 100%);
}

/* 全局重置和基础样式 */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  background: var(--gradient-bg);
  color: var(--text-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* 背景装饰 */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(6, 182, 212, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-primary);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-quaternary);
}

/* 选择文本样式 */
::selection {
  background: var(--primary-color-light);
  color: var(--text-primary);
}

/* 企业级组件样式 */
.glass-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  box-shadow: var(--glass-shadow);
  transition: all var(--duration-normal) var(--ease-out);
}

.glass-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl), var(--shadow-glow);
  border-color: var(--primary-color-light);
}

.gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glow-effect {
  position: relative;
}

.glow-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  border-radius: inherit;
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-out);
  z-index: -1;
  filter: blur(20px);
}

.glow-effect:hover::before {
  opacity: 0.3;
}

/* 动画类 */
.fade-in {
  animation: fadeIn var(--duration-slow) var(--ease-out);
}

.slide-up {
  animation: slideUp var(--duration-normal) var(--ease-out);
}

.scale-in {
  animation: scaleIn var(--duration-normal) var(--ease-out);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 工具类 */
.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.border-gradient {
  position: relative;
  background: var(--bg-surface);
  border-radius: var(--radius-lg);
}

.border-gradient::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 1px;
  background: var(--gradient-primary);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  -webkit-mask-composite: xor;
}

html {
  line-height: 1.6;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 
               'Helvetica Neue', Arial, 'Noto Sans', sans-serif,
               'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
               'Noto Color Emoji';
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background: var(--gradient-primary);
}

#app {
  min-height: 100vh;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 毛玻璃卡片样式 */
.glass-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius-card);
  box-shadow: var(--shadow-card);
}

/* 渐变按钮 */
.gradient-button {
  background: var(--gradient-primary);
  border: none;
  color: white;
  border-radius: var(--border-radius-medium);
  transition: all var(--transition-medium);
  box-shadow: var(--shadow-light);
}

.gradient-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

/* 动画类 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--transition-medium);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all var(--transition-medium);
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.slide-up-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

.scale-enter-active,
.scale-leave-active {
  transition: all var(--transition-medium);
}

.scale-enter-from,
.scale-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

/* 加载动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.spin {
  animation: spin 1s linear infinite;
}

/* 响应式工具类 */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-sm {
  gap: var(--spacing-sm);
}

.gap-md {
  gap: var(--spacing-md);
}

.gap-lg {
  gap: var(--spacing-lg);
}

/* 文本工具类 */
.text-center {
  text-align: center;
}

.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 隐藏类 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 响应式断点 */
@media (max-width: 640px) {
  .container {
    padding: 0 var(--spacing-sm);
  }
}

@media (max-width: 768px) {
  :root {
    --spacing-md: 12px;
    --spacing-lg: 20px;
    --spacing-xl: 28px;
  }
}
