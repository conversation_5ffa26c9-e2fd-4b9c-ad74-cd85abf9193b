/**
 * API相关类型定义
 */

// 基础响应类型
export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data: T
  timestamp: string
  code?: string
}

// 分页响应类型
export interface PageResponse<T = any> extends ApiResponse<T> {
  data: {
    content: T[]
    totalElements: number
    totalPages: number
    size: number
    number: number
    first: boolean
    last: boolean
  }
}

// 用户相关类型
export interface User {
  id: number
  username: string
  email: string
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED'
  role: 'ADMIN' | 'USER' | 'GUEST'
  lastLoginAt?: string
  lastLoginIp?: string
  apiCallCount: number
  createdAt: string
  updatedAt: string
  subscriptions?: Subscription[]
}

export interface LoginRequest {
  usernameOrEmail: string
  password: string
}

export interface RegisterRequest {
  username: string
  email: string
  password: string
  role?: 'USER' | 'ADMIN'
}

export interface LoginResponse {
  user: User
  accessToken: string
  refreshToken: string
  expiresIn: number
}

// 订阅相关类型
export interface Subscription {
  id: number
  userId: number
  username?: string
  serviceConfigId: number
  serviceName: string
  serviceDisplayName: string
  status: 'ACTIVE' | 'EXPIRED' | 'SUSPENDED' | 'CANCELLED'
  startDate: string
  endDate: string
  callLimit: number
  usedCalls: number
  remainingCalls: number
  notes?: string
  createdAt: string
  updatedAt: string
}

export interface CreateSubscriptionRequest {
  userId: number
  serviceConfigId: number
  callLimit?: number
  endDate?: string
  notes?: string
}

// MCP服务相关类型
export interface McpService {
  id: number
  serviceName: string
  serviceType: 'LOCAL' | 'REMOTE'
  displayName: string
  description?: string
  version: string
  status: 'RUNNING' | 'STOPPED' | 'ERROR' | 'STARTING' | 'STOPPING'
  isAvailable: boolean
  isHealthy: boolean
  endpoint?: string
  tools?: McpTool[]
  resources?: McpResource[]
  toolCount: number
  resourceCount: number
  lastHealthCheck?: string
  createdAt: string
  updatedAt: string
}

export interface McpTool {
  name: string
  description: string
  inputSchema: Record<string, any>
  serviceId?: number
  serviceName?: string
  serviceType?: string
}

export interface McpResource {
  uri: string
  name: string
  description?: string
  mimeType?: string
  serviceId?: number
  serviceName?: string
  serviceType?: string
}

export interface McpServerInfo {
  name: string
  version: string
  description: string
  timestamp: number
  statistics: {
    totalServices: number
    totalTools: number
    totalResources: number
  }
  services: McpService[]
}

export interface ToolCallRequest {
  jsonrpc: '2.0'
  method: 'tools/call'
  params: {
    name: string
    arguments: Record<string, any>
  }
  id: number | string
}

export interface ToolCallResponse {
  jsonrpc: '2.0'
  result?: {
    content: Array<{
      type: 'text' | 'image' | 'resource'
      text?: string
      data?: string
      mimeType?: string
    }>
    isError?: boolean
  }
  error?: {
    code: number
    message: string
    data?: any
  }
  id: number | string
}

// 实时通信相关类型
export interface RealtimeMessage {
  type: string
  messageId?: string
  serviceId?: string
  taskId?: string
  toolName?: string
  title?: string
  content?: string
  level?: 'INFO' | 'WARN' | 'ERROR'
  status?: string
  progress?: number
  success?: boolean
  data?: any
  timestamp?: string
  senderId?: string
  receiverId?: string
}

export interface SubscriptionRequest {
  userId: string
  topic: string
  filters?: Record<string, any>
}

// 系统监控相关类型
export interface SystemHealth {
  status: 'UP' | 'DOWN' | 'DEGRADED'
  totalServices: number
  healthyServices: number
  unhealthyServices: number
  timestamp: number
}

export interface ApiCallStats {
  totalCalls: number
  successCalls: number
  errorCalls: number
  successRate: number
  averageResponseTime: number
  timestamp: number
}

// MCP命令相关类型
export interface McpCommandRequest {
  command: string
  serviceName?: string
}

export interface McpServiceStartResult {
  success: boolean
  serviceName: string
  message: string
  command: string
  config?: {
    serviceType: string
    servicePath: string
    args: string[]
  }
}

// 错误类型
export interface ApiError {
  code: string
  message: string
  details?: any
  timestamp: string
}
