/**
 * 通用类型定义
 */

// 主题类型
export type ThemeMode = 'light' | 'dark' | 'auto'

// 菜单项类型
export interface MenuItem {
  key: string
  label: string
  icon?: string
  path?: string
  children?: MenuItem[]
  disabled?: boolean
  badge?: string | number
  show?: boolean
}

// 面包屑类型
export interface BreadcrumbItem {
  label: string
  path?: string
  icon?: string
}

// 表格列类型
export interface TableColumn {
  key: string
  title: string
  width?: number
  minWidth?: number
  maxWidth?: number
  align?: 'left' | 'center' | 'right'
  sortable?: boolean
  filterable?: boolean
  render?: (row: any, index: number) => any
}

// 分页配置
export interface PaginationConfig {
  page: number
  pageSize: number
  total: number
  showSizePicker?: boolean
  showQuickJumper?: boolean
  pageSizes?: number[]
}

// 加载状态
export interface LoadingState {
  loading: boolean
  error: string | null
  lastUpdated: number | null
}

// 表单验证规则
export interface FormRule {
  required?: boolean
  message?: string
  trigger?: string | string[]
  validator?: (rule: any, value: any) => boolean | Promise<boolean>
  min?: number
  max?: number
  pattern?: RegExp
}

// 选项类型
export interface SelectOption {
  label: string
  value: string | number
  disabled?: boolean
  children?: SelectOption[]
}

// 统计卡片数据
export interface StatCard {
  title: string
  value: string | number
  icon: string
  color: string
  trend?: {
    value: number
    type: 'up' | 'down'
  }
  description?: string
}

// 图表数据类型
export interface ChartData {
  labels: string[]
  datasets: Array<{
    label: string
    data: number[]
    backgroundColor?: string | string[]
    borderColor?: string | string[]
    borderWidth?: number
  }>
}

// 通知类型
export interface NotificationItem {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  content: string
  timestamp: string
  read: boolean
  actions?: Array<{
    label: string
    action: () => void
  }>
}

// 操作日志类型
export interface OperationLog {
  id: string
  userId: number
  username: string
  action: string
  resource: string
  details: string
  ip: string
  userAgent: string
  timestamp: string
  status: 'SUCCESS' | 'FAILED'
}

// 文件上传类型
export interface UploadFile {
  id: string
  name: string
  size: number
  type: string
  status: 'pending' | 'uploading' | 'success' | 'error'
  progress: number
  url?: string
  error?: string
}

// 搜索过滤器
export interface SearchFilter {
  keyword?: string
  status?: string[]
  dateRange?: [string, string]
  tags?: string[]
  [key: string]: any
}

// 排序配置
export interface SortConfig {
  field: string
  order: 'asc' | 'desc'
}

// 导出配置
export interface ExportConfig {
  format: 'csv' | 'excel' | 'json'
  fields: string[]
  filename?: string
}

// 权限类型
export interface Permission {
  resource: string
  actions: string[]
}

// 用户偏好设置
export interface UserPreferences {
  theme: ThemeMode
  language: string
  timezone: string
  pageSize: number
  autoRefresh: boolean
  refreshInterval: number
  notifications: {
    email: boolean
    push: boolean
    sound: boolean
  }
}

// 系统配置
export interface SystemConfig {
  siteName: string
  siteDescription: string
  logo: string
  favicon: string
  defaultTheme: ThemeMode
  allowRegistration: boolean
  maintenanceMode: boolean
  version: string
}

// 响应式断点
export type Breakpoint = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl'

// 动画配置
export interface AnimationConfig {
  duration: number
  easing: string
  delay?: number
}

// 快捷键配置
export interface ShortcutKey {
  key: string
  ctrl?: boolean
  alt?: boolean
  shift?: boolean
  action: () => void
  description: string
}
