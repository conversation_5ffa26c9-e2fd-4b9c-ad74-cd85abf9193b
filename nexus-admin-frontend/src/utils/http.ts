/**
 * HTTP客户端配置
 */
import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import type { ApiResponse, ApiError } from '@/types/api'
import { useAuthStore } from '@/stores/auth'
import { useMessage } from 'naive-ui'

// 创建axios实例
const createHttpClient = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: import.meta.env.VITE_API_BASE_URL,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json'
    }
  })

  // 请求拦截器
  instance.interceptors.request.use(
    (config) => {
      const authStore = useAuthStore()
      
      // 添加认证token
      if (authStore.token) {
        config.headers.Authorization = `Bearer ${authStore.token}`
      }

      // 添加请求ID用于追踪
      config.headers['X-Request-ID'] = generateRequestId()

      // 记录请求日志
      console.log(`[HTTP] ${config.method?.toUpperCase()} ${config.url}`, {
        params: config.params,
        data: config.data
      })

      return config
    },
    (error) => {
      console.error('[HTTP] Request Error:', error)
      return Promise.reject(error)
    }
  )

  // 响应拦截器
  instance.interceptors.response.use(
    (response: AxiosResponse<ApiResponse>) => {
      const { data } = response

      // 记录响应日志
      console.log(`[HTTP] Response ${response.status}:`, data)

      // 检查业务状态码
      if (data.success === false) {
        const error: ApiError = {
          code: data.code || 'BUSINESS_ERROR',
          message: data.message || '业务处理失败',
          details: data.data,
          timestamp: data.timestamp || new Date().toISOString()
        }
        return Promise.reject(error)
      }

      return response
    },
    async (error) => {
      const message = useMessage()
      
      console.error('[HTTP] Response Error:', error)

      // 处理网络错误
      if (!error.response) {
        const networkError: ApiError = {
          code: 'NETWORK_ERROR',
          message: '网络连接失败，请检查网络设置',
          timestamp: new Date().toISOString()
        }
        message.error(networkError.message)
        return Promise.reject(networkError)
      }

      const { status, data } = error.response

      // 处理认证错误
      if (status === 401) {
        const authStore = useAuthStore()
        
        // 尝试刷新token
        if (authStore.refreshToken && !error.config._retry) {
          error.config._retry = true
          
          try {
            await authStore.refreshAccessToken()
            // 重新发送原请求
            return instance.request(error.config)
          } catch (refreshError) {
            // 刷新失败，跳转到登录页
            authStore.logout()
            window.location.href = '/login'
          }
        } else {
          authStore.logout()
          window.location.href = '/login'
        }
      }

      // 处理其他HTTP错误
      const apiError: ApiError = {
        code: data?.code || `HTTP_${status}`,
        message: data?.message || getHttpErrorMessage(status),
        details: data?.data,
        timestamp: data?.timestamp || new Date().toISOString()
      }

      // 显示错误消息（排除401，因为会自动跳转登录）
      if (status !== 401) {
        message.error(apiError.message)
      }

      return Promise.reject(apiError)
    }
  )

  return instance
}

// 生成请求ID
const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// 获取HTTP错误消息
const getHttpErrorMessage = (status: number): string => {
  const messages: Record<number, string> = {
    400: '请求参数错误',
    401: '未授权访问',
    403: '权限不足',
    404: '请求的资源不存在',
    405: '请求方法不允许',
    408: '请求超时',
    409: '请求冲突',
    422: '请求参数验证失败',
    429: '请求过于频繁',
    500: '服务器内部错误',
    502: '网关错误',
    503: '服务不可用',
    504: '网关超时'
  }
  
  return messages[status] || `HTTP错误 ${status}`
}

// 创建HTTP客户端实例
export const http = createHttpClient()

// 便捷方法
export const httpGet = <T = any>(url: string, config?: AxiosRequestConfig) => 
  http.get<ApiResponse<T>>(url, config).then(res => res.data)

export const httpPost = <T = any>(url: string, data?: any, config?: AxiosRequestConfig) => 
  http.post<ApiResponse<T>>(url, data, config).then(res => res.data)

export const httpPut = <T = any>(url: string, data?: any, config?: AxiosRequestConfig) => 
  http.put<ApiResponse<T>>(url, data, config).then(res => res.data)

export const httpDelete = <T = any>(url: string, config?: AxiosRequestConfig) => 
  http.delete<ApiResponse<T>>(url, config).then(res => res.data)

export const httpPatch = <T = any>(url: string, data?: any, config?: AxiosRequestConfig) => 
  http.patch<ApiResponse<T>>(url, data, config).then(res => res.data)

// 文件上传
export const httpUpload = <T = any>(url: string, file: File, onProgress?: (progress: number) => void) => {
  const formData = new FormData()
  formData.append('file', file)
  
  return http.post<ApiResponse<T>>(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(progress)
      }
    }
  }).then(res => res.data)
}

// 文件下载
export const httpDownload = (url: string, filename?: string) => {
  return http.get(url, {
    responseType: 'blob'
  }).then(response => {
    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  })
}

export default http
