{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue", "auto-imports.d.ts", "components.d.ts"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"composite": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/pages/*": ["./src/pages/*"], "@/api/*": ["./src/api/*"], "@/stores/*": ["./src/stores/*"], "@/utils/*": ["./src/utils/*"], "@/types/*": ["./src/types/*"], "@/composables/*": ["./src/composables/*"], "@/layouts/*": ["./src/layouts/*"], "@/styles/*": ["./src/styles/*"]}, "types": ["node", "vite/client"], "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "skipLibCheck": true}}