# Nexus Auth Service Nacos Profile Configuration
# Data ID: nexus-auth-service-nacos.yml, Group: DEFAULT_GROUP

server:
  port: 8081
  servlet:
    context-path: /auth

spring:
  application:
    name: nexus-auth-service

  # Redis database allocation for nacos profile
  redis:
    database: 1

  # RocketMQ configuration for nacos profile
  rocketmq:
    name-server: localhost:9876
    producer:
      group: nexus-auth-producer-group
      send-message-timeout: 3000
      retry-times-when-send-failed: 2
    consumer:
      group: nexus-auth-consumer-group

# Authentication service configuration for nacos profile
nexus:
  auth:
    jwt:
      secret: nexus-microservices-jwt-secret-key-2024
      expiration: 86400
      refresh-expiration: 604800

    # Password policy
    password:
      min-length: 8
      require-uppercase: true
      require-lowercase: true
      require-digits: true
      require-special-chars: false

    # Account lockout policy
    lockout:
      enabled: true
      max-attempts: 5
      lockout-duration: 300

    # Session configuration
    session:
      timeout: 1800
      max-concurrent-sessions: 3

  # Email service configuration
  email:
    enabled: false
    mock: true
    from: <EMAIL>
    from-name: Nexus Microservices Platform

  # RocketMQ listener control
  rocketmq:
    subscription:
      enabled: false
    user:
      enabled: true
    notification:
      enabled: true

# Enhanced logging configuration
logging:
  level:
    com.nexus.auth: DEBUG
    com.nexus.common: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
