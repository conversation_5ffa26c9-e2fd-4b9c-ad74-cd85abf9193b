package com.nexus.auth;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Nexus认证服务启动类
 * 
 * 功能：
 * - 用户认证和授权
 * - JWT Token管理
 * - API Key验证
 * - 用户信息管理
 * - 权限控制
 */
@SpringBootApplication(
    scanBasePackages = {
        "com.nexus.auth",      // 当前服务包
        "com.nexus.common"     // 公共组件包
    },
    exclude = {
        org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration.class
    }
)
@EnableDiscoveryClient
@EnableCaching
@EnableTransactionManagement
@EntityScan(basePackages = {
    "com.nexus.common.entity",  // 公共实体类
    "com.nexus.auth.entity"     // 认证服务实体类
})
@EnableJpaRepositories(basePackages = {
    "com.nexus.auth.repository" // 认证服务Repository
})
public class NexusAuthServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(NexusAuthServiceApplication.class, args);
    }
}
