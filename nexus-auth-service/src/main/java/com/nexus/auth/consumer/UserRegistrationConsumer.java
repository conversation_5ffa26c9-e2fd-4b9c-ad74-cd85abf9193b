package com.nexus.auth.consumer;

import com.nexus.common.constants.RocketMQConstants;
import com.nexus.common.event.UserRegistrationEvent;
import com.nexus.common.event.EmailNotificationEvent;
import com.nexus.common.event.StatisticsUpdateEvent;
import com.nexus.common.event.AuditLogEvent;
import com.nexus.common.service.RocketMQProducerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户注册事件消费者
 * 处理用户注册后的异步任务：欢迎邮件、统计更新、审计日志
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
    topic = RocketMQConstants.USER_EVENT_TOPIC,
    selectorExpression = RocketMQConstants.USER_REGISTRATION_TAG,
    consumerGroup = RocketMQConstants.USER_EVENT_CONSUMER_GROUP,
    nameServer = "${rocketmq.name-server:localhost:9876}"
)
public class UserRegistrationConsumer implements RocketMQListener<UserRegistrationEvent> {

    private final RocketMQProducerService messageProducerService;

    /**
     * RocketMQ消息监听器实现
     */
    @Override
    public void onMessage(UserRegistrationEvent event) {
        log.info("接收到用户注册事件: userId={}, username={}, email={}",
                event.getUserId(), event.getUsername(), event.getEmail());

        try {
            // 验证事件数据
            if (!event.isValid()) {
                log.warn("用户注册事件数据无效: userId={}", event.getUserId());
                return;
            }

            // 处理用户注册后续任务
            processUserRegistration(event);

            log.info("用户注册事件处理成功: userId={}", event.getUserId());

        } catch (Exception e) {
            log.error("用户注册事件处理失败: userId={} - {}",
                    event.getUserId(), e.getMessage(), e);
            // RocketMQ会自动重试
        }
    }

    /**
     * 处理用户注册后续任务
     */
    private void processUserRegistration(UserRegistrationEvent event) {
        // 1. 发送欢迎邮件事件
        boolean emailSent = sendWelcomeEmailEvent(event);

        // 2. 发送统计更新事件
        boolean statsSent = sendStatisticsUpdateEvent(event);

        // 3. 发送审计日志事件
        boolean auditSent = sendAuditLogEvent(event);

        // 检查所有事件是否发送成功
        if (emailSent && statsSent && auditSent) {
            // 标记用户初始化完成
            markUserInitializationCompleted(event.getUserId());
        } else {
            String errorMsg = String.format("部分事件发送失败: email=%s, stats=%s, audit=%s",
                    emailSent, statsSent, auditSent);
            log.warn("用户注册事件处理部分失败: userId={}, error={}", event.getUserId(), errorMsg);
            // 标记用户初始化失败
            markUserInitializationFailed(event.getUserId(), errorMsg);
            throw new RuntimeException(errorMsg); // 抛出异常触发重试
        }
    }

    /**
     * 标记用户初始化完成
     */
    private void markUserInitializationCompleted(String userId) {
        try {
            // 这里应该调用UserInitializationService
            // 为了简化，我们使用Redis直接操作
            log.info("标记用户初始化完成: userId={}", userId);
            // userInitializationService.markInitializationCompleted(userId);
        } catch (Exception e) {
            log.error("标记用户初始化完成失败: userId={} - {}", userId, e.getMessage(), e);
        }
    }

    /**
     * 标记用户初始化失败
     */
    private void markUserInitializationFailed(String userId, String reason) {
        try {
            // 这里应该调用UserInitializationService
            // 为了简化，我们使用Redis直接操作
            log.error("标记用户初始化失败: userId={}, reason={}", userId, reason);
            // userInitializationService.markInitializationFailed(userId, reason);
        } catch (Exception e) {
            log.error("标记用户初始化失败操作失败: userId={} - {}", userId, e.getMessage(), e);
        }
    }
    
    /**
     * 发送欢迎邮件事件
     */
    private boolean sendWelcomeEmailEvent(UserRegistrationEvent event) {
        try {
            // 准备邮件模板参数
            Map<String, Object> templateParams = new HashMap<>();
            templateParams.put("username", event.getUsername());
            templateParams.put("email", event.getEmail());
            templateParams.put("apiKey", event.getApiKey());
            templateParams.put("registrationTime", event.getTimestamp());
            
            // 创建邮件通知事件
            EmailNotificationEvent emailEvent = new EmailNotificationEvent(
                    event.getUserId(),
                    event.getEmail(),
                    event.getUsername(),
                    "欢迎加入Nexus微服务平台！",
                    "welcome_email", // 模板名称
                    templateParams,
                    EmailNotificationEvent.EmailType.WELCOME
            );
            
            // 设置来源服务
            emailEvent.setSourceService("nexus-auth-service");
            
            boolean sent = messageProducerService.sendEvent(emailEvent);
            if (sent) {
                log.debug("欢迎邮件事件发送成功: userId={}", event.getUserId());
            } else {
                log.warn("欢迎邮件事件发送失败: userId={}", event.getUserId());
            }
            
            return sent;
            
        } catch (Exception e) {
            log.error("发送欢迎邮件事件异常: userId={} - {}", event.getUserId(), e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 发送统计更新事件
     */
    private boolean sendStatisticsUpdateEvent(UserRegistrationEvent event) {
        try {
            // 准备统计指标
            Map<String, Object> metrics = new HashMap<>();
            metrics.put("userId", event.getUserId());
            metrics.put("username", event.getUsername());
            metrics.put("email", event.getEmail());
            metrics.put("role", event.getRole());
            metrics.put("registrationIp", event.getRegistrationIp());
            metrics.put("registrationTime", event.getTimestamp());
            
            // 准备增量数据
            Map<String, Number> increments = new HashMap<>();
            increments.put("totalUsers", 1);
            increments.put("dailyRegistrations", 1);
            increments.put("monthlyRegistrations", 1);
            
            // 按角色统计
            String roleKey = "registrations_" + event.getRole().toLowerCase();
            increments.put(roleKey, 1);
            
            // 创建统计更新事件
            StatisticsUpdateEvent statsEvent = new StatisticsUpdateEvent(
                    event.getUserId(),
                    StatisticsUpdateEvent.StatisticsType.USER_REGISTRATION,
                    "global", // 全局维度
                    metrics,
                    increments,
                    "daily" // 时间窗口
            );
            
            // 设置来源服务
            statsEvent.setSourceService("nexus-auth-service");
            
            boolean sent = messageProducerService.sendEvent(statsEvent);
            if (sent) {
                log.debug("统计更新事件发送成功: userId={}", event.getUserId());
            } else {
                log.warn("统计更新事件发送失败: userId={}", event.getUserId());
            }
            
            return sent;
            
        } catch (Exception e) {
            log.error("发送统计更新事件异常: userId={} - {}", event.getUserId(), e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 发送审计日志事件
     */
    private boolean sendAuditLogEvent(UserRegistrationEvent event) {
        try {
            // 准备审计属性
            Map<String, Object> attributes = new HashMap<>();
            attributes.put("username", event.getUsername());
            attributes.put("email", event.getEmail());
            attributes.put("role", event.getRole());
            attributes.put("apiKeyGenerated", event.getApiKey() != null);
            
            // 创建审计日志事件
            AuditLogEvent auditEvent = new AuditLogEvent(
                    event.getUserId(),
                    "USER_REGISTRATION", // 操作类型
                    "USER", // 资源类型
                    event.getUserId(), // 资源ID
                    "SUCCESS", // 操作结果
                    String.format("用户注册成功: %s (%s)", event.getUsername(), event.getEmail()),
                    event.getRegistrationIp(),
                    null, // userAgent
                    event.getEventId() // requestId
            );
            
            auditEvent.setAttributes(attributes);
            auditEvent.setSourceService("nexus-auth-service");
            
            boolean sent = messageProducerService.sendEvent(auditEvent);
            if (sent) {
                log.debug("审计日志事件发送成功: userId={}", event.getUserId());
            } else {
                log.warn("审计日志事件发送失败: userId={}", event.getUserId());
            }
            
            return sent;
            
        } catch (Exception e) {
            log.error("发送审计日志事件异常: userId={} - {}", event.getUserId(), e.getMessage(), e);
            return false;
        }
    }
}
