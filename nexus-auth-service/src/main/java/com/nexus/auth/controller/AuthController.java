package com.nexus.auth.controller;

import com.nexus.auth.service.AuthService;
import com.nexus.auth.service.RateLimitService;
import com.nexus.common.dto.UserDTO;
import com.nexus.common.exception.NexusException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 认证控制器
 * 提供用户认证相关的REST API
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/auth")
@RequiredArgsConstructor
@Validated
@Tag(name = "认证管理", description = "用户认证和授权相关接口")
public class AuthController {

    private final AuthService authService;
    private final RateLimitService rateLimitService;

    /**
     * 用户登录
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "使用用户名/邮箱和密码进行登录")
    public ResponseEntity<Map<String, Object>> login(
            @Valid @RequestBody UserDTO.LoginDTO loginDTO,
            HttpServletRequest request) {
        
        String clientIp = getClientIp(request);
        log.info("用户登录请求: {} from {}", loginDTO.getUsernameOrEmail(), clientIp);
        
        try {
            Map<String, Object> result = authService.login(loginDTO, clientIp);
            return ResponseEntity.ok(buildSuccessResponse("登录成功", result));
        } catch (Exception e) {
            log.error("用户登录失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    @Operation(summary = "用户注册", description = "创建新用户账户")
    public ResponseEntity<Map<String, Object>> register(
            @Valid @RequestBody UserDTO.CreateUserDTO createUserDTO,
            HttpServletRequest request) {
        
        String clientIp = getClientIp(request);
        log.info("用户注册请求: {} from {}", createUserDTO.getUsername(), clientIp);
        
        try {
            UserDTO user = authService.register(createUserDTO, clientIp);
            return ResponseEntity.ok(buildSuccessResponse("注册成功", user));
        } catch (Exception e) {
            log.error("用户注册失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 刷新令牌
     */
    @PostMapping("/refresh")
    @Operation(summary = "刷新令牌", description = "使用刷新令牌获取新的访问令牌")
    public ResponseEntity<Map<String, Object>> refreshToken(
            @Parameter(description = "刷新令牌") @RequestParam String refreshToken) {
        
        log.debug("令牌刷新请求");
        
        try {
            Map<String, Object> result = authService.refreshToken(refreshToken);
            return ResponseEntity.ok(buildSuccessResponse("令牌刷新成功", result));
        } catch (Exception e) {
            log.error("令牌刷新失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 验证API密钥
     */
    @PostMapping("/validate/api-key")
    @Operation(summary = "验证API密钥", description = "验证API密钥的有效性")
    public ResponseEntity<Map<String, Object>> validateApiKey(
            @Parameter(description = "API密钥") @RequestParam String apiKey) {
        
        log.debug("API密钥验证请求");
        
        try {
            UserDTO user = authService.validateApiKey(apiKey);
            return ResponseEntity.ok(buildSuccessResponse("API密钥验证成功", user));
        } catch (Exception e) {
            log.error("API密钥验证失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 验证JWT令牌
     */
    @PostMapping("/validate/token")
    @Operation(summary = "验证JWT令牌", description = "验证JWT令牌的有效性")
    public ResponseEntity<Map<String, Object>> validateToken(
            @Parameter(description = "JWT令牌") @RequestParam String token) {
        
        log.debug("JWT令牌验证请求");
        
        try {
            UserDTO user = authService.validateToken(token);
            return ResponseEntity.ok(buildSuccessResponse("令牌验证成功", user));
        } catch (Exception e) {
            log.error("JWT令牌验证失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 生成新的API密钥
     */
    @PostMapping("/api-key/generate")
    @Operation(summary = "生成API密钥", description = "为指定用户生成新的API密钥")
    public ResponseEntity<Map<String, Object>> generateApiKey(
            @Parameter(description = "用户ID") @RequestParam Long userId) {
        
        log.info("生成API密钥请求: 用户ID {}", userId);
        
        try {
            String apiKey = authService.generateNewApiKey(userId);
            Map<String, Object> result = new HashMap<>();
            result.put("apiKey", apiKey);
            return ResponseEntity.ok(buildSuccessResponse("API密钥生成成功", result));
        } catch (Exception e) {
            log.error("API密钥生成失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 修改密码
     */
    @PostMapping("/password/change")
    @Operation(summary = "修改密码", description = "修改用户密码")
    public ResponseEntity<Map<String, Object>> changePassword(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @Valid @RequestBody UserDTO.ChangePasswordDTO changePasswordDTO) {
        
        log.info("修改密码请求: 用户ID {}", userId);
        
        try {
            authService.changePassword(userId, changePasswordDTO);
            return ResponseEntity.ok(buildSuccessResponse("密码修改成功", null));
        } catch (Exception e) {
            log.error("密码修改失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 获取用户信息
     */
    @GetMapping("/user/{userId}")
    @Operation(summary = "获取用户信息", description = "根据用户ID获取用户详细信息")
    public ResponseEntity<Map<String, Object>> getUserInfo(
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        
        log.debug("获取用户信息请求: 用户ID {}", userId);
        
        try {
            UserDTO user = authService.getUserInfo(userId);
            return ResponseEntity.ok(buildSuccessResponse("获取用户信息成功", user));
        } catch (Exception e) {
            log.error("获取用户信息失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 获取限流信息
     */
    @GetMapping("/rate-limit/{action}")
    @Operation(summary = "获取限流信息", description = "获取指定动作的限流状态")
    public ResponseEntity<Map<String, Object>> getRateLimitInfo(
            @Parameter(description = "动作类型") @PathVariable String action,
            @Parameter(description = "标识符") @RequestParam String identifier) {
        
        log.debug("获取限流信息请求: action={}, identifier={}", action, identifier);
        
        try {
            RateLimitService.RateLimitInfo info = rateLimitService.getRateLimitInfo(action, identifier);
            return ResponseEntity.ok(buildSuccessResponse("获取限流信息成功", info));
        } catch (Exception e) {
            log.error("获取限流信息失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "检查认证服务的健康状态")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "nexus-auth-service");
        health.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(health);
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    /**
     * 构建成功响应
     */
    private Map<String, Object> buildSuccessResponse(String message, Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", message);
        response.put("timestamp", System.currentTimeMillis());
        if (data != null) {
            response.put("data", data);
        }
        return response;
    }
}
