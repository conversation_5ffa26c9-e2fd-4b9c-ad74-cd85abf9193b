package com.nexus.auth.exception;

import com.nexus.common.exception.NexusException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 * 统一处理认证服务中的异常
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理认证异常
     */
    @ExceptionHandler(NexusException.AuthenticationException.class)
    public ResponseEntity<Map<String, Object>> handleAuthenticationException(NexusException.AuthenticationException e) {
        log.warn("认证异常: {}", e.getMessage());
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(buildErrorResponse("认证失败", e.getMessage(), HttpStatus.UNAUTHORIZED.value()));
    }

    /**
     * 处理授权异常
     */
    @ExceptionHandler(NexusException.AuthorizationException.class)
    public ResponseEntity<Map<String, Object>> handleAuthorizationException(NexusException.AuthorizationException e) {
        log.warn("授权异常: {}", e.getMessage());
        return ResponseEntity.status(HttpStatus.FORBIDDEN)
                .body(buildErrorResponse("授权失败", e.getMessage(), HttpStatus.FORBIDDEN.value()));
    }

    /**
     * 处理资源未找到异常
     */
    @ExceptionHandler(NexusException.ResourceNotFoundException.class)
    public ResponseEntity<Map<String, Object>> handleResourceNotFoundException(NexusException.ResourceNotFoundException e) {
        log.warn("资源未找到: {}", e.getMessage());
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(buildErrorResponse("资源未找到", e.getMessage(), HttpStatus.NOT_FOUND.value()));
    }

    /**
     * 处理业务异常
     */
    @ExceptionHandler(NexusException.BusinessException.class)
    public ResponseEntity<Map<String, Object>> handleBusinessException(NexusException.BusinessException e) {
        log.warn("业务异常: {}", e.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(buildErrorResponse("业务处理失败", e.getMessage(), HttpStatus.BAD_REQUEST.value()));
    }

    /**
     * 处理参数验证异常
     */
    @ExceptionHandler(NexusException.ValidationException.class)
    public ResponseEntity<Map<String, Object>> handleValidationException(NexusException.ValidationException e) {
        log.warn("参数验证异常: {}", e.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(buildErrorResponse("参数验证失败", e.getMessage(), HttpStatus.BAD_REQUEST.value()));
    }

    /**
     * 处理限流异常
     */
    @ExceptionHandler(NexusException.RateLimitException.class)
    public ResponseEntity<Map<String, Object>> handleRateLimitException(NexusException.RateLimitException e) {
        log.warn("限流异常: {}", e.getMessage());
        return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS)
                .body(buildErrorResponse("请求过于频繁", e.getMessage(), HttpStatus.TOO_MANY_REQUESTS.value()));
    }

    /**
     * 处理方法参数验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<Map<String, Object>> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        log.warn("方法参数验证异常: {}", e.getMessage());
        
        Map<String, String> errors = new HashMap<>();
        e.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });
        
        Map<String, Object> response = buildErrorResponse("参数验证失败", "请求参数不符合要求", HttpStatus.BAD_REQUEST.value());
        response.put("fieldErrors", errors);
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    /**
     * 处理绑定异常
     */
    @ExceptionHandler(BindException.class)
    public ResponseEntity<Map<String, Object>> handleBindException(BindException e) {
        log.warn("绑定异常: {}", e.getMessage());
        
        Map<String, String> errors = new HashMap<>();
        e.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });
        
        Map<String, Object> response = buildErrorResponse("参数绑定失败", "请求参数格式错误", HttpStatus.BAD_REQUEST.value());
        response.put("fieldErrors", errors);
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    /**
     * 处理约束违反异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<Map<String, Object>> handleConstraintViolationException(ConstraintViolationException e) {
        log.warn("约束违反异常: {}", e.getMessage());
        
        String errors = e.getConstraintViolations().stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining(", "));
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(buildErrorResponse("约束验证失败", errors, HttpStatus.BAD_REQUEST.value()));
    }

    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<Map<String, Object>> handleIllegalArgumentException(IllegalArgumentException e) {
        log.warn("非法参数异常: {}", e.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(buildErrorResponse("参数错误", e.getMessage(), HttpStatus.BAD_REQUEST.value()));
    }

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<Map<String, Object>> handleRuntimeException(RuntimeException e) {
        log.error("运行时异常: ", e);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(buildErrorResponse("系统错误", "服务暂时不可用，请稍后重试", HttpStatus.INTERNAL_SERVER_ERROR.value()));
    }

    /**
     * 处理通用异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, Object>> handleException(Exception e) {
        log.error("未知异常: ", e);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(buildErrorResponse("系统错误", "服务暂时不可用，请稍后重试", HttpStatus.INTERNAL_SERVER_ERROR.value()));
    }

    /**
     * 构建错误响应
     */
    private Map<String, Object> buildErrorResponse(String error, String message, int code) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("error", error);
        response.put("message", message);
        response.put("code", code);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }
}
