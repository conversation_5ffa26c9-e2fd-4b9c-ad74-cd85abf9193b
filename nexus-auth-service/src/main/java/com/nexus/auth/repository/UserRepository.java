package com.nexus.auth.repository;

import com.nexus.common.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户数据访问层
 * 提供用户相关的数据库操作
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    /**
     * 根据用户名查找用户
     */
    Optional<User> findByUsername(String username);

    /**
     * 根据邮箱查找用户
     */
    Optional<User> findByEmail(String email);

    /**
     * 根据用户名或邮箱查找用户
     */
    @Query("SELECT u FROM User u WHERE u.username = :usernameOrEmail OR u.email = :usernameOrEmail")
    Optional<User> findByUsernameOrEmail(@Param("usernameOrEmail") String usernameOrEmail);

    /**
     * 根据API密钥查找用户
     */
    Optional<User> findByApiKey(String apiKey);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 检查API密钥是否存在
     */
    boolean existsByApiKey(String apiKey);

    /**
     * 根据状态查找用户列表
     */
    List<User> findByStatus(User.UserStatus status);

    /**
     * 根据角色查找用户列表
     */
    List<User> findByRole(User.UserRole role);

    /**
     * 查找激活的用户列表
     */
    @Query("SELECT u FROM User u WHERE u.status = 'ACTIVE'")
    List<User> findActiveUsers();

    /**
     * 查找最近登录的用户列表
     */
    @Query("SELECT u FROM User u WHERE u.lastLoginAt >= :since ORDER BY u.lastLoginAt DESC")
    List<User> findRecentlyLoggedInUsers(@Param("since") LocalDateTime since);

    /**
     * 更新用户最后登录信息
     */
    @Modifying
    @Query("UPDATE User u SET u.lastLoginAt = :loginTime, u.lastLoginIp = :ip WHERE u.id = :userId")
    void updateLastLogin(@Param("userId") Long userId, 
                        @Param("loginTime") LocalDateTime loginTime, 
                        @Param("ip") String ip);

    /**
     * 增加用户API调用次数
     */
    @Modifying
    @Query("UPDATE User u SET u.apiCallCount = u.apiCallCount + 1 WHERE u.id = :userId")
    void incrementApiCallCount(@Param("userId") Long userId);

    /**
     * 更新用户状态
     */
    @Modifying
    @Query("UPDATE User u SET u.status = :status WHERE u.id = :userId")
    void updateUserStatus(@Param("userId") Long userId, @Param("status") User.UserStatus status);

    /**
     * 更新用户API密钥
     */
    @Modifying
    @Query("UPDATE User u SET u.apiKey = :apiKey WHERE u.id = :userId")
    void updateApiKey(@Param("userId") Long userId, @Param("apiKey") String apiKey);

    /**
     * 更新用户密码
     */
    @Modifying
    @Query("UPDATE User u SET u.passwordHash = :passwordHash WHERE u.id = :userId")
    void updatePassword(@Param("userId") Long userId, @Param("passwordHash") String passwordHash);

    /**
     * 查找API调用次数最多的用户
     */
    @Query("SELECT u FROM User u ORDER BY u.apiCallCount DESC")
    List<User> findTopApiUsers();

    /**
     * 统计用户总数
     */
    @Query("SELECT COUNT(u) FROM User u")
    long countTotalUsers();

    /**
     * 统计激活用户数
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.status = 'ACTIVE'")
    long countActiveUsers();

    /**
     * 统计指定时间段内注册的用户数
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.createdAt >= :startTime AND u.createdAt <= :endTime")
    long countUsersByRegistrationPeriod(@Param("startTime") LocalDateTime startTime, 
                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 查找长时间未登录的用户
     */
    @Query("SELECT u FROM User u WHERE u.lastLoginAt < :threshold OR u.lastLoginAt IS NULL")
    List<User> findInactiveUsers(@Param("threshold") LocalDateTime threshold);

    /**
     * 根据用户名模糊查询
     */
    @Query("SELECT u FROM User u WHERE u.username LIKE %:keyword% OR u.email LIKE %:keyword%")
    List<User> searchUsers(@Param("keyword") String keyword);

    /**
     * 查找需要清理的用户（已删除状态超过指定天数）
     */
    @Query("SELECT u FROM User u WHERE u.status = 'DELETED' AND u.updatedAt < :threshold")
    List<User> findUsersToCleanup(@Param("threshold") LocalDateTime threshold);
}
