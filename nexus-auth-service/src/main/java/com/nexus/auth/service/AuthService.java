package com.nexus.auth.service;

import com.nexus.auth.repository.UserRepository;
import com.nexus.common.dto.UserDTO;
import com.nexus.common.entity.User;
import com.nexus.common.event.UserRegistrationEvent;
import com.nexus.common.exception.NexusException;
import com.nexus.common.service.RocketMQProducerService;
import com.nexus.common.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * 认证服务
 * 提供用户认证、注册、登录等核心功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthService {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtil jwtUtil;
    private final RateLimitService rateLimitService;
    private final RocketMQProducerService messageProducerService;

    /**
     * 用户登录
     */
    @Transactional
    public Map<String, Object> login(UserDTO.LoginDTO loginDTO, String clientIp) {
        log.info("用户登录尝试: {}", loginDTO.getUsernameOrEmail());

        // 检查登录限流
        if (!rateLimitService.tryAcquire("login", clientIp)) {
            throw new NexusException.RateLimitException("登录尝试过于频繁，请稍后再试");
        }

        // 查找用户
        User user = userRepository.findByUsernameOrEmail(loginDTO.getUsernameOrEmail())
                .orElseThrow(() -> new NexusException.AuthenticationException("用户名或密码错误"));

        // 检查用户状态
        if (!user.isActive()) {
            throw new NexusException.AuthenticationException("账户已被禁用或未激活");
        }

        // 验证密码
        if (!passwordEncoder.matches(loginDTO.getPassword(), user.getPasswordHash())) {
            throw new NexusException.AuthenticationException("用户名或密码错误");
        }

        // 更新最后登录信息
        user.updateLastLogin(clientIp);
        userRepository.save(user);

        // 生成JWT令牌
        String accessToken = jwtUtil.generateAccessToken(
                user.getId().toString(),
                user.getUsername(),
                user.getRole().name()
        );
        String refreshToken = jwtUtil.generateRefreshToken(user.getId().toString());

        // 构建响应
        Map<String, Object> response = new HashMap<>();
        response.put("accessToken", accessToken);
        response.put("refreshToken", refreshToken);
        response.put("tokenType", "Bearer");
        response.put("expiresIn", 86400); // 24小时
        response.put("user", UserDTO.fromEntity(user));

        log.info("用户登录成功: {} (ID: {})", user.getUsername(), user.getId());
        return response;
    }

    /**
     * 用户注册
     */
    @Transactional
    public UserDTO register(UserDTO.CreateUserDTO createUserDTO, String clientIp) {
        log.info("用户注册尝试: {}", createUserDTO.getUsername());

        // 检查注册限流
        if (!rateLimitService.tryAcquire("register", clientIp)) {
            throw new NexusException.RateLimitException("注册尝试过于频繁，请稍后再试");
        }

        // 检查用户名是否已存在
        if (userRepository.existsByUsername(createUserDTO.getUsername())) {
            throw new NexusException.ValidationException("用户名已存在");
        }

        // 检查邮箱是否已存在
        if (userRepository.existsByEmail(createUserDTO.getEmail())) {
            throw new NexusException.ValidationException("邮箱已被注册");
        }

        // 创建用户
        User user = User.builder()
                .username(createUserDTO.getUsername())
                .email(createUserDTO.getEmail())
                .passwordHash(passwordEncoder.encode(createUserDTO.getPassword()))
                .role(createUserDTO.getRole())
                .status(User.UserStatus.ACTIVE)
                .apiCallCount(0L)
                .build();

        // 生成API密钥
        String apiKey = generateApiKey(user);
        user.setApiKey(apiKey);

        user = userRepository.save(user);

        log.info("用户注册成功: {} (ID: {})", user.getUsername(), user.getId());

        // 发送用户注册事件（异步处理）
        sendUserRegistrationEvent(user, clientIp);

        return UserDTO.fromEntity(user);
    }

    /**
     * 发送用户注册事件
     */
    private void sendUserRegistrationEvent(User user, String clientIp) {
        try {
            UserRegistrationEvent event = new UserRegistrationEvent(
                    user.getId().toString(),
                    user.getUsername(),
                    user.getEmail(),
                    user.getRole().name(),
                    clientIp,
                    user.getApiKey()
            );

            // 设置来源服务
            event.setSourceService("nexus-auth-service");

            // 异步发送事件
            boolean sent = messageProducerService.sendEventAsync(event);

            if (sent) {
                log.debug("用户注册事件发送成功: userId={}", user.getId());
            } else {
                log.warn("用户注册事件发送失败: userId={}", user.getId());
                // 注意：这里不抛异常，避免影响用户注册的主流程
                // 可以考虑记录到失败队列或者发送告警
            }

        } catch (Exception e) {
            log.error("发送用户注册事件异常: userId={} - {}", user.getId(), e.getMessage(), e);
            // 同样不抛异常，保证主流程不受影响
        }
    }

    /**
     * 刷新令牌
     */
    public Map<String, Object> refreshToken(String refreshToken) {
        log.debug("刷新令牌请求");

        // 验证刷新令牌
        if (!jwtUtil.validateRefreshToken(refreshToken)) {
            throw new NexusException.AuthenticationException("刷新令牌无效或已过期");
        }

        // 提取用户信息
        String userId = jwtUtil.extractUserId(refreshToken);
        User user = userRepository.findById(Long.valueOf(userId))
                .orElseThrow(() -> new NexusException.ResourceNotFoundException("用户不存在"));

        // 检查用户状态
        if (!user.isActive()) {
            throw new NexusException.AuthenticationException("账户已被禁用");
        }

        // 生成新的访问令牌
        String newAccessToken = jwtUtil.generateAccessToken(
                user.getId().toString(),
                user.getUsername(),
                user.getRole().name()
        );

        // 构建响应
        Map<String, Object> response = new HashMap<>();
        response.put("accessToken", newAccessToken);
        response.put("tokenType", "Bearer");
        response.put("expiresIn", 86400); // 24小时

        log.debug("令牌刷新成功: 用户ID {}", userId);
        return response;
    }

    /**
     * 验证API密钥
     */
    @Cacheable(value = "user_auth", key = "#apiKey")
    public UserDTO validateApiKey(String apiKey) {
        log.debug("验证API密钥");

        User user = userRepository.findByApiKey(apiKey)
                .orElseThrow(() -> new NexusException.AuthenticationException("API密钥无效"));

        // 检查用户状态
        if (!user.isActive()) {
            throw new NexusException.AuthenticationException("账户已被禁用");
        }

        // 增加API调用次数
        user.incrementApiCallCount();
        userRepository.save(user);

        return UserDTO.fromEntity(user);
    }

    /**
     * 验证JWT令牌
     */
    @Cacheable(value = "user_auth", key = "#token")
    public UserDTO validateToken(String token) {
        log.debug("验证JWT令牌");

        // 验证令牌
        if (!jwtUtil.validateAccessToken(token)) {
            throw new NexusException.AuthenticationException("令牌无效或已过期");
        }

        // 提取用户信息
        String userId = jwtUtil.extractUserId(token);
        User user = userRepository.findById(Long.valueOf(userId))
                .orElseThrow(() -> new NexusException.ResourceNotFoundException("用户不存在"));

        // 检查用户状态
        if (!user.isActive()) {
            throw new NexusException.AuthenticationException("账户已被禁用");
        }

        return UserDTO.fromEntity(user);
    }

    /**
     * 生成新的API密钥
     */
    @Transactional
    @CacheEvict(value = "user_auth", allEntries = true)
    public String generateNewApiKey(Long userId) {
        log.info("为用户生成新的API密钥: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new NexusException.ResourceNotFoundException("用户不存在"));

        String newApiKey = generateApiKey(user);
        user.setApiKey(newApiKey);
        userRepository.save(user);

        log.info("API密钥生成成功: 用户ID {}", userId);
        return newApiKey;
    }

    /**
     * 修改密码
     */
    @Transactional
    @CacheEvict(value = "user_auth", allEntries = true)
    public void changePassword(Long userId, UserDTO.ChangePasswordDTO changePasswordDTO) {
        log.info("用户修改密码: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new NexusException.ResourceNotFoundException("用户不存在"));

        // 验证当前密码
        if (!passwordEncoder.matches(changePasswordDTO.getCurrentPassword(), user.getPasswordHash())) {
            throw new NexusException.ValidationException("当前密码错误");
        }

        // 更新密码
        user.setPasswordHash(passwordEncoder.encode(changePasswordDTO.getNewPassword()));
        userRepository.save(user);

        log.info("密码修改成功: 用户ID {}", userId);
    }

    /**
     * 生成API密钥
     */
    private String generateApiKey(User user) {
        // 使用JWT生成长期有效的API密钥
        return jwtUtil.generateApiKeyToken(
                user.getId().toString(),
                user.getUsername(),
                user.getRole().name(),
                365 // 1年有效期
        );
    }

    /**
     * 获取用户信息
     */
    @Cacheable(value = "users", key = "#userId")
    public UserDTO getUserInfo(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new NexusException.ResourceNotFoundException("用户不存在"));
        return UserDTO.fromEntity(user);
    }
}
