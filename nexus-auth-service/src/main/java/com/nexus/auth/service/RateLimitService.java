package com.nexus.auth.service;

import io.github.bucket4j.Bandwidth;
import io.github.bucket4j.Bucket;
import io.github.bucket4j.Bucket4j;
import io.github.bucket4j.Refill;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * 限流服务
 * 基于Redis和Bucket4j实现分布式限流
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RateLimitService {

    private final RedissonClient redissonClient;

    @Value("${nexus.security.rate-limit.login.capacity:10}")
    private int loginCapacity;

    @Value("${nexus.security.rate-limit.login.refill-period:3600}")
    private int loginRefillPeriod;

    @Value("${nexus.security.rate-limit.register.capacity:5}")
    private int registerCapacity;

    @Value("${nexus.security.rate-limit.register.refill-period:3600}")
    private int registerRefillPeriod;

    @Value("${nexus.security.rate-limit.api.capacity:1000}")
    private int apiCapacity;

    @Value("${nexus.security.rate-limit.api.refill-period:3600}")
    private int apiRefillPeriod;

    /**
     * 尝试获取令牌
     */
    public boolean tryAcquire(String action, String identifier) {
        return tryAcquire(action, identifier, 1);
    }

    /**
     * 尝试获取指定数量的令牌
     */
    public boolean tryAcquire(String action, String identifier, int tokens) {
        String key = buildKey(action, identifier);
        Bucket bucket = getBucket(key, action);
        
        boolean acquired = bucket.tryConsume(tokens);
        
        if (!acquired) {
            log.warn("限流触发: action={}, identifier={}, tokens={}", action, identifier, tokens);
        } else {
            log.debug("令牌获取成功: action={}, identifier={}, tokens={}, remaining={}", 
                     action, identifier, tokens, bucket.getAvailableTokens());
        }
        
        return acquired;
    }

    /**
     * 获取剩余令牌数
     */
    public long getAvailableTokens(String action, String identifier) {
        String key = buildKey(action, identifier);
        Bucket bucket = getBucket(key, action);
        return bucket.getAvailableTokens();
    }

    /**
     * 检查是否被限流
     */
    public boolean isRateLimited(String action, String identifier) {
        return getAvailableTokens(action, identifier) <= 0;
    }

    /**
     * 重置限流桶
     */
    public void resetBucket(String action, String identifier) {
        String key = buildKey(action, identifier);
        RMapCache<String, Bucket> cache = redissonClient.getMapCache("rateLimitBuckets");
        cache.remove(key);
        log.info("限流桶已重置: action={}, identifier={}", action, identifier);
    }

    /**
     * 获取或创建限流桶
     */
    private Bucket getBucket(String key, String action) {
        RMapCache<String, Bucket> cache = redissonClient.getMapCache("rateLimitBuckets");
        
        return cache.computeIfAbsent(key, k -> {
            Bandwidth bandwidth = createBandwidth(action);
            Bucket bucket = Bucket4j.builder()
                    .addLimit(bandwidth)
                    .build();
            
            // 设置缓存过期时间为限流周期的2倍
            int ttl = getBandwidthRefillPeriod(action) * 2;
            cache.put(k, bucket, ttl, TimeUnit.SECONDS);
            
            log.debug("创建新的限流桶: key={}, action={}", k, action);
            return bucket;
        });
    }

    /**
     * 根据动作类型创建带宽限制
     */
    private Bandwidth createBandwidth(String action) {
        switch (action.toLowerCase()) {
            case "login":
                return Bandwidth.classic(loginCapacity, 
                        Refill.intervally(loginCapacity, Duration.ofSeconds(loginRefillPeriod)));
            
            case "register":
                return Bandwidth.classic(registerCapacity, 
                        Refill.intervally(registerCapacity, Duration.ofSeconds(registerRefillPeriod)));
            
            case "api":
                return Bandwidth.classic(apiCapacity, 
                        Refill.intervally(apiCapacity, Duration.ofSeconds(apiRefillPeriod)));
            
            default:
                // 默认限流策略：100次/小时
                return Bandwidth.classic(100, 
                        Refill.intervally(100, Duration.ofHours(1)));
        }
    }

    /**
     * 获取带宽刷新周期
     */
    private int getBandwidthRefillPeriod(String action) {
        switch (action.toLowerCase()) {
            case "login":
                return loginRefillPeriod;
            case "register":
                return registerRefillPeriod;
            case "api":
                return apiRefillPeriod;
            default:
                return 3600; // 1小时
        }
    }

    /**
     * 构建缓存键
     */
    private String buildKey(String action, String identifier) {
        return String.format("rate_limit:%s:%s", action, identifier);
    }

    /**
     * 获取限流统计信息
     */
    public RateLimitInfo getRateLimitInfo(String action, String identifier) {
        String key = buildKey(action, identifier);
        Bucket bucket = getBucket(key, action);
        
        return RateLimitInfo.builder()
                .action(action)
                .identifier(identifier)
                .availableTokens(bucket.getAvailableTokens())
                .capacity(getBandwidthCapacity(action))
                .refillPeriod(getBandwidthRefillPeriod(action))
                .isLimited(bucket.getAvailableTokens() <= 0)
                .build();
    }

    /**
     * 获取带宽容量
     */
    private long getBandwidthCapacity(String action) {
        switch (action.toLowerCase()) {
            case "login":
                return loginCapacity;
            case "register":
                return registerCapacity;
            case "api":
                return apiCapacity;
            default:
                return 100;
        }
    }

    /**
     * 限流信息DTO
     */
    @lombok.Data
    @lombok.Builder
    public static class RateLimitInfo {
        private String action;
        private String identifier;
        private long availableTokens;
        private long capacity;
        private int refillPeriod;
        private boolean isLimited;
        
        /**
         * 获取使用百分比
         */
        public double getUsagePercentage() {
            return ((double) (capacity - availableTokens) / capacity) * 100.0;
        }
        
        /**
         * 获取剩余百分比
         */
        public double getRemainingPercentage() {
            return ((double) availableTokens / capacity) * 100.0;
        }
    }
}
