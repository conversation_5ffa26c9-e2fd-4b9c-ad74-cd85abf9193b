package com.nexus.auth.service;

import com.nexus.common.entity.User;
import com.nexus.auth.repository.UserRepository;
import com.nexus.auth.util.ApiKeyUtil;
import com.nexus.common.util.JwtUtil;
import com.nexus.common.constants.CacheNames;
import com.nexus.common.dto.UserDTO;
import com.nexus.common.exception.NexusException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 用户认证服务
 * 负责用户登录、注册、JWT令牌管理等功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserAuthService {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtil jwtUtil;
    private final ApiKeyUtil apiKeyUtil;

    /**
     * 用户登录
     */
    @Transactional
    public AuthResult login(LoginRequest request) {
        log.info("用户登录尝试: {}", request.getUsername());

        try {
            // 查找用户
            Optional<User> userOpt = userRepository.findByUsernameOrEmail(request.getUsername());
            if (!userOpt.isPresent()) {
                return AuthResult.failure("用户名或密码错误");
            }

            User user = userOpt.get();

            // 检查用户状态
            if (!User.UserStatus.ACTIVE.equals(user.getStatus())) {
                return AuthResult.failure("用户账户已被禁用");
            }

            // 验证密码
            if (!passwordEncoder.matches(request.getPassword(), user.getPasswordHash())) {
                return AuthResult.failure("用户名或密码错误");
            }

            // 更新最后登录时间
            user.updateLastLogin("127.0.0.1"); // TODO: 获取真实IP
            userRepository.save(user);

            // 生成JWT令牌
            String accessToken = jwtUtil.generateAccessToken(user.getId().toString(), user.getUsername(), user.getRole().name());
            String refreshToken = jwtUtil.generateRefreshToken(user.getId().toString());

            log.info("用户登录成功: {} (ID: {})", user.getUsername(), user.getId());
            return AuthResult.success(accessToken, refreshToken, user, "登录成功");

        } catch (Exception e) {
            log.error("用户登录失败: {}", e.getMessage(), e);
            return AuthResult.failure("登录失败，请稍后重试");
        }
    }

    /**
     * 用户注册
     */
    @Transactional
    @CacheEvict(value = CacheNames.USERS, allEntries = true)
    public UserDTO register(UserDTO.CreateUserDTO createUserDTO) {
        log.info("用户注册: {}", createUserDTO.getUsername());

        // 检查用户名是否已存在
        if (userRepository.existsByUsername(createUserDTO.getUsername())) {
            throw new NexusException.BusinessException("用户名已存在");
        }

        // 检查邮箱是否已存在
        if (userRepository.existsByEmail(createUserDTO.getEmail())) {
            throw new NexusException.BusinessException("邮箱已存在");
        }

        // 创建用户
        User user = User.builder()
                .username(createUserDTO.getUsername())
                .email(createUserDTO.getEmail())
                .passwordHash(passwordEncoder.encode(createUserDTO.getPassword()))
                .role(createUserDTO.getRole() != null ? createUserDTO.getRole() : User.UserRole.USER)
                .status(User.UserStatus.ACTIVE)
                .build();

        // 生成API密钥
        String apiKey = apiKeyUtil.generateUserApiKey(user.getId());
        user.setApiKey(apiKey);

        user = userRepository.save(user);

        log.info("用户注册成功: {} (ID: {})", user.getUsername(), user.getId());
        return UserDTO.fromEntity(user);
    }

    /**
     * 验证JWT令牌
     */
    @Cacheable(value = CacheNames.JWT_TOKENS, key = "#token")
    public Map<String, Object> validateToken(String token) {
        if (!jwtUtil.validateAccessToken(token)) {
            throw new NexusException.AuthenticationException("无效的令牌");
        }

        String username = jwtUtil.extractUsername(token);
        String userIdStr = jwtUtil.extractUserId(token);
        Long userId = Long.valueOf(userIdStr);
        String role = jwtUtil.extractRole(token);

        if (username == null || userId == null) {
            throw new NexusException.AuthenticationException("令牌信息不完整");
        }

        // 验证用户是否仍然存在且活跃
        Optional<User> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent() || !User.UserStatus.ACTIVE.equals(userOpt.get().getStatus())) {
            throw new NexusException.AuthenticationException("用户不存在或已被禁用");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("valid", true);
        result.put("userId", userId);
        result.put("username", username);
        result.put("role", role);
        result.put("expiresIn", jwtUtil.getTokenRemainingTime(token));

        return result;
    }

    /**
     * 验证API密钥
     */
    @Cacheable(value = CacheNames.API_KEYS, key = "#apiKey")
    public Map<String, Object> validateApiKey(String apiKey) {
        if (!apiKeyUtil.isValidApiKeyFormat(apiKey)) {
            throw new NexusException.AuthenticationException("无效的API密钥格式");
        }

        Optional<User> userOpt = userRepository.findByApiKey(apiKey);
        if (!userOpt.isPresent()) {
            throw new NexusException.AuthenticationException("无效的API密钥");
        }

        User user = userOpt.get();
        if (!User.UserStatus.ACTIVE.equals(user.getStatus())) {
            throw new NexusException.AuthenticationException("用户账户已被禁用");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("valid", true);
        result.put("userId", user.getId());
        result.put("username", user.getUsername());
        result.put("role", user.getRole().name());

        return result;
    }

    /**
     * 刷新JWT令牌
     */
    @CacheEvict(value = CacheNames.JWT_TOKENS, key = "#oldToken")
    public Map<String, Object> refreshToken(String oldToken) {
        if (!jwtUtil.validateAccessToken(oldToken)) {
            throw new NexusException.AuthenticationException("无效的令牌");
        }

        String userIdStr = jwtUtil.extractUserId(oldToken);
        String newToken = jwtUtil.generateRefreshToken(userIdStr);
        
        Map<String, Object> response = new HashMap<>();
        response.put("token", newToken);
        response.put("tokenType", "Bearer");
        response.put("expiresIn", jwtUtil.getTokenRemainingTime(newToken));

        return response;
    }

    /**
     * 用户登出
     */
    @CacheEvict(value = {CacheNames.JWT_TOKENS, CacheNames.API_KEYS}, allEntries = true)
    public void logout(String token) {
        // 这里可以将令牌加入黑名单
        // 目前简单地清除缓存
        log.info("用户登出");
    }

    /**
     * 修改密码
     */
    @Transactional
    @CacheEvict(value = CacheNames.USERS, key = "#userId")
    public void changePassword(Long userId, String oldPassword, String newPassword) {
        log.info("用户修改密码: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new NexusException.ResourceNotFoundException("用户不存在"));

        // 验证旧密码
        if (!passwordEncoder.matches(oldPassword, user.getPasswordHash())) {
            throw new NexusException.BusinessException("原密码错误");
        }

        // 更新密码
        user.setPasswordHash(passwordEncoder.encode(newPassword));
        userRepository.save(user);

        log.info("用户密码修改成功: {}", userId);
    }

    /**
     * 重置API密钥
     */
    @Transactional
    @CacheEvict(value = {CacheNames.USERS, CacheNames.API_KEYS}, allEntries = true)
    public String resetApiKey(Long userId) {
        log.info("重置用户API密钥: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new NexusException.ResourceNotFoundException("用户不存在"));

        String newApiKey = apiKeyUtil.generateUserApiKey(userId);
        userRepository.updateApiKey(userId, newApiKey);

        log.info("用户API密钥重置成功: {}", userId);
        return newApiKey;
    }

    /**
     * 获取用户信息
     */
    @Cacheable(value = CacheNames.USERS, key = "#userId")
    public UserDTO getUserInfo(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new NexusException.ResourceNotFoundException("用户不存在"));
        
        return UserDTO.fromEntity(user);
    }

    // ========== 内部DTO类 ==========

    /**
     * 用户注册请求DTO
     */
    @lombok.Data
    public static class RegisterRequest {
        private String username;
        private String email;
        private String password;
    }

    /**
     * 用户登录请求DTO
     */
    @lombok.Data
    public static class LoginRequest {
        private String username;
        private String password;
    }

    /**
     * 认证结果DTO
     */
    @lombok.Data
    @lombok.Builder
    public static class AuthResult {
        private boolean success;
        private String message;
        private String accessToken;
        private String refreshToken;
        private User user;

        public static AuthResult success(String accessToken, String refreshToken, User user, String message) {
            return AuthResult.builder()
                    .success(true)
                    .message(message)
                    .accessToken(accessToken)
                    .refreshToken(refreshToken)
                    .user(user)
                    .build();
        }

        public static AuthResult failure(String message) {
            return AuthResult.builder()
                    .success(false)
                    .message(message)
                    .build();
        }
    }
}
