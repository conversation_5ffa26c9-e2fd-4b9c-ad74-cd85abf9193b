package com.nexus.auth.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.security.SecureRandom;
import java.util.Base64;

/**
 * API密钥工具类
 * 负责API密钥的生成、验证和管理
 */
@Slf4j
@Component
public class ApiKeyUtil {

    private static final String API_KEY_PREFIX = "nxs_";
    private static final int API_KEY_LENGTH = 32;
    private static final SecureRandom SECURE_RANDOM = new SecureRandom();

    /**
     * 生成用户API密钥
     */
    public String generateUserApiKey(Long userId) {
        byte[] randomBytes = new byte[API_KEY_LENGTH];
        SECURE_RANDOM.nextBytes(randomBytes);
        
        String randomPart = Base64.getUrlEncoder()
                .withoutPadding()
                .encodeToString(randomBytes);
        
        String apiKey = API_KEY_PREFIX + randomPart;
        
        log.debug("为用户 {} 生成API密钥", userId);
        return apiKey;
    }

    /**
     * 验证API密钥格式
     */
    public boolean isValidApiKeyFormat(String apiKey) {
        if (apiKey == null || apiKey.trim().isEmpty()) {
            return false;
        }
        
        return apiKey.startsWith(API_KEY_PREFIX) && 
               apiKey.length() > API_KEY_PREFIX.length();
    }

    /**
     * 提取API密钥前缀
     */
    public String extractPrefix(String apiKey) {
        if (!isValidApiKeyFormat(apiKey)) {
            return null;
        }
        
        return apiKey.substring(0, Math.min(apiKey.length(), 12));
    }

    /**
     * 掩码API密钥（用于日志显示）
     */
    public String maskApiKey(String apiKey) {
        if (!isValidApiKeyFormat(apiKey)) {
            return "invalid";
        }
        
        if (apiKey.length() <= 8) {
            return "***";
        }
        
        return apiKey.substring(0, 8) + "***";
    }
}
