server:
  port: 8081
  servlet:
    context-path: /auth

spring:
  application:
    name: nexus-auth-service
  
  profiles:
    active: local
  
  # 数据库配置
  datasource:
    url: **************************************
    username: nexus
    password: nexus123
    driver-class-name: org.postgresql.Driver
    hikari:
      pool-name: NexusAuthPool
      maximum-pool-size: 10
      minimum-idle: 2
      idle-timeout: 300000
      connection-timeout: 20000
      max-lifetime: 1200000
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
  
  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 1
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 10
        max-idle: 5
        min-idle: 2
        max-wait: 3000ms
  
  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 1800000 # 30分钟

# 认证服务配置
nexus:
  auth:
    # JWT配置
    jwt:
      secret: nexus-auth-jwt-secret-key-2024-local-development
      expiration: 86400 # 24小时
    
    # API密钥配置
    api-key:
      prefix: nxs_
      length: 32
    
    # 安全配置
    security:
      # 密码策略
      password:
        min-length: 6
        require-uppercase: false
        require-lowercase: false
        require-numbers: false
        require-special-chars: false
      
      # 登录限制
      login:
        max-attempts: 5
        lockout-duration: 300000 # 5分钟
    
    # 限流配置
    rate-limit:
      enabled: true
      requests-per-minute: 60
      burst-capacity: 100

# 日志配置
logging:
  level:
    com.nexus.auth: DEBUG
    com.nexus.common: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{50}] - %msg%n"

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
