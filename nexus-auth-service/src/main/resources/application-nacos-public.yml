server:
  port: 8081
  servlet:
    context-path: /auth

spring:
  application:
    name: nexus-auth-service
  
  profiles:
    active: nacos

  # Spring Cloud Nacos配置 - 使用public命名空间
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        # namespace: 不指定，使用public命名空间
        group: DEFAULT_GROUP
        service: ${spring.application.name}
        metadata:
          version: 1.0.0
          zone: default
      config:
        server-addr: 127.0.0.1:8848
        # namespace: 不指定，使用public命名空间
        group: DEFAULT_GROUP
        file-extension: yml
        shared-configs:
          - data-id: nexus-common-config.yml
            group: DEFAULT_GROUP
            refresh: true

# 本地开发时的fallback配置
---
spring:
  profiles: local
  datasource:
    url: **************************************
    username: nexus
    password: nexus123
    driver-class-name: org.postgresql.Driver
  redis:
    host: localhost
    port: 6379
    database: 1
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
