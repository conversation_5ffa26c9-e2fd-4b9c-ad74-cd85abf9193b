server:
  port: 8081
  servlet:
    context-path: /auth

spring:
  application:
    name: nexus-auth-service

  config:
    import: "optional:nacos:nexus-auth-service.yml"

  # Spring Cloud Nacos配置
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        namespace: nexus-microservices
        group: DEFAULT_GROUP
        service: ${spring.application.name}
        metadata:
          version: 1.0.0
          zone: default
        # 启用服务发现
        enabled: true
        # 心跳间隔
        heart-beat-interval: 5000
        # 心跳超时
        heart-beat-timeout: 15000
      config:
        server-addr: 127.0.0.1:8848
        namespace: nexus-microservices
        group: DEFAULT_GROUP
        file-extension: yml
        shared-configs:
          - data-id: nexus-common-config.yml
            group: DEFAULT_GROUP
            refresh: true

# Nexus安全配置
nexus:
  security:
    permit-all-paths: "/api/v1/auth/register,/api/v1/auth/login,/api/v1/auth/health,/actuator/**,/swagger-ui/**,/v3/api-docs/**"

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

# 日志配置
logging:
  level:
    com.nexus.auth: DEBUG
    com.nexus.common: DEBUG
    org.springframework.security: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{50}] - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{50}] - %msg%n"
