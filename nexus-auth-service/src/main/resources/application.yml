server:
  port: 8081
  servlet:
    context-path: /auth

spring:
  application:
    name: nexus-auth-service

  profiles:
    active: nacos

  config:
    import: "nacos:nexus-auth-service.yml"

  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration

  # Spring Cloud Nacos配置
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        namespace: nexus-microservices
        group: DEFAULT_GROUP
        service: ${spring.application.name}
        metadata:
          version: 1.0.0
          zone: default
      config:
        server-addr: 127.0.0.1:8848
        namespace: nexus-microservices
        group: DEFAULT_GROUP
        file-extension: yml
        shared-configs:
          - data-id: nexus-common-config.yml
            group: DEFAULT_GROUP
            refresh: true

# Nacos profile的fallback配置
---
spring:
  profiles: nacos
  datasource:
    url: *****************************************************************************************************************************************************************************
    username: neondb_owner
    password: npg_kc2S7QGCPbEh
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 3
      connection-timeout: 30000
      idle-timeout: 120000
      max-lifetime: 300000

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQL10Dialect
        format_sql: false
  redis:
    host: localhost
    port: 6379
    database: 1

  # RocketMQ配置 - 必须在本地配置以确保消费者能正确初始化
  rocketmq:
    name-server: localhost:9876
    producer:
      group: nexus-auth-producer-group
      name-server: localhost:9876
    consumer:
      name-server: localhost:9876
      group: nexus-auth-consumer-group

# JWT配置 - 使用公共配置
nexus:
  auth:
    jwt:
      secret: ${nexus.auth.jwt.secret:nexus-microservices-jwt-secret-key-2024}
      expiration: ${nexus.auth.jwt.expiration:86400} # 24小时
      refresh-expiration: 604800 # 7天

  # 邮件服务配置
  email:
    enabled: false  # 暂时禁用邮件服务
    mock: true  # 开发环境使用模拟模式
    from: <EMAIL>
    from-name: Nexus微服务平台

  # RocketMQ监听器控制
  rocketmq:
    subscription:
      enabled: true  # 启用订阅事件监听器

  # 安全配置
  security:
    # 不需要认证的路径
    permit-all-paths:
      - /auth/api/v1/auth/login
      - /auth/api/v1/auth/register
      - /auth/api/v1/auth/refresh
      - /auth/actuator/**
      - /auth/swagger-ui/**
      - /auth/v3/api-docs/**

    # 限流配置
    rate-limit:
      login:
        capacity: 10 # 10次/小时
        refill-period: 3600 # 1小时
      register:
        capacity: 5 # 5次/小时
        refill-period: 3600 # 1小时
      api:
        capacity: 1000 # 1000次/小时
        refill-period: 3600 # 1小时

# 日志配置
logging:
  level:
    com.nexus.auth: DEBUG
    com.nexus.common: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{50}] - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{50}] - %msg%n"

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true