# 测试环境配置
spring:
  # 数据库配置 - 使用H2内存数据库
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: false
  
  # H2控制台
  h2:
    console:
      enabled: false
  
  # Redis配置 - 使用嵌入式Redis或禁用
  redis:
    host: localhost
    port: 6379
    timeout: 2000ms
    database: 1
  
  # RabbitMQ配置 - 测试环境
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
    virtual-host: /
    connection-timeout: 15000
    publisher-confirm-type: correlated
    publisher-returns: true
    template:
      mandatory: true
    listener:
      simple:
        acknowledge-mode: manual
        retry:
          enabled: true
          initial-interval: 1000
          max-attempts: 3
          multiplier: 2

# Nexus配置
nexus:
  # 认证配置
  auth:
    jwt:
      secret: test-jwt-secret-key-for-testing-only
      expiration: 3600 # 1小时
      refresh-expiration: 7200 # 2小时
  
  # 邮件配置
  email:
    enabled: true
    mock: true # 测试环境使用模拟邮件
    from: <EMAIL>
    from-name: Nexus测试平台
  
  # 消息队列配置
  messaging:
    enable-confirm: true
    enable-return: true
    default-timeout: 10000
    consumer:
      default-concurrency: 1-2
      high-concurrency: 2-3
      low-concurrency: 1-1
      prefetch-count: 5
      receive-timeout: 5000
      recovery-interval: 2000
  
  # 安全配置
  security:
    permit-all-paths:
      - /auth/api/v1/auth/login
      - /auth/api/v1/auth/register
      - /auth/api/v1/auth/refresh
      - /auth/actuator/**
      - /h2-console/**
    
    # 限流配置 - 测试环境放宽限制
    rate-limit:
      login:
        capacity: 100
        refill-period: 60
      register:
        capacity: 50
        refill-period: 60
      api:
        capacity: 1000
        refill-period: 60

# 日志配置
logging:
  level:
    com.nexus: DEBUG
    org.springframework.amqp: INFO
    org.springframework.mail: DEBUG
    org.thymeleaf: INFO
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level [%X{traceId},%X{spanId}] %logger{36} - %msg%n"

# 管理端点
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
