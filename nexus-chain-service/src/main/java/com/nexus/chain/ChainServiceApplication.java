package com.nexus.chain;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 服务链管理微服务启动类
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@SpringBootApplication(
    scanBasePackages = {"com.nexus.chain", "com.nexus.common"},
    exclude = {
        org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration.class
    }
)
@EnableDiscoveryClient
@EnableCaching
@EnableAsync
@EnableScheduling
public class ChainServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(ChainServiceApplication.class, args);
    }
}
