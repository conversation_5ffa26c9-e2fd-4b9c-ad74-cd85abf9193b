package com.nexus.chain.client;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * MCP服务客户端
 * 用于调用本地和远程MCP服务
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class MCPServiceClient {

    private final RestTemplate restTemplate;

    @Value("${service.mcp-local.url}")
    private String mcpLocalServiceUrl;

    @Value("${service.mcp-remote.url}")
    private String mcpRemoteServiceUrl;

    /**
     * 调用MCP工具
     */
    public Map<String, Object> callMCPTool(String serviceName, String toolName, 
                                          Map<String, Object> parameters, boolean isRemote) {
        try {
            String baseUrl = isRemote ? mcpRemoteServiceUrl : mcpLocalServiceUrl;
            String url = UriComponentsBuilder.fromHttpUrl(baseUrl)
                    .path("/api/mcp/tools/call")
                    .toUriString();

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("serviceName", serviceName);
            requestBody.put("toolName", toolName);
            requestBody.put("parameters", parameters != null ? parameters : Collections.emptyMap());

            log.debug("调用MCP工具: {} - {} (remote: {})", serviceName, toolName, isRemote);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    request,
                    new ParameterizedTypeReference<Map<String, Object>>() {}
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                if (Boolean.TRUE.equals(responseBody.get("success"))) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> result = (Map<String, Object>) responseBody.get("data");
                    log.debug("MCP工具调用成功: {} - {}", serviceName, toolName);
                    return result;
                } else {
                    String errorMessage = (String) responseBody.get("message");
                    log.warn("MCP工具调用失败: {} - {} - {}", serviceName, toolName, errorMessage);
                    return Collections.singletonMap("error", errorMessage);
                }
            }

            log.warn("MCP工具调用响应异常: {} - {} - {}", serviceName, toolName, response.getStatusCode());
            return Collections.singletonMap("error", "服务响应异常: " + response.getStatusCode());

        } catch (Exception e) {
            log.error("MCP工具调用异常: {} - {} - {}", serviceName, toolName, e.getMessage(), e);
            return Collections.singletonMap("error", "调用异常: " + e.getMessage());
        }
    }

    /**
     * 获取MCP服务列表
     */
    public Map<String, Object> getMCPServices(boolean isRemote) {
        try {
            String baseUrl = isRemote ? mcpRemoteServiceUrl : mcpLocalServiceUrl;
            String path = isRemote ? "/api/mcp/remote/services" : "/api/mcp/services";
            String url = UriComponentsBuilder.fromHttpUrl(baseUrl)
                    .path(path)
                    .toUriString();

            log.debug("获取MCP服务列表 (remote: {})", isRemote);

            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    null,
                    new ParameterizedTypeReference<Map<String, Object>>() {}
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                if (Boolean.TRUE.equals(responseBody.get("success"))) {
                    log.debug("获取MCP服务列表成功 (remote: {})", isRemote);
                    return responseBody;
                }
            }

            log.warn("获取MCP服务列表失败 (remote: {}) - {}", isRemote, response.getStatusCode());
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "获取服务列表失败");
            return errorResult;

        } catch (Exception e) {
            log.error("获取MCP服务列表异常 (remote: {}) - {}", isRemote, e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "获取服务列表异常: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 获取MCP服务详情
     */
    public Map<String, Object> getMCPServiceDetail(String serviceName, boolean isRemote) {
        try {
            String baseUrl = isRemote ? mcpRemoteServiceUrl : mcpLocalServiceUrl;
            String path = isRemote ? "/api/mcp/remote/services/" : "/api/mcp/services/";
            String url = UriComponentsBuilder.fromHttpUrl(baseUrl)
                    .path(path + serviceName)
                    .toUriString();

            log.debug("获取MCP服务详情: {} (remote: {})", serviceName, isRemote);

            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    null,
                    new ParameterizedTypeReference<Map<String, Object>>() {}
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                if (Boolean.TRUE.equals(responseBody.get("success"))) {
                    log.debug("获取MCP服务详情成功: {} (remote: {})", serviceName, isRemote);
                    return responseBody;
                }
            }

            log.warn("获取MCP服务详情失败: {} (remote: {}) - {}", serviceName, isRemote, response.getStatusCode());
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "获取服务详情失败");
            return errorResult;

        } catch (Exception e) {
            log.error("获取MCP服务详情异常: {} (remote: {}) - {}", serviceName, isRemote, e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "获取服务详情异常: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 检查MCP服务状态
     */
    public Map<String, Object> checkMCPServiceStatus(String serviceName, boolean isRemote) {
        try {
            String baseUrl = isRemote ? mcpRemoteServiceUrl : mcpLocalServiceUrl;
            String path = isRemote ? "/api/mcp/remote/services/" : "/api/mcp/services/";
            String url = UriComponentsBuilder.fromHttpUrl(baseUrl)
                    .path(path + serviceName + "/status")
                    .toUriString();

            log.debug("检查MCP服务状态: {} (remote: {})", serviceName, isRemote);

            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    null,
                    new ParameterizedTypeReference<Map<String, Object>>() {}
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                if (Boolean.TRUE.equals(responseBody.get("success"))) {
                    log.debug("检查MCP服务状态成功: {} (remote: {})", serviceName, isRemote);
                    return responseBody;
                }
            }

            log.warn("检查MCP服务状态失败: {} (remote: {}) - {}", serviceName, isRemote, response.getStatusCode());
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("status", "UNKNOWN");
            errorResult.put("message", "检查状态失败");
            return errorResult;

        } catch (Exception e) {
            log.error("检查MCP服务状态异常: {} (remote: {}) - {}", serviceName, isRemote, e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("status", "ERROR");
            errorResult.put("message", "检查状态异常: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 异步调用MCP工具
     */
    public Map<String, Object> callMCPToolAsync(String serviceName, String toolName, 
                                               Map<String, Object> parameters, boolean isRemote) {
        try {
            String baseUrl = isRemote ? mcpRemoteServiceUrl : mcpLocalServiceUrl;
            String url = UriComponentsBuilder.fromHttpUrl(baseUrl)
                    .path("/api/mcp/tools/call-async")
                    .toUriString();

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("serviceName", serviceName);
            requestBody.put("toolName", toolName);
            requestBody.put("parameters", parameters != null ? parameters : Collections.emptyMap());

            log.debug("异步调用MCP工具: {} - {} (remote: {})", serviceName, toolName, isRemote);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    request,
                    new ParameterizedTypeReference<Map<String, Object>>() {}
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                if (Boolean.TRUE.equals(responseBody.get("success"))) {
                    log.debug("异步MCP工具调用成功: {} - {}", serviceName, toolName);
                    return responseBody;
                } else {
                    String errorMessage = (String) responseBody.get("message");
                    log.warn("异步MCP工具调用失败: {} - {} - {}", serviceName, toolName, errorMessage);
                    return Collections.singletonMap("success", false);
                }
            }

            log.warn("异步MCP工具调用响应异常: {} - {} - {}", serviceName, toolName, response.getStatusCode());
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("error", "服务响应异常: " + response.getStatusCode());
            return errorResult;

        } catch (Exception e) {
            log.error("异步MCP工具调用异常: {} - {} - {}", serviceName, toolName, e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("error", "调用异常: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 获取异步任务状态
     */
    public Map<String, Object> getAsyncTaskStatus(String taskId, boolean isRemote) {
        try {
            String baseUrl = isRemote ? mcpRemoteServiceUrl : mcpLocalServiceUrl;
            String url = UriComponentsBuilder.fromHttpUrl(baseUrl)
                    .path("/api/mcp/tasks/" + taskId + "/status")
                    .toUriString();

            log.debug("获取异步任务状态: {} (remote: {})", taskId, isRemote);

            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    null,
                    new ParameterizedTypeReference<Map<String, Object>>() {}
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                if (Boolean.TRUE.equals(responseBody.get("success"))) {
                    log.debug("获取异步任务状态成功: {} (remote: {})", taskId, isRemote);
                    return responseBody;
                }
            }

            log.warn("获取异步任务状态失败: {} (remote: {}) - {}", taskId, isRemote, response.getStatusCode());
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("status", "UNKNOWN");
            errorResult.put("message", "获取任务状态失败");
            return errorResult;

        } catch (Exception e) {
            log.error("获取异步任务状态异常: {} (remote: {}) - {}", taskId, isRemote, e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("status", "ERROR");
            errorResult.put("message", "获取任务状态异常: " + e.getMessage());
            return errorResult;
        }
    }
}
