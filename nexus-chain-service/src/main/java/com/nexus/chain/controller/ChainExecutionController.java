package com.nexus.chain.controller;

import com.nexus.chain.dto.ServiceChainDTO;
import com.nexus.chain.service.ChainExecutionService;
import com.nexus.common.dto.UserDTO;
import com.nexus.common.response.ApiResponse;
import com.nexus.common.util.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 服务链执行状态查询控制器
 * 提供执行记录查询、状态管理等REST API
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/v1/chains/executions")
@RequiredArgsConstructor
@Slf4j
@Validated
@Tag(name = "服务链执行管理", description = "服务链执行状态查询和管理相关API")
public class ChainExecutionController {

    private final ChainExecutionService chainExecutionService;
    private final JwtUtil jwtUtil;

    /**
     * 获取执行详情
     */
    @GetMapping("/{executionId}")
    @Operation(summary = "获取执行详情", description = "根据执行ID获取服务链执行的详细信息")
    public ResponseEntity<ApiResponse<ServiceChainDTO.ChainExecutionResponseDTO>> getExecutionDetail(
            @Parameter(description = "执行ID") @PathVariable @NotBlank String executionId,
            HttpServletRequest request) {

        try {
            // 验证JWT并获取用户信息
            UserDTO currentUser = jwtUtil.getCurrentUserFromRequest(request);
            if (currentUser == null) {
                return ResponseEntity.status(401)
                        .body(ApiResponse.error(401, "未授权访问"));
            }

            log.debug("用户 {} 获取执行详情: {}", currentUser.getUsername(), executionId);

            // 获取执行详情
            ServiceChainDTO.ChainExecutionResponseDTO response =
                    chainExecutionService.getExecutionDetail(executionId, currentUser);

            return ResponseEntity.ok(ApiResponse.success("获取执行详情成功", response));

        } catch (Exception e) {
            log.error("获取执行详情失败: {} - {}", executionId, e.getMessage(), e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("获取执行详情失败: " + e.getMessage()));
        }
    }

    /**
     * 查询用户的执行记录列表
     */
    @GetMapping
    @Operation(summary = "查询执行记录列表", description = "查询当前用户的服务链执行记录，支持分页")
    public ResponseEntity<ApiResponse<ServiceChainDTO.ChainExecutionListResponseDTO>> getUserExecutions(
            @Parameter(description = "服务链ID") @RequestParam(required = false) String chainId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页数量") @RequestParam(defaultValue = "10") Integer size,
            HttpServletRequest request) {

        try {
            // 验证JWT并获取用户信息
            UserDTO currentUser = jwtUtil.getCurrentUserFromRequest(request);
            if (currentUser == null) {
                return ResponseEntity.status(401)
                        .body(ApiResponse.error(401, "未授权访问"));
            }

            log.debug("用户 {} 查询执行记录列表: chainId={}", currentUser.getUsername(), chainId);

            // 查询执行记录列表
            ServiceChainDTO.ChainExecutionListResponseDTO response =
                    chainExecutionService.getUserExecutions(chainId, currentUser, page, size);

            return ResponseEntity.ok(ApiResponse.success("查询执行记录列表成功", response));

        } catch (Exception e) {
            log.error("查询执行记录列表失败: {}", e.getMessage(), e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("查询执行记录列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取执行统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取执行统计", description = "获取当前用户的服务链执行统计信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getExecutionStatistics(
            HttpServletRequest request) {

        try {
            // 验证JWT并获取用户信息
            UserDTO currentUser = jwtUtil.getCurrentUserFromRequest(request);
            if (currentUser == null) {
                return ResponseEntity.status(401)
                        .body(ApiResponse.error(401, "未授权访问"));
            }

            log.debug("用户 {} 获取执行统计", currentUser.getUsername());

            // 获取执行统计
            Map<String, Object> statistics = chainExecutionService.getExecutionStatistics(currentUser);

            return ResponseEntity.ok(ApiResponse.success("获取执行统计成功", statistics));

        } catch (Exception e) {
            log.error("获取执行统计失败: {}", e.getMessage(), e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("获取执行统计失败: " + e.getMessage()));
        }
    }

    /**
     * 取消执行
     */
    @PostMapping("/{executionId}/cancel")
    @Operation(summary = "取消执行", description = "取消正在运行的服务链执行")
    public ResponseEntity<ApiResponse<String>> cancelExecution(
            @Parameter(description = "执行ID") @PathVariable @NotBlank String executionId,
            HttpServletRequest request) {

        try {
            // 验证JWT并获取用户信息
            UserDTO currentUser = jwtUtil.getCurrentUserFromRequest(request);
            if (currentUser == null) {
                return ResponseEntity.status(401)
                        .body(ApiResponse.error(401, "未授权访问"));
            }

            log.info("用户 {} 取消执行: {}", currentUser.getUsername(), executionId);

            // 取消执行
            boolean success = chainExecutionService.cancelExecution(executionId, currentUser);

            if (success) {
                return ResponseEntity.ok(ApiResponse.success("执行已成功取消"));
            } else {
                return ResponseEntity.status(400)
                        .body(ApiResponse.error(400, "取消失败"));
            }

        } catch (Exception e) {
            log.error("取消执行失败: {} - {}", executionId, e.getMessage(), e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("取消执行失败: " + e.getMessage()));
        }
    }

    /**
     * 重试执行
     */
    @PostMapping("/{executionId}/retry")
    @Operation(summary = "重试执行", description = "重试失败的服务链执行")
    public ResponseEntity<ApiResponse<String>> retryExecution(
            @Parameter(description = "执行ID") @PathVariable @NotBlank String executionId,
            HttpServletRequest request) {

        try {
            // 验证JWT并获取用户信息
            UserDTO currentUser = jwtUtil.getCurrentUserFromRequest(request);
            if (currentUser == null) {
                return ResponseEntity.status(401)
                        .body(ApiResponse.error(401, "未授权访问"));
            }

            log.info("用户 {} 重试执行: {}", currentUser.getUsername(), executionId);

            // 重试执行
            boolean success = chainExecutionService.retryExecution(executionId, currentUser);

            if (success) {
                return ResponseEntity.ok(ApiResponse.success("执行已加入重试队列"));
            } else {
                return ResponseEntity.status(400)
                        .body(ApiResponse.error(400, "重试失败"));
            }

        } catch (Exception e) {
            log.error("重试执行失败: {} - {}", executionId, e.getMessage(), e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("重试执行失败: " + e.getMessage()));
        }
    }

    /**
     * 获取执行上下文
     */
    @GetMapping("/{executionId}/context")
    @Operation(summary = "获取执行上下文", description = "获取服务链执行的上下文数据")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getExecutionContext(
            @Parameter(description = "执行ID") @PathVariable @NotBlank String executionId,
            HttpServletRequest request) {

        try {
            // 验证JWT并获取用户信息
            UserDTO currentUser = jwtUtil.getCurrentUserFromRequest(request);
            if (currentUser == null) {
                return ResponseEntity.status(401)
                        .body(ApiResponse.error(401, "未授权访问"));
            }

            log.debug("用户 {} 获取执行上下文: {}", currentUser.getUsername(), executionId);

            // 获取执行上下文
            Map<String, Object> context = chainExecutionService.getExecutionContext(executionId, currentUser);

            return ResponseEntity.ok(ApiResponse.success("获取执行上下文成功", context));

        } catch (Exception e) {
            log.error("获取执行上下文失败: {} - {}", executionId, e.getMessage(), e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("获取执行上下文失败: " + e.getMessage()));
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "执行服务健康检查接口")
    public ResponseEntity<ApiResponse<Map<String, Object>>> health() {
        Map<String, Object> healthInfo = Map.of(
                "status", "UP",
                "service", "chain-execution-service",
                "timestamp", System.currentTimeMillis()
        );
        return ResponseEntity.ok(ApiResponse.success("执行服务正常", healthInfo));
    }
}
