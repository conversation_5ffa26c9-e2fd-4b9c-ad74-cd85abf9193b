package com.nexus.chain.controller;

import com.nexus.chain.dto.ServiceChainDTO;
import com.nexus.chain.service.ServiceChainExecutor;
import com.nexus.chain.service.ServiceChainService;
import com.nexus.common.dto.UserDTO;
import com.nexus.common.response.ApiResponse;
import com.nexus.common.util.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * 服务链管理控制器
 * 提供服务链CRUD和执行相关的REST API
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/v1/chains")
@RequiredArgsConstructor
@Slf4j
@Validated
@Tag(name = "服务链管理", description = "服务链CRUD和执行相关API")
public class ServiceChainController {

    private final ServiceChainService serviceChainService;
    private final ServiceChainExecutor serviceChainExecutor;
    private final JwtUtil jwtUtil;

    /**
     * 创建服务链
     */
    @PostMapping
    @Operation(summary = "创建服务链", description = "创建新的服务链配置")
    public ResponseEntity<ApiResponse<ServiceChainDTO.ServiceChainResponseDTO>> createServiceChain(
            @Valid @RequestBody ServiceChainDTO.CreateServiceChainDTO createDTO,
            HttpServletRequest request) {

        try {
            // 验证JWT并获取用户信息
            UserDTO currentUser = jwtUtil.getCurrentUserFromRequest(request);
            if (currentUser == null) {
                return ResponseEntity.status(401)
                        .body(ApiResponse.error(401, "未授权访问"));
            }

            log.info("用户 {} 创建服务链: {}", currentUser.getUsername(), createDTO.getName());

            // 创建服务链
            ServiceChainDTO.ServiceChainResponseDTO response = 
                    serviceChainService.createServiceChain(createDTO, currentUser);

            log.info("用户 {} 创建服务链成功: {} - {}", 
                    currentUser.getUsername(), response.getChainId(), response.getName());

            return ResponseEntity.ok(ApiResponse.success("创建服务链成功", response));

        } catch (Exception e) {
            log.error("创建服务链失败: {}", e.getMessage(), e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("创建服务链失败: " + e.getMessage()));
        }
    }

    /**
     * 获取服务链详情
     */
    @GetMapping("/{chainId}")
    @Operation(summary = "获取服务链详情", description = "根据链ID获取服务链的详细信息")
    public ResponseEntity<ApiResponse<ServiceChainDTO.ServiceChainResponseDTO>> getServiceChain(
            @Parameter(description = "服务链ID") @PathVariable @NotBlank String chainId,
            HttpServletRequest request) {

        try {
            // 验证JWT并获取用户信息
            UserDTO currentUser = jwtUtil.getCurrentUserFromRequest(request);
            if (currentUser == null) {
                return ResponseEntity.status(401)
                        .body(ApiResponse.error(401, "未授权访问"));
            }

            log.debug("用户 {} 获取服务链详情: {}", currentUser.getUsername(), chainId);

            // 获取服务链详情
            ServiceChainDTO.ServiceChainResponseDTO response = 
                    serviceChainService.getServiceChain(chainId, currentUser);

            return ResponseEntity.ok(ApiResponse.success("获取服务链详情成功", response));

        } catch (Exception e) {
            log.error("获取服务链详情失败: {} - {}", chainId, e.getMessage(), e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("获取服务链详情失败: " + e.getMessage()));
        }
    }

    /**
     * 更新服务链
     */
    @PutMapping("/{chainId}")
    @Operation(summary = "更新服务链", description = "更新指定服务链的配置信息")
    public ResponseEntity<ApiResponse<ServiceChainDTO.ServiceChainResponseDTO>> updateServiceChain(
            @Parameter(description = "服务链ID") @PathVariable @NotBlank String chainId,
            @Valid @RequestBody ServiceChainDTO.UpdateServiceChainDTO updateDTO,
            HttpServletRequest request) {

        try {
            // 验证JWT并获取用户信息
            UserDTO currentUser = jwtUtil.getCurrentUserFromRequest(request);
            if (currentUser == null) {
                return ResponseEntity.status(401)
                        .body(ApiResponse.error(401, "未授权访问"));
            }

            log.info("用户 {} 更新服务链: {}", currentUser.getUsername(), chainId);

            // 更新服务链
            ServiceChainDTO.ServiceChainResponseDTO response = 
                    serviceChainService.updateServiceChain(chainId, updateDTO, currentUser);

            log.info("用户 {} 更新服务链成功: {} - {}", 
                    currentUser.getUsername(), response.getChainId(), response.getName());

            return ResponseEntity.ok(ApiResponse.success("更新服务链成功", response));

        } catch (Exception e) {
            log.error("更新服务链失败: {} - {}", chainId, e.getMessage(), e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("更新服务链失败: " + e.getMessage()));
        }
    }

    /**
     * 删除服务链
     */
    @DeleteMapping("/{chainId}")
    @Operation(summary = "删除服务链", description = "删除指定的服务链")
    public ResponseEntity<ApiResponse<String>> deleteServiceChain(
            @Parameter(description = "服务链ID") @PathVariable @NotBlank String chainId,
            HttpServletRequest request) {

        try {
            // 验证JWT并获取用户信息
            UserDTO currentUser = jwtUtil.getCurrentUserFromRequest(request);
            if (currentUser == null) {
                return ResponseEntity.status(401)
                        .body(ApiResponse.error(401, "未授权访问"));
            }

            log.info("用户 {} 删除服务链: {}", currentUser.getUsername(), chainId);

            // 删除服务链
            serviceChainService.deleteServiceChain(chainId, currentUser);

            log.info("用户 {} 删除服务链成功: {}", currentUser.getUsername(), chainId);

            return ResponseEntity.ok(ApiResponse.success("删除服务链成功"));

        } catch (Exception e) {
            log.error("删除服务链失败: {} - {}", chainId, e.getMessage(), e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("删除服务链失败: " + e.getMessage()));
        }
    }

    /**
     * 查询用户的服务链列表
     */
    @GetMapping
    @Operation(summary = "查询服务链列表", description = "查询当前用户的服务链列表，支持分页和过滤")
    public ResponseEntity<ApiResponse<ServiceChainDTO.ChainListResponseDTO>> getUserChains(
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "分类") @RequestParam(required = false) String category,
            @Parameter(description = "状态") @RequestParam(required = false) String status,
            @Parameter(description = "是否公开") @RequestParam(required = false) Boolean isPublic,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "desc") String sortOrder,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页数量") @RequestParam(defaultValue = "10") Integer size,
            HttpServletRequest request) {

        try {
            // 验证JWT并获取用户信息
            UserDTO currentUser = jwtUtil.getCurrentUserFromRequest(request);
            if (currentUser == null) {
                return ResponseEntity.status(401)
                        .body(ApiResponse.error(401, "未授权访问"));
            }

            // 构建查询参数
            ServiceChainDTO.ChainQueryDTO queryDTO = ServiceChainDTO.ChainQueryDTO.builder()
                    .keyword(keyword)
                    .category(category)
                    .status(status != null ? com.nexus.chain.entity.ServiceChain.ChainStatus.valueOf(status.toUpperCase()) : null)
                    .isPublic(isPublic)
                    .sortBy(sortBy)
                    .sortOrder(sortOrder)
                    .page(page)
                    .size(Math.min(size, 100))
                    .build();

            log.debug("用户 {} 查询服务链列表: {}", currentUser.getUsername(), queryDTO);

            // 查询服务链列表
            ServiceChainDTO.ChainListResponseDTO response = 
                    serviceChainService.getUserChains(queryDTO, currentUser);

            return ResponseEntity.ok(ApiResponse.success("查询服务链列表成功", response));

        } catch (Exception e) {
            log.error("查询服务链列表失败: {}", e.getMessage(), e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("查询服务链列表失败: " + e.getMessage()));
        }
    }

    /**
     * 执行服务链（同步）
     */
    @PostMapping("/{chainId}/execute")
    @Operation(summary = "执行服务链", description = "同步执行指定的服务链")
    public ResponseEntity<ApiResponse<ServiceChainDTO.ChainExecutionResponseDTO>> executeChain(
            @Parameter(description = "服务链ID") @PathVariable @NotBlank String chainId,
            @Valid @RequestBody ServiceChainDTO.ExecuteChainDTO executeDTO,
            HttpServletRequest request) {

        try {
            // 验证JWT并获取用户信息
            UserDTO currentUser = jwtUtil.getCurrentUserFromRequest(request);
            if (currentUser == null) {
                return ResponseEntity.status(401)
                        .body(ApiResponse.error(401, "未授权访问"));
            }

            log.info("用户 {} 执行服务链: {}", currentUser.getUsername(), chainId);

            // 执行服务链
            ServiceChainDTO.ChainExecutionResponseDTO response = 
                    serviceChainExecutor.executeChain(chainId, executeDTO, currentUser);

            log.info("用户 {} 执行服务链完成: {} - status: {}", 
                    currentUser.getUsername(), chainId, response.getStatus());

            return ResponseEntity.ok(ApiResponse.success("执行服务链完成", response));

        } catch (Exception e) {
            log.error("执行服务链失败: {} - {}", chainId, e.getMessage(), e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("执行服务链失败: " + e.getMessage()));
        }
    }

    /**
     * 执行服务链（异步）
     */
    @PostMapping("/{chainId}/execute-async")
    @Operation(summary = "异步执行服务链", description = "异步执行指定的服务链")
    public ResponseEntity<ApiResponse<ServiceChainDTO.ChainExecutionResponseDTO>> executeChainAsync(
            @Parameter(description = "服务链ID") @PathVariable @NotBlank String chainId,
            @Valid @RequestBody ServiceChainDTO.ExecuteChainDTO executeDTO,
            HttpServletRequest request) {

        try {
            // 验证JWT并获取用户信息
            UserDTO currentUser = jwtUtil.getCurrentUserFromRequest(request);
            if (currentUser == null) {
                return ResponseEntity.status(401)
                        .body(ApiResponse.error(401, "未授权访问"));
            }

            log.info("用户 {} 异步执行服务链: {}", currentUser.getUsername(), chainId);

            // 异步执行服务链
            ServiceChainDTO.ChainExecutionResponseDTO response = 
                    serviceChainExecutor.executeChainAsync(chainId, executeDTO, currentUser);

            log.info("用户 {} 异步执行服务链启动: {} - executionId: {}", 
                    currentUser.getUsername(), chainId, response.getExecutionId());

            return ResponseEntity.ok(ApiResponse.success("异步执行服务链启动成功", response));

        } catch (Exception e) {
            log.error("异步执行服务链失败: {} - {}", chainId, e.getMessage(), e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("异步执行服务链失败: " + e.getMessage()));
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "服务健康检查接口")
    public ResponseEntity<ApiResponse<java.util.Map<String, Object>>> health() {
        java.util.Map<String, Object> healthInfo = java.util.Map.of(
                "status", "UP",
                "service", "nexus-chain-service",
                "timestamp", System.currentTimeMillis()
        );
        return ResponseEntity.ok(ApiResponse.success("服务正常", healthInfo));
    }
}
