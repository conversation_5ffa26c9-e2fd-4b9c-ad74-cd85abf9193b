package com.nexus.chain.dto;

import com.nexus.chain.entity.ServiceChain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 服务链相关DTO
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public class ServiceChainDTO {

    /**
     * 服务链创建请求DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreateServiceChainDTO {
        
        @NotBlank(message = "服务链名称不能为空")
        @Size(max = 200, message = "服务链名称长度不能超过200字符")
        private String name;
        
        @Size(max = 1000, message = "描述长度不能超过1000字符")
        private String description;
        
        @NotNull(message = "服务链配置不能为空")
        @Valid
        private ChainConfigDTO chainConfig;
        
        private String templateId;
        private String category;
        private List<String> tags;
        private Boolean isPublic = false;
    }

    /**
     * 服务链更新请求DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UpdateServiceChainDTO {
        
        @Size(max = 200, message = "服务链名称长度不能超过200字符")
        private String name;
        
        @Size(max = 1000, message = "描述长度不能超过1000字符")
        private String description;
        
        @Valid
        private ChainConfigDTO chainConfig;
        
        private ServiceChain.ChainStatus status;
        private String category;
        private List<String> tags;
        private Boolean isPublic;
    }

    /**
     * 服务链响应DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ServiceChainResponseDTO {
        
        private Long id;
        private String chainId;
        private Long userId;
        private String name;
        private String description;
        private ChainConfigDTO chainConfig;
        private String templateId;
        private ServiceChain.ChainStatus status;
        private Integer version;
        private Boolean isPublic;
        private List<String> tags;
        private String category;
        private Long executionCount;
        private Long successCount;
        private Double successRate;
        private LocalDateTime lastExecutedAt;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
        private String createdBy;
        private String updatedBy;
    }

    /**
     * 服务链配置DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChainConfigDTO {
        
        @NotNull(message = "服务链步骤不能为空")
        @Size(min = 1, message = "服务链至少需要一个步骤")
        private List<@Valid ChainStepDTO> steps;
        
        private Map<String, Object> globalParams;
        private Map<String, String> globalVariables;
        private Integer timeout;
        private Integer maxRetries;
        private Boolean enableParallel = false;
    }

    /**
     * 服务链步骤DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChainStepDTO {
        
        @NotBlank(message = "步骤ID不能为空")
        private String stepId;
        
        @NotBlank(message = "步骤名称不能为空")
        private String name;
        
        private String description;
        
        @NotBlank(message = "服务名称不能为空")
        private String serviceName;
        
        @NotBlank(message = "工具名称不能为空")
        private String toolName;
        
        @Valid
        private StepConfigDTO config;
        
        private List<String> dependsOn;
        private String condition;
        private Integer order;
        private Boolean enabled = true;
        private Integer timeout;
        private Integer retries;
    }

    /**
     * 步骤配置DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StepConfigDTO {
        
        private Map<String, Object> staticParams;
        private Map<String, String> dynamicParams;
        private Map<String, String> inputMapping;
        private Map<String, String> outputMapping;
        private Boolean async = false;
        private Integer timeout;
        private Map<String, Object> metadata;
    }

    /**
     * 服务链执行请求DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExecuteChainDTO {
        
        @NotNull(message = "输入参数不能为空")
        private Map<String, Object> input;
        
        private Boolean async = false;
        private Integer timeout;
        private Map<String, Object> context;
    }

    /**
     * 服务链执行响应DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChainExecutionResponseDTO {
        
        private String executionId;
        private String chainId;
        private String status;
        private Map<String, Object> output;
        private String errorMessage;
        private String errorStep;
        private Integer totalSteps;
        private Integer completedSteps;
        private String currentStep;
        private Double progressPercentage;
        private Long executionTimeMs;
        private LocalDateTime startedAt;
        private LocalDateTime completedAt;
        private Boolean isAsync;
        private String statusUrl;
    }

    /**
     * 服务链列表查询DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChainQueryDTO {
        
        private String keyword;
        private String category;
        private ServiceChain.ChainStatus status;
        private Boolean isPublic;
        private List<String> tags;
        private String sortBy = "createdAt";
        private String sortOrder = "desc";
        private Integer page = 1;
        private Integer size = 10;
    }

    /**
     * 服务链列表响应DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChainListResponseDTO {

        private List<ServiceChainResponseDTO> chains;
        private Long totalCount;
        private Integer currentPage;
        private Integer totalPages;
        private Integer pageSize;
        private Boolean hasNext;
        private Boolean hasPrevious;
    }

    /**
     * 执行记录列表响应DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChainExecutionListResponseDTO {

        private List<ChainExecutionResponseDTO> executions;
        private Long totalCount;
        private Integer currentPage;
        private Integer totalPages;
        private Integer pageSize;
        private Boolean hasNext;
        private Boolean hasPrevious;
    }
}
