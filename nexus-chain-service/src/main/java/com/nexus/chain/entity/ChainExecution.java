package com.nexus.chain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 服务链执行记录实体类
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Entity
@Table(name = "chain_executions")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChainExecution {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "execution_id", unique = true, nullable = false)
    private String executionId;

    @Column(name = "chain_id", nullable = false)
    private String chainId;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private ExecutionStatus status;

    @Column(name = "input_data", columnDefinition = "TEXT")
    private String inputData;

    @Column(name = "output_data", columnDefinition = "TEXT")
    private String outputData;

    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    @Column(name = "error_step")
    private String errorStep;

    @Column(name = "total_steps")
    private Integer totalSteps;

    @Column(name = "completed_steps")
    private Integer completedSteps;

    @Column(name = "current_step")
    private String currentStep;

    @Column(name = "execution_time_ms")
    private Long executionTimeMs;

    @Column(name = "started_at")
    private LocalDateTime startedAt;

    @Column(name = "completed_at")
    private LocalDateTime completedAt;

    @Column(name = "is_async")
    private Boolean isAsync;

    @Column(name = "retry_count")
    private Integer retryCount;

    @Column(name = "max_retries")
    private Integer maxRetries;

    @Column(name = "execution_metadata", columnDefinition = "TEXT")
    private String executionMetadata;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 执行状态枚举
     */
    public enum ExecutionStatus {
        PENDING,    // 等待中
        RUNNING,    // 执行中
        COMPLETED,  // 已完成
        FAILED,     // 失败
        CANCELLED,  // 已取消
        TIMEOUT     // 超时
    }

    /**
     * 标记执行开始
     */
    public void markAsStarted() {
        this.status = ExecutionStatus.RUNNING;
        this.startedAt = LocalDateTime.now();
    }

    /**
     * 标记执行完成
     */
    public void markAsCompleted(String outputData) {
        this.status = ExecutionStatus.COMPLETED;
        this.outputData = outputData;
        this.completedAt = LocalDateTime.now();
        if (this.startedAt != null) {
            this.executionTimeMs = java.time.Duration.between(this.startedAt, this.completedAt).toMillis();
        }
    }

    /**
     * 标记执行失败
     */
    public void markAsFailed(String errorMessage, String errorStep) {
        this.status = ExecutionStatus.FAILED;
        this.errorMessage = errorMessage;
        this.errorStep = errorStep;
        this.completedAt = LocalDateTime.now();
        if (this.startedAt != null) {
            this.executionTimeMs = java.time.Duration.between(this.startedAt, this.completedAt).toMillis();
        }
    }

    /**
     * 更新执行进度
     */
    public void updateProgress(String currentStep, Integer completedSteps) {
        this.currentStep = currentStep;
        this.completedSteps = completedSteps;
    }

    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        this.retryCount = (this.retryCount != null ? this.retryCount : 0) + 1;
    }

    /**
     * 检查是否可以重试
     */
    public boolean canRetry() {
        int currentRetries = this.retryCount != null ? this.retryCount : 0;
        int maxRetries = this.maxRetries != null ? this.maxRetries : 0;
        return currentRetries < maxRetries;
    }

    /**
     * 计算执行进度百分比
     */
    public Double getProgressPercentage() {
        if (totalSteps == null || totalSteps == 0) {
            return 0.0;
        }
        int completed = completedSteps != null ? completedSteps : 0;
        return (double) completed / totalSteps * 100.0;
    }
}
