package com.nexus.chain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 服务链实体类
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Entity
@Table(name = "service_chains")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceChain {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "chain_id", unique = true, nullable = false)
    private String chainId;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "chain_config", columnDefinition = "TEXT", nullable = false)
    private String chainConfig;

    @Column(name = "template_id")
    private String templateId;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private ChainStatus status;

    @Column(name = "version")
    private Integer version;

    @Column(name = "is_public")
    private Boolean isPublic;

    @Column(name = "tags")
    private String tags;

    @Column(name = "category")
    private String category;

    @Column(name = "execution_count")
    private Long executionCount;

    @Column(name = "success_count")
    private Long successCount;

    @Column(name = "last_executed_at")
    private LocalDateTime lastExecutedAt;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "updated_by")
    private String updatedBy;

    /**
     * 服务链状态枚举
     */
    public enum ChainStatus {
        ACTIVE,     // 活跃
        DRAFT,      // 草稿
        DISABLED,   // 禁用
        ARCHIVED    // 归档
    }

    /**
     * 增加执行计数
     */
    public void incrementExecutionCount() {
        this.executionCount = (this.executionCount != null ? this.executionCount : 0L) + 1;
        this.lastExecutedAt = LocalDateTime.now();
    }

    /**
     * 增加成功计数
     */
    public void incrementSuccessCount() {
        this.successCount = (this.successCount != null ? this.successCount : 0L) + 1;
    }

    /**
     * 计算成功率
     */
    public Double getSuccessRate() {
        if (executionCount == null || executionCount == 0) {
            return 0.0;
        }
        long success = successCount != null ? successCount : 0L;
        return (double) success / executionCount * 100.0;
    }
}
