package com.nexus.chain.repository;

import com.nexus.chain.entity.ChainExecution;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 服务链执行记录Repository
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Repository
public interface ChainExecutionRepository extends JpaRepository<ChainExecution, Long> {

    /**
     * 根据执行ID查找执行记录
     */
    Optional<ChainExecution> findByExecutionId(String executionId);

    /**
     * 根据链ID查找执行记录
     */
    List<ChainExecution> findByChainId(String chainId);

    /**
     * 分页查询链的执行记录
     */
    Page<ChainExecution> findByChainId(String chainId, Pageable pageable);

    /**
     * 根据用户ID查找执行记录
     */
    List<ChainExecution> findByUserId(Long userId);

    /**
     * 分页查询用户的执行记录
     */
    Page<ChainExecution> findByUserId(Long userId, Pageable pageable);

    /**
     * 根据链ID和用户ID查找执行记录
     */
    List<ChainExecution> findByChainIdAndUserId(String chainId, Long userId);

    /**
     * 分页查询用户指定链的执行记录
     */
    Page<ChainExecution> findByChainIdAndUserId(String chainId, Long userId, Pageable pageable);

    /**
     * 根据状态查找执行记录
     */
    List<ChainExecution> findByStatus(ChainExecution.ExecutionStatus status);

    /**
     * 分页查询指定状态的执行记录
     */
    Page<ChainExecution> findByStatus(ChainExecution.ExecutionStatus status, Pageable pageable);

    /**
     * 查找正在运行的执行记录
     */
    @Query("SELECT ce FROM ChainExecution ce WHERE ce.status IN ('PENDING', 'RUNNING')")
    List<ChainExecution> findRunningExecutions();

    /**
     * 查找超时的执行记录
     */
    @Query("SELECT ce FROM ChainExecution ce WHERE ce.status = 'RUNNING' " +
           "AND ce.startedAt < :timeoutThreshold")
    List<ChainExecution> findTimeoutExecutions(@Param("timeoutThreshold") LocalDateTime timeoutThreshold);

    /**
     * 查找失败的执行记录
     */
    List<ChainExecution> findByStatusOrderByCreatedAtDesc(ChainExecution.ExecutionStatus status);

    /**
     * 查找最近的执行记录
     */
    @Query("SELECT ce FROM ChainExecution ce WHERE ce.createdAt >= :since " +
           "ORDER BY ce.createdAt DESC")
    List<ChainExecution> findRecentExecutions(@Param("since") LocalDateTime since);

    /**
     * 统计链的执行次数
     */
    @Query("SELECT COUNT(ce) FROM ChainExecution ce WHERE ce.chainId = :chainId")
    Long countByChainId(@Param("chainId") String chainId);

    /**
     * 统计链的成功执行次数
     */
    @Query("SELECT COUNT(ce) FROM ChainExecution ce WHERE ce.chainId = :chainId " +
           "AND ce.status = 'COMPLETED'")
    Long countSuccessfulExecutionsByChainId(@Param("chainId") String chainId);

    /**
     * 统计用户的执行次数
     */
    @Query("SELECT COUNT(ce) FROM ChainExecution ce WHERE ce.userId = :userId")
    Long countByUserId(@Param("userId") Long userId);

    /**
     * 统计各状态的执行数量
     */
    @Query("SELECT ce.status, COUNT(ce) FROM ChainExecution ce GROUP BY ce.status")
    List<Object[]> countExecutionsByStatus();

    /**
     * 查询平均执行时间
     */
    @Query("SELECT AVG(ce.executionTimeMs) FROM ChainExecution ce WHERE ce.chainId = :chainId " +
           "AND ce.status = 'COMPLETED' AND ce.executionTimeMs IS NOT NULL")
    Double getAverageExecutionTime(@Param("chainId") String chainId);

    /**
     * 查询最长执行时间
     */
    @Query("SELECT MAX(ce.executionTimeMs) FROM ChainExecution ce WHERE ce.chainId = :chainId " +
           "AND ce.status = 'COMPLETED' AND ce.executionTimeMs IS NOT NULL")
    Long getMaxExecutionTime(@Param("chainId") String chainId);

    /**
     * 查询最短执行时间
     */
    @Query("SELECT MIN(ce.executionTimeMs) FROM ChainExecution ce WHERE ce.chainId = :chainId " +
           "AND ce.status = 'COMPLETED' AND ce.executionTimeMs IS NOT NULL")
    Long getMinExecutionTime(@Param("chainId") String chainId);

    /**
     * 查询执行成功率
     */
    @Query("SELECT (COUNT(CASE WHEN ce.status = 'COMPLETED' THEN 1 END) * 100.0 / COUNT(ce)) " +
           "FROM ChainExecution ce WHERE ce.chainId = :chainId")
    Double getSuccessRate(@Param("chainId") String chainId);

    /**
     * 查询用户最近的执行记录
     */
    @Query("SELECT ce FROM ChainExecution ce WHERE ce.userId = :userId " +
           "ORDER BY ce.createdAt DESC")
    List<ChainExecution> findUserRecentExecutions(@Param("userId") Long userId, Pageable pageable);

    /**
     * 查询需要重试的执行记录
     */
    @Query("SELECT ce FROM ChainExecution ce WHERE ce.status = 'FAILED' " +
           "AND ce.retryCount < ce.maxRetries")
    List<ChainExecution> findExecutionsToRetry();

    /**
     * 查询需要清理的执行记录（旧的已完成记录）
     */
    @Query("SELECT ce FROM ChainExecution ce WHERE ce.status IN ('COMPLETED', 'FAILED', 'CANCELLED') " +
           "AND ce.completedAt < :threshold")
    List<ChainExecution> findExecutionsToCleanup(@Param("threshold") LocalDateTime threshold);

    /**
     * 删除旧的执行记录
     */
    @Query("DELETE FROM ChainExecution ce WHERE ce.completedAt < :threshold " +
           "AND ce.status IN ('COMPLETED', 'FAILED', 'CANCELLED')")
    int deleteOldExecutions(@Param("threshold") LocalDateTime threshold);

    /**
     * 查询异步执行记录
     */
    List<ChainExecution> findByIsAsyncTrue();

    /**
     * 查询同步执行记录
     */
    List<ChainExecution> findByIsAsyncFalse();

    /**
     * 复合条件查询执行记录
     */
    @Query("SELECT ce FROM ChainExecution ce WHERE " +
           "(:chainId IS NULL OR ce.chainId = :chainId) " +
           "AND (:userId IS NULL OR ce.userId = :userId) " +
           "AND (:status IS NULL OR ce.status = :status) " +
           "AND (:isAsync IS NULL OR ce.isAsync = :isAsync) " +
           "AND (:startDate IS NULL OR ce.createdAt >= :startDate) " +
           "AND (:endDate IS NULL OR ce.createdAt <= :endDate)")
    Page<ChainExecution> findExecutionsWithFilters(
            @Param("chainId") String chainId,
            @Param("userId") Long userId,
            @Param("status") ChainExecution.ExecutionStatus status,
            @Param("isAsync") Boolean isAsync,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate,
            Pageable pageable);
}
