package com.nexus.chain.repository;

import com.nexus.chain.entity.ServiceChain;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 服务链Repository
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Repository
public interface ServiceChainRepository extends JpaRepository<ServiceChain, Long> {

    /**
     * 根据链ID查找服务链
     */
    Optional<ServiceChain> findByChainId(String chainId);

    /**
     * 根据链ID和用户ID查找服务链
     */
    Optional<ServiceChain> findByChainIdAndUserId(String chainId, Long userId);

    /**
     * 检查链ID是否存在
     */
    boolean existsByChainId(String chainId);

    /**
     * 根据用户ID查找服务链
     */
    List<ServiceChain> findByUserId(Long userId);

    /**
     * 根据用户ID和状态查找服务链
     */
    List<ServiceChain> findByUserIdAndStatus(Long userId, ServiceChain.ChainStatus status);

    /**
     * 分页查询用户的服务链
     */
    Page<ServiceChain> findByUserId(Long userId, Pageable pageable);

    /**
     * 分页查询用户的指定状态服务链
     */
    Page<ServiceChain> findByUserIdAndStatus(Long userId, ServiceChain.ChainStatus status, Pageable pageable);

    /**
     * 查找公开的服务链
     */
    List<ServiceChain> findByIsPublicTrue();

    /**
     * 分页查询公开的服务链
     */
    Page<ServiceChain> findByIsPublicTrue(Pageable pageable);

    /**
     * 根据分类查找服务链
     */
    List<ServiceChain> findByCategory(String category);

    /**
     * 分页查询指定分类的服务链
     */
    Page<ServiceChain> findByCategory(String category, Pageable pageable);

    /**
     * 复合条件查询服务链
     */
    @Query("SELECT sc FROM ServiceChain sc WHERE " +
           "(:userId IS NULL OR sc.userId = :userId) " +
           "AND (:category IS NULL OR sc.category = :category) " +
           "AND (:status IS NULL OR sc.status = :status) " +
           "AND (:isPublic IS NULL OR sc.isPublic = :isPublic) " +
           "AND (:keyword IS NULL OR LOWER(sc.name) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
           "     OR LOWER(sc.description) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<ServiceChain> findChainsWithFilters(
            @Param("userId") Long userId,
            @Param("category") String category,
            @Param("status") ServiceChain.ChainStatus status,
            @Param("isPublic") Boolean isPublic,
            @Param("keyword") String keyword,
            Pageable pageable);

    /**
     * 根据标签查询服务链
     */
    @Query("SELECT sc FROM ServiceChain sc WHERE " +
           "sc.tags IS NOT NULL AND sc.tags LIKE CONCAT('%', :tag, '%')")
    List<ServiceChain> findByTag(@Param("tag") String tag);

    /**
     * 查询热门服务链（按执行次数排序）
     */
    @Query("SELECT sc FROM ServiceChain sc WHERE sc.isPublic = true " +
           "AND sc.status = 'ACTIVE' ORDER BY sc.executionCount DESC")
    List<ServiceChain> findPopularChains(Pageable pageable);

    /**
     * 查询最近创建的服务链
     */
    @Query("SELECT sc FROM ServiceChain sc WHERE sc.isPublic = true " +
           "AND sc.status = 'ACTIVE' AND sc.createdAt >= :since ORDER BY sc.createdAt DESC")
    List<ServiceChain> findRecentChains(@Param("since") LocalDateTime since, Pageable pageable);

    /**
     * 查询用户最近使用的服务链
     */
    @Query("SELECT sc FROM ServiceChain sc WHERE sc.userId = :userId " +
           "AND sc.lastExecutedAt IS NOT NULL ORDER BY sc.lastExecutedAt DESC")
    List<ServiceChain> findRecentlyUsedChains(@Param("userId") Long userId, Pageable pageable);

    /**
     * 统计用户的服务链数量
     */
    @Query("SELECT COUNT(sc) FROM ServiceChain sc WHERE sc.userId = :userId")
    Long countByUserId(@Param("userId") Long userId);

    /**
     * 统计各状态的服务链数量
     */
    @Query("SELECT sc.status, COUNT(sc) FROM ServiceChain sc GROUP BY sc.status")
    List<Object[]> countChainsByStatus();

    /**
     * 统计各分类的服务链数量
     */
    @Query("SELECT sc.category, COUNT(sc) FROM ServiceChain sc " +
           "WHERE sc.category IS NOT NULL GROUP BY sc.category")
    List<Object[]> countChainsByCategory();

    /**
     * 查询执行次数最多的服务链
     */
    @Query("SELECT sc FROM ServiceChain sc WHERE sc.executionCount > 0 " +
           "ORDER BY sc.executionCount DESC")
    List<ServiceChain> findMostExecutedChains(Pageable pageable);

    /**
     * 查询成功率最高的服务链
     */
    @Query("SELECT sc FROM ServiceChain sc WHERE sc.executionCount > 0 " +
           "AND sc.successCount > 0 ORDER BY (sc.successCount * 1.0 / sc.executionCount) DESC")
    List<ServiceChain> findMostSuccessfulChains(Pageable pageable);

    /**
     * 根据模板ID查找服务链
     */
    List<ServiceChain> findByTemplateId(String templateId);

    /**
     * 查询需要清理的服务链（长时间未使用的草稿）
     */
    @Query("SELECT sc FROM ServiceChain sc WHERE sc.status = 'DRAFT' " +
           "AND sc.updatedAt < :threshold")
    List<ServiceChain> findChainsToCleanup(@Param("threshold") LocalDateTime threshold);

    /**
     * 批量更新服务链状态
     */
    @Query("UPDATE ServiceChain sc SET sc.status = :status WHERE sc.id IN :ids")
    int updateChainStatus(@Param("ids") List<Long> ids, @Param("status") ServiceChain.ChainStatus status);
}
