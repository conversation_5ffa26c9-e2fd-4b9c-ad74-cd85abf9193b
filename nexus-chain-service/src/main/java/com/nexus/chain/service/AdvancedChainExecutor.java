package com.nexus.chain.service;

import com.nexus.chain.dto.ServiceChainDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 高级服务链执行引擎
 * 支持并行执行、依赖关系处理、超时控制等高级特性
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AdvancedChainExecutor {

    private final ChainContextManager contextManager;
    private final ParameterMapper parameterMapper;
    private final StepExecutionService stepExecutionService;

    @Qualifier("stepExecutor")
    private final Executor stepExecutor;

    /**
     * 执行服务链步骤（支持并行和依赖关系）
     */
    public Map<String, Object> executeChainStepsAdvanced(
            String executionId, 
            ServiceChainDTO.ChainConfigDTO chainConfig, 
            Map<String, Object> input) throws Exception {
        
        log.info("开始高级执行服务链步骤: {} - 共 {} 个步骤", executionId, chainConfig.getSteps().size());

        // 构建步骤依赖图
        Map<String, ServiceChainDTO.ChainStepDTO> stepMap = buildStepMap(chainConfig.getSteps());
        Map<String, Set<String>> dependencyGraph = buildDependencyGraph(chainConfig.getSteps());
        
        // 验证依赖关系
        validateDependencies(stepMap, dependencyGraph);

        // 获取执行上下文
        Map<String, Object> context = contextManager.getContext(executionId);
        
        // 添加全局配置到上下文
        if (chainConfig.getGlobalParams() != null) {
            context.putAll(chainConfig.getGlobalParams());
        }

        // 执行步骤
        Map<String, Object> finalResult = new HashMap<>();
        
        if (Boolean.TRUE.equals(chainConfig.getEnableParallel())) {
            finalResult = executeStepsInParallel(executionId, stepMap, dependencyGraph, context, chainConfig);
        } else {
            finalResult = executeStepsSequentially(executionId, stepMap, dependencyGraph, context, chainConfig);
        }

        log.info("高级服务链步骤执行完成: {}", executionId);
        return finalResult;
    }

    /**
     * 并行执行步骤
     */
    private Map<String, Object> executeStepsInParallel(
            String executionId,
            Map<String, ServiceChainDTO.ChainStepDTO> stepMap,
            Map<String, Set<String>> dependencyGraph,
            Map<String, Object> context,
            ServiceChainDTO.ChainConfigDTO chainConfig) throws Exception {
        
        log.info("开始并行执行步骤: {}", executionId);

        Set<String> completedSteps = new HashSet<>();
        Set<String> failedSteps = new HashSet<>();
        Map<String, CompletableFuture<Map<String, Object>>> runningTasks = new HashMap<>();
        Map<String, Object> finalResult = new HashMap<>();

        // 获取可以立即执行的步骤（无依赖）
        Set<String> readySteps = getReadySteps(stepMap.keySet(), dependencyGraph, completedSteps);

        while (!readySteps.isEmpty() || !runningTasks.isEmpty()) {
            // 启动准备好的步骤
            for (String stepId : readySteps) {
                ServiceChainDTO.ChainStepDTO step = stepMap.get(stepId);
                if (Boolean.TRUE.equals(step.getEnabled())) {
                    CompletableFuture<Map<String, Object>> future = executeStepAsync(
                            executionId, step, context, chainConfig);
                    runningTasks.put(stepId, future);
                    log.debug("启动并行步骤: {}", stepId);
                }
            }
            readySteps.clear();

            // 等待至少一个任务完成
            if (!runningTasks.isEmpty()) {
                CompletableFuture<Object> anyCompleted = CompletableFuture.anyOf(
                        runningTasks.values().toArray(new CompletableFuture[0]));
                
                try {
                    anyCompleted.get(30, TimeUnit.SECONDS); // 30秒超时
                } catch (TimeoutException e) {
                    log.warn("步骤执行超时，继续等待: {}", executionId);
                }

                // 检查已完成的任务
                Iterator<Map.Entry<String, CompletableFuture<Map<String, Object>>>> iterator = 
                        runningTasks.entrySet().iterator();
                
                while (iterator.hasNext()) {
                    Map.Entry<String, CompletableFuture<Map<String, Object>>> entry = iterator.next();
                    String stepId = entry.getKey();
                    CompletableFuture<Map<String, Object>> future = entry.getValue();

                    if (future.isDone()) {
                        try {
                            Map<String, Object> stepResult = future.get();
                            completedSteps.add(stepId);
                            
                            // 处理步骤结果
                            processStepResult(executionId, stepMap.get(stepId), stepResult, context);
                            finalResult.putAll(stepResult);
                            
                            log.info("并行步骤完成: {}", stepId);
                            
                        } catch (Exception e) {
                            log.error("并行步骤失败: {} - {}", stepId, e.getMessage(), e);
                            failedSteps.add(stepId);
                            
                            // 根据错误处理策略决定是否继续
                            if (shouldStopOnError(chainConfig)) {
                                throw new RuntimeException("步骤执行失败: " + stepId, e);
                            }
                        }
                        
                        iterator.remove();
                    }
                }

                // 获取新的准备好的步骤
                readySteps = getReadySteps(stepMap.keySet(), dependencyGraph, completedSteps);
                readySteps.removeAll(completedSteps);
                readySteps.removeAll(failedSteps);
                readySteps.removeAll(runningTasks.keySet());
            }
        }

        log.info("并行执行完成: {} - 成功: {}, 失败: {}", 
                executionId, completedSteps.size(), failedSteps.size());

        return finalResult;
    }

    /**
     * 顺序执行步骤
     */
    private Map<String, Object> executeStepsSequentially(
            String executionId,
            Map<String, ServiceChainDTO.ChainStepDTO> stepMap,
            Map<String, Set<String>> dependencyGraph,
            Map<String, Object> context,
            ServiceChainDTO.ChainConfigDTO chainConfig) throws Exception {
        
        log.info("开始顺序执行步骤: {}", executionId);

        // 拓扑排序获取执行顺序
        List<String> executionOrder = topologicalSort(stepMap.keySet(), dependencyGraph);
        Map<String, Object> finalResult = new HashMap<>();

        for (String stepId : executionOrder) {
            ServiceChainDTO.ChainStepDTO step = stepMap.get(stepId);
            
            if (!Boolean.TRUE.equals(step.getEnabled())) {
                log.info("步骤已禁用，跳过: {}", stepId);
                continue;
            }

            try {
                log.info("执行步骤: {} - {}", stepId, step.getName());
                
                // 评估执行条件
                if (!parameterMapper.evaluateCondition(step.getCondition(), context)) {
                    log.info("步骤条件不满足，跳过: {}", stepId);
                    continue;
                }

                // 执行步骤
                Map<String, Object> stepResult = stepExecutionService.executeStep(
                        executionId, step, context, chainConfig);
                
                // 处理结果
                processStepResult(executionId, step, stepResult, context);
                finalResult.putAll(stepResult);
                
                log.info("步骤执行成功: {}", stepId);
                
            } catch (Exception e) {
                log.error("步骤执行失败: {} - {}", stepId, e.getMessage(), e);
                
                if (shouldStopOnError(chainConfig)) {
                    throw new RuntimeException("步骤执行失败: " + stepId, e);
                }
            }
        }

        log.info("顺序执行完成: {}", executionId);
        return finalResult;
    }

    /**
     * 异步执行单个步骤
     */
    private CompletableFuture<Map<String, Object>> executeStepAsync(
            String executionId,
            ServiceChainDTO.ChainStepDTO step,
            Map<String, Object> context,
            ServiceChainDTO.ChainConfigDTO chainConfig) {
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                log.debug("异步执行步骤: {} - {}", step.getStepId(), step.getName());
                
                // 评估执行条件
                if (!parameterMapper.evaluateCondition(step.getCondition(), context)) {
                    log.info("步骤条件不满足，跳过: {}", step.getStepId());
                    return new HashMap<>();
                }

                // 执行步骤
                return stepExecutionService.executeStep(executionId, step, context, chainConfig);
                
            } catch (Exception e) {
                log.error("异步步骤执行失败: {} - {}", step.getStepId(), e.getMessage(), e);
                throw new RuntimeException("异步步骤执行失败: " + step.getStepId(), e);
            }
        }, stepExecutor);
    }

    /**
     * 处理步骤结果
     */
    private void processStepResult(String executionId, ServiceChainDTO.ChainStepDTO step, 
                                  Map<String, Object> stepResult, Map<String, Object> context) {
        // 存储步骤结果
        contextManager.setStepResult(executionId, step.getStepId(), stepResult);
        
        // 处理输出映射
        if (step.getConfig() != null && step.getConfig().getOutputMapping() != null) {
            Map<String, Object> mappedResults = parameterMapper.mapOutputResults(
                    stepResult, step.getConfig().getOutputMapping());
            
            // 更新上下文
            for (Map.Entry<String, Object> entry : mappedResults.entrySet()) {
                contextManager.setContextValue(executionId, entry.getKey(), entry.getValue());
                context.put(entry.getKey(), entry.getValue());
            }
        }
    }

    /**
     * 构建步骤映射
     */
    private Map<String, ServiceChainDTO.ChainStepDTO> buildStepMap(List<ServiceChainDTO.ChainStepDTO> steps) {
        return steps.stream()
                .collect(Collectors.toMap(
                        ServiceChainDTO.ChainStepDTO::getStepId,
                        step -> step
                ));
    }

    /**
     * 构建依赖关系图
     */
    private Map<String, Set<String>> buildDependencyGraph(List<ServiceChainDTO.ChainStepDTO> steps) {
        Map<String, Set<String>> graph = new HashMap<>();
        
        for (ServiceChainDTO.ChainStepDTO step : steps) {
            String stepId = step.getStepId();
            Set<String> dependencies = new HashSet<>();
            
            if (step.getDependsOn() != null) {
                dependencies.addAll(step.getDependsOn());
            }
            
            graph.put(stepId, dependencies);
        }
        
        return graph;
    }

    /**
     * 验证依赖关系
     */
    private void validateDependencies(Map<String, ServiceChainDTO.ChainStepDTO> stepMap, 
                                    Map<String, Set<String>> dependencyGraph) {
        // 检查循环依赖
        if (hasCyclicDependency(dependencyGraph)) {
            throw new IllegalArgumentException("服务链存在循环依赖");
        }
        
        // 检查依赖的步骤是否存在
        for (Map.Entry<String, Set<String>> entry : dependencyGraph.entrySet()) {
            for (String dependency : entry.getValue()) {
                if (!stepMap.containsKey(dependency)) {
                    throw new IllegalArgumentException("依赖的步骤不存在: " + dependency);
                }
            }
        }
    }

    /**
     * 检查循环依赖
     */
    private boolean hasCyclicDependency(Map<String, Set<String>> graph) {
        Set<String> visited = new HashSet<>();
        Set<String> recursionStack = new HashSet<>();
        
        for (String node : graph.keySet()) {
            if (hasCyclicDependencyUtil(node, graph, visited, recursionStack)) {
                return true;
            }
        }
        
        return false;
    }

    private boolean hasCyclicDependencyUtil(String node, Map<String, Set<String>> graph, 
                                          Set<String> visited, Set<String> recursionStack) {
        if (recursionStack.contains(node)) {
            return true;
        }
        
        if (visited.contains(node)) {
            return false;
        }
        
        visited.add(node);
        recursionStack.add(node);
        
        Set<String> dependencies = graph.get(node);
        if (dependencies != null) {
            for (String dependency : dependencies) {
                if (hasCyclicDependencyUtil(dependency, graph, visited, recursionStack)) {
                    return true;
                }
            }
        }
        
        recursionStack.remove(node);
        return false;
    }

    /**
     * 获取准备好执行的步骤
     */
    private Set<String> getReadySteps(Set<String> allSteps, Map<String, Set<String>> dependencyGraph, 
                                     Set<String> completedSteps) {
        Set<String> readySteps = new HashSet<>();
        
        for (String step : allSteps) {
            Set<String> dependencies = dependencyGraph.get(step);
            if (dependencies == null || dependencies.isEmpty() || completedSteps.containsAll(dependencies)) {
                readySteps.add(step);
            }
        }
        
        return readySteps;
    }

    /**
     * 拓扑排序
     */
    private List<String> topologicalSort(Set<String> nodes, Map<String, Set<String>> graph) {
        Map<String, Integer> inDegree = new HashMap<>();
        Queue<String> queue = new LinkedList<>();
        List<String> result = new ArrayList<>();
        
        // 计算入度
        for (String node : nodes) {
            inDegree.put(node, 0);
        }
        
        for (Set<String> dependencies : graph.values()) {
            for (String dependency : dependencies) {
                inDegree.put(dependency, inDegree.get(dependency) + 1);
            }
        }
        
        // 找到入度为0的节点
        for (Map.Entry<String, Integer> entry : inDegree.entrySet()) {
            if (entry.getValue() == 0) {
                queue.offer(entry.getKey());
            }
        }
        
        // 拓扑排序
        while (!queue.isEmpty()) {
            String current = queue.poll();
            result.add(current);
            
            Set<String> dependencies = graph.get(current);
            if (dependencies != null) {
                for (String dependency : dependencies) {
                    inDegree.put(dependency, inDegree.get(dependency) - 1);
                    if (inDegree.get(dependency) == 0) {
                        queue.offer(dependency);
                    }
                }
            }
        }
        
        return result;
    }

    /**
     * 检查是否应该在错误时停止
     */
    private boolean shouldStopOnError(ServiceChainDTO.ChainConfigDTO chainConfig) {
        // 这里可以根据全局配置或步骤配置决定错误处理策略
        return true; // 默认遇到错误就停止
    }
}
