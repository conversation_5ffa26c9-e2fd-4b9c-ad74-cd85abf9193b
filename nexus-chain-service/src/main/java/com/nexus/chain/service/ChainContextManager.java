package com.nexus.chain.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 服务链执行上下文管理器
 * 使用Redis存储执行过程中的上下文数据
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ChainContextManager {

    private final RedisTemplate<String, Object> redisTemplate;
    private final ObjectMapper objectMapper;

    @Value("${chain.context.ttl:24}")
    private int contextTtlHours;

    @Value("${chain.context.max-size:10485760}")
    private long maxContextSize;

    private static final String CONTEXT_KEY_PREFIX = "chain_context:";
    private static final String METADATA_KEY = "metadata";
    private static final String INPUT_KEY = "input";
    private static final String STEPS_KEY = "steps";
    private static final String VARIABLES_KEY = "variables";

    /**
     * 初始化执行上下文
     */
    public boolean initializeContext(String executionId, Map<String, Object> initialInput) {
        log.debug("初始化执行上下文: {}", executionId);

        try {
            String contextKey = buildContextKey(executionId);

            // 创建初始上下文结构
            Map<String, Object> context = new HashMap<>();
            
            // 元数据
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("executionId", executionId);
            metadata.put("startTime", System.currentTimeMillis());
            metadata.put("status", "RUNNING");
            metadata.put("version", "1.0");
            context.put(METADATA_KEY, metadata);

            // 输入数据
            context.put(INPUT_KEY, initialInput != null ? initialInput : new HashMap<>());

            // 步骤结果
            context.put(STEPS_KEY, new HashMap<String, Object>());

            // 全局变量
            context.put(VARIABLES_KEY, new HashMap<String, Object>());

            // 存储到Redis
            Map<String, String> flatContext = flattenContext(context);
            redisTemplate.opsForHash().putAll(contextKey, flatContext);
            redisTemplate.expire(contextKey, contextTtlHours, TimeUnit.HOURS);

            log.debug("执行上下文初始化成功: {}", executionId);
            return true;

        } catch (Exception e) {
            log.error("初始化执行上下文失败: {}", executionId, e);
            return false;
        }
    }

    /**
     * 获取完整的执行上下文
     */
    public Map<String, Object> getContext(String executionId) {
        log.debug("获取执行上下文: {}", executionId);

        try {
            String contextKey = buildContextKey(executionId);
            Map<Object, Object> flatContext = redisTemplate.opsForHash().entries(contextKey);

            if (flatContext.isEmpty()) {
                log.warn("执行上下文不存在: {}", executionId);
                return new HashMap<>();
            }

            return unflattenContext(flatContext);

        } catch (Exception e) {
            log.error("获取执行上下文失败: {}", executionId, e);
            return new HashMap<>();
        }
    }

    /**
     * 获取上下文中的特定值
     */
    public Object getContextValue(String executionId, String key) {
        log.debug("获取上下文值: {} - {}", executionId, key);

        try {
            String contextKey = buildContextKey(executionId);
            Object value = redisTemplate.opsForHash().get(contextKey, key);

            if (value instanceof String) {
                try {
                    return objectMapper.readValue((String) value, Object.class);
                } catch (JsonProcessingException e) {
                    return value;
                }
            }

            return value;

        } catch (Exception e) {
            log.error("获取上下文值失败: {} - {}", executionId, key, e);
            return null;
        }
    }

    /**
     * 设置上下文中的值
     */
    public boolean setContextValue(String executionId, String key, Object value) {
        log.debug("设置上下文值: {} - {} = {}", executionId, key, value);

        try {
            String contextKey = buildContextKey(executionId);
            String serializedValue = objectMapper.writeValueAsString(value);
            
            // 检查大小限制
            if (serializedValue.length() > maxContextSize) {
                log.warn("上下文值过大，被截断: {} - {} bytes", key, serializedValue.length());
                serializedValue = serializedValue.substring(0, (int) maxContextSize);
            }

            redisTemplate.opsForHash().put(contextKey, key, serializedValue);
            return true;

        } catch (Exception e) {
            log.error("设置上下文值失败: {} - {}", executionId, key, e);
            return false;
        }
    }

    /**
     * 设置步骤结果
     */
    public boolean setStepResult(String executionId, String stepId, Map<String, Object> result) {
        log.debug("设置步骤结果: {} - {}", executionId, stepId);

        try {
            String stepKey = STEPS_KEY + "." + stepId;
            return setContextValue(executionId, stepKey, result);

        } catch (Exception e) {
            log.error("设置步骤结果失败: {} - {}", executionId, stepId, e);
            return false;
        }
    }

    /**
     * 获取步骤结果
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> getStepResult(String executionId, String stepId) {
        log.debug("获取步骤结果: {} - {}", executionId, stepId);

        try {
            String stepKey = STEPS_KEY + "." + stepId;
            Object result = getContextValue(executionId, stepKey);
            
            if (result instanceof Map) {
                return (Map<String, Object>) result;
            }
            
            return new HashMap<>();

        } catch (Exception e) {
            log.error("获取步骤结果失败: {} - {}", executionId, stepId, e);
            return new HashMap<>();
        }
    }

    /**
     * 设置全局变量
     */
    public boolean setVariable(String executionId, String variableName, Object value) {
        log.debug("设置全局变量: {} - {} = {}", executionId, variableName, value);

        try {
            String variableKey = VARIABLES_KEY + "." + variableName;
            return setContextValue(executionId, variableKey, value);

        } catch (Exception e) {
            log.error("设置全局变量失败: {} - {}", executionId, variableName, e);
            return false;
        }
    }

    /**
     * 获取全局变量
     */
    public Object getVariable(String executionId, String variableName) {
        log.debug("获取全局变量: {} - {}", executionId, variableName);

        try {
            String variableKey = VARIABLES_KEY + "." + variableName;
            return getContextValue(executionId, variableKey);

        } catch (Exception e) {
            log.error("获取全局变量失败: {} - {}", executionId, variableName, e);
            return null;
        }
    }

    /**
     * 更新执行状态
     */
    public boolean updateExecutionStatus(String executionId, String status) {
        log.debug("更新执行状态: {} - {}", executionId, status);

        try {
            String metadataKey = METADATA_KEY + ".status";
            return setContextValue(executionId, metadataKey, status);

        } catch (Exception e) {
            log.error("更新执行状态失败: {} - {}", executionId, status, e);
            return false;
        }
    }

    /**
     * 更新当前步骤
     */
    public boolean updateCurrentStep(String executionId, String stepId) {
        log.debug("更新当前步骤: {} - {}", executionId, stepId);

        try {
            String currentStepKey = METADATA_KEY + ".currentStep";
            return setContextValue(executionId, currentStepKey, stepId);

        } catch (Exception e) {
            log.error("更新当前步骤失败: {} - {}", executionId, stepId, e);
            return false;
        }
    }

    /**
     * 清理执行上下文
     */
    public boolean clearContext(String executionId) {
        log.debug("清理执行上下文: {}", executionId);

        try {
            String contextKey = buildContextKey(executionId);
            Boolean deleted = redisTemplate.delete(contextKey);
            
            log.debug("执行上下文清理完成: {} - {}", executionId, deleted);
            return Boolean.TRUE.equals(deleted);

        } catch (Exception e) {
            log.error("清理执行上下文失败: {}", executionId, e);
            return false;
        }
    }

    /**
     * 检查上下文是否存在
     */
    public boolean contextExists(String executionId) {
        try {
            String contextKey = buildContextKey(executionId);
            return Boolean.TRUE.equals(redisTemplate.hasKey(contextKey));

        } catch (Exception e) {
            log.error("检查上下文存在性失败: {}", executionId, e);
            return false;
        }
    }

    /**
     * 延长上下文TTL
     */
    public boolean extendContextTtl(String executionId, int additionalHours) {
        try {
            String contextKey = buildContextKey(executionId);
            return Boolean.TRUE.equals(redisTemplate.expire(contextKey, 
                    contextTtlHours + additionalHours, TimeUnit.HOURS));

        } catch (Exception e) {
            log.error("延长上下文TTL失败: {}", executionId, e);
            return false;
        }
    }

    /**
     * 构建上下文键
     */
    private String buildContextKey(String executionId) {
        return CONTEXT_KEY_PREFIX + executionId;
    }

    /**
     * 扁平化上下文数据
     */
    private Map<String, String> flattenContext(Map<String, Object> context) throws JsonProcessingException {
        Map<String, String> flatContext = new HashMap<>();
        
        for (Map.Entry<String, Object> entry : context.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            if (value instanceof Map || value instanceof List) {
                flatContext.put(key, objectMapper.writeValueAsString(value));
            } else {
                flatContext.put(key, value != null ? value.toString() : null);
            }
        }
        
        return flatContext;
    }

    /**
     * 反扁平化上下文数据
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> unflattenContext(Map<Object, Object> flatContext) {
        Map<String, Object> context = new HashMap<>();
        
        for (Map.Entry<Object, Object> entry : flatContext.entrySet()) {
            String key = entry.getKey().toString();
            Object value = entry.getValue();
            
            if (value instanceof String) {
                String stringValue = (String) value;
                try {
                    // 尝试解析为JSON
                    Object parsedValue = objectMapper.readValue(stringValue, Object.class);
                    context.put(key, parsedValue);
                } catch (JsonProcessingException e) {
                    // 如果不是JSON，直接存储字符串
                    context.put(key, stringValue);
                }
            } else {
                context.put(key, value);
            }
        }
        
        return context;
    }
}
