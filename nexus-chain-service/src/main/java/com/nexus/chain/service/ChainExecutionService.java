package com.nexus.chain.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nexus.chain.dto.ServiceChainDTO;
import com.nexus.chain.entity.ChainExecution;
import com.nexus.chain.repository.ChainExecutionRepository;
import com.nexus.common.dto.UserDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 服务链执行查询服务
 * 负责执行记录的查询、状态管理等
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(readOnly = true)
public class ChainExecutionService {

    private final ChainExecutionRepository chainExecutionRepository;
    private final ChainContextManager contextManager;
    private final ObjectMapper objectMapper;

    /**
     * 获取执行详情
     */
    @Cacheable(value = "executionDetail", key = "#executionId + '_' + #user.id")
    public ServiceChainDTO.ChainExecutionResponseDTO getExecutionDetail(String executionId, UserDTO user) {
        log.debug("获取执行详情: {} for user: {}", executionId, user.getUsername());

        ChainExecution execution = chainExecutionRepository.findByExecutionId(executionId)
                .orElseThrow(() -> new RuntimeException("执行记录不存在: " + executionId));

        // 验证权限
        if (!execution.getUserId().equals(user.getId())) {
            throw new RuntimeException("无权限访问执行记录: " + executionId);
        }

        return convertToResponseDTO(execution);
    }

    /**
     * 查询用户的执行记录列表
     */
    public ServiceChainDTO.ChainExecutionListResponseDTO getUserExecutions(
            String chainId, UserDTO user, int page, int size) {
        
        log.debug("查询用户执行记录: chainId={}, user={}", chainId, user.getUsername());

        Pageable pageable = PageRequest.of(
                Math.max(0, page - 1), 
                Math.min(size, 100), 
                Sort.by(Sort.Direction.DESC, "createdAt"));

        Page<ChainExecution> executionPage;
        if (chainId != null && !chainId.trim().isEmpty()) {
            executionPage = chainExecutionRepository.findByChainIdAndUserId(chainId, user.getId(), pageable);
        } else {
            executionPage = chainExecutionRepository.findByUserId(user.getId(), pageable);
        }

        List<ServiceChainDTO.ChainExecutionResponseDTO> executions = executionPage.getContent().stream()
                .map(this::convertToResponseDTO)
                .collect(Collectors.toList());

        return ServiceChainDTO.ChainExecutionListResponseDTO.builder()
                .executions(executions)
                .totalCount(executionPage.getTotalElements())
                .currentPage(page)
                .totalPages(executionPage.getTotalPages())
                .pageSize(size)
                .hasNext(executionPage.hasNext())
                .hasPrevious(executionPage.hasPrevious())
                .build();
    }

    /**
     * 获取执行统计信息
     */
    public Map<String, Object> getExecutionStatistics(UserDTO user) {
        log.debug("获取执行统计: user={}", user.getUsername());

        try {
            Long totalExecutions = chainExecutionRepository.countByUserId(user.getId());
            
            List<Object[]> statusCounts = chainExecutionRepository.countExecutionsByStatus();
            Map<String, Long> statusMap = statusCounts.stream()
                    .collect(Collectors.toMap(
                            row -> row[0].toString(),
                            row -> (Long) row[1]
                    ));

            long completedExecutions = statusMap.getOrDefault("COMPLETED", 0L);
            long failedExecutions = statusMap.getOrDefault("FAILED", 0L);
            long runningExecutions = statusMap.getOrDefault("RUNNING", 0L);

            double successRate = totalExecutions > 0 ? 
                    (double) completedExecutions / totalExecutions * 100.0 : 0.0;

            return Map.of(
                    "totalExecutions", totalExecutions,
                    "completedExecutions", completedExecutions,
                    "failedExecutions", failedExecutions,
                    "runningExecutions", runningExecutions,
                    "successRate", successRate,
                    "statusBreakdown", statusMap
            );

        } catch (Exception e) {
            log.error("获取执行统计失败: user={}", user.getUsername(), e);
            return Map.of("error", e.getMessage());
        }
    }

    /**
     * 取消执行
     */
    @Transactional
    public boolean cancelExecution(String executionId, UserDTO user) {
        log.info("取消执行: {} by user: {}", executionId, user.getUsername());

        try {
            ChainExecution execution = chainExecutionRepository.findByExecutionId(executionId)
                    .orElseThrow(() -> new RuntimeException("执行记录不存在: " + executionId));

            // 验证权限
            if (!execution.getUserId().equals(user.getId())) {
                throw new RuntimeException("无权限取消执行: " + executionId);
            }

            // 只能取消运行中或等待中的执行
            if (execution.getStatus() != ChainExecution.ExecutionStatus.RUNNING &&
                execution.getStatus() != ChainExecution.ExecutionStatus.PENDING) {
                throw new RuntimeException("执行状态不允许取消: " + execution.getStatus());
            }

            // 更新状态
            execution.setStatus(ChainExecution.ExecutionStatus.CANCELLED);
            execution.setCompletedAt(LocalDateTime.now());
            if (execution.getStartedAt() != null) {
                execution.setExecutionTimeMs(
                        java.time.Duration.between(execution.getStartedAt(), execution.getCompletedAt()).toMillis());
            }
            chainExecutionRepository.save(execution);

            // 更新上下文状态
            contextManager.updateExecutionStatus(executionId, "CANCELLED");

            log.info("执行取消成功: {}", executionId);
            return true;

        } catch (Exception e) {
            log.error("取消执行失败: {}", executionId, e);
            return false;
        }
    }

    /**
     * 获取执行上下文
     */
    public Map<String, Object> getExecutionContext(String executionId, UserDTO user) {
        log.debug("获取执行上下文: {} for user: {}", executionId, user.getUsername());

        try {
            // 验证执行记录存在且有权限
            ChainExecution execution = chainExecutionRepository.findByExecutionId(executionId)
                    .orElseThrow(() -> new RuntimeException("执行记录不存在: " + executionId));

            if (!execution.getUserId().equals(user.getId())) {
                throw new RuntimeException("无权限访问执行上下文: " + executionId);
            }

            // 获取上下文
            Map<String, Object> context = contextManager.getContext(executionId);
            
            if (context.isEmpty()) {
                log.warn("执行上下文为空或已过期: {}", executionId);
                return Map.of("message", "执行上下文为空或已过期");
            }

            return context;

        } catch (Exception e) {
            log.error("获取执行上下文失败: {}", executionId, e);
            return Map.of("error", e.getMessage());
        }
    }

    /**
     * 清理过期的执行记录
     */
    @Transactional
    public int cleanupExpiredExecutions(int daysToKeep) {
        log.info("清理过期执行记录，保留天数: {}", daysToKeep);

        try {
            LocalDateTime threshold = LocalDateTime.now().minusDays(daysToKeep);
            int deletedCount = chainExecutionRepository.deleteOldExecutions(threshold);
            
            log.info("清理过期执行记录完成，删除数量: {}", deletedCount);
            return deletedCount;

        } catch (Exception e) {
            log.error("清理过期执行记录失败", e);
            return 0;
        }
    }

    /**
     * 重试失败的执行
     */
    @Transactional
    public boolean retryExecution(String executionId, UserDTO user) {
        log.info("重试执行: {} by user: {}", executionId, user.getUsername());

        try {
            ChainExecution execution = chainExecutionRepository.findByExecutionId(executionId)
                    .orElseThrow(() -> new RuntimeException("执行记录不存在: " + executionId));

            // 验证权限
            if (!execution.getUserId().equals(user.getId())) {
                throw new RuntimeException("无权限重试执行: " + executionId);
            }

            // 只能重试失败的执行
            if (execution.getStatus() != ChainExecution.ExecutionStatus.FAILED) {
                throw new RuntimeException("只能重试失败的执行: " + execution.getStatus());
            }

            // 检查是否可以重试
            if (!execution.canRetry()) {
                throw new RuntimeException("执行已达最大重试次数");
            }

            // 增加重试次数
            execution.incrementRetryCount();
            execution.setStatus(ChainExecution.ExecutionStatus.PENDING);
            execution.setErrorMessage(null);
            execution.setErrorStep(null);
            execution.setCompletedAt(null);
            chainExecutionRepository.save(execution);

            // 这里可以触发重新执行的逻辑
            // 例如发送消息到队列或调用执行器

            log.info("执行重试设置成功: {}", executionId);
            return true;

        } catch (Exception e) {
            log.error("重试执行失败: {}", executionId, e);
            return false;
        }
    }

    /**
     * 转换为响应DTO
     */
    private ServiceChainDTO.ChainExecutionResponseDTO convertToResponseDTO(ChainExecution execution) {
        try {
            Map<String, Object> output = null;
            if (execution.getOutputData() != null) {
                output = objectMapper.readValue(execution.getOutputData(), Map.class);
            }

            return ServiceChainDTO.ChainExecutionResponseDTO.builder()
                    .executionId(execution.getExecutionId())
                    .chainId(execution.getChainId())
                    .status(execution.getStatus().name())
                    .output(output)
                    .errorMessage(execution.getErrorMessage())
                    .errorStep(execution.getErrorStep())
                    .totalSteps(execution.getTotalSteps())
                    .completedSteps(execution.getCompletedSteps())
                    .currentStep(execution.getCurrentStep())
                    .progressPercentage(execution.getProgressPercentage())
                    .executionTimeMs(execution.getExecutionTimeMs())
                    .startedAt(execution.getStartedAt())
                    .completedAt(execution.getCompletedAt())
                    .isAsync(execution.getIsAsync())
                    .statusUrl("/api/v1/chains/executions/" + execution.getExecutionId())
                    .build();

        } catch (JsonProcessingException e) {
            log.error("转换执行记录失败: {}", execution.getExecutionId(), e);
            throw new RuntimeException("转换执行记录失败", e);
        }
    }
}
