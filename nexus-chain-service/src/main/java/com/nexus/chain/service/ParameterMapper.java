package com.nexus.chain.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 参数映射服务
 * 使用Spring Expression Language (SpEL) 实现动态参数映射
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ParameterMapper {

    private final ExpressionParser expressionParser = new SpelExpressionParser();

    /**
     * 映射步骤输入参数
     * 将配置中的静态参数和动态参数映射为实际的调用参数
     */
    public Map<String, Object> mapInputParameters(
            Map<String, Object> staticParams,
            Map<String, String> dynamicParams,
            Map<String, Object> context) {
        
        log.debug("开始映射输入参数");
        
        Map<String, Object> mappedParams = new HashMap<>();
        
        // 添加静态参数
        if (staticParams != null) {
            mappedParams.putAll(staticParams);
            log.debug("添加静态参数: {}", staticParams.keySet());
        }
        
        // 处理动态参数
        if (dynamicParams != null) {
            StandardEvaluationContext evalContext = createEvaluationContext(context);
            
            for (Map.Entry<String, String> entry : dynamicParams.entrySet()) {
                String paramName = entry.getKey();
                String expression = entry.getValue();
                
                try {
                    Object value = evaluateExpression(expression, evalContext);
                    mappedParams.put(paramName, value);
                    log.debug("动态参数映射: {} = {} (from: {})", paramName, value, expression);
                } catch (Exception e) {
                    log.warn("动态参数映射失败: {} - {}", paramName, expression, e);
                    // 继续处理其他参数，不因单个参数失败而中断
                }
            }
        }
        
        log.debug("参数映射完成，共 {} 个参数", mappedParams.size());
        return mappedParams;
    }

    /**
     * 映射步骤输出结果
     * 根据输出映射配置，将步骤执行结果映射到上下文变量
     */
    public Map<String, Object> mapOutputResults(
            Map<String, Object> stepResult,
            Map<String, String> outputMapping) {
        
        log.debug("开始映射输出结果");
        
        Map<String, Object> mappedResults = new HashMap<>();
        
        if (outputMapping == null || outputMapping.isEmpty()) {
            log.debug("无输出映射配置，直接返回步骤结果");
            return stepResult != null ? stepResult : new HashMap<>();
        }
        
        if (stepResult == null) {
            log.warn("步骤结果为空，无法进行输出映射");
            return mappedResults;
        }
        
        for (Map.Entry<String, String> entry : outputMapping.entrySet()) {
            String resultKey = entry.getKey();      // 步骤结果中的键
            String contextKey = entry.getValue();   // 上下文中的键
            
            if (stepResult.containsKey(resultKey)) {
                Object value = stepResult.get(resultKey);
                mappedResults.put(contextKey, value);
                log.debug("输出映射: {} -> {} = {}", resultKey, contextKey, value);
            } else {
                log.warn("步骤结果中不存在键: {}", resultKey);
            }
        }
        
        log.debug("输出映射完成，共 {} 个变量", mappedResults.size());
        return mappedResults;
    }

    /**
     * 评估执行条件
     * 使用SpEL表达式评估步骤是否应该执行
     */
    public boolean evaluateCondition(String condition, Map<String, Object> context) {
        if (condition == null || condition.trim().isEmpty()) {
            return true; // 无条件时默认执行
        }
        
        log.debug("评估执行条件: {}", condition);
        
        try {
            StandardEvaluationContext evalContext = createEvaluationContext(context);
            Object result = evaluateExpression(condition, evalContext);
            
            boolean shouldExecute = Boolean.TRUE.equals(result);
            log.debug("条件评估结果: {} -> {}", condition, shouldExecute);
            return shouldExecute;
            
        } catch (Exception e) {
            log.warn("条件评估失败，默认执行: {} - {}", condition, e.getMessage());
            return true; // 条件评估失败时默认执行
        }
    }

    /**
     * 映射输入参数（使用输入映射配置）
     * 根据输入映射配置，从上下文中提取参数
     */
    public Map<String, Object> mapInputFromContext(
            Map<String, String> inputMapping,
            Map<String, Object> context) {
        
        log.debug("开始从上下文映射输入参数");
        
        Map<String, Object> mappedInputs = new HashMap<>();
        
        if (inputMapping == null || inputMapping.isEmpty()) {
            log.debug("无输入映射配置");
            return mappedInputs;
        }
        
        StandardEvaluationContext evalContext = createEvaluationContext(context);
        
        for (Map.Entry<String, String> entry : inputMapping.entrySet()) {
            String paramName = entry.getKey();
            String expression = entry.getValue();
            
            try {
                Object value = evaluateExpression(expression, evalContext);
                mappedInputs.put(paramName, value);
                log.debug("输入映射: {} = {} (from: {})", paramName, value, expression);
            } catch (Exception e) {
                log.warn("输入映射失败: {} - {}", paramName, expression, e);
            }
        }
        
        log.debug("输入映射完成，共 {} 个参数", mappedInputs.size());
        return mappedInputs;
    }

    /**
     * 创建SpEL评估上下文
     */
    private StandardEvaluationContext createEvaluationContext(Map<String, Object> context) {
        StandardEvaluationContext evalContext = new StandardEvaluationContext();
        
        if (context != null) {
            // 设置根对象为整个上下文
            evalContext.setRootObject(context);
            
            // 添加上下文变量
            for (Map.Entry<String, Object> entry : context.entrySet()) {
                evalContext.setVariable(entry.getKey(), entry.getValue());
            }
            
            // 添加常用的辅助变量
            evalContext.setVariable("ctx", context);
            evalContext.setVariable("context", context);
            
            // 添加输入数据的快捷访问
            if (context.containsKey("input")) {
                evalContext.setVariable("input", context.get("input"));
            }
            
            // 添加步骤结果的快捷访问
            if (context.containsKey("steps")) {
                evalContext.setVariable("steps", context.get("steps"));
            }
            
            // 添加全局变量的快捷访问
            if (context.containsKey("variables")) {
                evalContext.setVariable("variables", context.get("variables"));
            }
        }
        
        return evalContext;
    }

    /**
     * 评估SpEL表达式
     */
    private Object evaluateExpression(String expression, StandardEvaluationContext evalContext) {
        try {
            Expression expr = expressionParser.parseExpression(expression);
            return expr.getValue(evalContext);
        } catch (Exception e) {
            log.error("SpEL表达式评估失败: {} - {}", expression, e.getMessage());
            throw new RuntimeException("表达式评估失败: " + expression, e);
        }
    }

    /**
     * 验证SpEL表达式语法
     */
    public boolean validateExpression(String expression) {
        if (expression == null || expression.trim().isEmpty()) {
            return true;
        }
        
        try {
            expressionParser.parseExpression(expression);
            return true;
        } catch (Exception e) {
            log.warn("SpEL表达式语法无效: {} - {}", expression, e.getMessage());
            return false;
        }
    }

    /**
     * 获取表达式中引用的变量名
     */
    public java.util.Set<String> getReferencedVariables(String expression) {
        java.util.Set<String> variables = new java.util.HashSet<>();
        
        if (expression == null || expression.trim().isEmpty()) {
            return variables;
        }
        
        try {
            // 简单的变量提取逻辑，可以根据需要扩展
            String[] tokens = expression.split("[\\s\\+\\-\\*\\/\\(\\)\\[\\]\\{\\}\\,\\;\\:]");
            for (String token : tokens) {
                token = token.trim();
                if (!token.isEmpty() && 
                    !token.matches("\\d+") && // 不是数字
                    !token.matches("'.*'") && // 不是字符串字面量
                    !token.matches("\".*\"") && // 不是字符串字面量
                    !token.equals("true") && 
                    !token.equals("false") && 
                    !token.equals("null")) {
                    variables.add(token);
                }
            }
        } catch (Exception e) {
            log.warn("提取表达式变量失败: {} - {}", expression, e.getMessage());
        }
        
        return variables;
    }

    /**
     * 测试表达式在给定上下文中的计算结果
     */
    public Object testExpression(String expression, Map<String, Object> testContext) {
        try {
            StandardEvaluationContext evalContext = createEvaluationContext(testContext);
            return evaluateExpression(expression, evalContext);
        } catch (Exception e) {
            log.warn("表达式测试失败: {} - {}", expression, e.getMessage());
            return null;
        }
    }
}
