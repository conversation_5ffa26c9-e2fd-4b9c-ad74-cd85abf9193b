package com.nexus.chain.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nexus.chain.client.MCPServiceClient;
import com.nexus.chain.dto.ServiceChainDTO;
import com.nexus.chain.entity.ChainExecution;
import com.nexus.chain.entity.ServiceChain;
import com.nexus.chain.repository.ChainExecutionRepository;
import com.nexus.chain.repository.ServiceChainRepository;
import com.nexus.common.dto.UserDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 服务链执行引擎
 * 负责解析配置、调度步骤、管理执行生命周期
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ServiceChainExecutor {

    private final ServiceChainRepository serviceChainRepository;
    private final ChainExecutionRepository chainExecutionRepository;
    private final ChainContextManager contextManager;
    private final ParameterMapper parameterMapper;
    private final MCPServiceClient mcpServiceClient;
    private final AdvancedChainExecutor advancedChainExecutor;
    private final ObjectMapper objectMapper;

    @Value("${chain.execution.timeout:300000}")
    private long executionTimeoutMs;

    @Value("${chain.execution.max-steps:50}")
    private int maxSteps;

    /**
     * 执行服务链（同步）
     */
    @Transactional
    public ServiceChainDTO.ChainExecutionResponseDTO executeChain(
            String chainId, ServiceChainDTO.ExecuteChainDTO executeDTO, UserDTO user) {
        
        String executionId = UUID.randomUUID().toString();
        LocalDateTime startTime = LocalDateTime.now();
        
        log.info("开始同步执行服务链: {} for user: {} - executionId: {}", 
                chainId, user.getUsername(), executionId);

        try {
            // 1. 加载服务链配置
            ServiceChain serviceChain = loadServiceChain(chainId, user.getId());
            ServiceChainDTO.ChainConfigDTO chainConfig = parseChainConfig(serviceChain.getChainConfig());

            // 2. 创建执行记录
            ChainExecution execution = createExecutionRecord(
                    executionId, serviceChain, user.getId(), executeDTO.getInput(), startTime);

            // 3. 初始化执行上下文
            contextManager.initializeContext(executionId, executeDTO.getInput());

            // 4. 执行服务链步骤（使用高级执行引擎）
            Map<String, Object> result = advancedChainExecutor.executeChainStepsAdvanced(
                    executionId, chainConfig, executeDTO.getInput());

            // 5. 更新执行记录
            LocalDateTime endTime = LocalDateTime.now();
            long durationMs = java.time.Duration.between(startTime, endTime).toMillis();
            
            execution.markAsCompleted(objectMapper.writeValueAsString(result));
            execution.setExecutionTimeMs(durationMs);
            chainExecutionRepository.save(execution);

            // 6. 更新服务链统计
            updateChainStatistics(serviceChain, true);

            // 7. 清理上下文
            contextManager.clearContext(executionId);

            log.info("服务链执行成功: {} - executionId: {} - duration: {}ms", 
                    chainId, executionId, durationMs);

            return ServiceChainDTO.ChainExecutionResponseDTO.builder()
                    .executionId(executionId)
                    .chainId(chainId)
                    .status("COMPLETED")
                    .output(result)
                    .totalSteps(chainConfig.getSteps().size())
                    .completedSteps(chainConfig.getSteps().size())
                    .executionTimeMs(durationMs)
                    .startedAt(startTime)
                    .completedAt(endTime)
                    .isAsync(false)
                    .build();

        } catch (Exception e) {
            log.error("服务链执行失败: {} - executionId: {}", chainId, executionId, e);

            // 更新执行记录为失败状态
            updateExecutionAsFailed(executionId, e.getMessage());
            contextManager.updateExecutionStatus(executionId, "FAILED");

            // 更新服务链统计
            ServiceChain serviceChain = serviceChainRepository.findByChainId(chainId).orElse(null);
            if (serviceChain != null) {
                updateChainStatistics(serviceChain, false);
            }

            return ServiceChainDTO.ChainExecutionResponseDTO.builder()
                    .executionId(executionId)
                    .chainId(chainId)
                    .status("FAILED")
                    .errorMessage(e.getMessage())
                    .startedAt(startTime)
                    .completedAt(LocalDateTime.now())
                    .isAsync(false)
                    .build();
        }
    }

    /**
     * 执行服务链（异步）
     */
    @Transactional
    public ServiceChainDTO.ChainExecutionResponseDTO executeChainAsync(
            String chainId, ServiceChainDTO.ExecuteChainDTO executeDTO, UserDTO user) {
        
        String executionId = UUID.randomUUID().toString();
        LocalDateTime startTime = LocalDateTime.now();
        
        log.info("开始异步执行服务链: {} for user: {} - executionId: {}", 
                chainId, user.getUsername(), executionId);

        try {
            // 创建初始执行记录
            ServiceChain serviceChain = loadServiceChain(chainId, user.getId());
            ChainExecution execution = createExecutionRecord(
                    executionId, serviceChain, user.getId(), executeDTO.getInput(), startTime);
            execution.setStatus(ChainExecution.ExecutionStatus.RUNNING);
            execution.setIsAsync(true);
            chainExecutionRepository.save(execution);

            // 异步执行
            executeChainAsyncInternal(chainId, executeDTO, user, executionId);

            return ServiceChainDTO.ChainExecutionResponseDTO.builder()
                    .executionId(executionId)
                    .chainId(chainId)
                    .status("RUNNING")
                    .startedAt(startTime)
                    .isAsync(true)
                    .statusUrl("/api/v1/chains/executions/" + executionId)
                    .build();

        } catch (Exception e) {
            log.error("异步执行服务链失败: {} - executionId: {}", chainId, executionId, e);
            updateExecutionAsFailed(executionId, e.getMessage());

            return ServiceChainDTO.ChainExecutionResponseDTO.builder()
                    .executionId(executionId)
                    .chainId(chainId)
                    .status("FAILED")
                    .errorMessage(e.getMessage())
                    .startedAt(startTime)
                    .completedAt(LocalDateTime.now())
                    .isAsync(true)
                    .build();
        }
    }

    /**
     * 异步执行内部方法
     */
    @Async
    public void executeChainAsyncInternal(String chainId, ServiceChainDTO.ExecuteChainDTO executeDTO, 
                                         UserDTO user, String executionId) {
        try {
            log.info("异步执行服务链内部方法: {} - executionId: {}", chainId, executionId);
            
            // 加载服务链配置
            ServiceChain serviceChain = loadServiceChain(chainId, user.getId());
            ServiceChainDTO.ChainConfigDTO chainConfig = parseChainConfig(serviceChain.getChainConfig());

            // 初始化执行上下文
            contextManager.initializeContext(executionId, executeDTO.getInput());

            // 执行服务链步骤（使用高级执行引擎）
            Map<String, Object> result = advancedChainExecutor.executeChainStepsAdvanced(
                    executionId, chainConfig, executeDTO.getInput());

            // 更新执行记录为成功
            ChainExecution execution = chainExecutionRepository.findByExecutionId(executionId)
                    .orElseThrow(() -> new RuntimeException("执行记录不存在: " + executionId));
            
            execution.markAsCompleted(objectMapper.writeValueAsString(result));
            if (execution.getStartedAt() != null) {
                execution.setExecutionTimeMs(
                        java.time.Duration.between(execution.getStartedAt(), execution.getCompletedAt()).toMillis());
            }
            chainExecutionRepository.save(execution);

            // 更新服务链统计
            updateChainStatistics(serviceChain, true);

            // 清理上下文
            contextManager.clearContext(executionId);

            log.info("异步服务链执行成功: {} - executionId: {}", chainId, executionId);

        } catch (Exception e) {
            log.error("异步服务链执行失败: {} - executionId: {}", chainId, executionId, e);
            updateExecutionAsFailed(executionId, e.getMessage());
            contextManager.updateExecutionStatus(executionId, "FAILED");

            // 更新服务链统计
            ServiceChain serviceChain = serviceChainRepository.findByChainId(chainId).orElse(null);
            if (serviceChain != null) {
                updateChainStatistics(serviceChain, false);
            }
        }
    }





    /**
     * 加载服务链
     */
    private ServiceChain loadServiceChain(String chainId, Long userId) {
        return serviceChainRepository.findByChainIdAndUserId(chainId, userId)
                .orElseThrow(() -> new RuntimeException("服务链不存在或无权限访问: " + chainId));
    }

    /**
     * 解析服务链配置
     */
    private ServiceChainDTO.ChainConfigDTO parseChainConfig(String chainConfigJson) throws JsonProcessingException {
        return objectMapper.readValue(chainConfigJson, ServiceChainDTO.ChainConfigDTO.class);
    }

    /**
     * 创建执行记录
     */
    private ChainExecution createExecutionRecord(String executionId, ServiceChain serviceChain, 
                                                Long userId, Map<String, Object> input, 
                                                LocalDateTime startTime) throws JsonProcessingException {
        return ChainExecution.builder()
                .executionId(executionId)
                .chainId(serviceChain.getChainId())
                .userId(userId)
                .status(ChainExecution.ExecutionStatus.PENDING)
                .inputData(objectMapper.writeValueAsString(input))
                .totalSteps(0) // 将在解析配置后更新
                .completedSteps(0)
                .startedAt(startTime)
                .isAsync(false)
                .retryCount(0)
                .maxRetries(3)
                .build();
    }

    /**
     * 更新执行记录为失败状态
     */
    @Transactional
    public void updateExecutionAsFailed(String executionId, String errorMessage) {
        chainExecutionRepository.findByExecutionId(executionId)
                .ifPresent(execution -> {
                    execution.markAsFailed(errorMessage, null);
                    chainExecutionRepository.save(execution);
                });
    }

    /**
     * 更新执行进度
     */
    @Transactional
    public void updateExecutionProgress(String executionId, String currentStep, int completedSteps) {
        chainExecutionRepository.findByExecutionId(executionId)
                .ifPresent(execution -> {
                    execution.updateProgress(currentStep, completedSteps);
                    chainExecutionRepository.save(execution);
                });
    }

    /**
     * 更新服务链统计
     */
    @Transactional
    public void updateChainStatistics(ServiceChain serviceChain, boolean success) {
        serviceChain.incrementExecutionCount();
        if (success) {
            serviceChain.incrementSuccessCount();
        }
        serviceChainRepository.save(serviceChain);
    }
}
