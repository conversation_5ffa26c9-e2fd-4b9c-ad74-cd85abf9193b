package com.nexus.chain.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nexus.chain.dto.ServiceChainDTO;
import com.nexus.chain.entity.ServiceChain;
import com.nexus.chain.repository.ServiceChainRepository;
import com.nexus.common.dto.UserDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 服务链管理服务
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(readOnly = true)
public class ServiceChainService {

    private final ServiceChainRepository serviceChainRepository;
    private final ObjectMapper objectMapper;

    /**
     * 创建服务链
     */
    @Transactional
    @CacheEvict(value = "userChains", key = "#user.id")
    public ServiceChainDTO.ServiceChainResponseDTO createServiceChain(
            ServiceChainDTO.CreateServiceChainDTO createDTO, UserDTO user) {
        
        log.info("用户 {} 创建服务链: {}", user.getUsername(), createDTO.getName());

        try {
            // 生成唯一的链ID
            String chainId = generateChainId(user.getId());
            
            // 验证链配置
            validateChainConfig(createDTO.getChainConfig());

            // 构建实体
            ServiceChain serviceChain = ServiceChain.builder()
                    .chainId(chainId)
                    .userId(user.getId())
                    .name(createDTO.getName())
                    .description(createDTO.getDescription())
                    .chainConfig(objectMapper.writeValueAsString(createDTO.getChainConfig()))
                    .templateId(createDTO.getTemplateId())
                    .status(ServiceChain.ChainStatus.ACTIVE)
                    .version(1)
                    .isPublic(createDTO.getIsPublic() != null ? createDTO.getIsPublic() : false)
                    .category(createDTO.getCategory())
                    .tags(createDTO.getTags() != null ? String.join(",", createDTO.getTags()) : null)
                    .executionCount(0L)
                    .successCount(0L)
                    .createdBy(user.getUsername())
                    .updatedBy(user.getUsername())
                    .build();

            // 保存到数据库
            ServiceChain savedChain = serviceChainRepository.save(serviceChain);
            
            log.info("服务链创建成功: {} - {}", savedChain.getChainId(), savedChain.getName());
            return convertToResponseDTO(savedChain);

        } catch (JsonProcessingException e) {
            log.error("序列化服务链配置失败: {}", e.getMessage(), e);
            throw new RuntimeException("服务链配置序列化失败", e);
        } catch (Exception e) {
            log.error("创建服务链失败: {}", e.getMessage(), e);
            throw new RuntimeException("创建服务链失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取服务链详情
     */
    @Cacheable(value = "chainDetail", key = "#chainId + '_' + #user.id")
    public ServiceChainDTO.ServiceChainResponseDTO getServiceChain(String chainId, UserDTO user) {
        log.debug("获取服务链详情: {} for user: {}", chainId, user.getUsername());

        ServiceChain serviceChain = serviceChainRepository.findByChainIdAndUserId(chainId, user.getId())
                .orElseThrow(() -> new RuntimeException("服务链不存在或无权限访问: " + chainId));

        return convertToResponseDTO(serviceChain);
    }

    /**
     * 更新服务链
     */
    @Transactional
    @CacheEvict(value = {"chainDetail", "userChains"}, key = "#chainId")
    public ServiceChainDTO.ServiceChainResponseDTO updateServiceChain(
            String chainId, ServiceChainDTO.UpdateServiceChainDTO updateDTO, UserDTO user) {
        
        log.info("用户 {} 更新服务链: {}", user.getUsername(), chainId);

        ServiceChain serviceChain = serviceChainRepository.findByChainIdAndUserId(chainId, user.getId())
                .orElseThrow(() -> new RuntimeException("服务链不存在或无权限访问: " + chainId));

        try {
            // 更新字段
            if (updateDTO.getName() != null) {
                serviceChain.setName(updateDTO.getName());
            }
            if (updateDTO.getDescription() != null) {
                serviceChain.setDescription(updateDTO.getDescription());
            }
            if (updateDTO.getChainConfig() != null) {
                validateChainConfig(updateDTO.getChainConfig());
                serviceChain.setChainConfig(objectMapper.writeValueAsString(updateDTO.getChainConfig()));
                serviceChain.setVersion(serviceChain.getVersion() + 1);
            }
            if (updateDTO.getStatus() != null) {
                serviceChain.setStatus(updateDTO.getStatus());
            }
            if (updateDTO.getCategory() != null) {
                serviceChain.setCategory(updateDTO.getCategory());
            }
            if (updateDTO.getTags() != null) {
                serviceChain.setTags(String.join(",", updateDTO.getTags()));
            }
            if (updateDTO.getIsPublic() != null) {
                serviceChain.setIsPublic(updateDTO.getIsPublic());
            }
            
            serviceChain.setUpdatedBy(user.getUsername());

            // 保存更新
            ServiceChain updatedChain = serviceChainRepository.save(serviceChain);
            
            log.info("服务链更新成功: {} - {}", updatedChain.getChainId(), updatedChain.getName());
            return convertToResponseDTO(updatedChain);

        } catch (JsonProcessingException e) {
            log.error("序列化服务链配置失败: {}", e.getMessage(), e);
            throw new RuntimeException("服务链配置序列化失败", e);
        } catch (Exception e) {
            log.error("更新服务链失败: {}", e.getMessage(), e);
            throw new RuntimeException("更新服务链失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除服务链
     */
    @Transactional
    @CacheEvict(value = {"chainDetail", "userChains"}, key = "#chainId")
    public void deleteServiceChain(String chainId, UserDTO user) {
        log.info("用户 {} 删除服务链: {}", user.getUsername(), chainId);

        ServiceChain serviceChain = serviceChainRepository.findByChainIdAndUserId(chainId, user.getId())
                .orElseThrow(() -> new RuntimeException("服务链不存在或无权限访问: " + chainId));

        // 软删除：标记为归档状态
        serviceChain.setStatus(ServiceChain.ChainStatus.ARCHIVED);
        serviceChain.setUpdatedBy(user.getUsername());
        serviceChainRepository.save(serviceChain);

        log.info("服务链删除成功: {} - {}", serviceChain.getChainId(), serviceChain.getName());
    }

    /**
     * 查询用户的服务链列表
     */
    @Cacheable(value = "userChains", key = "#user.id + '_' + #queryDTO.toString()")
    public ServiceChainDTO.ChainListResponseDTO getUserChains(
            ServiceChainDTO.ChainQueryDTO queryDTO, UserDTO user) {
        
        log.debug("查询用户服务链: {} - {}", user.getUsername(), queryDTO);

        // 构建分页参数
        Sort sort = buildSort(queryDTO.getSortBy(), queryDTO.getSortOrder());
        Pageable pageable = PageRequest.of(
                Math.max(0, queryDTO.getPage() - 1), 
                Math.min(queryDTO.getSize(), 100), 
                sort);

        // 查询数据
        Page<ServiceChain> chainPage = serviceChainRepository.findChainsWithFilters(
                user.getId(),
                queryDTO.getCategory(),
                queryDTO.getStatus(),
                queryDTO.getIsPublic(),
                queryDTO.getKeyword(),
                pageable);

        // 转换为DTO
        List<ServiceChainDTO.ServiceChainResponseDTO> chains = chainPage.getContent().stream()
                .map(this::convertToResponseDTO)
                .collect(Collectors.toList());

        return ServiceChainDTO.ChainListResponseDTO.builder()
                .chains(chains)
                .totalCount(chainPage.getTotalElements())
                .currentPage(queryDTO.getPage())
                .totalPages(chainPage.getTotalPages())
                .pageSize(queryDTO.getSize())
                .hasNext(chainPage.hasNext())
                .hasPrevious(chainPage.hasPrevious())
                .build();
    }

    /**
     * 查询公开的服务链列表
     */
    @Cacheable(value = "publicChains", key = "#queryDTO.toString()")
    public ServiceChainDTO.ChainListResponseDTO getPublicChains(ServiceChainDTO.ChainQueryDTO queryDTO) {
        log.debug("查询公开服务链: {}", queryDTO);

        // 构建分页参数
        Sort sort = buildSort(queryDTO.getSortBy(), queryDTO.getSortOrder());
        Pageable pageable = PageRequest.of(
                Math.max(0, queryDTO.getPage() - 1), 
                Math.min(queryDTO.getSize(), 100), 
                sort);

        // 查询数据
        Page<ServiceChain> chainPage = serviceChainRepository.findChainsWithFilters(
                null, // 不限制用户
                queryDTO.getCategory(),
                queryDTO.getStatus(),
                true, // 只查询公开的
                queryDTO.getKeyword(),
                pageable);

        // 转换为DTO
        List<ServiceChainDTO.ServiceChainResponseDTO> chains = chainPage.getContent().stream()
                .map(this::convertToResponseDTO)
                .collect(Collectors.toList());

        return ServiceChainDTO.ChainListResponseDTO.builder()
                .chains(chains)
                .totalCount(chainPage.getTotalElements())
                .currentPage(queryDTO.getPage())
                .totalPages(chainPage.getTotalPages())
                .pageSize(queryDTO.getSize())
                .hasNext(chainPage.hasNext())
                .hasPrevious(chainPage.hasPrevious())
                .build();
    }

    /**
     * 生成唯一的链ID
     */
    private String generateChainId(Long userId) {
        String prefix = "chain_" + userId + "_";
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        return prefix + uuid;
    }

    /**
     * 验证服务链配置
     */
    private void validateChainConfig(ServiceChainDTO.ChainConfigDTO chainConfig) {
        if (chainConfig == null) {
            throw new IllegalArgumentException("服务链配置不能为空");
        }
        
        if (chainConfig.getSteps() == null || chainConfig.getSteps().isEmpty()) {
            throw new IllegalArgumentException("服务链至少需要一个步骤");
        }

        // 验证步骤配置
        for (ServiceChainDTO.ChainStepDTO step : chainConfig.getSteps()) {
            if (step.getStepId() == null || step.getStepId().trim().isEmpty()) {
                throw new IllegalArgumentException("步骤ID不能为空");
            }
            if (step.getServiceName() == null || step.getServiceName().trim().isEmpty()) {
                throw new IllegalArgumentException("服务名称不能为空");
            }
            if (step.getToolName() == null || step.getToolName().trim().isEmpty()) {
                throw new IllegalArgumentException("工具名称不能为空");
            }
        }

        log.debug("服务链配置验证通过，共 {} 个步骤", chainConfig.getSteps().size());
    }

    /**
     * 构建排序参数
     */
    private Sort buildSort(String sortBy, String sortOrder) {
        Sort.Direction direction = "asc".equalsIgnoreCase(sortOrder) ? 
                Sort.Direction.ASC : Sort.Direction.DESC;
        
        String sortField = sortBy != null ? sortBy.toLowerCase() : "createdat";
        String property;
        switch (sortField) {
            case "name":
                property = "name";
                break;
            case "updatedat":
                property = "updatedAt";
                break;
            case "executioncount":
                property = "executionCount";
                break;
            case "successrate":
                property = "successCount";
                break;
            default:
                property = "createdAt";
                break;
        }
        
        return Sort.by(direction, property);
    }

    /**
     * 转换为响应DTO
     */
    private ServiceChainDTO.ServiceChainResponseDTO convertToResponseDTO(ServiceChain serviceChain) {
        try {
            ServiceChainDTO.ChainConfigDTO chainConfig = null;
            if (serviceChain.getChainConfig() != null) {
                chainConfig = objectMapper.readValue(
                        serviceChain.getChainConfig(), 
                        ServiceChainDTO.ChainConfigDTO.class);
            }

            List<String> tags = null;
            if (serviceChain.getTags() != null && !serviceChain.getTags().trim().isEmpty()) {
                tags = List.of(serviceChain.getTags().split(","));
            }

            return ServiceChainDTO.ServiceChainResponseDTO.builder()
                    .id(serviceChain.getId())
                    .chainId(serviceChain.getChainId())
                    .userId(serviceChain.getUserId())
                    .name(serviceChain.getName())
                    .description(serviceChain.getDescription())
                    .chainConfig(chainConfig)
                    .templateId(serviceChain.getTemplateId())
                    .status(serviceChain.getStatus())
                    .version(serviceChain.getVersion())
                    .isPublic(serviceChain.getIsPublic())
                    .tags(tags)
                    .category(serviceChain.getCategory())
                    .executionCount(serviceChain.getExecutionCount())
                    .successCount(serviceChain.getSuccessCount())
                    .successRate(serviceChain.getSuccessRate())
                    .lastExecutedAt(serviceChain.getLastExecutedAt())
                    .createdAt(serviceChain.getCreatedAt())
                    .updatedAt(serviceChain.getUpdatedAt())
                    .createdBy(serviceChain.getCreatedBy())
                    .updatedBy(serviceChain.getUpdatedBy())
                    .build();

        } catch (JsonProcessingException e) {
            log.error("反序列化服务链配置失败: {}", e.getMessage(), e);
            throw new RuntimeException("服务链配置反序列化失败", e);
        }
    }
}
