package com.nexus.chain.service;

import com.nexus.chain.client.MCPServiceClient;
import com.nexus.chain.dto.ServiceChainDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 步骤执行服务
 * 负责单个步骤的执行逻辑，包括超时控制、重试机制等
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class StepExecutionService {

    private final MCPServiceClient mcpServiceClient;
    private final ParameterMapper parameterMapper;
    private final ChainContextManager contextManager;

    /**
     * 执行单个步骤
     */
    public Map<String, Object> executeStep(
            String executionId,
            ServiceChainDTO.ChainStepDTO step,
            Map<String, Object> context,
            ServiceChainDTO.ChainConfigDTO chainConfig) throws Exception {
        
        log.info("执行步骤: {} - {}", step.getStepId(), step.getName());

        // 更新当前步骤
        contextManager.updateCurrentStep(executionId, step.getStepId());

        // 构建步骤参数
        Map<String, Object> stepParams = buildStepParameters(step, context);

        // 执行步骤（带重试）
        Map<String, Object> stepResult = executeStepWithRetry(step, stepParams);

        // 验证结果
        validateStepResult(step, stepResult);

        log.info("步骤执行成功: {} - {}", step.getStepId(), step.getName());
        return stepResult;
    }

    /**
     * 带重试的步骤执行
     */
    private Map<String, Object> executeStepWithRetry(
            ServiceChainDTO.ChainStepDTO step,
            Map<String, Object> parameters) throws Exception {
        
        int maxRetries = step.getRetries() != null ? step.getRetries() : 0;
        int currentRetry = 0;
        Exception lastException = null;

        while (currentRetry <= maxRetries) {
            try {
                log.debug("执行步骤尝试: {} - 第 {}/{} 次", 
                         step.getStepId(), currentRetry + 1, maxRetries + 1);

                // 执行步骤
                Map<String, Object> result = executeStepInternal(step, parameters);
                
                if (currentRetry > 0) {
                    log.info("步骤重试成功: {} - 第 {} 次尝试", step.getStepId(), currentRetry + 1);
                }
                
                return result;

            } catch (Exception e) {
                lastException = e;
                currentRetry++;
                
                log.warn("步骤执行失败: {} - 第 {}/{} 次尝试 - {}", 
                        step.getStepId(), currentRetry, maxRetries + 1, e.getMessage());

                if (currentRetry <= maxRetries) {
                    // 计算重试延迟（指数退避）
                    long delayMs = calculateRetryDelay(currentRetry);
                    
                    log.info("步骤将在 {}ms 后重试: {}", delayMs, step.getStepId());
                    
                    try {
                        Thread.sleep(delayMs);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("步骤重试被中断: " + step.getStepId(), ie);
                    }
                }
            }
        }

        // 所有重试都失败了
        throw new RuntimeException("步骤执行失败，已达最大重试次数: " + step.getStepId(), lastException);
    }

    /**
     * 内部步骤执行逻辑
     */
    private Map<String, Object> executeStepInternal(
            ServiceChainDTO.ChainStepDTO step,
            Map<String, Object> parameters) throws Exception {
        
        // 获取超时配置
        long timeoutMs = getStepTimeout(step);
        
        log.debug("调用MCP服务: {} - {} (timeout: {}ms)", 
                 step.getServiceName(), step.getToolName(), timeoutMs);

        // 执行MCP调用（带超时）
        Map<String, Object> result = executeWithTimeout(() -> {
            return callMCPService(step, parameters);
        }, timeoutMs);

        // 检查结果中是否有错误
        if (result.containsKey("error")) {
            String errorMessage = result.get("error").toString();
            throw new RuntimeException("MCP服务调用失败: " + errorMessage);
        }

        return result;
    }

    /**
     * 带超时的执行
     */
    private Map<String, Object> executeWithTimeout(
            java.util.concurrent.Callable<Map<String, Object>> task,
            long timeoutMs) throws Exception {
        
        java.util.concurrent.ExecutorService executor = 
                java.util.concurrent.Executors.newSingleThreadExecutor();
        
        try {
            java.util.concurrent.Future<Map<String, Object>> future = executor.submit(task);
            return future.get(timeoutMs, TimeUnit.MILLISECONDS);
            
        } catch (java.util.concurrent.TimeoutException e) {
            throw new RuntimeException("步骤执行超时: " + timeoutMs + "ms");
        } finally {
            executor.shutdown();
        }
    }

    /**
     * 调用MCP服务
     */
    private Map<String, Object> callMCPService(
            ServiceChainDTO.ChainStepDTO step,
            Map<String, Object> parameters) {
        
        log.debug("调用MCP服务: {} - {}", step.getServiceName(), step.getToolName());
        
        // 检查是否为异步步骤
        boolean isAsync = step.getConfig() != null && 
                         Boolean.TRUE.equals(step.getConfig().getAsync());
        
        if (isAsync) {
            return callMCPServiceAsync(step, parameters);
        } else {
            return callMCPServiceSync(step, parameters);
        }
    }

    /**
     * 同步调用MCP服务
     */
    private Map<String, Object> callMCPServiceSync(
            ServiceChainDTO.ChainStepDTO step,
            Map<String, Object> parameters) {
        
        // 先尝试本地服务
        Map<String, Object> result = mcpServiceClient.callMCPTool(
                step.getServiceName(), step.getToolName(), parameters, false);
        
        // 如果本地服务失败，尝试远程服务
        if (result.containsKey("error")) {
            log.debug("本地MCP服务调用失败，尝试远程服务: {} - {}", 
                     step.getServiceName(), step.getToolName());
            result = mcpServiceClient.callMCPTool(
                    step.getServiceName(), step.getToolName(), parameters, true);
        }
        
        return result;
    }

    /**
     * 异步调用MCP服务
     */
    private Map<String, Object> callMCPServiceAsync(
            ServiceChainDTO.ChainStepDTO step,
            Map<String, Object> parameters) {
        
        log.debug("异步调用MCP服务: {} - {}", step.getServiceName(), step.getToolName());
        
        // 先尝试本地异步服务
        Map<String, Object> result = mcpServiceClient.callMCPToolAsync(
                step.getServiceName(), step.getToolName(), parameters, false);
        
        if (result.containsKey("error") || !Boolean.TRUE.equals(result.get("success"))) {
            log.debug("本地异步MCP服务调用失败，尝试远程服务: {} - {}", 
                     step.getServiceName(), step.getToolName());
            result = mcpServiceClient.callMCPToolAsync(
                    step.getServiceName(), step.getToolName(), parameters, true);
        }
        
        // 如果是异步调用，需要轮询结果
        if (Boolean.TRUE.equals(result.get("success")) && result.containsKey("taskId")) {
            String taskId = result.get("taskId").toString();
            return pollAsyncTaskResult(taskId, step);
        }
        
        return result;
    }

    /**
     * 轮询异步任务结果
     */
    private Map<String, Object> pollAsyncTaskResult(String taskId, ServiceChainDTO.ChainStepDTO step) {
        log.debug("轮询异步任务结果: {} for step: {}", taskId, step.getStepId());
        
        long timeoutMs = getStepTimeout(step);
        long startTime = System.currentTimeMillis();
        long pollInterval = 1000; // 1秒轮询间隔
        
        while (System.currentTimeMillis() - startTime < timeoutMs) {
            try {
                // 检查任务状态
                Map<String, Object> statusResult = mcpServiceClient.getAsyncTaskStatus(taskId, false);
                
                if (!Boolean.TRUE.equals(statusResult.get("success"))) {
                    // 尝试远程服务
                    statusResult = mcpServiceClient.getAsyncTaskStatus(taskId, true);
                }
                
                if (Boolean.TRUE.equals(statusResult.get("success"))) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> data = (Map<String, Object>) statusResult.get("data");
                    String status = data.get("status").toString();
                    
                    if ("COMPLETED".equals(status)) {
                        log.debug("异步任务完成: {} for step: {}", taskId, step.getStepId());
                        return (Map<String, Object>) data.get("result");
                    } else if ("FAILED".equals(status)) {
                        String errorMessage = data.get("error").toString();
                        throw new RuntimeException("异步任务失败: " + errorMessage);
                    }
                    // 如果状态是RUNNING或PENDING，继续轮询
                }
                
                Thread.sleep(pollInterval);
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("异步任务轮询被中断: " + taskId);
            } catch (Exception e) {
                log.warn("轮询异步任务状态失败: {} - {}", taskId, e.getMessage());
                // 继续轮询
            }
        }
        
        throw new RuntimeException("异步任务超时: " + taskId);
    }

    /**
     * 构建步骤参数
     */
    private Map<String, Object> buildStepParameters(
            ServiceChainDTO.ChainStepDTO step,
            Map<String, Object> context) {
        
        Map<String, Object> stepParams = new HashMap<>();
        
        if (step.getConfig() != null) {
            // 处理输入映射
            if (step.getConfig().getInputMapping() != null) {
                Map<String, Object> mappedInputs = parameterMapper.mapInputFromContext(
                        step.getConfig().getInputMapping(), context);
                stepParams.putAll(mappedInputs);
            }
            
            // 处理参数映射
            Map<String, Object> mappedParams = parameterMapper.mapInputParameters(
                    step.getConfig().getStaticParams(),
                    step.getConfig().getDynamicParams(),
                    context);
            stepParams.putAll(mappedParams);
        }
        
        return stepParams;
    }

    /**
     * 验证步骤结果
     */
    private void validateStepResult(ServiceChainDTO.ChainStepDTO step, Map<String, Object> result) {
        if (result == null) {
            throw new RuntimeException("步骤结果为空: " + step.getStepId());
        }
        
        // 这里可以添加更多的结果验证逻辑
        // 例如检查必需的输出字段、数据格式等
    }

    /**
     * 获取步骤超时时间
     */
    private long getStepTimeout(ServiceChainDTO.ChainStepDTO step) {
        if (step.getTimeout() != null && step.getTimeout() > 0) {
            return step.getTimeout();
        }
        
        if (step.getConfig() != null && step.getConfig().getTimeout() != null && 
            step.getConfig().getTimeout() > 0) {
            return step.getConfig().getTimeout();
        }
        
        return 30000; // 默认30秒
    }

    /**
     * 计算重试延迟（指数退避）
     */
    private long calculateRetryDelay(int retryCount) {
        // 指数退避：1s, 2s, 4s, 8s, ...
        return Math.min(1000L * (1L << (retryCount - 1)), 30000L); // 最大30秒
    }
}
