server:
  port: 8086
  servlet:
    context-path: /chain

spring:
  application:
    name: nexus-chain-service

  profiles:
    active: nacos

  config:
    import: "nacos:nexus-chain-service.yml"

  # Spring Cloud Nacos配置
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        namespace: nexus-microservices
        group: DEFAULT_GROUP
        service: ${spring.application.name}
        metadata:
          version: 1.0.0
          zone: default
      config:
        server-addr: 127.0.0.1:8848
        namespace: nexus-microservices
        group: DEFAULT_GROUP
        file-extension: yml
        shared-configs:
          - data-id: nexus-common-config.yml
            group: DEFAULT_GROUP
            refresh: true

  # Fallback配置，当Nacos配置解析失败时使用
  datasource:
    url: *****************************************************************************************************************************************************************************
    username: neondb_owner
    password: npg_kc2S7QGCPbEh
    driver-class-name: org.postgresql.Driver
    hikari:
      pool-name: NexusMicroservicesPool
      maximum-pool-size: 10
      minimum-idle: 3
      connection-timeout: 30000
      idle-timeout: 120000
      max-lifetime: 300000

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQL10Dialect
        format_sql: false

  redis:
    host: localhost
    port: 6379
    database: 5
    timeout: 5000ms

  # 暂时禁用RocketMQ以避免启动阻塞
  # rocketmq:
  #   name-server: localhost:9876
  #   producer:
  #     group: nexus-producer-group
  #   consumer:
  #     group: nexus-consumer-group

# Chain service specific configuration
nexus:
  chain:
    service-chain:
      enabled: true
      max-chain-length: 10
      execution-timeout: 300000
    execution:
      retry-enabled: true
      max-retry-attempts: 3
      retry-delay: 1000
    storage:
      enabled: true
      max-stored-chains: 1000
    monitoring:
      metrics-enabled: true
      performance-tracking: true
    security:
      validation-enabled: true
      sandbox-mode: true

  redis:
    host: localhost
    port: 6379
    password: 
    database: 3
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 5000ms

  cache:
    type: redis
    redis:
      time-to-live: 300000  # 5分钟缓存

# 服务间调用配置
service:
  auth:
    url: http://nexus-auth-service:8081
  subscription:
    url: http://nexus-subscription-service:8084
  mcp-local:
    url: http://nexus-mcp-local-service:8082
  mcp-remote:
    url: http://nexus-mcp-remote-service:8083

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# 日志配置
logging:
  level:
    com.nexus.chain: DEBUG
    org.springframework.amqp: INFO
    org.springframework.cache: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{50}] - %msg%n"

# 业务配置
chain:
  execution:
    timeout: 300000  # 执行超时时间(毫秒) - 5分钟
    max-steps: 50    # 最大步骤数
    max-parallel: 10 # 最大并行步骤数
  context:
    ttl: 24          # 上下文TTL(小时)
    max-size: 10485760  # 上下文最大大小(字节) - 10MB
  async:
    core-pool-size: 5
    max-pool-size: 20
    queue-capacity: 100
