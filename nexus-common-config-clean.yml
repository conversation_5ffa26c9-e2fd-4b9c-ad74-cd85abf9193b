spring:
  main:
    web-application-type: servlet
    allow-bean-definition-overriding: true
    allow-circular-references: true

  http:
    encoding:
      charset: UTF-8
      enabled: true
      force: true

  datasource:
    url: *****************************************************************************************************************************************************************************
    username: neondb_owner
    password: npg_kc2S7QGCPbEh
    driver-class-name: org.postgresql.Driver
    hikari:
      pool-name: NexusMicroservicesPool
      maximum-pool-size: 10
      minimum-idle: 3
      connection-timeout: 30000
      idle-timeout: 120000
      max-lifetime: 300000

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    open-in-view: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQL10Dialect
        format_sql: false

  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 3000ms

  rocketmq:
    name-server: localhost:9876
    producer:
      group: nexus-producer-group
      send-message-timeout: 3000
      retry-times-when-send-failed: 2
      retry-times-when-send-async-failed: 2
      max-message-size: 4194304
    consumer:
      group: nexus-consumer-group
      pull-batch-size: 32
      name-server: localhost:9876

  cache:
    type: redis
    redis:
      time-to-live: 1800000
      cache-null-values: false

nexus:
  auth:
    jwt:
      secret: nexus-microservices-jwt-secret-key-2024
      expiration: 86400
      refresh-expiration: 604800

  email:
    enabled: false
    mock: true
    from: <EMAIL>
    from-name: Nexus Microservices Platform

  rocketmq:
    subscription:
      enabled: true
    mcp:
      enabled: true
    user:
      enabled: true
    notification:
      enabled: true

  mcp:
    discovery:
      enabled: true
      health-check-interval: 60000
    local:
      enabled: true
      base-path: /opt/mcp-servers
    remote:
      enabled: true
      timeout: 30000

logging:
  level:
    root: INFO
    com.nexus: DEBUG
    io.grpc: INFO
  charset:
    console: UTF-8
    file: UTF-8

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,refresh
  endpoint:
    health:
      show-details: when-authorized
  health:
    defaults:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
