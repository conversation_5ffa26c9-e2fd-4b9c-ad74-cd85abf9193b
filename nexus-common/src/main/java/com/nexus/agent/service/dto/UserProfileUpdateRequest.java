package com.nexus.agent.service.dto;

import lombok.Data;
import java.util.Map;
import java.io.Serializable;

/**
 * 用户资料更新请求DTO (迁移到 Common 模块)
 */
@Data
public class UserProfileUpdateRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    private String email;
    private String fullName;
    private String avatarUrl;
    private Map<String, String> metadata;
    private String currentPassword;
    private String newPassword;
}