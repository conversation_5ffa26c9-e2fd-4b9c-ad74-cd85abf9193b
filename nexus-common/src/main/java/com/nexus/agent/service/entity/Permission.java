package com.nexus.agent.service.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.io.Serializable;

/**
 * 权限实体类 (迁移到 Common 模块)
 * 管理用户对MCP服务工具和资源的细粒度权限控制
 */
@Entity
@Table(name = "permissions", indexes = {
    @Index(name = "idx_permission_subscription", columnList = "subscription_id"),
    @Index(name = "idx_permission_type", columnList = "permissionType"),
    @Index(name = "idx_permission_resource", columnList = "resourceName")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Permission implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 所属订阅
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "subscription_id", nullable = false)
    @NotNull(message = "订阅不能为空")
    private Subscription subscription;

    /**
     * 权限类型
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private PermissionType permissionType;

    /**
     * 资源名称（工具名或资源名）
     */
    @Column(nullable = false, length = 200)
    @NotBlank(message = "资源名称不能为空")
    private String resourceName;

    /**
     * 权限操作
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private PermissionAction action;

    /**
     * 权限描述
     */
    @Column(length = 500)
    private String description;

    /**
     * 权限参数（JSON格式，用于存储额外的权限配置）
     */
    @Column(columnDefinition = "TEXT")
    private String parameters;

    /**
     * 是否启用
     */
    @Builder.Default
    private Boolean enabled = true;

    /**
     * 权限创建时间
     */
    @CreationTimestamp
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 权限更新时间
     */
    @UpdateTimestamp
    @Column(nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 权限类型枚举
     */
    public enum PermissionType {
        TOOL("工具权限", "对MCP服务工具的访问权限"),
        RESOURCE("资源权限", "对MCP服务资源的访问权限"),
        SERVICE("服务权限", "对整个MCP服务的访问权限");

        private final String displayName;
        private final String description;

        PermissionType(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 权限操作枚举
     */
    public enum PermissionAction {
        READ("读取", "只读访问权限"),
        WRITE("写入", "写入访问权限"),
        EXECUTE("执行", "执行访问权限"),
        DELETE("删除", "删除访问权限"),
        FULL("完全", "完全访问权限"),
        DENY("拒绝", "拒绝访问权限");

        private final String displayName;
        private final String description;

        PermissionAction(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 检查权限是否有效
     */
    public boolean isValid() {
        return enabled && subscription != null && subscription.isValid();
    }

    /**
     * 检查是否允许指定操作
     */
    public boolean allowsAction(PermissionAction requestedAction) {
        if (!isValid()) {
            return false;
        }

        if (PermissionAction.DENY.equals(this.action)) {
            return false;
        }

        if (PermissionAction.FULL.equals(this.action)) {
            return true;
        }

        return this.action.equals(requestedAction);
    }

    /**
     * 检查是否为工具权限
     */
    public boolean isToolPermission() {
        return PermissionType.TOOL.equals(this.permissionType);
    }

    /**
     * 检查是否为资源权限
     */
    public boolean isResourcePermission() {
        return PermissionType.RESOURCE.equals(this.permissionType);
    }

    /**
     * 检查是否为服务权限
     */
    public boolean isServicePermission() {
        return PermissionType.SERVICE.equals(this.permissionType);
    }

    /**
     * 启用权限
     */
    public void enable() {
        this.enabled = true;
    }

    /**
     * 禁用权限
     */
    public void disable() {
        this.enabled = false;
    }

    /**
     * 创建工具权限
     */
    public static Permission createToolPermission(Subscription subscription, String toolName, PermissionAction action) {
        return Permission.builder()
                .subscription(subscription)
                .permissionType(PermissionType.TOOL)
                .resourceName(toolName)
                .action(action)
                .description("工具 " + toolName + " 的 " + action.getDisplayName() + " 权限")
                .build();
    }

    /**
     * 创建资源权限
     */
    public static Permission createResourcePermission(Subscription subscription, String resourceName, PermissionAction action) {
        return Permission.builder()
                .subscription(subscription)
                .permissionType(PermissionType.RESOURCE)
                .resourceName(resourceName)
                .action(action)
                .description("资源 " + resourceName + " 的 " + action.getDisplayName() + " 权限")
                .build();
    }

    /**
     * 创建服务权限
     */
    public static Permission createServicePermission(Subscription subscription, String serviceName, PermissionAction action) {
        return Permission.builder()
                .subscription(subscription)
                .permissionType(PermissionType.SERVICE)
                .resourceName(serviceName)
                .action(action)
                .description("服务 " + serviceName + " 的 " + action.getDisplayName() + " 权限")
                .build();
    }
}