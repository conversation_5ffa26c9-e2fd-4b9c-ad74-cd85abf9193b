package com.nexus.agent.service.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;
import java.io.Serializable;

/**
 * MCP服务配置实体类 (迁移到 Common 模块)
 * 存储MCP服务的配置信息和元数据
 */
@Entity
@Table(name = "service_configs", indexes = {
    @Index(name = "idx_service_name", columnList = "serviceName"),
    @Index(name = "idx_service_type", columnList = "serviceType"),
    @Index(name = "idx_service_status", columnList = "status")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 服务名称，唯一标识
     */
    @Column(unique = true, nullable = false, length = 100)
    @NotBlank(message = "服务名称不能为空")
    @Size(min = 1, max = 100, message = "服务名称长度必须在1-100个字符之间")
    private String serviceName;

    /**
     * 服务显示名称
     */
    @Column(nullable = false, length = 200)
    @NotBlank(message = "服务显示名称不能为空")
    private String displayName;

    /**
     * 服务描述
     */
    @Column(length = 1000)
    private String description;

    /**
     * 服务类型
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private ServiceType serviceType;

    /**
     * 服务版本
     */
    @Column(length = 50)
    private String version;

    /**
     * 服务命令（用于启动服务）
     */
    @Column(length = 500)
    private String command;

    /**
     * 服务配置参数（JSON格式）
     */
    @Column(columnDefinition = "TEXT")
    private String configParams;

    /**
     * 服务状态
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Builder.Default
    private ServiceStatus status = ServiceStatus.INACTIVE;

    /**
     * 服务端点URL（对于远程服务）
     */
    @Column(length = 500)
    private String endpoint;

    /**
     * 服务图标URL
     */
    @Column(length = 500)
    private String iconUrl;

    /**
     * 服务文档URL
     */
    @Column(length = 500)
    private String documentationUrl;

    /**
     * 服务标签（用于分类和搜索）
     */
    @Column(length = 500)
    private String tags;

    /**
     * 是否为公共服务（所有用户可见）
     */
    @Builder.Default
    private Boolean isPublic = false;

    /**
     * 服务优先级（用于排序）
     */
    @Builder.Default
    private Integer priority = 0;

    /**
     * 最大并发连接数
     */
    @Builder.Default
    private Integer maxConnections = 10;

    /**
     * 超时时间（秒）
     */
    @Builder.Default
    private Integer timeoutSeconds = 30;

    /**
     * 服务创建时间
     */
    @CreationTimestamp
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 服务更新时间
     */
    @UpdateTimestamp
    @Column(nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 服务的订阅列表
     */
    @OneToMany(mappedBy = "serviceConfig", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Builder.Default
    private Set<Subscription> subscriptions = new HashSet<>();

    /**
     * 服务类型枚举
     */
    public enum ServiceType {
        SERVER_SIDE("服务端服务", "部署在服务端的MCP服务"),
        LOCAL_SIDE("本地服务", "部署在用户本地的MCP服务"),
        HYBRID("混合服务", "支持服务端和本地部署的MCP服务");

        private final String displayName;
        private final String description;

        ServiceType(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 服务状态枚举
     */
    public enum ServiceStatus {
        ACTIVE("激活"),
        INACTIVE("未激活"),
        MAINTENANCE("维护中"),
        DEPRECATED("已弃用"),
        ERROR("错误");

        private final String description;

        ServiceStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 检查服务是否可用
     */
    public boolean isAvailable() {
        return ServiceStatus.ACTIVE.equals(this.status);
    }

    /**
     * 检查是否为服务端服务
     */
    public boolean isServerSide() {
        return ServiceType.SERVER_SIDE.equals(this.serviceType) || 
               ServiceType.HYBRID.equals(this.serviceType);
    }

    /**
     * 检查是否为本地服务
     */
    public boolean isLocalSide() {
        return ServiceType.LOCAL_SIDE.equals(this.serviceType) || 
               ServiceType.HYBRID.equals(this.serviceType);
    }

    /**
     * 激活服务
     */
    public void activate() {
        this.status = ServiceStatus.ACTIVE;
    }

    /**
     * 停用服务
     */
    public void deactivate() {
        this.status = ServiceStatus.INACTIVE;
    }

    /**
     * 设置为维护状态
     */
    public void setMaintenance() {
        this.status = ServiceStatus.MAINTENANCE;
    }

    /**
     * 标记为已弃用
     */
    public void deprecate() {
        this.status = ServiceStatus.DEPRECATED;
    }

    /**
     * 标记为错误状态
     */
    public void markError() {
        this.status = ServiceStatus.ERROR;
    }
}