package com.nexus.agent.service.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;
import java.io.Serializable;

/**
 * 用户订阅实体类 (迁移到 Common 模块)
 * 管理用户对MCP服务的订阅关系
 */
@Entity
@Table(name = "subscriptions", indexes = {
        @Index(name = "idx_subscription_user", columnList = "user_id"),
        @Index(name = "idx_subscription_service", columnList = "service_config_id"),
        @Index(name = "idx_subscription_status", columnList = "status")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Subscription implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 订阅的用户
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    @NotNull(message = "用户不能为空")
    private User user;

    /**
     * 订阅的服务配置
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "service_config_id", nullable = false)
    @NotNull(message = "服务配置不能为空")
    private ServiceConfig serviceConfig;

    /**
     * 订阅状态
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Builder.Default
    private SubscriptionStatus status = SubscriptionStatus.ACTIVE;

    /**
     * 订阅开始时间
     */
    @Column(nullable = false)
    private LocalDateTime startDate;

    /**
     * 订阅结束时间（null表示永久订阅）
     */
    private LocalDateTime endDate;

    /**
     * 调用次数限制（null表示无限制）
     */
    private Long callLimit;

    /**
     * 已使用的调用次数
     */
    @Builder.Default
    private Long usedCalls = 0L;

    /**
     * 订阅备注
     */
    @Column(length = 500)
    private String notes;

    /**
     * 订阅创建时间
     */
    @CreationTimestamp
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 订阅更新时间
     */
    @UpdateTimestamp
    @Column(nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 订阅的权限列表
     */
    @OneToMany(mappedBy = "subscription", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Builder.Default
    private Set<Permission> permissions = new HashSet<>();

    /**
     * 订阅状态枚举
     */
    public enum SubscriptionStatus {
        ACTIVE("激活"),
        SUSPENDED("暂停"),
        EXPIRED("过期"),
        CANCELLED("取消");

        private final String description;

        SubscriptionStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 检查订阅是否有效
     */
    public boolean isValid() {
        if (!SubscriptionStatus.ACTIVE.equals(this.status)) {
            return false;
        }

        LocalDateTime now = LocalDateTime.now();

        // 检查是否在有效期内
        if (endDate != null && now.isAfter(endDate)) {
            return false;
        }

        // 检查调用次数是否超限
        if (callLimit != null && usedCalls >= callLimit) {
            return false;
        }

        return true;
    }

    /**
     * 增加调用次数
     */
    public void incrementUsedCalls() {
        this.usedCalls++;
    }

    /**
     * 检查是否还有剩余调用次数
     */
    public boolean hasRemainingCalls() {
        if (callLimit == null) {
            return true; // 无限制
        }
        return usedCalls < callLimit;
    }

    /**
     * 获取剩余调用次数
     */
    public Long getRemainingCalls() {
        if (callLimit == null) {
            return null; // 无限制
        }
        return Math.max(0, callLimit - usedCalls);
    }

    /**
     * 检查订阅是否即将过期（7天内）
     */
    public boolean isExpiringSoon() {
        if (endDate == null) {
            return false; // 永久订阅
        }

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime sevenDaysLater = now.plusDays(7);

        return endDate.isBefore(sevenDaysLater) && endDate.isAfter(now);
    }

    /**
     * 暂停订阅
     */
    public void suspend() {
        this.status = SubscriptionStatus.SUSPENDED;
    }

    /**
     * 激活订阅
     */
    public void activate() {
        this.status = SubscriptionStatus.ACTIVE;
    }

    /**
     * 取消订阅
     */
    public void cancel() {
        this.status = SubscriptionStatus.CANCELLED;
    }

    /**
     * 标记为过期
     */
    public void markExpired() {
        this.status = SubscriptionStatus.EXPIRED;
    }

    /**
     * 自定义hashCode方法，避免递归调用
     */
    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }

    /**
     * 自定义equals方法，避免递归调用
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null || getClass() != obj.getClass())
            return false;

        Subscription subscription = (Subscription) obj;

        // 使用id比较
        return id != null && id.equals(subscription.id);
    }
}