package com.nexus.agent.service.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;

/**
 * API密钥生成工具类 (迁移到 Common 模块)
 * 提供安全的API密钥生成和验证功能
 */
@Component
@Slf4j
public class ApiKeyUtil {

    private static final String API_KEY_PREFIX = "nxs_";
    private static final int KEY_LENGTH = 32; // 32字节 = 256位
    private static final SecureRandom secureRandom = new SecureRandom();
    private static final Base64.Encoder encoder = Base64.getUrlEncoder().withoutPadding();

    /**
     * 生成新的API密钥
     * 格式: nxs_<timestamp>_<random_string>
     */
    public String generateApiKey() {
        // 生成时间戳部分（用于排序和调试）
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        
        // 生成随机字节
        byte[] randomBytes = new byte[KEY_LENGTH];
        secureRandom.nextBytes(randomBytes);
        
        // 编码为Base64URL
        String randomPart = encoder.encodeToString(randomBytes);
        
        // 组合最终的API密钥
        String apiKey = API_KEY_PREFIX + timestamp + "_" + randomPart;
        
        log.debug("生成新的API密钥，长度: {}", apiKey.length());
        return apiKey;
    }

    /**
     * 生成用户专用API密钥
     * 格式: nxs_user_<user_id>_<timestamp>_<random_string>
     */
    public String generateUserApiKey(Long userId) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        
        byte[] randomBytes = new byte[24]; // 稍短一些，因为包含了用户ID
        secureRandom.nextBytes(randomBytes);
        String randomPart = encoder.encodeToString(randomBytes);
        
        String apiKey = API_KEY_PREFIX + "user_" + userId + "_" + timestamp + "_" + randomPart;
        
        log.debug("为用户 {} 生成API密钥", userId);
        return apiKey;
    }

    /**
     * 生成服务专用API密钥
     * 格式: nxs_service_<service_name>_<timestamp>_<random_string>
     */
    public String generateServiceApiKey(String serviceName) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        
        byte[] randomBytes = new byte[24];
        secureRandom.nextBytes(randomBytes);
        String randomPart = encoder.encodeToString(randomBytes);
        
        // 清理服务名称，只保留字母数字和下划线
        String cleanServiceName = serviceName.replaceAll("[^a-zA-Z0-9_]", "_").toLowerCase();
        
        String apiKey = API_KEY_PREFIX + "service_" + cleanServiceName + "_" + timestamp + "_" + randomPart;
        
        log.debug("为服务 {} 生成API密钥", serviceName);
        return apiKey;
    }

    /**
     * 验证API密钥格式
     */
    public boolean isValidApiKeyFormat(String apiKey) {
        if (apiKey == null || apiKey.trim().isEmpty()) {
            return false;
        }

        // 检查前缀
        if (!apiKey.startsWith(API_KEY_PREFIX)) {
            return false;
        }

        // 检查最小长度
        if (apiKey.length() < 20) {
            return false;
        }

        // 检查是否包含非法字符（只允许字母、数字、下划线、连字符）
        if (!apiKey.matches("^[a-zA-Z0-9_-]+$")) {
            return false;
        }

        return true;
    }

    /**
     * 从API密钥中提取用户ID（如果是用户密钥）
     */
    public Long extractUserIdFromApiKey(String apiKey) {
        if (!isValidApiKeyFormat(apiKey)) {
            return null;
        }

        if (!apiKey.startsWith(API_KEY_PREFIX + "user_")) {
            return null;
        }

        try {
            // 格式: nxs_user_<user_id>_<timestamp>_<random_string>
            String[] parts = apiKey.split("_");
            if (parts.length >= 4) {
                return Long.parseLong(parts[2]);
            }
        } catch (NumberFormatException e) {
            log.warn("无法从API密钥中提取用户ID: {}", apiKey);
        }

        return null;
    }

    /**
     * 从API密钥中提取服务名称（如果是服务密钥）
     */
    public String extractServiceNameFromApiKey(String apiKey) {
        if (!isValidApiKeyFormat(apiKey)) {
            return null;
        }

        if (!apiKey.startsWith(API_KEY_PREFIX + "service_")) {
            return null;
        }

        try {
            // 格式: nxs_service_<service_name>_<timestamp>_<random_string>
            String[] parts = apiKey.split("_");
            if (parts.length >= 5) {
                return parts[2];
            }
        } catch (Exception e) {
            log.warn("无法从API密钥中提取服务名称: {}", apiKey);
        }

        return null;
    }

    /**
     * 从API密钥中提取时间戳
     */
    public LocalDateTime extractTimestampFromApiKey(String apiKey) {
        if (!isValidApiKeyFormat(apiKey)) {
            return null;
        }

        try {
            String[] parts = apiKey.split("_");
            String timestampStr = null;

            if (apiKey.startsWith(API_KEY_PREFIX + "user_") && parts.length >= 5) {
                timestampStr = parts[3];
            } else if (apiKey.startsWith(API_KEY_PREFIX + "service_") && parts.length >= 5) {
                timestampStr = parts[3];
            } else if (parts.length >= 3) {
                timestampStr = parts[1];
            }

            if (timestampStr != null && timestampStr.length() == 14) {
                return LocalDateTime.parse(timestampStr, DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            }
        } catch (Exception e) {
            log.warn("无法从API密钥中提取时间戳: {}", apiKey);
        }

        return null;
    }

    /**
     * 获取API密钥类型
     */
    public ApiKeyType getApiKeyType(String apiKey) {
        if (!isValidApiKeyFormat(apiKey)) {
            return ApiKeyType.INVALID;
        }

        if (apiKey.startsWith(API_KEY_PREFIX + "user_")) {
            return ApiKeyType.USER;
        } else if (apiKey.startsWith(API_KEY_PREFIX + "service_")) {
            return ApiKeyType.SERVICE;
        } else {
            return ApiKeyType.GENERAL;
        }
    }

    /**
     * 掩码API密钥用于日志显示
     * 只显示前缀和后4位字符
     */
    public String maskApiKey(String apiKey) {
        if (apiKey == null || apiKey.length() < 8) {
            return "****";
        }

        String prefix = apiKey.substring(0, Math.min(8, apiKey.length()));
        String suffix = apiKey.substring(Math.max(0, apiKey.length() - 4));
        
        return prefix + "****" + suffix;
    }

    /**
     * 检查API密钥是否过期（基于生成时间）
     * 默认有效期为1年
     */
    public boolean isApiKeyExpired(String apiKey) {
        return isApiKeyExpired(apiKey, 365);
    }

    /**
     * 检查API密钥是否过期
     * @param apiKey API密钥
     * @param validDays 有效天数
     */
    public boolean isApiKeyExpired(String apiKey, int validDays) {
        LocalDateTime timestamp = extractTimestampFromApiKey(apiKey);
        if (timestamp == null) {
            return true; // 无法提取时间戳，认为已过期
        }

        LocalDateTime expiryDate = timestamp.plusDays(validDays);
        return LocalDateTime.now().isAfter(expiryDate);
    }

    /**
     * API密钥类型枚举
     */
    public enum ApiKeyType {
        USER("用户密钥"),
        SERVICE("服务密钥"),
        GENERAL("通用密钥"),
        INVALID("无效密钥");

        private final String description;

        ApiKeyType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}