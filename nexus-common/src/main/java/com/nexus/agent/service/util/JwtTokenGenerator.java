package com.nexus.agent.service.util;

import com.nexus.agent.service.entity.User;
// 注意：在 Common 模块中不应依赖 Repository，此工具类的设计需要调整
// import com.nexus.agent.service.repository.UserRepository; 
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * JWT Token生成工具 (迁移到 Common 模块 - 注意：此工具依赖于具体应用的 Repository，在微服务中可能需要重构)
 * 用于为现有用户生成JWT token
 * 
 * [重构说明]：在微服务架构中，这个类可能更适合放在 Auth Service 中，因为它依赖 UserRepository。
 * 暂时放在 Common 中，但需要注意其依赖关系。
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class JwtTokenGenerator implements CommandLineRunner {

    // private final UserRepository userRepository; // 需要重构以移除对 UserRepository 的直接依赖
    private final JwtUtil jwtUtil;

    @Override
    public void run(String... args) throws Exception {
        // 此处逻辑依赖 UserRepository，在 Common 模块中无法直接执行
        // if (args.length > 0 && "generate-jwt".equals(args[0])) {
        //     generateJwtForAllUsers();
        // }
        log.warn("JwtTokenGenerator: CommandLineRunner execution skipped in Common module.");
    }

    /**
     * 为所有用户生成JWT token (依赖 UserRepository，需在具体服务中实现)
     */
    public void generateJwtForAllUsers() {
        throw new UnsupportedOperationException("generateJwtForAllUsers not implemented in Common module.");
    }

    /**
     * 为特定用户生成JWT token (依赖 UserRepository，需在具体服务中实现)
     */
    public String generateJwtForUser(String username) {
        throw new UnsupportedOperationException("generateJwtForUser not implemented in Common module.");
    }
    
    /**
     * 为 User 对象生成 JWT token (不依赖 Repository 的重载方法)
     */
    public String generateJwtForUser(User user) {
        if (user == null) {
            return null;
        }
        
        String jwtToken = jwtUtil.generateAccessToken(
                user.getId(),
                user.getUsername(),
                user.getRole().name());
        
        log.info("Generated JWT token for user: {}", user.getUsername());
        return jwtToken;
    }
}