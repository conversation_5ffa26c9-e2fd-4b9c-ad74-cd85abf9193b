package com.nexus.agent.service.util;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.crypto.SecretKey;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;
import java.io.Serializable;

/**
 * JWT工具类 (迁移到 Common 模块)
 * 提供JWT令牌的生成、解析和验证功能
 */
@Component
@Slf4j
public class JwtUtil {

    // 注入值，使用统一的JWT配置路径
    @Value("${nexus.auth.jwt.secret:nexus-microservices-jwt-secret-key-2024}")
    private String jwtSecret;

    @Value("${nexus.auth.jwt.expiration:86400}")
    private int jwtExpirationSeconds;

    @Value("${nexus.auth.jwt.refresh-expiration:604800}")
    private int refreshExpirationSeconds;

    @Value("${nexus.auth.jwt.issuer:nexus-microservices}")
    private String jwtIssuer;

    private SecretKey secretKey;

    @PostConstruct
    public void init() {
        // 确保密钥长度足够（至少256位）
        if (jwtSecret.length() < 32) {
            jwtSecret = jwtSecret + "0".repeat(32 - jwtSecret.length());
        }
        this.secretKey = Keys.hmacShaKeyFor(jwtSecret.getBytes());
        log.info("JWT工具类初始化完成，过期时间: {}秒", jwtExpirationSeconds);
    }

    /**
     * 生成访问令牌
     */
    public String generateAccessToken(Long userId, String username, String role) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId.toString()); // 转换为String以匹配网关期望
        claims.put("username", username);
        claims.put("role", role);
        claims.put("type", "access");

        return createToken(claims, username, jwtExpirationSeconds * 1000L);
    }

    /**
     * 生成访问令牌（向后兼容方法）
     */
    public String generateAccessToken(String userId, String username, String role) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        claims.put("username", username);
        claims.put("role", role);
        claims.put("type", "access");

        return createToken(claims, username, jwtExpirationSeconds * 1000L);
    }

    /**
     * 生成刷新令牌
     */
    public String generateRefreshToken(Long userId, String username) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId.toString()); // 转换为String以匹配网关期望
        claims.put("username", username);
        claims.put("type", "refresh");

        return createToken(claims, username, refreshExpirationSeconds * 1000L);
    }

    /**
     * 生成刷新令牌（向后兼容方法）
     */
    public String generateRefreshToken(String userId) {
        // 从数据库查找用户名（这里简化处理，实际应该注入UserRepository）
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        claims.put("type", "refresh");

        return createToken(claims, userId, refreshExpirationSeconds * 1000L);
    }

    /**
     * 创建JWT令牌
     */
    private String createToken(Map<String, Object> claims, String subject, long expirationTime) {
        Date now = new Date();
        Date expiration = new Date(now.getTime() + expirationTime);

        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuer(jwtIssuer)
                .setIssuedAt(now)
                .setExpiration(expiration)
                .signWith(secretKey, SignatureAlgorithm.HS256)
                .compact();
    }

    /**
     * 从令牌中提取用户名
     */
    public String extractUsername(String token) {
        return extractClaim(token, Claims::getSubject);
    }

    /**
     * 从令牌中提取用户ID
     */
    public String extractUserId(String token) {
        return extractClaim(token, claims -> claims.get("userId", String.class));
    }

    /**
     * 从令牌中提取用户角色
     */
    public String extractRole(String token) {
        return extractClaim(token, claims -> claims.get("role", String.class));
    }

    /**
     * 从令牌中提取令牌类型
     */
    public String extractTokenType(String token) {
        return extractClaim(token, claims -> claims.get("type", String.class));
    }

    /**
     * 从令牌中提取过期时间
     */
    public Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }

    /**
     * 从令牌中提取指定声明
     */
    public <T> T extractClaim(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = extractAllClaims(token);
        return claimsResolver.apply(claims);
    }

    /**
     * 提取所有声明
     */
    public Claims extractAllClaims(String token) {
        try {
            return Jwts.parserBuilder()
                    .setSigningKey(secretKey)
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
        } catch (ExpiredJwtException e) {
            log.warn("JWT令牌已过期: {}", e.getMessage());
            throw e;
        } catch (UnsupportedJwtException e) {
            log.error("不支持的JWT令牌: {}", e.getMessage());
            throw e;
        } catch (MalformedJwtException e) {
            log.error("JWT令牌格式错误: {}", e.getMessage());
            throw e;
        } catch (SecurityException e) {
            log.error("JWT令牌签名验证失败: {}", e.getMessage());
            throw e;
        } catch (IllegalArgumentException e) {
            log.error("JWT令牌参数错误: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 检查令牌是否过期
     */
    public Boolean isTokenExpired(String token) {
        try {
            return extractExpiration(token).before(new Date());
        } catch (ExpiredJwtException e) {
            return true;
        }
    }

    /**
     * 验证令牌
     */
    public Boolean validateToken(String token, String username) {
        try {
            final String extractedUsername = extractUsername(token);
            return (extractedUsername.equals(username) && !isTokenExpired(token));
        } catch (Exception e) {
            log.warn("JWT令牌验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 验证访问令牌
     */
    public Boolean validateAccessToken(String token) {
        try {
            String tokenType = extractTokenType(token);
            return "access".equals(tokenType) && !isTokenExpired(token);
        } catch (Exception e) {
            log.warn("访问令牌验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 验证刷新令牌
     */
    public Boolean validateRefreshToken(String token) {
        try {
            String tokenType = extractTokenType(token);
            return "refresh".equals(tokenType) && !isTokenExpired(token);
        } catch (Exception e) {
            log.warn("刷新令牌验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取令牌剩余有效时间（分钟）
     */
    public long getTokenRemainingTime(String token) {
        try {
            Date expiration = extractExpiration(token);
            Date now = new Date();
            long remainingTime = expiration.getTime() - now.getTime();
            return Math.max(0, remainingTime / (1000 * 60)); // 转换为分钟
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 检查令牌是否即将过期（30分钟内）
     */
    public boolean isTokenExpiringSoon(String token) {
        return getTokenRemainingTime(token) <= 30;
    }

    /**
     * 从令牌中提取所有用户信息
     */
    public UserInfo extractUserInfo(String token) {
        try {
            Claims claims = extractAllClaims(token);
            return UserInfo.builder()
                    .userId(claims.get("userId", Long.class))
                    .username(claims.getSubject())
                    .role(claims.get("role", String.class))
                    .tokenType(claims.get("type", String.class))
                    .issuedAt(claims.getIssuedAt().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())
                    .expiresAt(claims.getExpiration().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())
                    .build();
        } catch (Exception e) {
            log.error("提取用户信息失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 用户信息数据类
     */
    @lombok.Data
    @lombok.Builder
    public static class UserInfo implements Serializable {
        private static final long serialVersionUID = 1L;
        private Long userId;
        private String username;
        private String role;
        private String tokenType;
        private LocalDateTime issuedAt;
        private LocalDateTime expiresAt;
    }
}