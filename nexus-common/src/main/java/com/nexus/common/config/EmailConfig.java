package com.nexus.common.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.spring5.SpringTemplateEngine;
import org.thymeleaf.templatemode.TemplateMode;
import org.thymeleaf.templateresolver.ClassLoaderTemplateResolver;

import java.util.Properties;

/**
 * 邮件配置类
 * 根据配置决定是否启用邮件功能
 */
@Slf4j
@Configuration
public class EmailConfig {

    /**
     * 配置JavaMailSender
     * 当nexus.email.enabled=true时启用
     */
    @Bean
    @ConditionalOnProperty(name = "nexus.email.enabled", havingValue = "true", matchIfMissing = false)
    public JavaMailSender javaMailSender() {
        log.info("配置邮件发送器 - 模拟模式");
        
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
        
        // 使用模拟配置，避免真实邮件发送
        mailSender.setHost("localhost");
        mailSender.setPort(25);
        mailSender.setUsername("test");
        mailSender.setPassword("test");
        
        Properties props = mailSender.getJavaMailProperties();
        props.put("mail.transport.protocol", "smtp");
        props.put("mail.smtp.auth", "false");
        props.put("mail.smtp.starttls.enable", "false");
        props.put("mail.debug", "false");
        
        return mailSender;
    }

    /**
     * 配置Thymeleaf模板引擎
     * 用于邮件模板渲染
     */
    @Bean
    @ConditionalOnProperty(name = "nexus.email.enabled", havingValue = "true", matchIfMissing = false)
    public TemplateEngine emailTemplateEngine() {
        log.info("配置邮件模板引擎");

        SpringTemplateEngine templateEngine = new SpringTemplateEngine();

        // 配置模板解析器
        ClassLoaderTemplateResolver templateResolver = new ClassLoaderTemplateResolver();
        templateResolver.setPrefix("templates/email/");
        templateResolver.setSuffix(".html");
        templateResolver.setTemplateMode(TemplateMode.HTML);
        templateResolver.setCharacterEncoding("UTF-8");
        templateResolver.setCacheable(false); // 开发环境不缓存

        templateEngine.setTemplateResolver(templateResolver);

        return templateEngine;
    }
}
