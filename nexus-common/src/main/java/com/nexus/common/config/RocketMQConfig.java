package com.nexus.common.config;

import com.nexus.common.constants.RocketMQConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * RocketMQ配置类
 * 配置RocketMQ生产者和消费者相关参数
 */
@Slf4j
@Configuration
public class RocketMQConfig {

    @Value("${rocketmq.name-server:localhost:9876}")
    private String nameServer;

    @Value("${rocketmq.producer.group:nexus-producer-group}")
    private String producerGroup;

    @Value("${rocketmq.producer.send-message-timeout:3000}")
    private int sendMessageTimeout;

    @Value("${rocketmq.producer.retry-times-when-send-failed:2}")
    private int retryTimesWhenSendFailed;

    @Value("${rocketmq.producer.retry-times-when-send-async-failed:2}")
    private int retryTimesWhenSendAsyncFailed;

    @Value("${rocketmq.producer.max-message-size:4194304}")
    private int maxMessageSize;

    @Value("${rocketmq.consumer.pull-batch-size:32}")
    private int pullBatchSize;

    /**
     * RocketMQ模板配置
     * 用于发送消息
     */
    @Bean
    public RocketMQTemplate rocketMQTemplate() {
        log.info("初始化RocketMQTemplate，NameServer: {}, ProducerGroup: {}", nameServer, producerGroup);
        return new RocketMQTemplate();
    }

    /**
     * 初始化RocketMQ相关配置
     */
    public void initializeRocketMQ() {
        log.info("开始初始化RocketMQ配置...");
        log.info("NameServer: {}", nameServer);
        log.info("Producer Group: {}", producerGroup);
        log.info("Send Message Timeout: {}ms", sendMessageTimeout);
        log.info("Retry Times When Send Failed: {}", retryTimesWhenSendFailed);
        log.info("Max Message Size: {} bytes", maxMessageSize);
        log.info("Pull Batch Size: {}", pullBatchSize);
        
        // 这里可以添加Topic创建逻辑（如果需要的话）
        createTopicsIfNeeded();

        log.info("RocketMQ配置初始化完成！");
    }

    /**
     * 创建必要的Topic（如果需要的话）
     * 注意：在生产环境中，通常建议通过管理工具预先创建Topic
     */
    private void createTopicsIfNeeded() {
        log.info("检查并创建必要的Topic...");

        // 在RocketMQ中，Topic通常在第一次发送消息时自动创建
        // 或者通过管理工具预先创建
        // 这里记录需要的Topic列表供参考
        String[] requiredTopics = {
            RocketMQConstants.MCP_ASYNC_TOPIC,
            RocketMQConstants.USER_EVENT_TOPIC,
            RocketMQConstants.SERVICE_EVENT_TOPIC,
            RocketMQConstants.SUBSCRIPTION_EVENT_TOPIC,
            RocketMQConstants.NOTIFICATION_TOPIC,
            RocketMQConstants.SYSTEM_EVENT_TOPIC
        };

        for (String topic : requiredTopics) {
            log.debug("需要的Topic: {}", topic);
        }

        log.info("Topic检查完成");
    }

    /**
     * 获取NameServer地址
     */
    public String getNameServer() {
        return nameServer;
    }

    /**
     * 获取生产者组
     */
    public String getProducerGroup() {
        return producerGroup;
    }

    /**
     * 获取发送消息超时时间
     */
    public int getSendMessageTimeout() {
        return sendMessageTimeout;
    }

    /**
     * 获取重试次数
     */
    public int getRetryTimesWhenSendFailed() {
        return retryTimesWhenSendFailed;
    }

    /**
     * 获取最大消息大小
     */
    public int getMaxMessageSize() {
        return maxMessageSize;
    }

    /**
     * 获取拉取批次大小
     */
    public int getPullBatchSize() {
        return pullBatchSize;
    }
}
