package com.nexus.common.constants;

/**
 * 缓存名称常量
 * 定义所有微服务中使用的缓存区域名称
 */
public final class CacheNames {

    private CacheNames() {
        // 工具类，禁止实例化
    }

    // ========== 用户相关缓存 ==========
    
    /**
     * 用户信息缓存
     */
    public static final String USERS = "users";

    /**
     * 用户认证信息缓存
     */
    public static final String USER_AUTH = "user_auth";

    /**
     * 用户权限缓存
     */
    public static final String USER_PERMISSIONS = "user_permissions";

    /**
     * 用户统计信息缓存
     */
    public static final String USER_STATS = "user_stats";

    // ========== 订阅相关缓存 ==========

    /**
     * 用户订阅信息缓存
     */
    public static final String USER_SUBSCRIPTIONS = "user_subscriptions";

    /**
     * 订阅详情缓存
     */
    public static final String SUBSCRIPTIONS = "subscriptions";

    /**
     * 订阅使用统计缓存
     */
    public static final String SUBSCRIPTION_USAGE = "subscription_usage";

    // ========== MCP服务相关缓存 ==========

    /**
     * MCP服务元数据缓存
     */
    public static final String MCP_SERVICES = "mcp_services";

    /**
     * MCP服务状态缓存
     */
    public static final String MCP_SERVICE_STATUS = "mcp_service_status";

    /**
     * MCP服务工具列表缓存
     */
    public static final String MCP_SERVICE_TOOLS = "mcp_service_tools";

    /**
     * MCP服务资源列表缓存
     */
    public static final String MCP_SERVICE_RESOURCES = "mcp_service_resources";

    // ========== 服务配置相关缓存 ==========

    /**
     * 服务配置缓存
     */
    public static final String SERVICE_CONFIGS = "service_configs";

    /**
     * 可用服务列表缓存
     */
    public static final String AVAILABLE_SERVICES = "available_services";

    // ========== 限流相关缓存 ==========

    /**
     * 限流桶缓存
     */
    public static final String RATE_LIMIT_BUCKETS = "rateLimitBuckets";

    /**
     * API调用计数缓存
     */
    public static final String API_CALL_COUNTS = "api_call_counts";

    // ========== 任务相关缓存 ==========

    /**
     * 异步任务结果缓存
     */
    public static final String TASK_RESULTS = "task_results";

    /**
     * 任务关联ID映射缓存
     */
    public static final String TASK_CORRELATIONS = "task_correlations";

    // ========== 会话相关缓存 ==========

    /**
     * 用户会话缓存
     */
    public static final String USER_SESSIONS = "user_sessions";

    /**
     * JWT Token缓存
     */
    public static final String JWT_TOKENS = "jwt_tokens";

    /**
     * API密钥缓存
     */
    public static final String API_KEYS = "api_keys";

    // ========== 配置相关缓存 ==========

    /**
     * 系统配置缓存
     */
    public static final String SYSTEM_CONFIGS = "system_configs";

    /**
     * 服务发现缓存
     */
    public static final String SERVICE_DISCOVERY = "service_discovery";

    // ========== 缓存TTL常量（秒） ==========

    /**
     * 短期缓存TTL - 5分钟
     */
    public static final int SHORT_TTL = 300;

    /**
     * 中期缓存TTL - 30分钟
     */
    public static final int MEDIUM_TTL = 1800;

    /**
     * 长期缓存TTL - 1小时
     */
    public static final int LONG_TTL = 3600;

    /**
     * 超长期缓存TTL - 24小时
     */
    public static final int EXTRA_LONG_TTL = 86400;

    // ========== Redis键前缀 ==========

    /**
     * 用户相关键前缀
     */
    public static final String USER_KEY_PREFIX = "nexus:user:";

    /**
     * 服务相关键前缀
     */
    public static final String SERVICE_KEY_PREFIX = "nexus:service:";

    /**
     * 任务相关键前缀
     */
    public static final String TASK_KEY_PREFIX = "nexus:task:";

    /**
     * 会话相关键前缀
     */
    public static final String SESSION_KEY_PREFIX = "nexus:session:";

    /**
     * 限流相关键前缀
     */
    public static final String RATE_LIMIT_KEY_PREFIX = "nexus:ratelimit:";

    // ========== 缓存键生成方法 ==========

    /**
     * 生成用户缓存键
     */
    public static String userKey(String suffix) {
        return USER_KEY_PREFIX + suffix;
    }

    /**
     * 生成服务缓存键
     */
    public static String serviceKey(String suffix) {
        return SERVICE_KEY_PREFIX + suffix;
    }

    /**
     * 生成任务缓存键
     */
    public static String taskKey(String suffix) {
        return TASK_KEY_PREFIX + suffix;
    }

    /**
     * 生成会话缓存键
     */
    public static String sessionKey(String suffix) {
        return SESSION_KEY_PREFIX + suffix;
    }

    /**
     * 生成限流缓存键
     */
    public static String rateLimitKey(String suffix) {
        return RATE_LIMIT_KEY_PREFIX + suffix;
    }
}
