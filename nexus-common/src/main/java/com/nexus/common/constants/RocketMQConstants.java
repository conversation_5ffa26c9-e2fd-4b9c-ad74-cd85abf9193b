package com.nexus.common.constants;

/**
 * RocketMQ常量定义
 * 定义所有微服务中使用的Topic、Tag和消费者组
 */
public final class RocketMQConstants {

    private RocketMQConstants() {
        // 工具类，禁止实例化
    }

    // ========== Topic定义 ==========

    /**
     * MCP异步任务Topic
     */
    public static final String MCP_ASYNC_TOPIC = "mcp-async-topic";

    /**
     * 用户事件Topic
     */
    public static final String USER_EVENT_TOPIC = "user-event-topic";

    /**
     * 服务事件Topic
     */
    public static final String SERVICE_EVENT_TOPIC = "service-event-topic";

    /**
     * 订阅事件Topic
     */
    public static final String SUBSCRIPTION_EVENT_TOPIC = "subscription-event-topic";

    /**
     * 订阅Topic（简化版）
     */
    public static final String SUBSCRIPTION_TOPIC = "subscription-topic";

    /**
     * 系统通知Topic
     */
    public static final String NOTIFICATION_TOPIC = "notification-topic";

    /**
     * 系统事件Topic
     */
    public static final String SYSTEM_EVENT_TOPIC = "system-event-topic";

    /**
     * 日志事件Topic
     */
    public static final String LOG_TOPIC = "log-event-topic";

    /**
     * 统计事件Topic
     */
    public static final String STATISTICS_TOPIC = "statistics-event-topic";

    // ========== Tag定义 ==========

    // MCP相关Tag
    /**
     * MCP异步任务Tag
     */
    public static final String MCP_ASYNC_TAG = "mcp-async-task";

    /**
     * MCP任务结果Tag
     */
    public static final String MCP_RESULT_TAG = "mcp-task-result";

    /**
     * MCP重试Tag
     */
    public static final String MCP_RETRY_TAG = "mcp-retry-task";

    /**
     * MCP服务日志Tag
     */
    public static final String MCP_LOG_TAG = "mcp-service-log";

    // 用户相关Tag
    /**
     * 用户注册Tag
     */
    public static final String USER_REGISTRATION_TAG = "user-registration";

    /**
     * 用户状态变更Tag
     */
    public static final String USER_STATUS_CHANGE_TAG = "user-status-change";

    /**
     * 用户登录事件Tag
     */
    public static final String USER_LOGIN_EVENT_TAG = "user-login-event";

    // 订阅相关Tag
    /**
     * 订阅创建Tag
     */
    public static final String SUBSCRIPTION_CREATE_TAG = "subscription-create";

    /**
     * 订阅状态变更Tag
     */
    public static final String SUBSCRIPTION_STATUS_CHANGE_TAG = "subscription-status-change";

    /**
     * 订阅使用统计Tag
     */
    public static final String SUBSCRIPTION_USAGE_TAG = "subscription-usage";

    /**
     * 订阅续费Tag
     */
    public static final String SUBSCRIPTION_RENEWAL_TAG = "subscription-renewal";

    // 服务相关Tag
    /**
     * 服务注册Tag
     */
    public static final String SERVICE_REGISTRATION_TAG = "service-registration";

    /**
     * 服务状态变更Tag
     */
    public static final String SERVICE_STATUS_CHANGE_TAG = "service-status-change";

    /**
     * 服务健康检查Tag
     */
    public static final String SERVICE_HEALTH_CHECK_TAG = "service-health-check";

    // 通知相关Tag
    /**
     * 邮件通知Tag
     */
    public static final String EMAIL_NOTIFICATION_TAG = "email-notification";

    /**
     * 系统通知Tag
     */
    public static final String SYSTEM_NOTIFICATION_TAG = "system-notification";

    // 系统事件相关Tag
    /**
     * 日志事件Tag
     */
    public static final String LOG_EVENT_TAG = "log-event";

    /**
     * 审计日志Tag
     */
    public static final String AUDIT_LOG_TAG = "audit-log";

    /**
     * 操作日志Tag
     */
    public static final String OPERATION_LOG_TAG = "operation-log";

    /**
     * 错误日志Tag
     */
    public static final String ERROR_LOG_TAG = "error-log";

    /**
     * 统计事件Tag
     */
    public static final String STATISTICS_TAG = "statistics-event";

    /**
     * 用户行为统计Tag
     */
    public static final String USER_BEHAVIOR_STATISTICS_TAG = "user-behavior-statistics";

    /**
     * 系统性能统计Tag
     */
    public static final String SYSTEM_PERFORMANCE_STATISTICS_TAG = "system-performance-statistics";

    // ========== 消费者组定义 ==========

    /**
     * MCP异步任务消费者组
     */
    public static final String MCP_ASYNC_CONSUMER_GROUP = "mcp-async-consumer-group";

    /**
     * MCP任务结果消费者组
     */
    public static final String MCP_RESULT_CONSUMER_GROUP = "mcp-result-consumer-group";

    /**
     * MCP重试消费者组
     */
    public static final String MCP_RETRY_CONSUMER_GROUP = "mcp-retry-consumer-group";

    /**
     * MCP日志消费者组
     */
    public static final String MCP_LOG_CONSUMER_GROUP = "mcp-log-consumer-group";

    /**
     * 用户事件消费者组
     */
    public static final String USER_EVENT_CONSUMER_GROUP = "user-event-consumer-group";

    /**
     * 订阅事件消费者组
     */
    public static final String SUBSCRIPTION_EVENT_CONSUMER_GROUP = "subscription-event-consumer-group";

    /**
     * 服务事件消费者组
     */
    public static final String SERVICE_EVENT_CONSUMER_GROUP = "service-event-consumer-group";

    /**
     * 通知消费者组
     */
    public static final String NOTIFICATION_CONSUMER_GROUP = "notification-consumer-group";

    /**
     * 日志消费者组
     */
    public static final String LOG_CONSUMER_GROUP = "log-consumer-group";

    /**
     * 统计消费者组
     */
    public static final String STATISTICS_CONSUMER_GROUP = "statistics-consumer-group";

    /**
     * 系统事件消费者组
     */
    public static final String SYSTEM_EVENT_CONSUMER_GROUP = "system-event-consumer-group";

    // ========== 生产者组定义 ==========

    /**
     * 默认生产者组
     */
    public static final String DEFAULT_PRODUCER_GROUP = "nexus-producer-group";

    /**
     * MCP生产者组
     */
    public static final String MCP_PRODUCER_GROUP = "mcp-producer-group";

    /**
     * 用户事件生产者组
     */
    public static final String USER_EVENT_PRODUCER_GROUP = "user-event-producer-group";

    /**
     * 订阅事件生产者组
     */
    public static final String SUBSCRIPTION_EVENT_PRODUCER_GROUP = "subscription-event-producer-group";

    /**
     * 通知生产者组
     */
    public static final String NOTIFICATION_PRODUCER_GROUP = "notification-producer-group";

    // ========== 配置常量 ==========

    /**
     * 默认发送超时时间（毫秒）
     */
    public static final int DEFAULT_SEND_TIMEOUT = 3000;

    /**
     * 默认重试次数
     */
    public static final int DEFAULT_RETRY_TIMES = 2;

    /**
     * 默认最大消息大小（4MB）
     */
    public static final int DEFAULT_MAX_MESSAGE_SIZE = 4 * 1024 * 1024;

    /**
     * 默认拉取批次大小
     */
    public static final int DEFAULT_PULL_BATCH_SIZE = 32;

    /**
     * 默认消费线程数
     */
    public static final int DEFAULT_CONSUME_THREAD_MIN = 5;

    /**
     * 默认最大消费线程数
     */
    public static final int DEFAULT_CONSUME_THREAD_MAX = 10;

    /**
     * 高并发消费线程数
     */
    public static final int HIGH_CONCURRENCY_CONSUME_THREAD_MIN = 10;

    /**
     * 高并发最大消费线程数
     */
    public static final int HIGH_CONCURRENCY_CONSUME_THREAD_MAX = 20;

    /**
     * 低并发消费线程数
     */
    public static final int LOW_CONCURRENCY_CONSUME_THREAD_MIN = 1;

    /**
     * 低并发最大消费线程数
     */
    public static final int LOW_CONCURRENCY_CONSUME_THREAD_MAX = 3;
}
