package com.nexus.common.constants;

/**
 * 安全相关常量
 */
public class SecurityConstants {
    
    /**
     * JWT Token 前缀
     */
    public static final String TOKEN_PREFIX = "Bearer ";
    
    /**
     * JWT Token Header 名称
     */
    public static final String TOKEN_HEADER = "Authorization";
    
    /**
     * JWT Token 类型
     */
    public static final String TOKEN_TYPE = "JWT";
    
    /**
     * JWT 签发者
     */
    public static final String TOKEN_ISSUER = "nexus-microservices";
    
    /**
     * JWT 受众
     */
    public static final String TOKEN_AUDIENCE = "nexus-users";
    
    /**
     * 用户ID在JWT中的键名
     */
    public static final String USER_ID_KEY = "userId";
    
    /**
     * 用户名在JWT中的键名
     */
    public static final String USERNAME_KEY = "username";
    
    /**
     * 用户角色在JWT中的键名
     */
    public static final String ROLES_KEY = "roles";
    
    /**
     * 权限在JWT中的键名
     */
    public static final String AUTHORITIES_KEY = "authorities";
    
    /**
     * API密钥前缀
     */
    public static final String API_KEY_PREFIX = "nxs_";
    
    /**
     * API密钥Header名称
     */
    public static final String API_KEY_HEADER = "X-API-Key";
    
    /**
     * 默认角色
     */
    public static final String DEFAULT_ROLE = "USER";
    
    /**
     * 管理员角色
     */
    public static final String ADMIN_ROLE = "ADMIN";
    
    /**
     * 系统角色
     */
    public static final String SYSTEM_ROLE = "SYSTEM";
    
    /**
     * 匿名用户
     */
    public static final String ANONYMOUS_USER = "anonymous";
    
    /**
     * 系统用户
     */
    public static final String SYSTEM_USER = "system";
    
    /**
     * 密码加密算法
     */
    public static final String PASSWORD_ENCODER = "bcrypt";
    
    /**
     * 密码最小长度
     */
    public static final int PASSWORD_MIN_LENGTH = 6;
    
    /**
     * 密码最大长度
     */
    public static final int PASSWORD_MAX_LENGTH = 128;
    
    /**
     * 用户名最小长度
     */
    public static final int USERNAME_MIN_LENGTH = 3;
    
    /**
     * 用户名最大长度
     */
    public static final int USERNAME_MAX_LENGTH = 50;
    
    /**
     * 邮箱最大长度
     */
    public static final int EMAIL_MAX_LENGTH = 100;
    
    /**
     * 登录失败最大尝试次数
     */
    public static final int MAX_LOGIN_ATTEMPTS = 5;
    
    /**
     * 账户锁定时间（毫秒）
     */
    public static final long ACCOUNT_LOCK_DURATION = 300000L; // 5分钟
    
    /**
     * 私有构造函数，防止实例化
     */
    private SecurityConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
