package com.nexus.common.consumer;

import com.nexus.common.constants.RocketMQConstants;
import com.nexus.common.event.AuditLogEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 审计日志事件消费者
 * 处理系统审计日志的记录和存储
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
    topic = RocketMQConstants.LOG_TOPIC,
    selectorExpression = RocketMQConstants.AUDIT_LOG_TAG,
    consumerGroup = RocketMQConstants.LOG_CONSUMER_GROUP,
    nameServer = "${rocketmq.name-server:localhost:9876}"
)
public class AuditLogConsumer implements RocketMQListener<AuditLogEvent> {
    
    /**
     * 内存中的审计日志缓存
     * 实际生产环境中应该写入数据库或日志文件
     */
    private final List<AuditLogRecord> auditLogs = new ArrayList<>();
    
    /**
     * 按用户ID索引的审计日志
     */
    private final Map<String, List<AuditLogRecord>> userAuditLogs = new ConcurrentHashMap<>();
    
    /**
     * 审计日志记录
     */
    public static class AuditLogRecord {
        private final String eventId;
        private final String userId;
        private final String operation;
        private final String resourceType;
        private final String resourceId;
        private final String result;
        private final String details;
        private final String clientIp;
        private final String userAgent;
        private final String requestId;
        private final Map<String, Object> attributes;
        private final Long duration;
        private final LocalDateTime timestamp;
        
        public AuditLogRecord(AuditLogEvent event) {
            this.eventId = event.getEventId();
            this.userId = event.getUserId();
            this.operation = event.getOperation();
            this.resourceType = event.getResourceType();
            this.resourceId = event.getResourceId();
            this.result = event.getResult();
            this.details = event.getDescription();
            this.clientIp = event.getClientIp();
            this.userAgent = event.getUserAgent();
            this.requestId = event.getRequestId();
            this.attributes = event.getAttributes();
            this.duration = event.getDuration();
            this.timestamp = event.getTimestamp();
        }
        
        // Getters
        public String getEventId() { return eventId; }
        public String getUserId() { return userId; }
        public String getOperation() { return operation; }
        public String getResourceType() { return resourceType; }
        public String getResourceId() { return resourceId; }
        public String getResult() { return result; }
        public String getDetails() { return details; }
        public String getClientIp() { return clientIp; }
        public String getUserAgent() { return userAgent; }
        public String getRequestId() { return requestId; }
        public Map<String, Object> getAttributes() { return attributes; }
        public Long getDuration() { return duration; }
        public LocalDateTime getTimestamp() { return timestamp; }
        
        @Override
        public String toString() {
            return String.format("[%s] %s %s %s/%s -> %s (%s) from %s", 
                    timestamp.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
                    userId != null ? userId : "SYSTEM",
                    operation,
                    resourceType,
                    resourceId,
                    result,
                    details,
                    clientIp);
        }
    }

    /**
     * RocketMQ消息监听器实现
     */
    @Override
    public void onMessage(AuditLogEvent event) {
        log.info("接收到审计日志事件: eventId={}, operation={}, resourceType={}",
                event.getEventId(), event.getOperation(), event.getResourceType());

        try {
            // 验证事件数据
            if (!event.isValid()) {
                log.warn("审计日志事件数据无效: eventId={}", event.getEventId());
                return;
            }

            // 处理审计日志
            processAuditLog(event);

            log.info("审计日志事件处理成功: eventId={}", event.getEventId());

        } catch (Exception e) {
            log.error("审计日志事件处理失败: eventId={} - {}",
                    event.getEventId(), e.getMessage(), e);
            // RocketMQ会自动重试
        }
    }

    /**
     * 处理审计日志事件
     */
    private void processAuditLog(AuditLogEvent event) {
        // 创建审计日志记录
        AuditLogRecord record = new AuditLogRecord(event);

        // 存储到内存缓存（实际应该存储到数据库）
        synchronized (auditLogs) {
            auditLogs.add(record);

            // 限制内存中的日志数量，避免内存溢出
            if (auditLogs.size() > 10000) {
                auditLogs.remove(0); // 移除最旧的记录
            }
        }

        // 按用户ID索引
        if (event.getUserId() != null) {
            userAuditLogs.computeIfAbsent(event.getUserId(), k -> new ArrayList<>()).add(record);
        }

        // 记录到日志文件
        logAuditRecord(record);

        // 检查是否需要告警
        checkForAlerts(event);

        log.debug("审计日志记录完成: eventId={}", event.getEventId());
    }
    
    /**
     * 记录审计日志到日志文件
     */
    private void logAuditRecord(AuditLogRecord record) {
        // 使用专门的审计日志记录器
        log.info("AUDIT_LOG: {}", record.toString());
        
        // 可以在这里添加更详细的日志记录逻辑
        if (record.getAttributes() != null && !record.getAttributes().isEmpty()) {
            log.debug("AUDIT_ATTRIBUTES: eventId={}, attributes={}", 
                    record.getEventId(), record.getAttributes());
        }
    }
    
    /**
     * 检查是否需要告警
     */
    private void checkForAlerts(AuditLogEvent event) {
        // 检查失败的操作
        if ("FAILURE".equals(event.getResult()) || "ERROR".equals(event.getResult())) {
            log.warn("检测到失败的操作: operation={}, resourceType={}, userId={}, details={}",
                    event.getOperation(), event.getResourceType(), event.getUserId(), event.getDescription());
            
            // 可以在这里触发告警通知
            // 比如发送到监控系统或通知管理员
        }
        
        // 检查敏感操作
        if (isSensitiveOperation(event.getOperation())) {
            log.info("检测到敏感操作: operation={}, userId={}, clientIp={}", 
                    event.getOperation(), event.getUserId(), event.getClientIp());
        }
        
        // 检查异常IP访问
        if (event.getClientIp() != null && isAbnormalIp(event.getClientIp())) {
            log.warn("检测到异常IP访问: ip={}, operation={}, userId={}", 
                    event.getClientIp(), event.getOperation(), event.getUserId());
        }
    }
    
    /**
     * 判断是否为敏感操作
     */
    private boolean isSensitiveOperation(String operation) {
        if (operation == null) return false;
        
        String[] sensitiveOps = {
            "USER_DELETE", "USER_ROLE_CHANGE", "PERMISSION_GRANT", "PERMISSION_REVOKE",
            "PASSWORD_RESET", "API_KEY_REGENERATE", "SYSTEM_CONFIG_CHANGE"
        };
        
        for (String sensitiveOp : sensitiveOps) {
            if (operation.contains(sensitiveOp)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 判断是否为异常IP
     */
    private boolean isAbnormalIp(String ip) {
        // 这里可以实现IP白名单/黑名单检查
        // 或者基于地理位置的异常检测
        
        // 简单示例：检查是否为内网IP
        if (ip.startsWith("192.168.") || ip.startsWith("10.") || ip.startsWith("172.")) {
            return false; // 内网IP认为是正常的
        }
        
        // 可以添加更复杂的异常检测逻辑
        return false;
    }
    
    /**
     * 获取所有审计日志
     */
    public List<AuditLogRecord> getAllAuditLogs() {
        synchronized (auditLogs) {
            return new ArrayList<>(auditLogs);
        }
    }
    
    /**
     * 获取指定用户的审计日志
     */
    public List<AuditLogRecord> getUserAuditLogs(String userId) {
        return userAuditLogs.getOrDefault(userId, new ArrayList<>());
    }
    
    /**
     * 获取最近的审计日志
     */
    public List<AuditLogRecord> getRecentAuditLogs(int limit) {
        synchronized (auditLogs) {
            int size = auditLogs.size();
            int fromIndex = Math.max(0, size - limit);
            return new ArrayList<>(auditLogs.subList(fromIndex, size));
        }
    }
    
    /**
     * 清理过期的审计日志
     */
    public void cleanupExpiredLogs(int daysToKeep) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(daysToKeep);
        
        synchronized (auditLogs) {
            auditLogs.removeIf(record -> record.getTimestamp().isBefore(cutoffTime));
        }
        
        // 清理用户索引中的过期日志
        userAuditLogs.values().forEach(userLogs -> 
            userLogs.removeIf(record -> record.getTimestamp().isBefore(cutoffTime))
        );
        
        log.info("清理过期审计日志完成，保留{}天内的日志", daysToKeep);
    }
    
    /**
     * 获取审计日志统计信息
     */
    public Map<String, Object> getAuditStatistics() {
        Map<String, Object> stats = new ConcurrentHashMap<>();
        
        synchronized (auditLogs) {
            stats.put("totalLogs", auditLogs.size());
            
            // 按操作类型统计
            Map<String, Long> operationStats = new ConcurrentHashMap<>();
            auditLogs.forEach(record -> {
                operationStats.merge(record.getOperation(), 1L, Long::sum);
            });
            stats.put("operationStats", operationStats);
            
            // 按结果统计
            Map<String, Long> resultStats = new ConcurrentHashMap<>();
            auditLogs.forEach(record -> {
                resultStats.merge(record.getResult(), 1L, Long::sum);
            });
            stats.put("resultStats", resultStats);
            
            // 活跃用户数
            stats.put("activeUsers", userAuditLogs.size());
        }
        
        return stats;
    }
}
