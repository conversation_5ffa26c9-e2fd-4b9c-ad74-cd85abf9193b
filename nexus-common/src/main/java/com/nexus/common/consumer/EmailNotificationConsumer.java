package com.nexus.common.consumer;

import com.nexus.common.constants.RocketMQConstants;
import com.nexus.common.event.EmailNotificationEvent;
import com.nexus.common.service.EmailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;

/**
 * 邮件通知事件消费者
 * 处理邮件发送事件
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnBean(EmailService.class)
@RocketMQMessageListener(
    topic = RocketMQConstants.NOTIFICATION_TOPIC,
    selectorExpression = RocketMQConstants.EMAIL_NOTIFICATION_TAG,
    consumerGroup = RocketMQConstants.NOTIFICATION_CONSUMER_GROUP,
    nameServer = "${rocketmq.name-server:localhost:9876}"
)
public class EmailNotificationConsumer implements RocketMQListener<EmailNotificationEvent> {
    
    private final EmailService emailService;

    /**
     * RocketMQ消息监听器实现
     */
    @Override
    public void onMessage(EmailNotificationEvent event) {
        log.info("接收到邮件通知事件: eventId={}, emailType={}, toEmail={}",
                event.getEventId(), event.getEmailType(), event.getToEmail());

        try {
            // 验证事件数据
            if (!event.isValid()) {
                log.warn("邮件通知事件数据无效: eventId={}", event.getEventId());
                return;
            }

            // 处理邮件发送
            processEmailNotification(event);

            log.info("邮件通知事件处理成功: eventId={}", event.getEventId());

        } catch (Exception e) {
            log.error("邮件通知事件处理失败: eventId={} - {}",
                    event.getEventId(), e.getMessage(), e);
            // RocketMQ会自动重试
        }
    }

    /**
     * 处理邮件通知事件
     */
    private void processEmailNotification(EmailNotificationEvent event) {
        // 检查邮件服务是否可用
        if (!emailService.isEmailServiceAvailable()) {
            log.warn("邮件服务不可用，跳过发送: eventId={}", event.getEventId());
            return; // 跳过发送
        }

        // 发送邮件
        boolean sent = emailService.sendEmailNotification(event);

        if (!sent) {
            log.warn("邮件发送失败: eventId={}, toEmail={}", event.getEventId(), event.getToEmail());
            throw new RuntimeException("邮件发送失败"); // 抛出异常触发重试
        }
    }
}
