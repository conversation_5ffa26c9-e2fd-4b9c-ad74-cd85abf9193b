package com.nexus.common.dto;

import com.nexus.common.entity.Permission;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 权限数据传输对象
 * 用于API接口的权限信息传输
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PermissionDTO {

    /**
     * 权限ID
     */
    private Long id;

    /**
     * 订阅ID
     */
    private Long subscriptionId;

    /**
     * 权限类型
     */
    private Permission.PermissionType permissionType;

    /**
     * 工具名称
     */
    private String toolName;

    /**
     * 资源名称
     */
    private String resourceName;

    /**
     * 权限描述
     */
    private String description;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 权限创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 权限更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 权限标识符
     */
    private String permissionIdentifier;

    /**
     * 从实体转换为DTO
     */
    public static PermissionDTO fromEntity(com.nexus.common.entity.Permission permission) {
        if (permission == null) {
            return null;
        }

        return PermissionDTO.builder()
                .id(permission.getId())
                .subscriptionId(permission.getSubscription() != null ? permission.getSubscription().getId() : null)
                .permissionType(permission.getPermissionType())
                .toolName(permission.getToolName())
                .resourceName(permission.getResourceName())
                .description(permission.getDescription())
                .enabled(permission.getEnabled())
                .createdAt(permission.getCreatedAt())
                .updatedAt(permission.getUpdatedAt())
                .permissionIdentifier(permission.getPermissionIdentifier())
                .build();
    }

    /**
     * 转换为实体
     */
    public com.nexus.common.entity.Permission toEntity() {
        return com.nexus.common.entity.Permission.builder()
                .id(this.id)
                .permissionType(this.permissionType)
                .toolName(this.toolName)
                .resourceName(this.resourceName)
                .description(this.description)
                .enabled(this.enabled)
                .createdAt(this.createdAt)
                .updatedAt(this.updatedAt)
                .build();
    }

    /**
     * 创建权限DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreatePermissionDTO {
        @NotNull(message = "订阅ID不能为空")
        private Long subscriptionId;

        @NotNull(message = "权限类型不能为空")
        private Permission.PermissionType permissionType;

        @Size(max = 100, message = "工具名称长度不能超过100个字符")
        private String toolName;

        @Size(max = 100, message = "资源名称长度不能超过100个字符")
        private String resourceName;

        @Size(max = 500, message = "权限描述长度不能超过500个字符")
        private String description;

        @Builder.Default
        private Boolean enabled = true;
    }

    /**
     * 更新权限DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UpdatePermissionDTO {
        @Size(max = 100, message = "工具名称长度不能超过100个字符")
        private String toolName;

        @Size(max = 100, message = "资源名称长度不能超过100个字符")
        private String resourceName;

        @Size(max = 500, message = "权限描述长度不能超过500个字符")
        private String description;

        private Boolean enabled;
    }

    /**
     * 权限检查结果DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PermissionCheckResult {
        private boolean hasPermission;

        private String reason;

        private Permission.PermissionType permissionType;

        private String identifier;

        /**
         * 创建有权限的结果
         */
        public static PermissionCheckResult allowed(Permission.PermissionType type, String identifier) {
            return PermissionCheckResult.builder()
                    .hasPermission(true)
                    .permissionType(type)
                    .identifier(identifier)
                    .reason("权限验证通过")
                    .build();
        }

        /**
         * 创建无权限的结果
         */
        public static PermissionCheckResult denied(String reason) {
            return PermissionCheckResult.builder()
                    .hasPermission(false)
                    .reason(reason)
                    .build();
        }
    }
}
