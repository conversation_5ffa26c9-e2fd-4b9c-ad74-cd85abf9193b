package com.nexus.common.dto;

import com.nexus.common.entity.Subscription;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订阅数据传输对象
 * 用于API接口的订阅信息传输
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SubscriptionDTO {

    /**
     * 订阅ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名（冗余字段，便于显示）
     */
    private String username;

    /**
     * 服务配置ID
     */
    private Long serviceConfigId;

    /**
     * 服务名称（冗余字段，便于显示）
     */
    private String serviceName;

    /**
     * 服务显示名称
     */
    private String serviceDisplayName;

    /**
     * 订阅状态
     */
    private Subscription.SubscriptionStatus status;

    /**
     * 订阅开始时间
     */
    private LocalDateTime startDate;

    /**
     * 订阅结束时间
     */
    private LocalDateTime endDate;

    /**
     * 调用次数限制
     */
    private Long callLimit;

    /**
     * 已使用的调用次数
     */
    private Long usedCalls;

    /**
     * 剩余调用次数
     */
    private Long remainingCalls;

    /**
     * 订阅备注
     */
    private String notes;

    /**
     * 订阅创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 订阅更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 权限列表
     */
    private List<PermissionDTO> permissions;

    /**
     * 是否有效
     */
    private Boolean isValid;

    /**
     * 是否即将过期
     */
    private Boolean isExpiringSoon;

    /**
     * 从实体转换为DTO
     */
    public static SubscriptionDTO fromEntity(com.nexus.common.entity.Subscription subscription) {
        if (subscription == null) {
            return null;
        }

        return SubscriptionDTO.builder()
                .id(subscription.getId())
                .userId(subscription.getUser() != null ? subscription.getUser().getId() : null)
                .username(subscription.getUser() != null ? subscription.getUser().getUsername() : null)
                .serviceConfigId(subscription.getServiceConfig() != null ? subscription.getServiceConfig().getId() : null)
                .serviceName(subscription.getServiceConfig() != null ? subscription.getServiceConfig().getServiceName() : null)
                .serviceDisplayName(subscription.getServiceConfig() != null ? subscription.getServiceConfig().getDisplayName() : null)
                .status(subscription.getStatus())
                .startDate(subscription.getStartDate())
                .endDate(subscription.getEndDate())
                .callLimit(subscription.getCallLimit())
                .usedCalls(subscription.getUsedCalls())
                .remainingCalls(subscription.getRemainingCalls())
                .notes(subscription.getNotes())
                .createdAt(subscription.getCreatedAt())
                .updatedAt(subscription.getUpdatedAt())
                .isValid(subscription.isValid())
                .isExpiringSoon(subscription.isExpiringSoon())
                .build();
    }

    /**
     * 转换为实体
     */
    public com.nexus.common.entity.Subscription toEntity() {
        return com.nexus.common.entity.Subscription.builder()
                .id(this.id)
                .status(this.status)
                .startDate(this.startDate)
                .endDate(this.endDate)
                .callLimit(this.callLimit)
                .usedCalls(this.usedCalls)
                .notes(this.notes)
                .createdAt(this.createdAt)
                .updatedAt(this.updatedAt)
                .build();
    }

    /**
     * 创建订阅DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreateSubscriptionDTO {
        @NotNull(message = "用户ID不能为空")
        private Long userId;

        @NotNull(message = "服务配置ID不能为空")
        private Long serviceConfigId;

        @NotNull(message = "订阅开始时间不能为空")
        private LocalDateTime startDate;

        private LocalDateTime endDate;

        private Long callLimit;

        private String notes;
    }

    /**
     * 更新订阅DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UpdateSubscriptionDTO {
        private Subscription.SubscriptionStatus status;

        private LocalDateTime endDate;

        private Long callLimit;

        private String notes;
    }

    /**
     * 订阅使用统计DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SubscriptionUsageDTO {
        private Long subscriptionId;

        private String serviceName;

        private Long callLimit;

        private Long usedCalls;

        private Long remainingCalls;

        private Double usagePercentage;

        private LocalDateTime lastCallAt;

        private LocalDateTime periodStart;

        private LocalDateTime periodEnd;

        /**
         * 计算使用百分比
         */
        public Double calculateUsagePercentage() {
            if (callLimit == null || callLimit == 0) {
                return 0.0;
            }
            return (usedCalls.doubleValue() / callLimit.doubleValue()) * 100.0;
        }
    }
}
