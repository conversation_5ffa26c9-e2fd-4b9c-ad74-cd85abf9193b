package com.nexus.common.dto;

import com.nexus.common.entity.User;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户数据传输对象
 * 用于API接口的用户信息传输
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDTO {

    /**
     * 用户ID
     */
    private Long id;

    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    private String username;

    /**
     * 邮箱地址
     */
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;

    /**
     * 用户状态
     */
    private User.UserStatus status;

    /**
     * 用户角色
     */
    private User.UserRole role;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginAt;

    /**
     * 最后登录IP
     */
    private String lastLoginIp;

    /**
     * API调用次数统计
     */
    private Long apiCallCount;

    /**
     * 账户创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 账户更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 用户订阅列表
     */
    private List<SubscriptionDTO> subscriptions;

    /**
     * 从实体转换为DTO
     */
    public static UserDTO fromEntity(com.nexus.common.entity.User user) {
        if (user == null) {
            return null;
        }

        return UserDTO.builder()
                .id(user.getId())
                .username(user.getUsername())
                .email(user.getEmail())
                .status(user.getStatus())
                .role(user.getRole())
                .lastLoginAt(user.getLastLoginAt())
                .lastLoginIp(user.getLastLoginIp())
                .apiCallCount(user.getApiCallCount())
                .createdAt(user.getCreatedAt())
                .updatedAt(user.getUpdatedAt())
                .build();
    }

    /**
     * 转换为实体（不包含敏感信息）
     */
    public com.nexus.common.entity.User toEntity() {
        return com.nexus.common.entity.User.builder()
                .id(this.id)
                .username(this.username)
                .email(this.email)
                .status(this.status)
                .role(this.role)
                .lastLoginAt(this.lastLoginAt)
                .lastLoginIp(this.lastLoginIp)
                .apiCallCount(this.apiCallCount)
                .createdAt(this.createdAt)
                .updatedAt(this.updatedAt)
                .build();
    }

    /**
     * 创建用户注册DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreateUserDTO {
        @NotBlank(message = "用户名不能为空")
        @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
        private String username;

        @NotBlank(message = "邮箱不能为空")
        @Email(message = "邮箱格式不正确")
        private String email;

        @NotBlank(message = "密码不能为空")
        @Size(min = 6, max = 100, message = "密码长度必须在6-100个字符之间")
        private String password;

        private User.UserRole role = User.UserRole.USER;
    }

    /**
     * 用户登录DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LoginDTO {
        @NotBlank(message = "用户名或邮箱不能为空")
        private String usernameOrEmail;

        @NotBlank(message = "密码不能为空")
        private String password;
    }

    /**
     * 用户更新DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UpdateUserDTO {
        @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
        private String username;

        @Email(message = "邮箱格式不正确")
        private String email;

        private User.UserStatus status;

        private User.UserRole role;
    }

    /**
     * 密码修改DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChangePasswordDTO {
        @NotBlank(message = "当前密码不能为空")
        private String currentPassword;

        @NotBlank(message = "新密码不能为空")
        @Size(min = 6, max = 100, message = "新密码长度必须在6-100个字符之间")
        private String newPassword;
    }
}
