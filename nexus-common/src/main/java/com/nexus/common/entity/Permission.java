package com.nexus.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 权限实体类
 * 管理用户对特定MCP服务工具的访问权限
 */
@Entity
@Table(name = "permissions", indexes = {
        @Index(name = "idx_permission_subscription", columnList = "subscription_id"),
        @Index(name = "idx_permission_tool", columnList = "toolName"),
        @Index(name = "idx_permission_resource", columnList = "resourceName")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Permission {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 关联的订阅
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "subscription_id", nullable = false)
    @NotNull(message = "订阅不能为空")
    private Subscription subscription;

    /**
     * 权限类型
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private PermissionType permissionType;

    /**
     * 工具名称（当权限类型为TOOL时）
     */
    @Column(length = 100)
    @Size(max = 100, message = "工具名称长度不能超过100个字符")
    private String toolName;

    /**
     * 资源名称（当权限类型为RESOURCE时）
     */
    @Column(length = 100)
    @Size(max = 100, message = "资源名称长度不能超过100个字符")
    private String resourceName;

    /**
     * 权限描述
     */
    @Column(length = 500)
    @Size(max = 500, message = "权限描述长度不能超过500个字符")
    private String description;

    /**
     * 是否启用
     */
    @Builder.Default
    private Boolean enabled = true;

    /**
     * 权限创建时间
     */
    @CreationTimestamp
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 权限更新时间
     */
    @UpdateTimestamp
    @Column(nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 权限类型枚举
     */
    public enum PermissionType {
        TOOL("工具权限", "允许调用特定的MCP工具"),
        RESOURCE("资源权限", "允许访问特定的MCP资源"),
        SERVICE("服务权限", "允许访问整个MCP服务");

        private final String displayName;
        private final String description;

        PermissionType(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 检查权限是否有效
     */
    public boolean isValid() {
        if (!enabled) {
            return false;
        }

        // 检查关联的订阅是否有效
        if (subscription == null || !subscription.isValid()) {
            return false;
        }

        return true;
    }

    /**
     * 检查是否为工具权限
     */
    public boolean isToolPermission() {
        return PermissionType.TOOL.equals(this.permissionType);
    }

    /**
     * 检查是否为资源权限
     */
    public boolean isResourcePermission() {
        return PermissionType.RESOURCE.equals(this.permissionType);
    }

    /**
     * 检查是否为服务权限
     */
    public boolean isServicePermission() {
        return PermissionType.SERVICE.equals(this.permissionType);
    }

    /**
     * 获取权限标识符
     */
    public String getPermissionIdentifier() {
        switch (permissionType) {
            case TOOL:
                return "tool:" + toolName;
            case RESOURCE:
                return "resource:" + resourceName;
            case SERVICE:
                return "service:" + subscription.getServiceConfig().getServiceName();
            default:
                return "unknown";
        }
    }
}
