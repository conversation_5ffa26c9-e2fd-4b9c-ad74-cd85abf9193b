package com.nexus.common.entity;

import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 服务配置实体类
 * 存储MCP服务的配置信息和元数据
 */
@Entity
@Table(name = "service_configs", indexes = {
        @Index(name = "idx_service_name", columnList = "serviceName"),
        @Index(name = "idx_service_type", columnList = "serviceType"),
        @Index(name = "idx_service_status", columnList = "status")
})
@TypeDef(name = "jsonb", typeClass = JsonBinaryType.class)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceConfig {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 服务名称，唯一标识
     */
    @Column(unique = true, nullable = false, length = 100)
    @NotBlank(message = "服务名称不能为空")
    @Size(max = 100, message = "服务名称长度不能超过100个字符")
    private String serviceName;

    /**
     * 服务显示名称
     */
    @Column(nullable = false, length = 200)
    @NotBlank(message = "服务显示名称不能为空")
    @Size(max = 200, message = "服务显示名称长度不能超过200个字符")
    private String displayName;

    /**
     * 服务描述
     */
    @Column(length = 1000)
    @Size(max = 1000, message = "服务描述长度不能超过1000个字符")
    private String description;

    /**
     * 服务类型
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private ServiceType serviceType;

    /**
     * 服务状态
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Builder.Default
    private ServiceStatus status = ServiceStatus.ACTIVE;

    /**
     * 服务版本
     */
    @Column(length = 50)
    private String version;

    /**
     * 服务端点URL（对于远程服务）
     */
    @Column(length = 500)
    private String endpoint;

    /**
     * 服务配置参数（JSON格式）
     */
    @Type(type = "jsonb")
    @Column(columnDefinition = "jsonb")
    @Builder.Default
    private Map<String, Object> configParams = new HashMap<>();

    /**
     * 服务元数据（JSON格式）
     */
    @Type(type = "jsonb")
    @Column(columnDefinition = "jsonb")
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();

    /**
     * 是否需要认证
     */
    @Builder.Default
    private Boolean requiresAuth = true;

    /**
     * 默认调用限制（每小时）
     */
    private Long defaultCallLimit;

    /**
     * 服务创建时间
     */
    @CreationTimestamp
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 服务更新时间
     */
    @UpdateTimestamp
    @Column(nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 服务的订阅关系
     */
    @OneToMany(mappedBy = "serviceConfig", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Builder.Default
    private Set<Subscription> subscriptions = new HashSet<>();

    /**
     * 服务类型枚举
     */
    public enum ServiceType {
        SERVER_SIDE("服务端服务", "部署在服务端的MCP服务"),
        LOCAL_SIDE("本地服务", "部署在用户本地的MCP服务"),
        HYBRID("混合服务", "支持服务端和本地部署的MCP服务");

        private final String displayName;
        private final String description;

        ServiceType(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 服务状态枚举
     */
    public enum ServiceStatus {
        ACTIVE("激活"),
        INACTIVE("未激活"),
        MAINTENANCE("维护中"),
        DEPRECATED("已弃用"),
        ERROR("错误");

        private final String description;

        ServiceStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 检查服务是否可用
     */
    public boolean isAvailable() {
        return ServiceStatus.ACTIVE.equals(this.status);
    }

    /**
     * 检查是否为本地服务
     */
    public boolean isLocalService() {
        return ServiceType.LOCAL_SIDE.equals(this.serviceType);
    }

    /**
     * 检查是否为远程服务
     */
    public boolean isRemoteService() {
        return ServiceType.SERVER_SIDE.equals(this.serviceType);
    }

    /**
     * 添加配置参数
     */
    public void addConfigParam(String key, Object value) {
        if (this.configParams == null) {
            this.configParams = new HashMap<>();
        }
        this.configParams.put(key, value);
    }

    /**
     * 添加元数据
     */
    public void addMetadata(String key, Object value) {
        if (this.metadata == null) {
            this.metadata = new HashMap<>();
        }
        this.metadata.put(key, value);
    }
}
