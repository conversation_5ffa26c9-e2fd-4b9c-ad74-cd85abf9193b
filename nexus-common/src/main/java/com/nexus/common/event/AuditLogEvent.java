package com.nexus.common.event;

import com.nexus.common.constants.RocketMQConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 审计日志事件
 * 记录需要审计的重要操作和安全相关事件
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class AuditLogEvent extends BaseEvent {

    /**
     * 审计ID
     */
    private String auditId;

    /**
     * 审计类型
     */
    private AuditType auditType;

    /**
     * 审计级别
     */
    private AuditLevel auditLevel;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 用户角色
     */
    private String userRole;

    /**
     * 操作动作
     */
    private String action;

    /**
     * 操作目标
     */
    private String target;

    /**
     * 操作目标ID
     */
    private String targetId;

    /**
     * 审计描述
     */
    private String description;

    /**
     * 审计时间
     */
    private LocalDateTime auditTime;

    /**
     * 操作结果
     */
    private String result;

    /**
     * 客户端IP
     */
    private String clientIp;
    
    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 资源类型
     */
    private String resourceType;

    /**
     * 资源ID
     */
    private String resourceId;

    /**
     * 权限信息
     */
    private String permissions;

    /**
     * 额外属性（向后兼容）
     */
    private Map<String, Object> attributes;

    /**
     * 操作耗时（毫秒）（向后兼容）
     */
    private Long duration;

    /**
     * 操作类型（向后兼容）
     */
    private String operation;

    /**
     * 审计类型枚举
     */
    public enum AuditType {
        AUTHENTICATION("身份认证"),
        AUTHORIZATION("权限授权"),
        DATA_ACCESS("数据访问"),
        DATA_MODIFICATION("数据修改"),
        SYSTEM_CONFIGURATION("系统配置"),
        USER_MANAGEMENT("用户管理"),
        SECURITY_EVENT("安全事件"),
        ADMIN_OPERATION("管理员操作");

        private final String description;

        AuditType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 审计级别枚举
     */
    public enum AuditLevel {
        LOW("低级别"),
        MEDIUM("中级别"),
        HIGH("高级别"),
        CRITICAL("关键级别");

        private final String description;

        AuditLevel(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    public AuditLogEvent(AuditType auditType, AuditLevel auditLevel, String userId, String username,
                        String action, String target) {
        super();
        this.auditId = generateAuditId();
        this.auditType = auditType;
        this.auditLevel = auditLevel;
        this.userId = userId;
        this.username = username;
        this.action = action;
        this.target = target;
        this.auditTime = LocalDateTime.now();
    }

    /**
     * 向后兼容的构造函数
     */
    public AuditLogEvent(String userId, String operation, String resourceType, String resourceId,
                        String result, String details, String clientIp, String userAgent, String requestId) {
        super();
        this.auditId = generateAuditId();
        this.userId = userId;
        this.operation = operation;
        this.resourceType = resourceType;
        this.resourceId = resourceId;
        this.result = result;
        this.description = details;
        this.clientIp = clientIp;
        this.userAgent = userAgent;
        this.requestId = requestId;
        this.auditTime = LocalDateTime.now();

        // 设置默认值
        this.auditType = AuditType.SYSTEM_CONFIGURATION;
        this.auditLevel = AuditLevel.LOW;
        this.action = operation;
        this.target = resourceType;
        this.targetId = resourceId;
    }

    /**
     * 生成审计ID
     */
    private String generateAuditId() {
        return "audit-" + System.currentTimeMillis() + "-" +
               Integer.toHexString(hashCode());
    }

    @Override
    public String getEventType() {
        return "AUDIT_LOG";
    }

    @Override
    public String getRoutingKey() {
        return "log.audit"; // RabbitMQ兼容
    }

    @Override
    public String getExchangeName() {
        return "log.exchange"; // RabbitMQ兼容
    }

    @Override
    public String getTopicName() {
        return RocketMQConstants.LOG_TOPIC;
    }

    @Override
    public String getTagName() {
        return RocketMQConstants.AUDIT_LOG_TAG;
    }

    @Override
    public EventPriority getPriority() {
        // 根据审计级别设置优先级
        if (auditLevel == null) {
            return EventPriority.LOW;
        }

        switch (auditLevel) {
            case CRITICAL:
                return EventPriority.URGENT;
            case HIGH:
                return EventPriority.HIGH;
            case MEDIUM:
                return EventPriority.NORMAL;
            case LOW:
            default:
                return EventPriority.LOW;
        }
    }

    @Override
    public boolean isValid() {
        // 基础验证
        boolean baseValid = getEventId() != null && !getEventId().trim().isEmpty() &&
                           getTimestamp() != null &&
                           getSourceService() != null && !getSourceService().trim().isEmpty();

        // 审计日志特定验证
        return baseValid &&
               auditId != null && !auditId.trim().isEmpty() &&
               auditType != null &&
               auditLevel != null &&
               action != null && !action.trim().isEmpty() &&
               auditTime != null;
    }

    /**
     * 向后兼容的方法
     */
    public Map<String, Object> getAttributes() {
        return attributes;
    }

    public void setAttributes(Map<String, Object> attributes) {
        this.attributes = attributes;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public String getOperation() {
        return operation != null ? operation : (action != null ? action : "UNKNOWN");
    }

    public void setOperation(String operation) {
        this.operation = operation;
        if (this.action == null) {
            this.action = operation;
        }
    }

    /**
     * 判断是否为安全相关审计
     */
    public boolean isSecurityRelated() {
        return auditType == AuditType.AUTHENTICATION ||
               auditType == AuditType.AUTHORIZATION ||
               auditType == AuditType.SECURITY_EVENT ||
               (auditLevel != null && auditLevel == AuditLevel.CRITICAL);
    }

    /**
     * 判断是否为高风险审计
     */
    public boolean isHighRisk() {
        return auditLevel == AuditLevel.CRITICAL ||
               auditLevel == AuditLevel.HIGH ||
               isSecurityRelated();
    }

    /**
     * 判断是否需要合规报告
     */
    public boolean needsComplianceReport() {
        return auditType == AuditType.DATA_ACCESS ||
               auditType == AuditType.DATA_MODIFICATION ||
               auditType == AuditType.USER_MANAGEMENT ||
               isHighRisk();
    }

    /**
     * 获取合规要求
     */
    public String getComplianceRequirement() {
        if (auditType == AuditType.DATA_ACCESS || auditType == AuditType.DATA_MODIFICATION) {
            return "GDPR,CCPA";
        } else if (auditType == AuditType.USER_MANAGEMENT) {
            return "SOX,GDPR";
        } else if (auditType == AuditType.SECURITY_EVENT) {
            return "ISO27001,SOC2";
        }
        return "GENERAL";
    }

    /**
     * 判断是否需要告警
     */
    public boolean needsAlert() {
        return isHighRisk() || needsComplianceReport();
    }
}
