package com.nexus.common.event;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 基础事件抽象类
 * 所有业务事件都应继承此类
 */
@Data
@SuperBuilder
@NoArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "eventType")
@JsonSubTypes({
    @JsonSubTypes.Type(value = UserRegistrationEvent.class, name = "USER_REGISTRATION"),
    @JsonSubTypes.Type(value = UserLoginEvent.class, name = "USER_LOGIN"),
    @JsonSubTypes.Type(value = SubscriptionCreatedEvent.class, name = "SUBSCRIPTION_CREATED"),
    @JsonSubTypes.Type(value = SubscriptionStatusChangedEvent.class, name = "SUBSCRIPTION_STATUS_CHANGED"),
    @JsonSubTypes.Type(value = McpTaskEvent.class, name = "MCP_TASK"),
    @JsonSubTypes.Type(value = StatisticsUpdateEvent.class, name = "STATISTICS_UPDATE"),
    @JsonSubTypes.Type(value = AuditLogEvent.class, name = "AUDIT_LOG"),
    @JsonSubTypes.Type(value = EmailNotificationEvent.class, name = "EMAIL_NOTIFICATION"),
    @JsonSubTypes.Type(value = SystemNotificationEvent.class, name = "SYSTEM_NOTIFICATION")
})
public abstract class BaseEvent {
    
    /**
     * 事件唯一标识
     */
    private String eventId;
    
    /**
     * 事件类型
     */
    private String eventType;
    
    /**
     * 事件发生时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timestamp;
    
    /**
     * 事件来源服务
     */
    private String sourceService;
    
    /**
     * 关联用户ID
     */
    private String userId;
    
    /**
     * 事件版本（用于事件演进）
     */
    private String version;
    
    /**
     * 事件优先级
     */
    private EventPriority priority;
    
    /**
     * 事件重试次数
     */
    private Integer retryCount;
    
    /**
     * 最大重试次数
     */
    private Integer maxRetries;
    
    /**
     * 事件过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expireAt;
    
    /**
     * 事件优先级枚举
     */
    public enum EventPriority {
        LOW(1),
        NORMAL(5),
        HIGH(8),
        URGENT(10);
        
        private final int value;
        
        EventPriority(int value) {
            this.value = value;
        }
        
        public int getValue() {
            return value;
        }
    }
    
    /**
     * 初始化事件基础信息
     */
    protected void initializeEvent() {
        if (this.eventId == null) {
            this.eventId = UUID.randomUUID().toString();
        }
        if (this.timestamp == null) {
            this.timestamp = LocalDateTime.now();
        }
        if (this.version == null) {
            this.version = "1.0";
        }
        if (this.priority == null) {
            this.priority = EventPriority.NORMAL;
        }
        if (this.retryCount == null) {
            this.retryCount = 0;
        }
        if (this.maxRetries == null) {
            this.maxRetries = 3;
        }
        if (this.expireAt == null) {
            this.expireAt = LocalDateTime.now().plusHours(24); // 默认24小时过期
        }
    }
    
    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        this.retryCount = (this.retryCount == null ? 0 : this.retryCount) + 1;
    }
    
    /**
     * 检查是否可以重试
     */
    public boolean canRetry() {
        return this.retryCount < this.maxRetries;
    }
    
    /**
     * 检查事件是否过期
     */
    public boolean isExpired() {
        return LocalDateTime.now().isAfter(this.expireAt);
    }
    
    /**
     * 获取事件的路由键（RabbitMQ兼容）
     * 子类可以重写此方法来自定义路由逻辑
     */
    public abstract String getRoutingKey();

    /**
     * 获取事件的交换机名称（RabbitMQ兼容）
     * 子类可以重写此方法来自定义交换机
     */
    public abstract String getExchangeName();

    /**
     * 获取RocketMQ Topic名称
     * 子类可以重写此方法来自定义Topic
     */
    public abstract String getTopicName();

    /**
     * 获取RocketMQ Tag名称
     * 子类可以重写此方法来自定义Tag
     */
    public abstract String getTagName();

    /**
     * 验证事件数据的有效性
     * 子类应该重写此方法来实现具体的验证逻辑
     */
    public abstract boolean isValid();
}
