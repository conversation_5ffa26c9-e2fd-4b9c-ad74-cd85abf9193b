package com.nexus.common.event;

import com.nexus.common.constants.RocketMQConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 业务指标统计事件
 * 记录业务相关的指标数据，用于业务分析和决策
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class BusinessMetricsStatisticsEvent extends BaseEvent {
    
    /**
     * 业务指标类型
     */
    private BusinessMetricType metricType;
    
    /**
     * 指标名称
     */
    private String metricName;
    
    /**
     * 指标值
     */
    private BigDecimal metricValue;
    
    /**
     * 指标单位
     */
    private String metricUnit;
    
    /**
     * 业务维度（如：用户ID、订阅ID、服务ID等）
     */
    private String businessDimension;
    
    /**
     * 维度值
     */
    private String dimensionValue;
    
    /**
     * 统计时间
     */
    private LocalDateTime statisticsTime;
    
    /**
     * 时间窗口（分钟）
     */
    private Integer timeWindowMinutes;
    
    /**
     * 用户数量
     */
    private Long userCount;
    
    /**
     * 订阅数量
     */
    private Long subscriptionCount;
    
    /**
     * 工具调用次数
     */
    private Long toolCallCount;
    
    /**
     * 成功调用次数
     */
    private Long successCallCount;
    
    /**
     * 失败调用次数
     */
    private Long failedCallCount;
    
    /**
     * 收入金额
     */
    private BigDecimal revenueAmount;
    
    /**
     * 货币类型
     */
    private String currency;
    
    /**
     * 平均响应时间（毫秒）
     */
    private Double averageResponseTime;
    
    /**
     * 用户满意度评分
     */
    private Double satisfactionScore;
    
    /**
     * 转化率（百分比）
     */
    private Double conversionRate;
    
    /**
     * 留存率（百分比）
     */
    private Double retentionRate;
    
    /**
     * 流失率（百分比）
     */
    private Double churnRate;
    
    /**
     * 额外的业务指标
     */
    private Map<String, Object> additionalMetrics;
    
    /**
     * 业务标签
     */
    private Map<String, String> businessTags;
    
    /**
     * 业务指标类型枚举
     */
    public enum BusinessMetricType {
        USER_ACTIVITY("用户活跃度"),
        SUBSCRIPTION_METRICS("订阅指标"),
        REVENUE_METRICS("收入指标"),
        TOOL_USAGE("工具使用"),
        PERFORMANCE_METRICS("性能指标"),
        SATISFACTION_METRICS("满意度指标"),
        CONVERSION_METRICS("转化指标"),
        RETENTION_METRICS("留存指标"),
        GROWTH_METRICS("增长指标"),
        CUSTOM_METRICS("自定义指标");
        
        private final String description;
        
        BusinessMetricType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    public BusinessMetricsStatisticsEvent(BusinessMetricType metricType, String metricName, 
                                         BigDecimal metricValue, String businessDimension, String dimensionValue) {
        super();
        this.metricType = metricType;
        this.metricName = metricName;
        this.metricValue = metricValue;
        this.businessDimension = businessDimension;
        this.dimensionValue = dimensionValue;
        this.statisticsTime = LocalDateTime.now();
    }
    
    public BusinessMetricsStatisticsEvent(BusinessMetricType metricType, String metricName, 
                                         BigDecimal metricValue, String metricUnit,
                                         String businessDimension, String dimensionValue) {
        this(metricType, metricName, metricValue, businessDimension, dimensionValue);
        this.metricUnit = metricUnit;
    }
    
    @Override
    public String getEventType() {
        return "BUSINESS_METRICS_STATISTICS";
    }
    
    @Override
    public String getRoutingKey() {
        return "statistics.business.metrics"; // RabbitMQ兼容
    }

    @Override
    public String getExchangeName() {
        return "statistics.exchange"; // RabbitMQ兼容
    }

    @Override
    public String getTopicName() {
        return RocketMQConstants.STATISTICS_TOPIC;
    }

    @Override
    public String getTagName() {
        return "business-metrics-statistics";
    }
    
    @Override
    public EventPriority getPriority() {
        // 业务指标事件优先级较高
        return EventPriority.HIGH;
    }
    
    @Override
    public boolean isValid() {
        // 基础验证
        boolean baseValid = getEventId() != null && !getEventId().trim().isEmpty() &&
                           getTimestamp() != null &&
                           getSourceService() != null && !getSourceService().trim().isEmpty();
        
        // 业务指标特定验证
        return baseValid && 
               metricType != null &&
               metricName != null && !metricName.trim().isEmpty() &&
               metricValue != null &&
               businessDimension != null && !businessDimension.trim().isEmpty() &&
               statisticsTime != null;
    }
    
    /**
     * 获取业务指标描述
     */
    public String getBusinessMetricDescription() {
        return String.format("业务指标 [%s] - %s: %s %s (维度: %s=%s)", 
                metricType.getDescription(), metricName, metricValue, 
                metricUnit != null ? metricUnit : "", businessDimension, dimensionValue);
    }
    
    /**
     * 添加额外指标
     */
    public void addAdditionalMetric(String key, Object value) {
        if (additionalMetrics == null) {
            additionalMetrics = new java.util.HashMap<>();
        }
        additionalMetrics.put(key, value);
    }
    
    /**
     * 获取额外指标
     */
    public Object getAdditionalMetric(String key) {
        return additionalMetrics != null ? additionalMetrics.get(key) : null;
    }
    
    /**
     * 添加业务标签
     */
    public void addBusinessTag(String key, String value) {
        if (businessTags == null) {
            businessTags = new java.util.HashMap<>();
        }
        businessTags.put(key, value);
    }
    
    /**
     * 获取业务标签
     */
    public String getBusinessTag(String key) {
        return businessTags != null ? businessTags.get(key) : null;
    }
    
    /**
     * 计算成功率
     */
    public Double getSuccessRate() {
        if (toolCallCount != null && toolCallCount > 0 && successCallCount != null) {
            return (double) successCallCount / toolCallCount * 100.0;
        }
        return null;
    }
    
    /**
     * 计算失败率
     */
    public Double getFailureRate() {
        if (toolCallCount != null && toolCallCount > 0 && failedCallCount != null) {
            return (double) failedCallCount / toolCallCount * 100.0;
        }
        return null;
    }
    
    /**
     * 检查是否为高价值指标
     */
    public boolean isHighValueMetric() {
        return metricType == BusinessMetricType.REVENUE_METRICS ||
               metricType == BusinessMetricType.CONVERSION_METRICS ||
               metricType == BusinessMetricType.RETENTION_METRICS;
    }
    
    /**
     * 检查是否为关键性能指标
     */
    public boolean isKPI() {
        return isHighValueMetric() || 
               metricType == BusinessMetricType.USER_ACTIVITY ||
               metricType == BusinessMetricType.SATISFACTION_METRICS;
    }
    
    /**
     * 检查是否需要告警
     */
    public boolean needsAlert() {
        // 根据不同指标类型设置告警条件
        if (metricType == BusinessMetricType.SATISFACTION_METRICS && satisfactionScore != null) {
            return satisfactionScore < 3.0; // 满意度低于3分
        }
        
        if (metricType == BusinessMetricType.PERFORMANCE_METRICS && averageResponseTime != null) {
            return averageResponseTime > 10000.0; // 响应时间超过10秒
        }
        
        if (conversionRate != null && conversionRate < 1.0) {
            return true; // 转化率低于1%
        }
        
        if (churnRate != null && churnRate > 10.0) {
            return true; // 流失率高于10%
        }
        
        return false;
    }
    
    /**
     * 获取告警级别
     */
    public String getAlertLevel() {
        if (churnRate != null && churnRate > 20.0) {
            return "CRITICAL";
        }
        
        if (satisfactionScore != null && satisfactionScore < 2.0) {
            return "CRITICAL";
        }
        
        if (needsAlert()) {
            return "WARNING";
        }
        
        return "NORMAL";
    }
    
    /**
     * 获取业务统计上下文
     */
    public Map<String, Object> getBusinessContext() {
        Map<String, Object> context = new java.util.HashMap<>();
        context.put("metricType", metricType.name());
        context.put("metricName", metricName);
        context.put("metricValue", metricValue);
        context.put("metricUnit", metricUnit);
        context.put("businessDimension", businessDimension);
        context.put("dimensionValue", dimensionValue);
        context.put("statisticsTime", statisticsTime);
        
        if (timeWindowMinutes != null) {
            context.put("timeWindowMinutes", timeWindowMinutes);
        }
        
        // 数量指标
        if (userCount != null) context.put("userCount", userCount);
        if (subscriptionCount != null) context.put("subscriptionCount", subscriptionCount);
        if (toolCallCount != null) context.put("toolCallCount", toolCallCount);
        if (successCallCount != null) context.put("successCallCount", successCallCount);
        if (failedCallCount != null) context.put("failedCallCount", failedCallCount);
        
        // 财务指标
        if (revenueAmount != null) {
            context.put("revenueAmount", revenueAmount);
            context.put("currency", currency);
        }
        
        // 性能指标
        if (averageResponseTime != null) context.put("averageResponseTime", averageResponseTime);
        
        // 业务指标
        if (satisfactionScore != null) context.put("satisfactionScore", satisfactionScore);
        if (conversionRate != null) context.put("conversionRate", conversionRate);
        if (retentionRate != null) context.put("retentionRate", retentionRate);
        if (churnRate != null) context.put("churnRate", churnRate);
        
        // 计算指标
        Double successRate = getSuccessRate();
        if (successRate != null) context.put("successRate", successRate);
        
        Double failureRate = getFailureRate();
        if (failureRate != null) context.put("failureRate", failureRate);
        
        // 分类标记
        context.put("isHighValueMetric", isHighValueMetric());
        context.put("isKPI", isKPI());
        context.put("needsAlert", needsAlert());
        context.put("alertLevel", getAlertLevel());
        
        if (businessTags != null && !businessTags.isEmpty()) {
            context.put("tags", businessTags);
        }
        
        if (additionalMetrics != null && !additionalMetrics.isEmpty()) {
            context.put("additionalMetrics", additionalMetrics);
        }
        
        return context;
    }
    
    /**
     * 设置用户相关指标
     */
    public void setUserMetrics(Long userCount, Double satisfactionScore, Double retentionRate, Double churnRate) {
        this.userCount = userCount;
        this.satisfactionScore = satisfactionScore;
        this.retentionRate = retentionRate;
        this.churnRate = churnRate;
    }
    
    /**
     * 设置工具使用指标
     */
    public void setToolUsageMetrics(Long toolCallCount, Long successCallCount, Long failedCallCount, Double averageResponseTime) {
        this.toolCallCount = toolCallCount;
        this.successCallCount = successCallCount;
        this.failedCallCount = failedCallCount;
        this.averageResponseTime = averageResponseTime;
    }
    
    /**
     * 设置收入指标
     */
    public void setRevenueMetrics(BigDecimal revenueAmount, String currency, Double conversionRate) {
        this.revenueAmount = revenueAmount;
        this.currency = currency;
        this.conversionRate = conversionRate;
    }
}
