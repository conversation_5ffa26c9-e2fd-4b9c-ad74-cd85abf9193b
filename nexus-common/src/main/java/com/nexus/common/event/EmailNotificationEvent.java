package com.nexus.common.event;

import com.nexus.common.constants.RocketMQConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Map;

/**
 * 邮件通知事件
 */
@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class EmailNotificationEvent extends BaseEvent {
    
    /**
     * 收件人邮箱
     */
    private String toEmail;
    
    /**
     * 收件人姓名
     */
    private String toName;
    
    /**
     * 邮件主题
     */
    private String subject;
    
    /**
     * 邮件模板
     */
    private String template;
    
    /**
     * 模板参数
     */
    private Map<String, Object> templateParams;
    
    /**
     * 邮件内容（如果不使用模板）
     */
    private String content;
    
    /**
     * 邮件类型
     */
    private EmailType emailType;
    
    /**
     * 是否HTML格式
     */
    private Boolean isHtml;
    
    /**
     * 邮件类型枚举
     */
    public enum EmailType {
        WELCOME,           // 欢迎邮件
        SUBSCRIPTION_CONFIRM,  // 订阅确认
        SUBSCRIPTION_EXPIRE,   // 订阅到期提醒
        PASSWORD_RESET,    // 密码重置
        SYSTEM_NOTIFICATION,   // 系统通知
        MARKETING         // 营销邮件
    }
    
    public EmailNotificationEvent(String userId, String toEmail, String toName, String subject, 
                                 String template, Map<String, Object> templateParams, EmailType emailType) {
        this.setUserId(userId);
        this.toEmail = toEmail;
        this.toName = toName;
        this.subject = subject;
        this.template = template;
        this.templateParams = templateParams;
        this.emailType = emailType;
        this.isHtml = true; // 默认HTML格式
        this.setEventType("EMAIL_NOTIFICATION");
        // 根据邮件类型设置优先级
        this.setPriority(getEmailPriority(emailType));
        this.initializeEvent();
    }
    
    public EmailNotificationEvent(String userId, String toEmail, String toName, String subject, 
                                 String content, EmailType emailType, Boolean isHtml) {
        this.setUserId(userId);
        this.toEmail = toEmail;
        this.toName = toName;
        this.subject = subject;
        this.content = content;
        this.emailType = emailType;
        this.isHtml = isHtml != null ? isHtml : true;
        this.setEventType("EMAIL_NOTIFICATION");
        this.setPriority(getEmailPriority(emailType));
        this.initializeEvent();
    }
    
    private EventPriority getEmailPriority(EmailType emailType) {
        switch (emailType) {
            case PASSWORD_RESET:
                return EventPriority.URGENT;
            case WELCOME:
            case SUBSCRIPTION_CONFIRM:
                return EventPriority.HIGH;
            case SUBSCRIPTION_EXPIRE:
            case SYSTEM_NOTIFICATION:
                return EventPriority.NORMAL;
            case MARKETING:
                return EventPriority.LOW;
            default:
                return EventPriority.NORMAL;
        }
    }
    
    @Override
    public String getRoutingKey() {
        return "notification.email"; // RabbitMQ兼容
    }

    @Override
    public String getExchangeName() {
        return "notification.exchange"; // RabbitMQ兼容
    }

    @Override
    public String getTopicName() {
        return RocketMQConstants.NOTIFICATION_TOPIC;
    }

    @Override
    public String getTagName() {
        return RocketMQConstants.EMAIL_NOTIFICATION_TAG;
    }
    
    @Override
    public boolean isValid() {
        boolean hasContent = (template != null && !template.trim().isEmpty()) 
                           || (content != null && !content.trim().isEmpty());
        
        return this.toEmail != null && !this.toEmail.trim().isEmpty()
                && this.subject != null && !this.subject.trim().isEmpty()
                && this.emailType != null
                && hasContent;
    }
}
