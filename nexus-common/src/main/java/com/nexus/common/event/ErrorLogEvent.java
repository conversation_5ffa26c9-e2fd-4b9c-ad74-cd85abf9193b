package com.nexus.common.event;

import com.nexus.common.constants.RocketMQConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 错误日志事件
 * 记录系统运行过程中发生的错误和异常
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class ErrorLogEvent extends BaseEvent {
    
    /**
     * 错误ID
     */
    private String errorId;
    
    /**
     * 错误级别
     */
    private ErrorLevel errorLevel;
    
    /**
     * 错误类型
     */
    private ErrorType errorType;
    
    /**
     * 错误消息
     */
    private String errorMessage;
    
    /**
     * 错误代码
     */
    private String errorCode;
    
    /**
     * 异常类名
     */
    private String exceptionClass;
    
    /**
     * 异常堆栈信息
     */
    private String stackTrace;
    
    /**
     * 发生时间
     */
    private LocalDateTime occurredTime;
    
    /**
     * 用户ID（如果与用户操作相关）
     */
    private String userId;
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 请求ID
     */
    private String requestId;
    
    /**
     * 客户端IP
     */
    private String clientIp;
    
    /**
     * 用户代理
     */
    private String userAgent;
    
    /**
     * 模块名称
     */
    private String moduleName;
    
    /**
     * 类名
     */
    private String className;
    
    /**
     * 方法名
     */
    private String methodName;
    
    /**
     * 行号
     */
    private Integer lineNumber;
    
    /**
     * 请求URL
     */
    private String requestUrl;
    
    /**
     * 请求方法
     */
    private String requestMethod;
    
    /**
     * 请求参数
     */
    private Map<String, Object> requestParams;
    
    /**
     * 错误上下文信息
     */
    private Map<String, Object> errorContext;
    
    /**
     * 错误标签
     */
    private Map<String, String> errorTags;
    
    /**
     * 是否已解决
     */
    private Boolean resolved = false;
    
    /**
     * 解决时间
     */
    private LocalDateTime resolvedTime;
    
    /**
     * 解决方案
     */
    private String resolution;
    
    /**
     * 错误级别枚举
     */
    public enum ErrorLevel {
        TRACE("跟踪"),
        DEBUG("调试"),
        INFO("信息"),
        WARN("警告"),
        ERROR("错误"),
        FATAL("致命错误");
        
        private final String description;
        
        ErrorLevel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 错误类型枚举
     */
    public enum ErrorType {
        SYSTEM_ERROR("系统错误"),
        BUSINESS_ERROR("业务错误"),
        VALIDATION_ERROR("验证错误"),
        AUTHENTICATION_ERROR("认证错误"),
        AUTHORIZATION_ERROR("授权错误"),
        NETWORK_ERROR("网络错误"),
        DATABASE_ERROR("数据库错误"),
        EXTERNAL_SERVICE_ERROR("外部服务错误"),
        CONFIGURATION_ERROR("配置错误"),
        RESOURCE_ERROR("资源错误"),
        TIMEOUT_ERROR("超时错误"),
        UNKNOWN_ERROR("未知错误");
        
        private final String description;
        
        ErrorType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    public ErrorLogEvent(ErrorLevel errorLevel, ErrorType errorType, String errorMessage) {
        super();
        this.errorId = generateErrorId();
        this.errorLevel = errorLevel;
        this.errorType = errorType;
        this.errorMessage = errorMessage;
        this.occurredTime = LocalDateTime.now();
    }
    
    public ErrorLogEvent(ErrorLevel errorLevel, ErrorType errorType, String errorMessage, 
                        String errorCode, Exception exception) {
        this(errorLevel, errorType, errorMessage);
        this.errorCode = errorCode;
        
        if (exception != null) {
            this.exceptionClass = exception.getClass().getName();
            this.stackTrace = getStackTraceString(exception);
        }
    }
    
    public ErrorLogEvent(ErrorLevel errorLevel, String errorMessage, Exception exception) {
        this(errorLevel, classifyException(exception), errorMessage, null, exception);
    }
    
    /**
     * 生成错误ID
     */
    private String generateErrorId() {
        return "err-" + System.currentTimeMillis() + "-" + 
               Integer.toHexString(hashCode());
    }
    
    /**
     * 根据异常类型分类错误
     */
    private static ErrorType classifyException(Exception exception) {
        if (exception == null) {
            return ErrorType.UNKNOWN_ERROR;
        }
        
        String className = exception.getClass().getSimpleName().toLowerCase();
        String message = exception.getMessage() != null ? exception.getMessage().toLowerCase() : "";
        
        if (className.contains("timeout") || message.contains("timeout")) {
            return ErrorType.TIMEOUT_ERROR;
        } else if (className.contains("network") || className.contains("connection") || 
                   message.contains("connection") || message.contains("network")) {
            return ErrorType.NETWORK_ERROR;
        } else if (className.contains("sql") || className.contains("database") ||
                   message.contains("database") || message.contains("sql")) {
            return ErrorType.DATABASE_ERROR;
        } else if (className.contains("validation") || message.contains("validation") ||
                   message.contains("invalid")) {
            return ErrorType.VALIDATION_ERROR;
        } else if (className.contains("authentication") || message.contains("authentication")) {
            return ErrorType.AUTHENTICATION_ERROR;
        } else if (className.contains("authorization") || className.contains("access") ||
                   message.contains("permission") || message.contains("access")) {
            return ErrorType.AUTHORIZATION_ERROR;
        } else if (className.contains("config") || message.contains("config")) {
            return ErrorType.CONFIGURATION_ERROR;
        } else if (className.contains("resource") || message.contains("resource")) {
            return ErrorType.RESOURCE_ERROR;
        } else {
            return ErrorType.SYSTEM_ERROR;
        }
    }
    
    /**
     * 获取异常堆栈信息
     */
    private String getStackTraceString(Exception exception) {
        try {
            java.io.StringWriter sw = new java.io.StringWriter();
            java.io.PrintWriter pw = new java.io.PrintWriter(sw);
            exception.printStackTrace(pw);
            return sw.toString();
        } catch (Exception e) {
            return "无法获取堆栈信息: " + e.getMessage();
        }
    }
    
    @Override
    public String getEventType() {
        return "ERROR_LOG";
    }
    
    @Override
    public String getRoutingKey() {
        return "log.error"; // RabbitMQ兼容
    }

    @Override
    public String getExchangeName() {
        return "log.exchange"; // RabbitMQ兼容
    }

    @Override
    public String getTopicName() {
        return RocketMQConstants.LOG_TOPIC;
    }

    @Override
    public String getTagName() {
        return RocketMQConstants.ERROR_LOG_TAG;
    }
    
    @Override
    public EventPriority getPriority() {
        // 根据错误级别设置优先级
        if (errorLevel == null) {
            return EventPriority.LOW;
        }

        switch (errorLevel) {
            case FATAL:
                return EventPriority.URGENT;
            case ERROR:
                return EventPriority.HIGH;
            case WARN:
                return EventPriority.NORMAL;
            case INFO:
            case DEBUG:
            case TRACE:
            default:
                return EventPriority.LOW;
        }
    }
    
    @Override
    public boolean isValid() {
        // 基础验证
        boolean baseValid = getEventId() != null && !getEventId().trim().isEmpty() &&
                           getTimestamp() != null &&
                           getSourceService() != null && !getSourceService().trim().isEmpty();
        
        // 错误日志特定验证
        return baseValid && 
               errorId != null && !errorId.trim().isEmpty() &&
               errorLevel != null &&
               errorType != null &&
               errorMessage != null && !errorMessage.trim().isEmpty() &&
               occurredTime != null;
    }
    
    /**
     * 获取错误描述
     */
    public String getErrorDescription() {
        return String.format("[%s] %s - %s: %s", 
                errorLevel.getDescription(), 
                errorType.getDescription(), 
                errorCode != null ? errorCode : "N/A", 
                errorMessage);
    }
    
    /**
     * 添加错误上下文
     */
    public void addErrorContext(String key, Object value) {
        if (errorContext == null) {
            errorContext = new java.util.HashMap<>();
        }
        errorContext.put(key, value);
    }
    
    /**
     * 获取错误上下文
     */
    public Object getErrorContext(String key) {
        return errorContext != null ? errorContext.get(key) : null;
    }
    
    /**
     * 添加错误标签
     */
    public void addErrorTag(String key, String value) {
        if (errorTags == null) {
            errorTags = new java.util.HashMap<>();
        }
        errorTags.put(key, value);
    }
    
    /**
     * 获取错误标签
     */
    public String getErrorTag(String key) {
        return errorTags != null ? errorTags.get(key) : null;
    }
    
    /**
     * 检查是否为严重错误
     */
    public boolean isSevereError() {
        return errorLevel == ErrorLevel.ERROR || errorLevel == ErrorLevel.FATAL;
    }
    
    /**
     * 检查是否为系统级错误
     */
    public boolean isSystemError() {
        return errorType == ErrorType.SYSTEM_ERROR || 
               errorType == ErrorType.DATABASE_ERROR ||
               errorType == ErrorType.CONFIGURATION_ERROR;
    }
    
    /**
     * 检查是否为用户相关错误
     */
    public boolean isUserRelatedError() {
        return userId != null || 
               errorType == ErrorType.AUTHENTICATION_ERROR ||
               errorType == ErrorType.AUTHORIZATION_ERROR ||
               errorType == ErrorType.VALIDATION_ERROR;
    }
    
    /**
     * 检查是否需要立即处理
     */
    public boolean needsImmediateAttention() {
        return errorLevel == ErrorLevel.FATAL || 
               (errorLevel == ErrorLevel.ERROR && isSystemError());
    }
    
    /**
     * 检查是否需要告警
     */
    public boolean needsAlert() {
        return needsImmediateAttention() || 
               (isSevereError() && !resolved);
    }
    
    /**
     * 标记为已解决
     */
    public void markAsResolved(String resolution) {
        this.resolved = true;
        this.resolvedTime = LocalDateTime.now();
        this.resolution = resolution;
    }
    
    /**
     * 设置请求信息
     */
    public void setRequestInfo(String requestUrl, String requestMethod, Map<String, Object> requestParams) {
        this.requestUrl = requestUrl;
        this.requestMethod = requestMethod;
        this.requestParams = requestParams;
    }
    
    /**
     * 设置位置信息
     */
    public void setLocationInfo(String className, String methodName, Integer lineNumber) {
        this.className = className;
        this.methodName = methodName;
        this.lineNumber = lineNumber;
    }
    
    /**
     * 设置用户信息
     */
    public void setUserInfo(String userId, String sessionId, String clientIp, String userAgent) {
        this.userId = userId;
        this.sessionId = sessionId;
        this.clientIp = clientIp;
        this.userAgent = userAgent;
    }
    
    /**
     * 获取完整的错误上下文
     */
    public Map<String, Object> getFullErrorContext() {
        Map<String, Object> context = new java.util.HashMap<>();
        context.put("errorId", errorId);
        context.put("errorLevel", errorLevel.name());
        context.put("errorType", errorType.name());
        context.put("errorMessage", errorMessage);
        context.put("errorCode", errorCode);
        context.put("exceptionClass", exceptionClass);
        context.put("occurredTime", occurredTime);
        context.put("resolved", resolved);
        
        if (resolvedTime != null) {
            context.put("resolvedTime", resolvedTime);
            context.put("resolution", resolution);
        }
        
        // 用户信息
        context.put("userId", userId);
        context.put("sessionId", sessionId);
        context.put("requestId", requestId);
        context.put("clientIp", clientIp);
        context.put("userAgent", userAgent);
        
        // 位置信息
        context.put("moduleName", moduleName);
        context.put("className", className);
        context.put("methodName", methodName);
        context.put("lineNumber", lineNumber);
        
        // 请求信息
        context.put("requestUrl", requestUrl);
        context.put("requestMethod", requestMethod);
        
        // 状态标记
        context.put("isSevereError", isSevereError());
        context.put("isSystemError", isSystemError());
        context.put("isUserRelatedError", isUserRelatedError());
        context.put("needsImmediateAttention", needsImmediateAttention());
        context.put("needsAlert", needsAlert());
        
        if (errorTags != null && !errorTags.isEmpty()) {
            context.put("tags", errorTags);
        }
        
        if (errorContext != null && !errorContext.isEmpty()) {
            context.put("customContext", errorContext);
        }
        
        if (requestParams != null && !requestParams.isEmpty()) {
            context.put("requestParams", requestParams);
        }
        
        return context;
    }
}
