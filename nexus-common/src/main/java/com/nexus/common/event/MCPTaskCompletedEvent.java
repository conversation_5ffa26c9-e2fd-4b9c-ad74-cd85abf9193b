package com.nexus.common.event;

import com.nexus.common.constants.RocketMQConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * MCP任务完成事件
 * 当MCP任务成功完成时触发
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class MCPTaskCompletedEvent extends BaseEvent {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 工具名称
     */
    private String toolName;
    
    /**
     * 服务ID
     */
    private Long serviceId;
    
    /**
     * 服务名称
     */
    private String serviceName;
    
    /**
     * 服务类型
     */
    private String serviceType;
    
    /**
     * 关联ID
     */
    private String correlationId;
    
    /**
     * 任务结果
     */
    private Map<String, Object> result;
    
    /**
     * 任务开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 任务完成时间
     */
    private LocalDateTime completedTime;
    
    /**
     * 执行时长（毫秒）
     */
    private Long executionDurationMs;
    
    /**
     * 执行器ID
     */
    private String executorId;
    
    /**
     * 执行器主机名
     */
    private String executorHost;
    
    /**
     * 结果大小（字节）
     */
    private Long resultSizeBytes;
    
    /**
     * 执行统计信息
     */
    private Map<String, Object> executionStats;
    
    /**
     * 性能指标
     */
    private Map<String, Number> performanceMetrics;
    
    public MCPTaskCompletedEvent(String taskId, String userId, String toolName, Long serviceId, 
                                String serviceName, String serviceType, String correlationId,
                                Map<String, Object> result) {
        super();
        this.taskId = taskId;
        this.userId = userId;
        this.toolName = toolName;
        this.serviceId = serviceId;
        this.serviceName = serviceName;
        this.serviceType = serviceType;
        this.correlationId = correlationId;
        this.result = result;
        this.completedTime = LocalDateTime.now();
    }
    
    public MCPTaskCompletedEvent(String taskId, String userId, String toolName, Long serviceId, 
                                String serviceName, String serviceType, String correlationId,
                                Map<String, Object> result, LocalDateTime startTime, String executorId) {
        this(taskId, userId, toolName, serviceId, serviceName, serviceType, correlationId, result);
        this.startTime = startTime;
        this.executorId = executorId;
        
        if (startTime != null) {
            this.executionDurationMs = java.time.Duration.between(startTime, completedTime).toMillis();
        }
        
        // 计算结果大小
        if (result != null) {
            this.resultSizeBytes = estimateResultSize(result);
        }
    }
    
    /**
     * 估算结果大小
     */
    private Long estimateResultSize(Map<String, Object> result) {
        try {
            // 简单估算，实际可以使用更精确的方法
            String jsonString = result.toString();
            return (long) jsonString.getBytes("UTF-8").length;
        } catch (Exception e) {
            return null;
        }
    }
    
    @Override
    public String getEventType() {
        return "MCP_TASK_COMPLETED";
    }
    
    @Override
    public String getRoutingKey() {
        return "mcp.task.completed"; // RabbitMQ兼容
    }

    @Override
    public String getExchangeName() {
        return "mcp.task.exchange"; // RabbitMQ兼容
    }

    @Override
    public String getTopicName() {
        return RocketMQConstants.MCP_ASYNC_TOPIC;
    }

    @Override
    public String getTagName() {
        return RocketMQConstants.MCP_RESULT_TAG;
    }
    
    @Override
    public EventPriority getPriority() {
        return EventPriority.NORMAL;
    }
    
    @Override
    public boolean isValid() {
        // 基础验证
        boolean baseValid = getEventId() != null && !getEventId().trim().isEmpty() &&
                           getTimestamp() != null &&
                           getSourceService() != null && !getSourceService().trim().isEmpty();

        // 任务特定验证
        return baseValid &&
               taskId != null && !taskId.trim().isEmpty() &&
               userId != null && !userId.trim().isEmpty() &&
               toolName != null && !toolName.trim().isEmpty() &&
               serviceId != null &&
               serviceName != null && !serviceName.trim().isEmpty() &&
               correlationId != null && !correlationId.trim().isEmpty() &&
               result != null;
    }
    
    /**
     * 获取任务描述
     */
    public String getTaskDescription() {
        return String.format("完成MCP任务 [%s] - 工具: %s, 服务: %s, 耗时: %dms", 
                taskId, toolName, serviceName, executionDurationMs != null ? executionDurationMs : 0);
    }
    
    /**
     * 检查是否为快速任务
     */
    public boolean isFastTask() {
        return executionDurationMs != null && executionDurationMs < 1000; // 小于1秒
    }
    
    /**
     * 检查是否为慢任务
     */
    public boolean isSlowTask() {
        return executionDurationMs != null && executionDurationMs > 10000; // 大于10秒
    }
    
    /**
     * 检查是否为大结果任务
     */
    public boolean isLargeResultTask() {
        return resultSizeBytes != null && resultSizeBytes > 1024 * 1024; // 大于1MB
    }
    
    /**
     * 添加性能指标
     */
    public void addPerformanceMetric(String name, Number value) {
        if (performanceMetrics == null) {
            performanceMetrics = new java.util.HashMap<>();
        }
        performanceMetrics.put(name, value);
    }
    
    /**
     * 获取性能指标
     */
    public Number getPerformanceMetric(String name) {
        return performanceMetrics != null ? performanceMetrics.get(name) : null;
    }
    
    /**
     * 设置执行统计信息
     */
    public void setExecutionStat(String key, Object value) {
        if (executionStats == null) {
            executionStats = new java.util.HashMap<>();
        }
        executionStats.put(key, value);
    }
    
    /**
     * 获取执行统计信息
     */
    public Object getExecutionStat(String key) {
        return executionStats != null ? executionStats.get(key) : null;
    }
    
    /**
     * 获取任务完成上下文信息
     */
    public Map<String, Object> getCompletionContext() {
        Map<String, Object> context = new java.util.HashMap<>();
        context.put("taskId", taskId);
        context.put("userId", userId);
        context.put("toolName", toolName);
        context.put("serviceId", serviceId);
        context.put("serviceName", serviceName);
        context.put("serviceType", serviceType);
        context.put("correlationId", correlationId);
        context.put("completedTime", completedTime);
        context.put("success", true);
        
        if (startTime != null) {
            context.put("startTime", startTime);
        }
        
        if (executionDurationMs != null) {
            context.put("executionDurationMs", executionDurationMs);
        }
        
        if (executorId != null) {
            context.put("executorId", executorId);
        }
        
        if (executorHost != null) {
            context.put("executorHost", executorHost);
        }
        
        if (resultSizeBytes != null) {
            context.put("resultSizeBytes", resultSizeBytes);
        }
        
        // 添加任务分类标签
        context.put("isFastTask", isFastTask());
        context.put("isSlowTask", isSlowTask());
        context.put("isLargeResultTask", isLargeResultTask());
        
        return context;
    }
    
    /**
     * 获取性能摘要
     */
    public Map<String, Object> getPerformanceSummary() {
        Map<String, Object> summary = new java.util.HashMap<>();
        
        if (executionDurationMs != null) {
            summary.put("executionDurationMs", executionDurationMs);
            summary.put("executionDurationSeconds", executionDurationMs / 1000.0);
        }
        
        if (resultSizeBytes != null) {
            summary.put("resultSizeBytes", resultSizeBytes);
            summary.put("resultSizeKB", resultSizeBytes / 1024.0);
        }
        
        // 性能等级评估
        String performanceGrade = "UNKNOWN";
        if (executionDurationMs != null) {
            if (executionDurationMs < 1000) {
                performanceGrade = "EXCELLENT";
            } else if (executionDurationMs < 5000) {
                performanceGrade = "GOOD";
            } else if (executionDurationMs < 15000) {
                performanceGrade = "FAIR";
            } else {
                performanceGrade = "POOR";
            }
        }
        summary.put("performanceGrade", performanceGrade);
        
        if (performanceMetrics != null && !performanceMetrics.isEmpty()) {
            summary.put("customMetrics", performanceMetrics);
        }
        
        return summary;
    }
    
    /**
     * 获取结果摘要（不包含完整结果数据）
     */
    public Map<String, Object> getResultSummary() {
        Map<String, Object> summary = new java.util.HashMap<>();
        
        if (result != null) {
            summary.put("hasResult", true);
            summary.put("resultKeys", result.keySet());
            summary.put("resultKeyCount", result.size());
            
            if (resultSizeBytes != null) {
                summary.put("resultSizeBytes", resultSizeBytes);
            }
        } else {
            summary.put("hasResult", false);
        }
        
        return summary;
    }
}
