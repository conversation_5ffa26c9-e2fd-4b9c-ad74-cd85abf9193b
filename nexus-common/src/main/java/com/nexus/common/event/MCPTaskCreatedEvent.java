package com.nexus.common.event;

import com.nexus.common.constants.RocketMQConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * MCP任务创建事件
 * 当新的MCP任务被创建时触发
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class MCPTaskCreatedEvent extends BaseEvent {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 工具名称
     */
    private String toolName;
    
    /**
     * 服务ID
     */
    private Long serviceId;
    
    /**
     * 服务名称
     */
    private String serviceName;
    
    /**
     * 服务类型（LOCAL/REMOTE）
     */
    private String serviceType;
    
    /**
     * 任务参数
     */
    private Map<String, Object> parameters;
    
    /**
     * 任务创建时间
     */
    private LocalDateTime taskCreatedAt;
    
    /**
     * 关联ID（用于追踪相关任务）
     */
    private String correlationId;
    
    /**
     * 任务优先级
     */
    private TaskPriority taskPriority = TaskPriority.NORMAL;
    
    /**
     * 任务超时时间（毫秒）
     */
    private Long timeoutMs;
    
    /**
     * 任务标签
     */
    private Map<String, String> taskTags;
    
    /**
     * 客户端IP
     */
    private String clientIp;
    
    /**
     * 用户代理
     */
    private String userAgent;
    
    /**
     * 任务优先级枚举
     */
    public enum TaskPriority {
        LOW(1),
        NORMAL(5),
        HIGH(8),
        URGENT(10);
        
        private final int value;
        
        TaskPriority(int value) {
            this.value = value;
        }
        
        public int getValue() {
            return value;
        }
    }
    
    public MCPTaskCreatedEvent(String taskId, String userId, String toolName, Long serviceId, 
                              String serviceName, String serviceType, Map<String, Object> parameters) {
        super();
        this.taskId = taskId;
        this.userId = userId;
        this.toolName = toolName;
        this.serviceId = serviceId;
        this.serviceName = serviceName;
        this.serviceType = serviceType;
        this.parameters = parameters;
        this.taskCreatedAt = LocalDateTime.now();
        this.correlationId = generateCorrelationId();
    }
    
    public MCPTaskCreatedEvent(String taskId, String userId, String toolName, Long serviceId, 
                              String serviceName, String serviceType, Map<String, Object> parameters,
                              TaskPriority priority, Long timeoutMs) {
        this(taskId, userId, toolName, serviceId, serviceName, serviceType, parameters);
        this.taskPriority = priority;
        this.timeoutMs = timeoutMs;
    }
    
    /**
     * 生成关联ID
     */
    private String generateCorrelationId() {
        return "mcp-task-" + System.currentTimeMillis() + "-" + 
               Integer.toHexString(hashCode());
    }
    
    @Override
    public String getEventType() {
        return "MCP_TASK_CREATED";
    }
    
    @Override
    public String getRoutingKey() {
        return "mcp.task.created";
    }

    @Override
    public String getExchangeName() {
        return "mcp.task.exchange";
    }

    @Override
    public String getTopicName() {
        return RocketMQConstants.MCP_ASYNC_TOPIC;
    }

    @Override
    public String getTagName() {
        return RocketMQConstants.MCP_ASYNC_TAG;
    }
    
    @Override
    public EventPriority getPriority() {
        // 根据任务优先级映射事件优先级
        switch (taskPriority) {
            case URGENT:
                return EventPriority.URGENT;
            case HIGH:
                return EventPriority.HIGH;
            case NORMAL:
                return EventPriority.NORMAL;
            case LOW:
            default:
                return EventPriority.LOW;
        }
    }
    
    @Override
    public boolean isValid() {
        // 基础验证
        boolean baseValid = getEventId() != null && !getEventId().trim().isEmpty() &&
                           getTimestamp() != null &&
                           getSourceService() != null && !getSourceService().trim().isEmpty();

        // 任务特定验证
        return baseValid &&
               taskId != null && !taskId.trim().isEmpty() &&
               userId != null && !userId.trim().isEmpty() &&
               toolName != null && !toolName.trim().isEmpty() &&
               serviceId != null &&
               serviceName != null && !serviceName.trim().isEmpty() &&
               serviceType != null && !serviceType.trim().isEmpty();
    }
    
    /**
     * 获取任务描述
     */
    public String getTaskDescription() {
        return String.format("创建MCP任务 [%s] - 工具: %s, 服务: %s (%s), 用户: %s", 
                taskId, toolName, serviceName, serviceType, userId);
    }
    
    /**
     * 检查任务是否超时
     */
    public boolean isTimeout() {
        if (timeoutMs == null || timeoutMs <= 0) {
            return false;
        }
        
        LocalDateTime timeoutTime = taskCreatedAt.plusNanos(timeoutMs * 1_000_000);
        return LocalDateTime.now().isAfter(timeoutTime);
    }
    
    /**
     * 获取任务剩余时间（毫秒）
     */
    public long getRemainingTimeMs() {
        if (timeoutMs == null || timeoutMs <= 0) {
            return -1; // 无超时限制
        }
        
        LocalDateTime timeoutTime = taskCreatedAt.plusNanos(timeoutMs * 1_000_000);
        LocalDateTime now = LocalDateTime.now();
        
        if (now.isAfter(timeoutTime)) {
            return 0; // 已超时
        }
        
        return java.time.Duration.between(now, timeoutTime).toMillis();
    }
    
    /**
     * 添加任务标签
     */
    public void addTaskTag(String key, String value) {
        if (taskTags == null) {
            taskTags = new java.util.HashMap<>();
        }
        taskTags.put(key, value);
    }
    
    /**
     * 获取任务标签
     */
    public String getTaskTag(String key) {
        return taskTags != null ? taskTags.get(key) : null;
    }
    
    /**
     * 检查是否包含指定标签
     */
    public boolean hasTaskTag(String key) {
        return taskTags != null && taskTags.containsKey(key);
    }
    
    /**
     * 检查是否为高优先级任务
     */
    public boolean isHighPriority() {
        return taskPriority == TaskPriority.HIGH || taskPriority == TaskPriority.URGENT;
    }
    
    /**
     * 检查是否为长时间运行任务
     */
    public boolean isLongRunningTask() {
        return timeoutMs != null && timeoutMs > 60000; // 超过1分钟认为是长时间任务
    }
    
    /**
     * 获取任务执行上下文信息
     */
    public Map<String, Object> getTaskContext() {
        Map<String, Object> context = new java.util.HashMap<>();
        context.put("taskId", taskId);
        context.put("userId", userId);
        context.put("toolName", toolName);
        context.put("serviceId", serviceId);
        context.put("serviceName", serviceName);
        context.put("serviceType", serviceType);
        context.put("correlationId", correlationId);
        context.put("priority", taskPriority.name());
        context.put("createdAt", taskCreatedAt);
        
        if (timeoutMs != null) {
            context.put("timeoutMs", timeoutMs);
            context.put("remainingTimeMs", getRemainingTimeMs());
        }
        
        if (taskTags != null && !taskTags.isEmpty()) {
            context.put("tags", taskTags);
        }
        
        if (clientIp != null) {
            context.put("clientIp", clientIp);
        }
        
        if (userAgent != null) {
            context.put("userAgent", userAgent);
        }
        
        return context;
    }
}
