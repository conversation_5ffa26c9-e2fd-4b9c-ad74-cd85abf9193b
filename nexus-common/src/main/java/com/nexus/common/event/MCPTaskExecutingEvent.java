package com.nexus.common.event;

import com.nexus.common.constants.RocketMQConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * MCP任务执行中事件
 * 当MCP任务开始执行时触发
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class MCPTaskExecutingEvent extends BaseEvent {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 工具名称
     */
    private String toolName;
    
    /**
     * 服务ID
     */
    private Long serviceId;
    
    /**
     * 服务名称
     */
    private String serviceName;
    
    /**
     * 服务类型
     */
    private String serviceType;
    
    /**
     * 任务参数
     */
    private Map<String, Object> parameters;
    
    /**
     * 关联ID
     */
    private String correlationId;
    
    /**
     * 任务开始执行时间
     */
    private LocalDateTime executionStartTime;
    
    /**
     * 执行器ID（处理该任务的消费者实例）
     */
    private String executorId;
    
    /**
     * 执行器主机名
     */
    private String executorHost;
    
    /**
     * 预估执行时间（毫秒）
     */
    private Long estimatedDurationMs;
    
    /**
     * 执行进度（0-100）
     */
    private Integer progress = 0;
    
    /**
     * 执行状态描述
     */
    private String statusDescription;
    
    /**
     * 中间结果（可选）
     */
    private Map<String, Object> intermediateResults;
    
    public MCPTaskExecutingEvent(String taskId, String userId, String toolName, Long serviceId, 
                                String serviceName, String serviceType, String correlationId) {
        super();
        this.taskId = taskId;
        this.userId = userId;
        this.toolName = toolName;
        this.serviceId = serviceId;
        this.serviceName = serviceName;
        this.serviceType = serviceType;
        this.correlationId = correlationId;
        this.executionStartTime = LocalDateTime.now();
        this.executorId = generateExecutorId();
        this.executorHost = getHostName();
    }
    
    public MCPTaskExecutingEvent(String taskId, String userId, String toolName, Long serviceId, 
                                String serviceName, String serviceType, String correlationId,
                                Map<String, Object> parameters) {
        this(taskId, userId, toolName, serviceId, serviceName, serviceType, correlationId);
        this.parameters = parameters;
    }
    
    /**
     * 生成执行器ID
     */
    private String generateExecutorId() {
        return "executor-" + System.currentTimeMillis() + "-" + 
               Thread.currentThread().getId();
    }
    
    /**
     * 获取主机名
     */
    private String getHostName() {
        try {
            return java.net.InetAddress.getLocalHost().getHostName();
        } catch (Exception e) {
            return "unknown-host";
        }
    }
    
    @Override
    public String getEventType() {
        return "MCP_TASK_EXECUTING";
    }
    
    @Override
    public String getRoutingKey() {
        return "mcp.task.executing";
    }

    @Override
    public String getExchangeName() {
        return "mcp.task.exchange";
    }

    @Override
    public String getTopicName() {
        return RocketMQConstants.MCP_ASYNC_TOPIC;
    }

    @Override
    public String getTagName() {
        return RocketMQConstants.MCP_ASYNC_TAG;
    }
    
    @Override
    public EventPriority getPriority() {
        return EventPriority.NORMAL;
    }
    
    @Override
    public boolean isValid() {
        // 基础验证
        boolean baseValid = getEventId() != null && !getEventId().trim().isEmpty() &&
                           getTimestamp() != null &&
                           getSourceService() != null && !getSourceService().trim().isEmpty();

        // 任务特定验证
        return baseValid &&
               taskId != null && !taskId.trim().isEmpty() &&
               userId != null && !userId.trim().isEmpty() &&
               toolName != null && !toolName.trim().isEmpty() &&
               serviceId != null &&
               serviceName != null && !serviceName.trim().isEmpty() &&
               correlationId != null && !correlationId.trim().isEmpty();
    }
    
    /**
     * 获取任务描述
     */
    public String getTaskDescription() {
        return String.format("执行MCP任务 [%s] - 工具: %s, 服务: %s, 执行器: %s", 
                taskId, toolName, serviceName, executorId);
    }
    
    /**
     * 获取执行时长（毫秒）
     */
    public long getExecutionDurationMs() {
        if (executionStartTime == null) {
            return 0;
        }
        return java.time.Duration.between(executionStartTime, LocalDateTime.now()).toMillis();
    }
    
    /**
     * 更新执行进度
     */
    public void updateProgress(int progress, String statusDescription) {
        this.progress = Math.max(0, Math.min(100, progress));
        this.statusDescription = statusDescription;
    }
    
    /**
     * 设置中间结果
     */
    public void setIntermediateResult(String key, Object value) {
        if (intermediateResults == null) {
            intermediateResults = new java.util.HashMap<>();
        }
        intermediateResults.put(key, value);
    }
    
    /**
     * 获取中间结果
     */
    public Object getIntermediateResult(String key) {
        return intermediateResults != null ? intermediateResults.get(key) : null;
    }
    
    /**
     * 检查是否有中间结果
     */
    public boolean hasIntermediateResults() {
        return intermediateResults != null && !intermediateResults.isEmpty();
    }
    
    /**
     * 检查是否为长时间运行任务
     */
    public boolean isLongRunningTask() {
        return estimatedDurationMs != null && estimatedDurationMs > 30000; // 超过30秒
    }
    
    /**
     * 检查执行是否超时
     */
    public boolean isExecutionTimeout(long timeoutMs) {
        return getExecutionDurationMs() > timeoutMs;
    }
    
    /**
     * 获取执行上下文信息
     */
    public Map<String, Object> getExecutionContext() {
        Map<String, Object> context = new java.util.HashMap<>();
        context.put("taskId", taskId);
        context.put("userId", userId);
        context.put("toolName", toolName);
        context.put("serviceId", serviceId);
        context.put("serviceName", serviceName);
        context.put("serviceType", serviceType);
        context.put("correlationId", correlationId);
        context.put("executorId", executorId);
        context.put("executorHost", executorHost);
        context.put("executionStartTime", executionStartTime);
        context.put("executionDurationMs", getExecutionDurationMs());
        context.put("progress", progress);
        
        if (statusDescription != null) {
            context.put("statusDescription", statusDescription);
        }
        
        if (estimatedDurationMs != null) {
            context.put("estimatedDurationMs", estimatedDurationMs);
        }
        
        if (hasIntermediateResults()) {
            context.put("intermediateResults", intermediateResults);
        }
        
        return context;
    }
    
    /**
     * 获取执行统计信息
     */
    public Map<String, Object> getExecutionStats() {
        Map<String, Object> stats = new java.util.HashMap<>();
        stats.put("executionDurationMs", getExecutionDurationMs());
        stats.put("progress", progress);
        stats.put("isLongRunning", isLongRunningTask());
        
        if (estimatedDurationMs != null) {
            long remainingMs = Math.max(0, estimatedDurationMs - getExecutionDurationMs());
            stats.put("estimatedRemainingMs", remainingMs);
            stats.put("completionPercentage", 
                    Math.min(100.0, (double) getExecutionDurationMs() / estimatedDurationMs * 100));
        }
        
        return stats;
    }
}
