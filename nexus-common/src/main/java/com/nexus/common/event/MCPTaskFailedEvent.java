package com.nexus.common.event;

import com.nexus.common.constants.RocketMQConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * MCP任务失败事件
 * 当MCP任务执行失败时触发
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class MCPTaskFailedEvent extends BaseEvent {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 工具名称
     */
    private String toolName;
    
    /**
     * 服务ID
     */
    private Long serviceId;
    
    /**
     * 服务名称
     */
    private String serviceName;
    
    /**
     * 服务类型
     */
    private String serviceType;
    
    /**
     * 关联ID
     */
    private String correlationId;
    
    /**
     * 错误消息
     */
    private String errorMessage;
    
    /**
     * 错误代码
     */
    private String errorCode;
    
    /**
     * 错误类型
     */
    private ErrorType errorType;
    
    /**
     * 异常堆栈信息
     */
    private String stackTrace;
    
    /**
     * 任务开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 任务失败时间
     */
    private LocalDateTime failedTime;
    
    /**
     * 执行时长（毫秒）
     */
    private Long executionDurationMs;
    
    /**
     * 执行器ID
     */
    private String executorId;
    
    /**
     * 执行器主机名
     */
    private String executorHost;
    
    /**
     * 重试次数
     */
    private Integer retryCount = 0;
    
    /**
     * 是否可重试
     */
    private Boolean retryable = true;
    
    /**
     * 失败上下文信息
     */
    private Map<String, Object> failureContext;
    
    /**
     * 错误类型枚举
     */
    public enum ErrorType {
        TIMEOUT,           // 超时
        NETWORK_ERROR,     // 网络错误
        SERVICE_ERROR,     // 服务错误
        VALIDATION_ERROR,  // 参数验证错误
        PERMISSION_ERROR,  // 权限错误
        RESOURCE_ERROR,    // 资源错误
        SYSTEM_ERROR,      // 系统错误
        UNKNOWN_ERROR      // 未知错误
    }
    
    public MCPTaskFailedEvent(String taskId, String userId, String toolName, Long serviceId, 
                             String serviceName, String serviceType, String correlationId,
                             String errorMessage, ErrorType errorType) {
        super();
        this.taskId = taskId;
        this.userId = userId;
        this.toolName = toolName;
        this.serviceId = serviceId;
        this.serviceName = serviceName;
        this.serviceType = serviceType;
        this.correlationId = correlationId;
        this.errorMessage = errorMessage;
        this.errorType = errorType;
        this.failedTime = LocalDateTime.now();
    }
    
    public MCPTaskFailedEvent(String taskId, String userId, String toolName, Long serviceId, 
                             String serviceName, String serviceType, String correlationId,
                             String errorMessage, ErrorType errorType, LocalDateTime startTime, 
                             String executorId) {
        this(taskId, userId, toolName, serviceId, serviceName, serviceType, correlationId, 
             errorMessage, errorType);
        this.startTime = startTime;
        this.executorId = executorId;
        
        if (startTime != null) {
            this.executionDurationMs = java.time.Duration.between(startTime, failedTime).toMillis();
        }
    }
    
    public MCPTaskFailedEvent(String taskId, String userId, String toolName, Long serviceId, 
                             String serviceName, String serviceType, String correlationId,
                             Exception exception) {
        this(taskId, userId, toolName, serviceId, serviceName, serviceType, correlationId,
             exception.getMessage(), classifyException(exception));
        this.stackTrace = getStackTraceString(exception);
    }
    
    /**
     * 根据异常类型分类错误
     */
    private static ErrorType classifyException(Exception exception) {
        String className = exception.getClass().getSimpleName().toLowerCase();
        String message = exception.getMessage() != null ? exception.getMessage().toLowerCase() : "";
        
        if (className.contains("timeout") || message.contains("timeout")) {
            return ErrorType.TIMEOUT;
        } else if (className.contains("network") || className.contains("connection") || 
                   message.contains("connection") || message.contains("network")) {
            return ErrorType.NETWORK_ERROR;
        } else if (className.contains("validation") || message.contains("validation") ||
                   message.contains("invalid")) {
            return ErrorType.VALIDATION_ERROR;
        } else if (className.contains("permission") || className.contains("access") ||
                   message.contains("permission") || message.contains("access")) {
            return ErrorType.PERMISSION_ERROR;
        } else if (className.contains("resource") || message.contains("resource")) {
            return ErrorType.RESOURCE_ERROR;
        } else {
            return ErrorType.UNKNOWN_ERROR;
        }
    }
    
    /**
     * 获取异常堆栈信息
     */
    private String getStackTraceString(Exception exception) {
        try {
            java.io.StringWriter sw = new java.io.StringWriter();
            java.io.PrintWriter pw = new java.io.PrintWriter(sw);
            exception.printStackTrace(pw);
            return sw.toString();
        } catch (Exception e) {
            return "无法获取堆栈信息: " + e.getMessage();
        }
    }
    
    @Override
    public String getEventType() {
        return "MCP_TASK_FAILED";
    }
    
    @Override
    public String getRoutingKey() {
        return "mcp.task.failed";
    }

    @Override
    public String getExchangeName() {
        return "mcp.task.exchange";
    }

    @Override
    public String getTopicName() {
        return RocketMQConstants.MCP_ASYNC_TOPIC;
    }

    @Override
    public String getTagName() {
        return RocketMQConstants.MCP_RESULT_TAG;
    }
    
    @Override
    public EventPriority getPriority() {
        // 失败事件优先级较高，便于及时处理
        return EventPriority.HIGH;
    }
    
    @Override
    public boolean isValid() {
        // 基础验证
        boolean baseValid = getEventId() != null && !getEventId().trim().isEmpty() &&
                           getTimestamp() != null &&
                           getSourceService() != null && !getSourceService().trim().isEmpty();

        // 任务特定验证
        return baseValid &&
               taskId != null && !taskId.trim().isEmpty() &&
               userId != null && !userId.trim().isEmpty() &&
               toolName != null && !toolName.trim().isEmpty() &&
               serviceId != null &&
               serviceName != null && !serviceName.trim().isEmpty() &&
               correlationId != null && !correlationId.trim().isEmpty() &&
               errorMessage != null && !errorMessage.trim().isEmpty();
    }
    
    /**
     * 获取任务描述
     */
    public String getTaskDescription() {
        return String.format("失败MCP任务 [%s] - 工具: %s, 服务: %s, 错误: %s", 
                taskId, toolName, serviceName, errorType);
    }
    
    /**
     * 检查是否为快速失败
     */
    public boolean isFastFailure() {
        return executionDurationMs != null && executionDurationMs < 1000; // 小于1秒
    }
    
    /**
     * 检查是否为超时失败
     */
    public boolean isTimeoutFailure() {
        return errorType == ErrorType.TIMEOUT;
    }
    
    /**
     * 检查是否为网络相关失败
     */
    public boolean isNetworkFailure() {
        return errorType == ErrorType.NETWORK_ERROR;
    }
    
    /**
     * 检查是否为系统错误
     */
    public boolean isSystemError() {
        return errorType == ErrorType.SYSTEM_ERROR;
    }
    
    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        this.retryCount++;
    }
    
    /**
     * 设置失败上下文信息
     */
    public void setFailureContext(String key, Object value) {
        if (failureContext == null) {
            failureContext = new java.util.HashMap<>();
        }
        failureContext.put(key, value);
    }
    
    /**
     * 获取失败上下文信息
     */
    public Object getFailureContext(String key) {
        return failureContext != null ? failureContext.get(key) : null;
    }
    
    /**
     * 获取任务失败上下文信息
     */
    public Map<String, Object> getFailureContextInfo() {
        Map<String, Object> context = new java.util.HashMap<>();
        context.put("taskId", taskId);
        context.put("userId", userId);
        context.put("toolName", toolName);
        context.put("serviceId", serviceId);
        context.put("serviceName", serviceName);
        context.put("serviceType", serviceType);
        context.put("correlationId", correlationId);
        context.put("errorMessage", errorMessage);
        context.put("errorType", errorType.name());
        context.put("failedTime", failedTime);
        context.put("retryCount", retryCount);
        context.put("retryable", retryable);
        context.put("success", false);
        
        if (errorCode != null) {
            context.put("errorCode", errorCode);
        }
        
        if (startTime != null) {
            context.put("startTime", startTime);
        }
        
        if (executionDurationMs != null) {
            context.put("executionDurationMs", executionDurationMs);
        }
        
        if (executorId != null) {
            context.put("executorId", executorId);
        }
        
        if (executorHost != null) {
            context.put("executorHost", executorHost);
        }
        
        // 添加失败分类标签
        context.put("isFastFailure", isFastFailure());
        context.put("isTimeoutFailure", isTimeoutFailure());
        context.put("isNetworkFailure", isNetworkFailure());
        context.put("isSystemError", isSystemError());
        
        if (failureContext != null && !failureContext.isEmpty()) {
            context.put("customContext", failureContext);
        }
        
        return context;
    }
    
    /**
     * 获取错误摘要
     */
    public Map<String, Object> getErrorSummary() {
        Map<String, Object> summary = new java.util.HashMap<>();
        summary.put("errorType", errorType.name());
        summary.put("errorMessage", errorMessage);
        summary.put("retryable", retryable);
        summary.put("retryCount", retryCount);
        
        if (errorCode != null) {
            summary.put("errorCode", errorCode);
        }
        
        if (executionDurationMs != null) {
            summary.put("executionDurationMs", executionDurationMs);
        }
        
        // 错误严重程度评估
        String severity = "MEDIUM";
        if (errorType == ErrorType.SYSTEM_ERROR || errorType == ErrorType.RESOURCE_ERROR) {
            severity = "HIGH";
        } else if (errorType == ErrorType.VALIDATION_ERROR || errorType == ErrorType.PERMISSION_ERROR) {
            severity = "LOW";
        }
        summary.put("severity", severity);
        
        return summary;
    }
}
