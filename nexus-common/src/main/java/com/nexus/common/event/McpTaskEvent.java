package com.nexus.common.event;

import com.nexus.common.constants.RocketMQConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Map;

/**
 * MCP任务事件
 */
@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class McpTaskEvent extends BaseEvent {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 关联ID
     */
    private String correlationId;
    
    /**
     * 服务ID
     */
    private String serviceId;
    
    /**
     * 方法名称
     */
    private String method;
    
    /**
     * 工具名称
     */
    private String toolName;
    
    /**
     * 任务参数
     */
    private Map<String, Object> parameters;
    
    /**
     * 任务状态
     */
    private String taskStatus;
    
    /**
     * 任务结果
     */
    private Map<String, Object> result;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 执行时间（毫秒）
     */
    private Long executionTime;
    
    /**
     * 超时时间（秒）
     */
    private Integer timeoutSeconds;
    
    public McpTaskEvent(String taskId, String correlationId, String userId, String serviceId, 
                       String method, String toolName, Map<String, Object> parameters, 
                       String taskStatus, Integer timeoutSeconds) {
        this.taskId = taskId;
        this.correlationId = correlationId;
        this.setUserId(userId);
        this.serviceId = serviceId;
        this.method = method;
        this.toolName = toolName;
        this.parameters = parameters;
        this.taskStatus = taskStatus;
        this.timeoutSeconds = timeoutSeconds;
        this.setEventType("MCP_TASK");
        this.setPriority(EventPriority.NORMAL);
        this.initializeEvent();
    }
    
    @Override
    public String getRoutingKey() {
        // 根据任务状态选择不同的路由键（RabbitMQ兼容）
        switch (taskStatus) {
            case "PENDING":
                return "mcp.async.task";
            case "COMPLETED":
            case "FAILED":
                return "mcp.task.result";
            case "RETRY":
                return "mcp.retry.task";
            default:
                return "mcp.service.log";
        }
    }

    @Override
    public String getExchangeName() {
        return "mcp.async.exchange"; // RabbitMQ兼容
    }

    @Override
    public String getTopicName() {
        return RocketMQConstants.MCP_ASYNC_TOPIC;
    }

    @Override
    public String getTagName() {
        // 根据任务状态选择不同的Tag
        switch (taskStatus) {
            case "PENDING":
                return RocketMQConstants.MCP_ASYNC_TAG;
            case "COMPLETED":
            case "FAILED":
                return RocketMQConstants.MCP_RESULT_TAG;
            case "RETRY":
                return RocketMQConstants.MCP_RETRY_TAG;
            default:
                return RocketMQConstants.MCP_LOG_TAG;
        }
    }
    
    @Override
    public boolean isValid() {
        return this.taskId != null && !this.taskId.trim().isEmpty()
                && this.getUserId() != null && !this.getUserId().trim().isEmpty()
                && this.serviceId != null && !this.serviceId.trim().isEmpty()
                && this.method != null && !this.method.trim().isEmpty()
                && this.taskStatus != null && !this.taskStatus.trim().isEmpty();
    }
}
