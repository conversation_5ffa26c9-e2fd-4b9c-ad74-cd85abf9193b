package com.nexus.common.event;

import com.nexus.common.constants.RocketMQConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 操作日志事件
 * 记录用户和系统的操作行为，用于审计和分析
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class OperationLogEvent extends BaseEvent {
    
    /**
     * 操作ID
     */
    private String operationId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 操作类型
     */
    private OperationType operationType;
    
    /**
     * 操作动作
     */
    private String action;
    
    /**
     * 操作目标
     */
    private String target;
    
    /**
     * 操作目标ID
     */
    private String targetId;
    
    /**
     * 操作描述
     */
    private String description;
    
    /**
     * 操作时间
     */
    private LocalDateTime operationTime;
    
    /**
     * 操作结果
     */
    private OperationResult result;
    
    /**
     * 操作耗时（毫秒）
     */
    private Long duration;
    
    /**
     * 客户端IP
     */
    private String clientIp;
    
    /**
     * 用户代理
     */
    private String userAgent;
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 请求ID
     */
    private String requestId;
    
    /**
     * 模块名称
     */
    private String moduleName;
    
    /**
     * 功能名称
     */
    private String functionName;
    
    /**
     * 操作前数据
     */
    private Map<String, Object> beforeData;
    
    /**
     * 操作后数据
     */
    private Map<String, Object> afterData;
    
    /**
     * 操作参数
     */
    private Map<String, Object> operationParams;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 错误代码
     */
    private String errorCode;
    
    /**
     * 风险等级
     */
    private RiskLevel riskLevel;
    
    /**
     * 操作标签
     */
    private Map<String, String> operationTags;
    
    /**
     * 操作类型枚举
     */
    public enum OperationType {
        CREATE("创建"),
        UPDATE("更新"),
        DELETE("删除"),
        QUERY("查询"),
        LOGIN("登录"),
        LOGOUT("登出"),
        REGISTER("注册"),
        UPLOAD("上传"),
        DOWNLOAD("下载"),
        EXPORT("导出"),
        IMPORT("导入"),
        APPROVE("审批"),
        REJECT("拒绝"),
        ENABLE("启用"),
        DISABLE("禁用"),
        RESET("重置"),
        CONFIGURE("配置"),
        EXECUTE("执行"),
        CANCEL("取消"),
        SYSTEM("系统操作");
        
        private final String description;
        
        OperationType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 操作结果枚举
     */
    public enum OperationResult {
        SUCCESS("成功"),
        FAILED("失败"),
        PARTIAL_SUCCESS("部分成功"),
        TIMEOUT("超时"),
        CANCELLED("已取消"),
        PENDING("处理中");
        
        private final String description;
        
        OperationResult(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 风险等级枚举
     */
    public enum RiskLevel {
        LOW("低风险"),
        MEDIUM("中风险"),
        HIGH("高风险"),
        CRITICAL("严重风险");
        
        private final String description;
        
        RiskLevel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    public OperationLogEvent(String userId, String username, OperationType operationType, 
                            String action, String target) {
        super();
        this.operationId = generateOperationId();
        this.userId = userId;
        this.username = username;
        this.operationType = operationType;
        this.action = action;
        this.target = target;
        this.operationTime = LocalDateTime.now();
        this.result = OperationResult.PENDING;
        this.riskLevel = RiskLevel.LOW;
    }
    
    public OperationLogEvent(String userId, String username, OperationType operationType, 
                            String action, String target, String targetId, String description) {
        this(userId, username, operationType, action, target);
        this.targetId = targetId;
        this.description = description;
    }
    
    /**
     * 生成操作ID
     */
    private String generateOperationId() {
        return "op-" + System.currentTimeMillis() + "-" + 
               Integer.toHexString(hashCode());
    }
    
    @Override
    public String getEventType() {
        return "OPERATION_LOG";
    }
    
    @Override
    public String getRoutingKey() {
        return "log.operation"; // RabbitMQ兼容
    }

    @Override
    public String getExchangeName() {
        return "log.exchange"; // RabbitMQ兼容
    }

    @Override
    public String getTopicName() {
        return RocketMQConstants.LOG_TOPIC;
    }

    @Override
    public String getTagName() {
        return RocketMQConstants.OPERATION_LOG_TAG;
    }
    
    @Override
    public EventPriority getPriority() {
        // 根据风险等级设置优先级
        if (riskLevel == null) {
            return EventPriority.LOW;
        }

        switch (riskLevel) {
            case CRITICAL:
                return EventPriority.URGENT;
            case HIGH:
                return EventPriority.HIGH;
            case MEDIUM:
                return EventPriority.NORMAL;
            case LOW:
            default:
                return EventPriority.LOW;
        }
    }
    
    @Override
    public boolean isValid() {
        // 基础验证
        boolean baseValid = getEventId() != null && !getEventId().trim().isEmpty() &&
                           getTimestamp() != null &&
                           getSourceService() != null && !getSourceService().trim().isEmpty();
        
        // 操作日志特定验证
        return baseValid && 
               operationId != null && !operationId.trim().isEmpty() &&
               operationType != null &&
               action != null && !action.trim().isEmpty() &&
               operationTime != null;
    }
    
    /**
     * 获取操作描述
     */
    public String getOperationDescription() {
        StringBuilder desc = new StringBuilder();
        
        if (username != null) {
            desc.append("用户 [").append(username).append("] ");
        } else if (userId != null) {
            desc.append("用户 [").append(userId).append("] ");
        } else {
            desc.append("系统 ");
        }
        
        desc.append("执行 [").append(operationType.getDescription()).append("] 操作: ");
        desc.append(action);
        
        if (target != null) {
            desc.append(" -> ").append(target);
        }
        
        if (targetId != null) {
            desc.append(" (ID: ").append(targetId).append(")");
        }
        
        return desc.toString();
    }
    
    /**
     * 添加操作参数
     */
    public void addOperationParam(String key, Object value) {
        if (operationParams == null) {
            operationParams = new java.util.HashMap<>();
        }
        operationParams.put(key, value);
    }
    
    /**
     * 获取操作参数
     */
    public Object getOperationParam(String key) {
        return operationParams != null ? operationParams.get(key) : null;
    }
    
    /**
     * 添加操作标签
     */
    public void addOperationTag(String key, String value) {
        if (operationTags == null) {
            operationTags = new java.util.HashMap<>();
        }
        operationTags.put(key, value);
    }
    
    /**
     * 获取操作标签
     */
    public String getOperationTag(String key) {
        return operationTags != null ? operationTags.get(key) : null;
    }
    
    /**
     * 检查是否为成功操作
     */
    public boolean isSuccessful() {
        return result == OperationResult.SUCCESS || result == OperationResult.PARTIAL_SUCCESS;
    }
    
    /**
     * 检查是否为失败操作
     */
    public boolean isFailed() {
        return result == OperationResult.FAILED || result == OperationResult.TIMEOUT;
    }
    
    /**
     * 检查是否为高风险操作
     */
    public boolean isHighRisk() {
        return riskLevel == RiskLevel.HIGH || riskLevel == RiskLevel.CRITICAL;
    }
    
    /**
     * 检查是否为敏感操作
     */
    public boolean isSensitiveOperation() {
        return operationType == OperationType.DELETE ||
               operationType == OperationType.RESET ||
               operationType == OperationType.CONFIGURE ||
               isHighRisk();
    }
    
    /**
     * 检查是否需要告警
     */
    public boolean needsAlert() {
        return isHighRisk() || 
               (isFailed() && isSensitiveOperation()) ||
               riskLevel == RiskLevel.CRITICAL;
    }
    
    /**
     * 设置操作结果
     */
    public void setOperationResult(OperationResult result, Long duration, String errorMessage) {
        this.result = result;
        this.duration = duration;
        this.errorMessage = errorMessage;
        
        if (result == OperationResult.FAILED && errorMessage != null) {
            // 根据错误信息调整风险等级
            if (errorMessage.toLowerCase().contains("security") || 
                errorMessage.toLowerCase().contains("unauthorized")) {
                this.riskLevel = RiskLevel.HIGH;
            }
        }
    }
    
    /**
     * 设置客户端信息
     */
    public void setClientInfo(String clientIp, String userAgent, String sessionId) {
        this.clientIp = clientIp;
        this.userAgent = userAgent;
        this.sessionId = sessionId;
    }
    
    /**
     * 设置模块信息
     */
    public void setModuleInfo(String moduleName, String functionName) {
        this.moduleName = moduleName;
        this.functionName = functionName;
    }
    
    /**
     * 设置数据变更信息
     */
    public void setDataChange(Map<String, Object> beforeData, Map<String, Object> afterData) {
        this.beforeData = beforeData;
        this.afterData = afterData;
    }
    
    /**
     * 获取操作上下文
     */
    public Map<String, Object> getOperationContext() {
        Map<String, Object> context = new java.util.HashMap<>();
        context.put("operationId", operationId);
        context.put("userId", userId);
        context.put("username", username);
        context.put("operationType", operationType.name());
        context.put("action", action);
        context.put("target", target);
        context.put("targetId", targetId);
        context.put("description", description);
        context.put("operationTime", operationTime);
        context.put("result", result.name());
        context.put("riskLevel", riskLevel.name());
        
        if (duration != null) {
            context.put("duration", duration);
        }
        
        context.put("clientIp", clientIp);
        context.put("userAgent", userAgent);
        context.put("sessionId", sessionId);
        context.put("requestId", requestId);
        context.put("moduleName", moduleName);
        context.put("functionName", functionName);
        
        if (errorMessage != null) {
            context.put("errorMessage", errorMessage);
            context.put("errorCode", errorCode);
        }
        
        // 状态标记
        context.put("isSuccessful", isSuccessful());
        context.put("isFailed", isFailed());
        context.put("isHighRisk", isHighRisk());
        context.put("isSensitiveOperation", isSensitiveOperation());
        context.put("needsAlert", needsAlert());
        
        if (operationTags != null && !operationTags.isEmpty()) {
            context.put("tags", operationTags);
        }
        
        if (operationParams != null && !operationParams.isEmpty()) {
            context.put("params", operationParams);
        }
        
        return context;
    }
}
