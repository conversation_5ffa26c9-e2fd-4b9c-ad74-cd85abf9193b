package com.nexus.common.event;

import com.nexus.common.constants.RocketMQConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Map;

/**
 * 统计数据更新事件
 */
@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class StatisticsUpdateEvent extends BaseEvent {
    
    /**
     * 统计类型
     */
    private StatisticsType statisticsType;
    
    /**
     * 统计维度（如用户ID、服务ID等）
     */
    private String dimension;
    
    /**
     * 统计指标
     */
    private Map<String, Object> metrics;
    
    /**
     * 增量数据
     */
    private Map<String, Number> increments;
    
    /**
     * 统计时间窗口
     */
    private String timeWindow;
    
    /**
     * 统计类型枚举
     */
    public enum StatisticsType {
        USER_REGISTRATION,     // 用户注册统计
        USER_LOGIN,           // 用户登录统计
        SUBSCRIPTION_USAGE,   // 订阅使用统计
        MCP_TASK_EXECUTION,   // MCP任务执行统计
        API_CALL,            // API调用统计
        SERVICE_PERFORMANCE,  // 服务性能统计
        SYSTEM_RESOURCE      // 系统资源统计
    }
    
    public StatisticsUpdateEvent(String userId, StatisticsType statisticsType, String dimension, 
                               Map<String, Object> metrics, Map<String, Number> increments, String timeWindow) {
        this.setUserId(userId);
        this.statisticsType = statisticsType;
        this.dimension = dimension;
        this.metrics = metrics;
        this.increments = increments;
        this.timeWindow = timeWindow;
        this.setEventType("STATISTICS_UPDATE");
        this.setPriority(EventPriority.LOW); // 统计更新是低优先级
        this.initializeEvent();
    }
    
    @Override
    public String getRoutingKey() {
        return "statistics.update"; // RabbitMQ兼容
    }

    @Override
    public String getExchangeName() {
        return "statistics.exchange"; // RabbitMQ兼容
    }

    @Override
    public String getTopicName() {
        return RocketMQConstants.STATISTICS_TOPIC;
    }

    @Override
    public String getTagName() {
        return RocketMQConstants.STATISTICS_TAG;
    }
    
    @Override
    public boolean isValid() {
        return this.statisticsType != null
                && this.dimension != null && !this.dimension.trim().isEmpty()
                && (this.metrics != null || this.increments != null);
    }
}
