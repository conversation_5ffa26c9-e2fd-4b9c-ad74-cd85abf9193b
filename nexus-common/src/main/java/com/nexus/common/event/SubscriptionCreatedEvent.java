package com.nexus.common.event;

import com.nexus.common.constants.RocketMQConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 订阅创建事件
 */
@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SubscriptionCreatedEvent extends BaseEvent {
    
    /**
     * 订阅ID
     */
    private Long subscriptionId;
    
    /**
     * 服务配置ID
     */
    private Long serviceConfigId;
    
    /**
     * 服务名称
     */
    private String serviceName;
    
    /**
     * 订阅开始时间
     */
    private LocalDateTime startDate;
    
    /**
     * 订阅结束时间
     */
    private LocalDateTime endDate;
    
    /**
     * 调用限制
     */
    private Long callLimit;
    
    /**
     * 订阅备注
     */
    private String notes;

    /**
     * 用户名
     */
    private String username;

    /**
     * 用户邮箱
     */
    private String userEmail;

    /**
     * 订阅类型
     */
    private SubscriptionType subscriptionType;

    /**
     * 计费周期
     */
    private BillingCycle billingCycle;

    /**
     * 订阅价格
     */
    private BigDecimal price;

    /**
     * 货币类型
     */
    private String currency;

    /**
     * 订阅状态
     */
    private SubscriptionStatus status;

    /**
     * 支付方式
     */
    private String paymentMethod;

    /**
     * 支付交易ID
     */
    private String paymentTransactionId;

    /**
     * 促销代码
     */
    private String promoCode;

    /**
     * 折扣金额
     */
    private BigDecimal discountAmount;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 推荐人ID
     */
    private String referrerId;

    /**
     * 订阅来源
     */
    private String subscriptionSource;

    /**
     * 额外属性
     */
    private Map<String, Object> attributes = new HashMap<>();
    
    public SubscriptionCreatedEvent(Long subscriptionId, String userId, Long serviceConfigId, String serviceName, 
                                   LocalDateTime startDate, LocalDateTime endDate, Long callLimit, String notes) {
        this.subscriptionId = subscriptionId;
        this.setUserId(userId);
        this.serviceConfigId = serviceConfigId;
        this.serviceName = serviceName;
        this.startDate = startDate;
        this.endDate = endDate;
        this.callLimit = callLimit;
        this.notes = notes;
        this.setEventType("SUBSCRIPTION_CREATED");
        this.setPriority(EventPriority.HIGH); // 订阅创建是高优先级事件
        this.initializeEvent();
    }
    
    @Override
    public String getRoutingKey() {
        return "subscription.created"; // RabbitMQ兼容
    }

    @Override
    public String getExchangeName() {
        return "subscription.event.exchange"; // RabbitMQ兼容
    }

    @Override
    public String getTopicName() {
        return RocketMQConstants.SUBSCRIPTION_EVENT_TOPIC;
    }

    @Override
    public String getTagName() {
        return "subscription-created";
    }
    
    @Override
    public boolean isValid() {
        return this.subscriptionId != null
                && this.getUserId() != null && !this.getUserId().trim().isEmpty()
                && this.serviceConfigId != null
                && this.serviceName != null && !this.serviceName.trim().isEmpty()
                && this.startDate != null;
    }

    /**
     * 设置用户信息
     */
    public void setUserInfo(String username, String userEmail) {
        this.username = username;
        this.userEmail = userEmail;
    }

    /**
     * 设置订阅计划信息
     */
    public void setSubscriptionPlan(SubscriptionType subscriptionType, BillingCycle billingCycle,
                                   BigDecimal price, String currency) {
        this.subscriptionType = subscriptionType;
        this.billingCycle = billingCycle;
        this.price = price;
        this.currency = currency;
    }

    /**
     * 设置支付信息
     */
    public void setPaymentInfo(String paymentMethod, String paymentTransactionId) {
        this.paymentMethod = paymentMethod;
        this.paymentTransactionId = paymentTransactionId;
    }

    /**
     * 设置促销信息
     */
    public void setPromoInfo(String promoCode, BigDecimal discountAmount) {
        this.promoCode = promoCode;
        this.discountAmount = discountAmount;
    }

    /**
     * 设置客户端信息
     */
    public void setClientInfo(String clientIp, String userAgent) {
        this.clientIp = clientIp;
        this.userAgent = userAgent;
    }

    /**
     * 设置推荐信息
     */
    public void setReferralInfo(String referrerId, String subscriptionSource) {
        this.referrerId = referrerId;
        this.subscriptionSource = subscriptionSource;
    }

    /**
     * 添加额外属性
     */
    public void addAttribute(String key, Object value) {
        this.attributes.put(key, value);
    }

    /**
     * 判断是否为试用订阅
     */
    public boolean isTrialSubscription() {
        return subscriptionType == SubscriptionType.TRIAL;
    }

    /**
     * 判断是否为付费订阅
     */
    public boolean isPaidSubscription() {
        return subscriptionType != null && subscriptionType != SubscriptionType.TRIAL &&
               price != null && price.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 判断是否有促销折扣
     */
    public boolean hasDiscount() {
        return promoCode != null && !promoCode.trim().isEmpty() &&
               discountAmount != null && discountAmount.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 判断是否为推荐订阅
     */
    public boolean isReferralSubscription() {
        return referrerId != null && !referrerId.trim().isEmpty();
    }

    /**
     * 获取实际支付金额
     */
    public BigDecimal getActualPaymentAmount() {
        if (price == null) {
            return BigDecimal.ZERO;
        }

        BigDecimal actualAmount = price;
        if (hasDiscount()) {
            actualAmount = actualAmount.subtract(discountAmount);
        }

        return actualAmount.max(BigDecimal.ZERO);
    }

    /**
     * 获取订阅描述
     */
    public String getSubscriptionDescription() {
        StringBuilder desc = new StringBuilder();
        desc.append("用户 ").append(username != null ? username : getUserId())
            .append(" 创建了订阅：").append(serviceName);

        if (subscriptionType != null) {
            desc.append("（").append(subscriptionType.getDescription()).append("）");
        }

        if (billingCycle != null) {
            desc.append("，计费周期：").append(billingCycle.getDescription());
        }

        if (isPaidSubscription()) {
            desc.append("，价格：").append(getActualPaymentAmount()).append(" ").append(currency);
        }

        if (hasDiscount()) {
            desc.append("，享受折扣：").append(discountAmount).append(" ").append(currency);
        }

        return desc.toString();
    }

    /**
     * 订阅类型枚举
     */
    public enum SubscriptionType {
        BASIC("基础版"),
        PREMIUM("高级版"),
        ENTERPRISE("企业版"),
        TRIAL("试用版");

        private final String description;

        SubscriptionType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 计费周期枚举
     */
    public enum BillingCycle {
        MONTHLY("月付"),
        QUARTERLY("季付"),
        YEARLY("年付"),
        LIFETIME("终身");

        private final String description;

        BillingCycle(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 订阅状态枚举
     */
    public enum SubscriptionStatus {
        ACTIVE("激活"),
        PENDING("待激活"),
        SUSPENDED("暂停"),
        CANCELLED("已取消"),
        EXPIRED("已过期");

        private final String description;

        SubscriptionStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
