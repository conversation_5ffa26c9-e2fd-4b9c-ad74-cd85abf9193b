package com.nexus.common.event;

import com.nexus.common.constants.RocketMQConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 订阅续费事件
 * 当订阅成功续费时触发
 */
@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SubscriptionRenewedEvent extends BaseEvent {
    
    /**
     * 订阅ID
     */
    private Long subscriptionId;
    
    /**
     * 服务配置ID
     */
    private Long serviceConfigId;
    
    /**
     * 服务名称
     */
    private String serviceName;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 用户邮箱
     */
    private String userEmail;
    
    /**
     * 续费类型
     */
    private RenewalType renewalType;
    
    /**
     * 原到期时间
     */
    private LocalDateTime oldEndDate;
    
    /**
     * 新到期时间
     */
    private LocalDateTime newEndDate;
    
    /**
     * 续费金额
     */
    private BigDecimal renewalAmount;
    
    /**
     * 货币类型
     */
    private String currency;
    
    /**
     * 支付方式
     */
    private String paymentMethod;
    
    /**
     * 支付交易ID
     */
    private String paymentTransactionId;
    
    /**
     * 促销代码
     */
    private String promoCode;
    
    /**
     * 折扣金额
     */
    private BigDecimal discountAmount;
    
    /**
     * 续费时间
     */
    private LocalDateTime renewalTime;
    
    /**
     * 客户端IP
     */
    private String clientIp;
    
    /**
     * 用户代理
     */
    private String userAgent;
    
    /**
     * 额外属性
     */
    private Map<String, Object> attributes = new HashMap<>();
    
    /**
     * 续费类型枚举
     */
    public enum RenewalType {
        AUTO("自动续费"),
        MANUAL("手动续费"),
        GRACE("宽限期续费");
        
        private final String description;
        
        RenewalType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    public SubscriptionRenewedEvent(Long subscriptionId, String userId, Long serviceConfigId, 
                                   String serviceName, RenewalType renewalType,
                                   LocalDateTime oldEndDate, LocalDateTime newEndDate,
                                   BigDecimal renewalAmount, String currency) {
        this.subscriptionId = subscriptionId;
        this.setUserId(userId);
        this.serviceConfigId = serviceConfigId;
        this.serviceName = serviceName;
        this.renewalType = renewalType;
        this.oldEndDate = oldEndDate;
        this.newEndDate = newEndDate;
        this.renewalAmount = renewalAmount;
        this.currency = currency;
        this.renewalTime = LocalDateTime.now();
        this.setEventType("SUBSCRIPTION_RENEWED");
        this.setPriority(EventPriority.HIGH); // 续费是高优先级事件
        this.initializeEvent();
    }
    
    /**
     * 设置用户信息
     */
    public void setUserInfo(String username, String userEmail) {
        this.username = username;
        this.userEmail = userEmail;
    }
    
    /**
     * 设置支付信息
     */
    public void setPaymentInfo(String paymentMethod, String paymentTransactionId) {
        this.paymentMethod = paymentMethod;
        this.paymentTransactionId = paymentTransactionId;
    }
    
    /**
     * 设置促销信息
     */
    public void setPromoInfo(String promoCode, BigDecimal discountAmount) {
        this.promoCode = promoCode;
        this.discountAmount = discountAmount;
    }
    
    /**
     * 设置客户端信息
     */
    public void setClientInfo(String clientIp, String userAgent) {
        this.clientIp = clientIp;
        this.userAgent = userAgent;
    }
    
    /**
     * 添加额外属性
     */
    public void addAttribute(String key, Object value) {
        this.attributes.put(key, value);
    }
    
    /**
     * 判断是否为自动续费
     */
    public boolean isAutoRenewal() {
        return renewalType == RenewalType.AUTO;
    }
    
    /**
     * 判断是否为手动续费
     */
    public boolean isManualRenewal() {
        return renewalType == RenewalType.MANUAL;
    }
    
    /**
     * 判断是否为宽限期续费
     */
    public boolean isGraceRenewal() {
        return renewalType == RenewalType.GRACE;
    }
    
    /**
     * 判断是否有促销折扣
     */
    public boolean hasDiscount() {
        return promoCode != null && !promoCode.trim().isEmpty() &&
               discountAmount != null && discountAmount.compareTo(BigDecimal.ZERO) > 0;
    }
    
    /**
     * 获取实际支付金额
     */
    public BigDecimal getActualPaymentAmount() {
        if (renewalAmount == null) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal actualAmount = renewalAmount;
        if (hasDiscount()) {
            actualAmount = actualAmount.subtract(discountAmount);
        }
        
        return actualAmount.max(BigDecimal.ZERO);
    }
    
    /**
     * 获取续费天数
     */
    public long getRenewalDays() {
        if (oldEndDate == null || newEndDate == null) {
            return 0;
        }
        
        return java.time.Duration.between(oldEndDate, newEndDate).toDays();
    }
    
    /**
     * 获取续费描述
     */
    public String getRenewalDescription() {
        StringBuilder desc = new StringBuilder();
        desc.append("用户 ").append(username != null ? username : getUserId())
            .append(" 成功续费订阅：").append(serviceName)
            .append("（").append(renewalType.getDescription()).append("）");
        
        if (renewalAmount != null) {
            desc.append("，续费金额：").append(getActualPaymentAmount()).append(" ").append(currency);
        }
        
        if (hasDiscount()) {
            desc.append("，享受折扣：").append(discountAmount).append(" ").append(currency);
        }
        
        long days = getRenewalDays();
        if (days > 0) {
            desc.append("，续费天数：").append(days).append("天");
        }
        
        return desc.toString();
    }
    
    @Override
    public String getRoutingKey() {
        return "subscription.renewed"; // RabbitMQ兼容
    }

    @Override
    public String getExchangeName() {
        return "subscription.event.exchange"; // RabbitMQ兼容
    }

    @Override
    public String getTopicName() {
        return RocketMQConstants.SUBSCRIPTION_EVENT_TOPIC;
    }

    @Override
    public String getTagName() {
        return "subscription-renewed";
    }
    
    @Override
    public boolean isValid() {
        return this.subscriptionId != null
                && this.getUserId() != null && !this.getUserId().trim().isEmpty()
                && this.serviceConfigId != null
                && this.serviceName != null && !this.serviceName.trim().isEmpty()
                && this.renewalType != null
                && this.oldEndDate != null
                && this.newEndDate != null
                && this.renewalAmount != null
                && this.currency != null && !this.currency.trim().isEmpty()
                && this.renewalTime != null;
    }
}
