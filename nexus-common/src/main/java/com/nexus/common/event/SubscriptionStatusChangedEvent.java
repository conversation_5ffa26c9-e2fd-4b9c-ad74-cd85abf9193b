package com.nexus.common.event;

import com.nexus.common.constants.RocketMQConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 订阅状态变更事件
 */
@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SubscriptionStatusChangedEvent extends BaseEvent {
    
    /**
     * 订阅ID
     */
    private Long subscriptionId;
    
    /**
     * 服务名称
     */
    private String serviceName;
    
    /**
     * 原状态
     */
    private String oldStatus;
    
    /**
     * 新状态
     */
    private String newStatus;
    
    /**
     * 变更原因
     */
    private String changeReason;
    
    /**
     * 操作人ID
     */
    private String operatorId;

    /**
     * 服务配置ID
     */
    private Long serviceConfigId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 用户邮箱
     */
    private String userEmail;

    /**
     * 变更描述
     */
    private String changeDescription;

    /**
     * 变更时间
     */
    private LocalDateTime changeTime;

    /**
     * 操作人类型
     */
    private OperatorType operatorType;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 额外属性
     */
    private Map<String, Object> attributes = new HashMap<>();
    
    public SubscriptionStatusChangedEvent(Long subscriptionId, String userId, String serviceName,
                                        String oldStatus, String newStatus, String changeReason, String operatorId) {
        this.subscriptionId = subscriptionId;
        this.setUserId(userId);
        this.serviceName = serviceName;
        this.oldStatus = oldStatus;
        this.newStatus = newStatus;
        this.changeReason = changeReason;
        this.operatorId = operatorId;
        this.changeTime = LocalDateTime.now();
        this.setEventType("SUBSCRIPTION_STATUS_CHANGED");
        this.setPriority(EventPriority.NORMAL);
        this.initializeEvent();
    }

    /**
     * 设置用户信息
     */
    public void setUserInfo(String username, String userEmail) {
        this.username = username;
        this.userEmail = userEmail;
    }

    /**
     * 设置变更描述
     */
    public void setChangeDescription(String changeDescription) {
        this.changeDescription = changeDescription;
    }

    /**
     * 设置客户端信息
     */
    public void setClientInfo(String clientIp, String userAgent) {
        this.clientIp = clientIp;
        this.userAgent = userAgent;
    }

    /**
     * 设置操作人类型
     */
    public void setOperatorType(OperatorType operatorType) {
        this.operatorType = operatorType;
    }

    /**
     * 添加额外属性
     */
    public void addAttribute(String key, Object value) {
        this.attributes.put(key, value);
    }

    /**
     * 判断是否为激活状态变更
     */
    public boolean isActivation() {
        return "ACTIVE".equals(newStatus) && !"ACTIVE".equals(oldStatus);
    }

    /**
     * 判断是否为取消状态变更
     */
    public boolean isCancellation() {
        return "CANCELLED".equals(newStatus);
    }

    /**
     * 判断是否为暂停状态变更
     */
    public boolean isSuspension() {
        return "SUSPENDED".equals(newStatus);
    }

    /**
     * 判断是否为过期状态变更
     */
    public boolean isExpiration() {
        return "EXPIRED".equals(newStatus);
    }

    /**
     * 判断是否为用户主动操作
     */
    public boolean isUserInitiated() {
        return operatorType == OperatorType.USER;
    }

    /**
     * 判断是否为管理员操作
     */
    public boolean isAdminInitiated() {
        return operatorType == OperatorType.ADMIN;
    }

    /**
     * 判断是否为系统自动操作
     */
    public boolean isSystemInitiated() {
        return operatorType == OperatorType.SYSTEM;
    }

    /**
     * 判断是否需要发送通知
     */
    public boolean needsNotification() {
        // 激活、取消、暂停、过期都需要通知用户
        return isActivation() || isCancellation() || isSuspension() || isExpiration();
    }

    /**
     * 获取状态变更描述
     */
    public String getStatusChangeDescription() {
        if (changeDescription != null && !changeDescription.trim().isEmpty()) {
            return changeDescription;
        }

        StringBuilder desc = new StringBuilder();
        desc.append("订阅 ").append(serviceName)
            .append(" 状态从 ").append(oldStatus)
            .append(" 变更为 ").append(newStatus)
            .append("，原因：").append(changeReason);

        if (operatorType == OperatorType.USER) {
            desc.append("（用户操作）");
        } else if (operatorType == OperatorType.ADMIN) {
            desc.append("（管理员操作）");
        } else if (operatorType == OperatorType.SYSTEM) {
            desc.append("（系统自动）");
        }

        return desc.toString();
    }
    
    @Override
    public String getRoutingKey() {
        return "subscription.status.changed"; // RabbitMQ兼容
    }

    @Override
    public String getExchangeName() {
        return "subscription.event.exchange"; // RabbitMQ兼容
    }

    @Override
    public String getTopicName() {
        return RocketMQConstants.SUBSCRIPTION_EVENT_TOPIC;
    }

    @Override
    public String getTagName() {
        return "subscription-status-changed";
    }
    
    @Override
    public boolean isValid() {
        return this.subscriptionId != null
                && this.getUserId() != null && !this.getUserId().trim().isEmpty()
                && this.serviceName != null && !this.serviceName.trim().isEmpty()
                && this.oldStatus != null && !this.oldStatus.trim().isEmpty()
                && this.newStatus != null && !this.newStatus.trim().isEmpty();
    }

    /**
     * 操作人类型枚举
     */
    public enum OperatorType {
        USER("用户"),
        ADMIN("管理员"),
        SYSTEM("系统");

        private final String description;

        OperatorType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
