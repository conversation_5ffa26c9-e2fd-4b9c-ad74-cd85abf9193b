package com.nexus.common.event;

import com.nexus.common.constants.RocketMQConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Map;

/**
 * 系统通知事件
 */
@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SystemNotificationEvent extends BaseEvent {
    
    /**
     * 通知标题
     */
    private String title;
    
    /**
     * 通知内容
     */
    private String content;
    
    /**
     * 通知类型
     */
    private NotificationType notificationType;
    
    /**
     * 目标用户（如果为空则为广播通知）
     */
    private String targetUserId;
    
    /**
     * 通知渠道
     */
    private NotificationChannel channel;
    
    /**
     * 额外数据
     */
    private Map<String, Object> extraData;
    
    /**
     * 是否需要确认
     */
    private Boolean requireAck;
    
    /**
     * 通知类型枚举
     */
    public enum NotificationType {
        INFO,       // 信息通知
        WARNING,    // 警告通知
        ERROR,      // 错误通知
        SUCCESS,    // 成功通知
        MAINTENANCE, // 维护通知
        ANNOUNCEMENT // 公告通知
    }
    
    /**
     * 通知渠道枚举
     */
    public enum NotificationChannel {
        WEBSOCKET,  // WebSocket推送
        EMAIL,      // 邮件通知
        SMS,        // 短信通知
        PUSH,       // 推送通知
        ALL         // 所有渠道
    }
    
    public SystemNotificationEvent(String userId, String title, String content, NotificationType notificationType, 
                                 String targetUserId, NotificationChannel channel, Boolean requireAck) {
        this.setUserId(userId);
        this.title = title;
        this.content = content;
        this.notificationType = notificationType;
        this.targetUserId = targetUserId;
        this.channel = channel;
        this.requireAck = requireAck != null ? requireAck : false;
        this.setEventType("SYSTEM_NOTIFICATION");
        // 根据通知类型设置优先级
        this.setPriority(getNotificationPriority(notificationType));
        this.initializeEvent();
    }
    
    private EventPriority getNotificationPriority(NotificationType notificationType) {
        switch (notificationType) {
            case ERROR:
                return EventPriority.URGENT;
            case WARNING:
            case MAINTENANCE:
                return EventPriority.HIGH;
            case SUCCESS:
            case INFO:
                return EventPriority.NORMAL;
            case ANNOUNCEMENT:
                return EventPriority.LOW;
            default:
                return EventPriority.NORMAL;
        }
    }
    
    @Override
    public String getRoutingKey() {
        return "system.notification"; // RabbitMQ兼容
    }

    @Override
    public String getExchangeName() {
        return "notification.exchange"; // RabbitMQ兼容
    }

    @Override
    public String getTopicName() {
        return RocketMQConstants.NOTIFICATION_TOPIC;
    }

    @Override
    public String getTagName() {
        return "system-notification";
    }
    
    @Override
    public boolean isValid() {
        return this.title != null && !this.title.trim().isEmpty()
                && this.content != null && !this.content.trim().isEmpty()
                && this.notificationType != null
                && this.channel != null;
    }
}
