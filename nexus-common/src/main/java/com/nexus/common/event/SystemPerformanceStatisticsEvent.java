package com.nexus.common.event;

import com.nexus.common.constants.RocketMQConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 系统性能统计事件
 * 记录系统的性能指标数据，用于监控和分析
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class SystemPerformanceStatisticsEvent extends BaseEvent {
    
    /**
     * 服务名称
     */
    private String serviceName;
    
    /**
     * 实例ID
     */
    private String instanceId;
    
    /**
     * 主机名
     */
    private String hostname;
    
    /**
     * 性能指标类型
     */
    private MetricType metricType;
    
    /**
     * 指标名称
     */
    private String metricName;
    
    /**
     * 指标值
     */
    private Double metricValue;
    
    /**
     * 指标单位
     */
    private String metricUnit;
    
    /**
     * 测量时间
     */
    private LocalDateTime measurementTime;
    
    /**
     * 时间窗口（秒）
     */
    private Integer timeWindow;
    
    /**
     * CPU使用率（百分比）
     */
    private Double cpuUsage;
    
    /**
     * 内存使用率（百分比）
     */
    private Double memoryUsage;
    
    /**
     * 磁盘使用率（百分比）
     */
    private Double diskUsage;
    
    /**
     * 网络入流量（字节/秒）
     */
    private Long networkInBytes;
    
    /**
     * 网络出流量（字节/秒）
     */
    private Long networkOutBytes;
    
    /**
     * 活跃连接数
     */
    private Integer activeConnections;
    
    /**
     * 请求处理时间（毫秒）
     */
    private Double responseTime;
    
    /**
     * 吞吐量（请求/秒）
     */
    private Double throughput;
    
    /**
     * 错误率（百分比）
     */
    private Double errorRate;
    
    /**
     * JVM堆内存使用（字节）
     */
    private Long jvmHeapUsed;
    
    /**
     * JVM堆内存最大值（字节）
     */
    private Long jvmHeapMax;
    
    /**
     * GC次数
     */
    private Integer gcCount;
    
    /**
     * GC耗时（毫秒）
     */
    private Long gcTime;
    
    /**
     * 线程数
     */
    private Integer threadCount;
    
    /**
     * 数据库连接池大小
     */
    private Integer dbConnectionPoolSize;
    
    /**
     * 数据库活跃连接数
     */
    private Integer dbActiveConnections;
    
    /**
     * 缓存命中率（百分比）
     */
    private Double cacheHitRate;
    
    /**
     * 队列长度
     */
    private Integer queueLength;
    
    /**
     * 额外的性能指标
     */
    private Map<String, Object> additionalMetrics;
    
    /**
     * 性能标签
     */
    private Map<String, String> performanceTags;
    
    /**
     * 性能指标类型枚举
     */
    public enum MetricType {
        CPU("CPU指标"),
        MEMORY("内存指标"),
        DISK("磁盘指标"),
        NETWORK("网络指标"),
        APPLICATION("应用指标"),
        DATABASE("数据库指标"),
        CACHE("缓存指标"),
        JVM("JVM指标"),
        CUSTOM("自定义指标");
        
        private final String description;
        
        MetricType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    public SystemPerformanceStatisticsEvent(String serviceName, String instanceId, 
                                           MetricType metricType, String metricName, Double metricValue) {
        super();
        this.serviceName = serviceName;
        this.instanceId = instanceId;
        this.metricType = metricType;
        this.metricName = metricName;
        this.metricValue = metricValue;
        this.measurementTime = LocalDateTime.now();
    }
    
    public SystemPerformanceStatisticsEvent(String serviceName, String instanceId, String hostname,
                                           MetricType metricType, String metricName, Double metricValue, String metricUnit) {
        this(serviceName, instanceId, metricType, metricName, metricValue);
        this.hostname = hostname;
        this.metricUnit = metricUnit;
    }
    
    @Override
    public String getEventType() {
        return "SYSTEM_PERFORMANCE_STATISTICS";
    }
    
    @Override
    public String getRoutingKey() {
        return "statistics.system.performance"; // RabbitMQ兼容
    }

    @Override
    public String getExchangeName() {
        return "statistics.exchange"; // RabbitMQ兼容
    }

    @Override
    public String getTopicName() {
        return RocketMQConstants.STATISTICS_TOPIC;
    }

    @Override
    public String getTagName() {
        return RocketMQConstants.SYSTEM_PERFORMANCE_STATISTICS_TAG;
    }
    
    @Override
    public EventPriority getPriority() {
        // 性能统计事件优先级中等
        return EventPriority.NORMAL;
    }
    
    @Override
    public boolean isValid() {
        // 基础验证
        boolean baseValid = getEventId() != null && !getEventId().trim().isEmpty() &&
                           getTimestamp() != null &&
                           getSourceService() != null && !getSourceService().trim().isEmpty();
        
        // 性能统计特定验证
        return baseValid && 
               serviceName != null && !serviceName.trim().isEmpty() &&
               instanceId != null && !instanceId.trim().isEmpty() &&
               metricType != null &&
               metricName != null && !metricName.trim().isEmpty() &&
               metricValue != null &&
               measurementTime != null;
    }
    
    /**
     * 获取性能描述
     */
    public String getPerformanceDescription() {
        return String.format("服务 [%s] 实例 [%s] 的 %s 指标 [%s]: %s %s", 
                serviceName, instanceId, metricType.getDescription(), metricName, 
                metricValue, metricUnit != null ? metricUnit : "");
    }
    
    /**
     * 添加额外指标
     */
    public void addAdditionalMetric(String key, Object value) {
        if (additionalMetrics == null) {
            additionalMetrics = new java.util.HashMap<>();
        }
        additionalMetrics.put(key, value);
    }
    
    /**
     * 获取额外指标
     */
    public Object getAdditionalMetric(String key) {
        return additionalMetrics != null ? additionalMetrics.get(key) : null;
    }
    
    /**
     * 添加性能标签
     */
    public void addPerformanceTag(String key, String value) {
        if (performanceTags == null) {
            performanceTags = new java.util.HashMap<>();
        }
        performanceTags.put(key, value);
    }
    
    /**
     * 获取性能标签
     */
    public String getPerformanceTag(String key) {
        return performanceTags != null ? performanceTags.get(key) : null;
    }
    
    /**
     * 检查是否为高CPU使用率
     */
    public boolean isHighCpuUsage() {
        return cpuUsage != null && cpuUsage > 80.0;
    }
    
    /**
     * 检查是否为高内存使用率
     */
    public boolean isHighMemoryUsage() {
        return memoryUsage != null && memoryUsage > 85.0;
    }
    
    /**
     * 检查是否为高磁盘使用率
     */
    public boolean isHighDiskUsage() {
        return diskUsage != null && diskUsage > 90.0;
    }
    
    /**
     * 检查是否为高错误率
     */
    public boolean isHighErrorRate() {
        return errorRate != null && errorRate > 5.0;
    }
    
    /**
     * 检查是否为慢响应
     */
    public boolean isSlowResponse() {
        return responseTime != null && responseTime > 5000.0; // 超过5秒
    }
    
    /**
     * 检查是否为低缓存命中率
     */
    public boolean isLowCacheHitRate() {
        return cacheHitRate != null && cacheHitRate < 70.0;
    }
    
    /**
     * 检查是否需要告警
     */
    public boolean needsAlert() {
        return isHighCpuUsage() || isHighMemoryUsage() || isHighDiskUsage() || 
               isHighErrorRate() || isSlowResponse() || isLowCacheHitRate();
    }
    
    /**
     * 获取告警级别
     */
    public String getAlertLevel() {
        if (isHighCpuUsage() || isHighMemoryUsage() || isHighErrorRate()) {
            return "CRITICAL";
        } else if (isHighDiskUsage() || isSlowResponse()) {
            return "WARNING";
        } else if (isLowCacheHitRate()) {
            return "INFO";
        } else {
            return "NORMAL";
        }
    }
    
    /**
     * 获取性能统计上下文
     */
    public Map<String, Object> getPerformanceContext() {
        Map<String, Object> context = new java.util.HashMap<>();
        context.put("serviceName", serviceName);
        context.put("instanceId", instanceId);
        context.put("hostname", hostname);
        context.put("metricType", metricType.name());
        context.put("metricName", metricName);
        context.put("metricValue", metricValue);
        context.put("metricUnit", metricUnit);
        context.put("measurementTime", measurementTime);
        
        if (timeWindow != null) {
            context.put("timeWindow", timeWindow);
        }
        
        // 系统资源指标
        if (cpuUsage != null) context.put("cpuUsage", cpuUsage);
        if (memoryUsage != null) context.put("memoryUsage", memoryUsage);
        if (diskUsage != null) context.put("diskUsage", diskUsage);
        
        // 网络指标
        if (networkInBytes != null) context.put("networkInBytes", networkInBytes);
        if (networkOutBytes != null) context.put("networkOutBytes", networkOutBytes);
        if (activeConnections != null) context.put("activeConnections", activeConnections);
        
        // 应用性能指标
        if (responseTime != null) context.put("responseTime", responseTime);
        if (throughput != null) context.put("throughput", throughput);
        if (errorRate != null) context.put("errorRate", errorRate);
        
        // JVM指标
        if (jvmHeapUsed != null) context.put("jvmHeapUsed", jvmHeapUsed);
        if (jvmHeapMax != null) context.put("jvmHeapMax", jvmHeapMax);
        if (gcCount != null) context.put("gcCount", gcCount);
        if (gcTime != null) context.put("gcTime", gcTime);
        if (threadCount != null) context.put("threadCount", threadCount);
        
        // 数据库指标
        if (dbConnectionPoolSize != null) context.put("dbConnectionPoolSize", dbConnectionPoolSize);
        if (dbActiveConnections != null) context.put("dbActiveConnections", dbActiveConnections);
        
        // 缓存指标
        if (cacheHitRate != null) context.put("cacheHitRate", cacheHitRate);
        
        // 队列指标
        if (queueLength != null) context.put("queueLength", queueLength);
        
        // 告警相关
        context.put("needsAlert", needsAlert());
        context.put("alertLevel", getAlertLevel());
        
        if (performanceTags != null && !performanceTags.isEmpty()) {
            context.put("tags", performanceTags);
        }
        
        if (additionalMetrics != null && !additionalMetrics.isEmpty()) {
            context.put("additionalMetrics", additionalMetrics);
        }
        
        return context;
    }
    
    /**
     * 设置系统资源指标
     */
    public void setSystemResourceMetrics(Double cpuUsage, Double memoryUsage, Double diskUsage) {
        this.cpuUsage = cpuUsage;
        this.memoryUsage = memoryUsage;
        this.diskUsage = diskUsage;
    }
    
    /**
     * 设置应用性能指标
     */
    public void setApplicationMetrics(Double responseTime, Double throughput, Double errorRate) {
        this.responseTime = responseTime;
        this.throughput = throughput;
        this.errorRate = errorRate;
    }
    
    /**
     * 设置JVM指标
     */
    public void setJvmMetrics(Long jvmHeapUsed, Long jvmHeapMax, Integer gcCount, Long gcTime, Integer threadCount) {
        this.jvmHeapUsed = jvmHeapUsed;
        this.jvmHeapMax = jvmHeapMax;
        this.gcCount = gcCount;
        this.gcTime = gcTime;
        this.threadCount = threadCount;
    }
}
