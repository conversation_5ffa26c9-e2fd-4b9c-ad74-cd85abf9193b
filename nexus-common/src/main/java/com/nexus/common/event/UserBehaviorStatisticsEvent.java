package com.nexus.common.event;

import com.nexus.common.constants.RocketMQConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 用户行为统计事件
 * 记录用户的各种行为数据，用于统计分析
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class UserBehaviorStatisticsEvent extends BaseEvent {
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 行为类型
     */
    private BehaviorType behaviorType;
    
    /**
     * 行为动作
     */
    private String action;
    
    /**
     * 行为目标（如：工具名称、页面路径等）
     */
    private String target;
    
    /**
     * 行为发生时间
     */
    private LocalDateTime behaviorTime;
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 客户端IP
     */
    private String clientIp;
    
    /**
     * 用户代理
     */
    private String userAgent;
    
    /**
     * 设备类型
     */
    private String deviceType;
    
    /**
     * 浏览器类型
     */
    private String browserType;
    
    /**
     * 操作系统
     */
    private String operatingSystem;
    
    /**
     * 地理位置信息
     */
    private Map<String, String> geoLocation;
    
    /**
     * 行为持续时间（毫秒）
     */
    private Long duration;
    
    /**
     * 行为结果状态
     */
    private String resultStatus;
    
    /**
     * 额外的行为数据
     */
    private Map<String, Object> behaviorData;
    
    /**
     * 行为标签
     */
    private Map<String, String> behaviorTags;
    
    /**
     * 行为类型枚举
     */
    public enum BehaviorType {
        LOGIN("登录"),
        LOGOUT("登出"),
        REGISTER("注册"),
        PAGE_VIEW("页面访问"),
        TOOL_CALL("工具调用"),
        API_REQUEST("API请求"),
        FILE_UPLOAD("文件上传"),
        FILE_DOWNLOAD("文件下载"),
        SEARCH("搜索"),
        SUBSCRIPTION("订阅操作"),
        PAYMENT("支付操作"),
        PROFILE_UPDATE("资料更新"),
        SETTINGS_CHANGE("设置变更"),
        ERROR_OCCURRED("错误发生"),
        FEATURE_USAGE("功能使用");
        
        private final String description;
        
        BehaviorType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    public UserBehaviorStatisticsEvent(String userId, BehaviorType behaviorType, String action, String target) {
        super();
        this.userId = userId;
        this.behaviorType = behaviorType;
        this.action = action;
        this.target = target;
        this.behaviorTime = LocalDateTime.now();
    }
    
    public UserBehaviorStatisticsEvent(String userId, BehaviorType behaviorType, String action, String target,
                                      String sessionId, String clientIp, String userAgent) {
        this(userId, behaviorType, action, target);
        this.sessionId = sessionId;
        this.clientIp = clientIp;
        this.userAgent = userAgent;
    }
    
    @Override
    public String getEventType() {
        return "USER_BEHAVIOR_STATISTICS";
    }
    
    @Override
    public String getRoutingKey() {
        return "statistics.user.behavior"; // RabbitMQ兼容
    }

    @Override
    public String getExchangeName() {
        return "statistics.exchange"; // RabbitMQ兼容
    }

    @Override
    public String getTopicName() {
        return RocketMQConstants.STATISTICS_TOPIC;
    }

    @Override
    public String getTagName() {
        return RocketMQConstants.USER_BEHAVIOR_STATISTICS_TAG;
    }
    
    @Override
    public EventPriority getPriority() {
        // 统计事件通常优先级较低
        return EventPriority.LOW;
    }
    
    @Override
    public boolean isValid() {
        // 基础验证
        boolean baseValid = getEventId() != null && !getEventId().trim().isEmpty() &&
                           getTimestamp() != null &&
                           getSourceService() != null && !getSourceService().trim().isEmpty();
        
        // 行为统计特定验证
        return baseValid && 
               userId != null && !userId.trim().isEmpty() &&
               behaviorType != null &&
               action != null && !action.trim().isEmpty() &&
               behaviorTime != null;
    }
    
    /**
     * 获取行为描述
     */
    public String getBehaviorDescription() {
        return String.format("用户 [%s] 执行 [%s] 操作: %s -> %s", 
                userId, behaviorType.getDescription(), action, target != null ? target : "N/A");
    }
    
    /**
     * 添加行为数据
     */
    public void addBehaviorData(String key, Object value) {
        if (behaviorData == null) {
            behaviorData = new java.util.HashMap<>();
        }
        behaviorData.put(key, value);
    }
    
    /**
     * 获取行为数据
     */
    public Object getBehaviorData(String key) {
        return behaviorData != null ? behaviorData.get(key) : null;
    }
    
    /**
     * 添加行为标签
     */
    public void addBehaviorTag(String key, String value) {
        if (behaviorTags == null) {
            behaviorTags = new java.util.HashMap<>();
        }
        behaviorTags.put(key, value);
    }
    
    /**
     * 获取行为标签
     */
    public String getBehaviorTag(String key) {
        return behaviorTags != null ? behaviorTags.get(key) : null;
    }
    
    /**
     * 检查是否为成功的行为
     */
    public boolean isSuccessfulBehavior() {
        return "SUCCESS".equalsIgnoreCase(resultStatus) || 
               "COMPLETED".equalsIgnoreCase(resultStatus) ||
               "OK".equalsIgnoreCase(resultStatus);
    }
    
    /**
     * 检查是否为失败的行为
     */
    public boolean isFailedBehavior() {
        return "FAILED".equalsIgnoreCase(resultStatus) || 
               "ERROR".equalsIgnoreCase(resultStatus) ||
               "TIMEOUT".equalsIgnoreCase(resultStatus);
    }
    
    /**
     * 检查是否为长时间行为
     */
    public boolean isLongDurationBehavior() {
        return duration != null && duration > 30000; // 超过30秒
    }
    
    /**
     * 检查是否为移动设备
     */
    public boolean isMobileDevice() {
        return deviceType != null && 
               (deviceType.toLowerCase().contains("mobile") || 
                deviceType.toLowerCase().contains("tablet"));
    }
    
    /**
     * 获取行为统计上下文
     */
    public Map<String, Object> getBehaviorContext() {
        Map<String, Object> context = new java.util.HashMap<>();
        context.put("userId", userId);
        context.put("behaviorType", behaviorType.name());
        context.put("action", action);
        context.put("target", target);
        context.put("behaviorTime", behaviorTime);
        context.put("sessionId", sessionId);
        context.put("clientIp", clientIp);
        context.put("deviceType", deviceType);
        context.put("browserType", browserType);
        context.put("operatingSystem", operatingSystem);
        
        if (duration != null) {
            context.put("duration", duration);
            context.put("isLongDuration", isLongDurationBehavior());
        }
        
        if (resultStatus != null) {
            context.put("resultStatus", resultStatus);
            context.put("isSuccessful", isSuccessfulBehavior());
            context.put("isFailed", isFailedBehavior());
        }
        
        context.put("isMobileDevice", isMobileDevice());
        
        if (geoLocation != null && !geoLocation.isEmpty()) {
            context.put("geoLocation", geoLocation);
        }
        
        if (behaviorTags != null && !behaviorTags.isEmpty()) {
            context.put("tags", behaviorTags);
        }
        
        return context;
    }
    
    /**
     * 设置地理位置信息
     */
    public void setGeoLocation(String country, String region, String city) {
        if (geoLocation == null) {
            geoLocation = new java.util.HashMap<>();
        }
        geoLocation.put("country", country);
        geoLocation.put("region", region);
        geoLocation.put("city", city);
    }
    
    /**
     * 设置设备信息
     */
    public void setDeviceInfo(String deviceType, String browserType, String operatingSystem) {
        this.deviceType = deviceType;
        this.browserType = browserType;
        this.operatingSystem = operatingSystem;
    }
    
    /**
     * 设置行为结果
     */
    public void setBehaviorResult(String resultStatus, Long duration) {
        this.resultStatus = resultStatus;
        this.duration = duration;
    }
}
