package com.nexus.common.event;

import com.nexus.common.constants.RocketMQConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 用户登录事件
 */
@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class UserLoginEvent extends BaseEvent {
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 登录IP
     */
    private String loginIp;
    
    /**
     * 登录方式（password/api_key）
     */
    private String loginMethod;
    
    /**
     * 登录是否成功
     */
    private Boolean loginSuccess;
    
    /**
     * 失败原因（如果登录失败）
     */
    private String failureReason;
    
    public UserLoginEvent(String userId, String username, String loginIp, String loginMethod, Boolean loginSuccess, String failureReason) {
        this.setUserId(userId);
        this.username = username;
        this.loginIp = loginIp;
        this.loginMethod = loginMethod;
        this.loginSuccess = loginSuccess;
        this.failureReason = failureReason;
        this.setEventType("USER_LOGIN");
        this.setPriority(EventPriority.NORMAL);
        this.initializeEvent();
    }
    
    @Override
    public String getRoutingKey() {
        return "user.login"; // RabbitMQ兼容
    }

    @Override
    public String getExchangeName() {
        return "user.event.exchange"; // RabbitMQ兼容
    }

    @Override
    public String getTopicName() {
        return RocketMQConstants.USER_EVENT_TOPIC;
    }

    @Override
    public String getTagName() {
        return "user-login";
    }
    
    @Override
    public boolean isValid() {
        return this.getUserId() != null && !this.getUserId().trim().isEmpty()
                && this.username != null && !this.username.trim().isEmpty()
                && this.loginIp != null && !this.loginIp.trim().isEmpty()
                && this.loginMethod != null && !this.loginMethod.trim().isEmpty()
                && this.loginSuccess != null;
    }
}
