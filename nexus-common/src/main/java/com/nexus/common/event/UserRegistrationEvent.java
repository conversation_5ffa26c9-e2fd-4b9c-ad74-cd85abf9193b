package com.nexus.common.event;

import com.nexus.common.constants.RocketMQConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 用户注册事件
 */
@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class UserRegistrationEvent extends BaseEvent {
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 用户角色
     */
    private String role;
    
    /**
     * 注册IP
     */
    private String registrationIp;
    
    /**
     * API密钥
     */
    private String apiKey;
    
    public UserRegistrationEvent(String userId, String username, String email, String role, String registrationIp, String apiKey) {
        this.setUserId(userId);
        this.username = username;
        this.email = email;
        this.role = role;
        this.registrationIp = registrationIp;
        this.apiKey = apiKey;
        this.setEventType("USER_REGISTRATION");
        this.setPriority(EventPriority.HIGH); // 用户注册是高优先级事件
        this.initializeEvent();
    }
    
    @Override
    public String getRoutingKey() {
        return "user.registration"; // RabbitMQ兼容
    }

    @Override
    public String getExchangeName() {
        return "user.event.exchange"; // RabbitMQ兼容
    }

    @Override
    public String getTopicName() {
        return RocketMQConstants.USER_EVENT_TOPIC;
    }

    @Override
    public String getTagName() {
        return RocketMQConstants.USER_REGISTRATION_TAG;
    }

    @Override
    public boolean isValid() {
        return this.getUserId() != null && !this.getUserId().trim().isEmpty()
                && this.username != null && !this.username.trim().isEmpty()
                && this.email != null && !this.email.trim().isEmpty()
                && this.role != null && !this.role.trim().isEmpty();
    }
}
