package com.nexus.common.exception;

/**
 * Nexus系统基础异常类
 * 所有业务异常的基类
 */
public class NexusException extends RuntimeException {

    private final String errorCode;
    private final Object[] args;

    public NexusException(String message) {
        super(message);
        this.errorCode = null;
        this.args = null;
    }

    public NexusException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = null;
        this.args = null;
    }

    public NexusException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.args = null;
    }

    public NexusException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.args = null;
    }

    public NexusException(String errorCode, String message, Object... args) {
        super(message);
        this.errorCode = errorCode;
        this.args = args;
    }

    public NexusException(String errorCode, String message, Throwable cause, Object... args) {
        super(message, cause);
        this.errorCode = errorCode;
        this.args = args;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public Object[] getArgs() {
        return args;
    }

    /**
     * 用户认证异常
     */
    public static class AuthenticationException extends NexusException {
        public AuthenticationException(String message) {
            super("AUTH_001", message);
        }

        public AuthenticationException(String message, Throwable cause) {
            super("AUTH_001", message, cause);
        }
    }

    /**
     * 用户授权异常
     */
    public static class AuthorizationException extends NexusException {
        public AuthorizationException(String message) {
            super("AUTH_002", message);
        }

        public AuthorizationException(String message, Throwable cause) {
            super("AUTH_002", message, cause);
        }
    }

    /**
     * 资源未找到异常
     */
    public static class ResourceNotFoundException extends NexusException {
        public ResourceNotFoundException(String message) {
            super("RESOURCE_001", message);
        }

        public ResourceNotFoundException(String resourceType, String resourceId) {
            super("RESOURCE_001", String.format("%s not found: %s", resourceType, resourceId));
        }
    }

    /**
     * 业务逻辑异常
     */
    public static class BusinessException extends NexusException {
        public BusinessException(String message) {
            super("BUSINESS_001", message);
        }

        public BusinessException(String message, Throwable cause) {
            super("BUSINESS_001", message, cause);
        }

        public BusinessException(String errorCode, String message) {
            super(errorCode, message);
        }
    }

    /**
     * 参数验证异常
     */
    public static class ValidationException extends NexusException {
        public ValidationException(String message) {
            super("VALIDATION_001", message);
        }

        public ValidationException(String field, String message) {
            super("VALIDATION_001", String.format("Field '%s': %s", field, message));
        }
    }

    /**
     * MCP服务异常
     */
    public static class MCPServiceException extends NexusException {
        public MCPServiceException(String message) {
            super("MCP_001", message);
        }

        public MCPServiceException(String message, Throwable cause) {
            super("MCP_001", message, cause);
        }

        public MCPServiceException(String serviceId, String message) {
            super("MCP_001", String.format("MCP Service '%s': %s", serviceId, message));
        }
    }

    /**
     * 订阅相关异常
     */
    public static class SubscriptionException extends NexusException {
        public SubscriptionException(String message) {
            super("SUBSCRIPTION_001", message);
        }

        public SubscriptionException(String message, Throwable cause) {
            super("SUBSCRIPTION_001", message, cause);
        }
    }

    /**
     * 限流异常
     */
    public static class RateLimitException extends NexusException {
        public RateLimitException(String message) {
            super("RATE_LIMIT_001", message);
        }

        public RateLimitException(String userId, String service) {
            super("RATE_LIMIT_001", String.format("Rate limit exceeded for user '%s' on service '%s'", userId, service));
        }
    }

    /**
     * 配置异常
     */
    public static class ConfigurationException extends NexusException {
        public ConfigurationException(String message) {
            super("CONFIG_001", message);
        }

        public ConfigurationException(String message, Throwable cause) {
            super("CONFIG_001", message, cause);
        }
    }

    /**
     * 外部服务异常
     */
    public static class ExternalServiceException extends NexusException {
        public ExternalServiceException(String service, String message) {
            super("EXTERNAL_001", String.format("External service '%s': %s", service, message));
        }

        public ExternalServiceException(String service, String message, Throwable cause) {
            super("EXTERNAL_001", String.format("External service '%s': %s", service, message), cause);
        }
    }

    /**
     * 任务执行异常
     */
    public static class TaskExecutionException extends NexusException {
        public TaskExecutionException(String message) {
            super("TASK_001", message);
        }

        public TaskExecutionException(String message, Throwable cause) {
            super("TASK_001", message, cause);
        }

        public TaskExecutionException(String taskId, String message) {
            super("TASK_001", String.format("Task '%s': %s", taskId, message));
        }
    }
}
