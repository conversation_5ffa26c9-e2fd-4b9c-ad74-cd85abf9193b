package com.nexus.common.mcp.config;

import com.nexus.common.mcp.process.McpServiceConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * MCP命令解析器
 * 
 * 解析常见的MCP服务启动命令，自动生成对应的McpServiceConfig
 * 支持npx、uvx、python、java、node等各种启动方式
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Component
public class McpCommandParser {
    
    // 命令模式匹配
    private static final Pattern NPX_PATTERN = Pattern.compile("npx\\s+(-y\\s+)?(.+?)(?:\\s+(.*))?");
    private static final Pattern UVX_PATTERN = Pattern.compile("uvx\\s+(.+?)(?:\\s+(.*))?");
    private static final Pattern PYTHON_PATTERN = Pattern.compile("python\\s+(-m\\s+)?(.+?)(?:\\s+(.*))?");
    private static final Pattern NODE_PATTERN = Pattern.compile("node\\s+(.+?)(?:\\s+(.*))?");
    private static final Pattern JAVA_PATTERN = Pattern.compile("java\\s+(.*?)-jar\\s+(.+?)(?:\\s+(.*))?");
    
    /**
     * 解析MCP启动命令，生成服务配置
     */
    public McpServiceConfig parseCommand(String command) {
        return parseCommand(command, null);
    }
    
    /**
     * 解析MCP启动命令，生成服务配置
     */
    public McpServiceConfig parseCommand(String command, String serviceName) {
        if (command == null || command.trim().isEmpty()) {
            throw new IllegalArgumentException("命令不能为空");
        }
        
        command = command.trim();
        log.debug("解析MCP命令: {}", command);
        
        // 尝试不同的命令模式
        McpServiceConfig config = null;
        
        if (command.startsWith("npx")) {
            config = parseNpxCommand(command, serviceName);
        } else if (command.startsWith("uvx")) {
            config = parseUvxCommand(command, serviceName);
        } else if (command.startsWith("python")) {
            config = parsePythonCommand(command, serviceName);
        } else if (command.startsWith("node")) {
            config = parseNodeCommand(command, serviceName);
        } else if (command.startsWith("java")) {
            config = parseJavaCommand(command, serviceName);
        } else {
            // 尝试作为可执行文件处理
            config = parseExecutableCommand(command, serviceName);
        }
        
        if (config == null) {
            throw new IllegalArgumentException("无法解析命令: " + command);
        }
        
        log.info("成功解析MCP命令: {} -> {}", command, config.getServiceName());
        return config;
    }
    
    /**
     * 解析npx命令
     * 例如: npx -y @modelcontextprotocol/server-filesystem /path/to/files
     */
    private McpServiceConfig parseNpxCommand(String command, String serviceName) {
        Matcher matcher = NPX_PATTERN.matcher(command);
        if (!matcher.matches()) {
            return null;
        }
        
        String yFlag = matcher.group(1); // -y 标志
        String packageName = matcher.group(2); // 包名
        String args = matcher.group(3); // 参数
        
        if (serviceName == null) {
            serviceName = extractServiceNameFromPackage(packageName);
        }
        
        McpServiceConfig.McpServiceConfigBuilder builder = McpServiceConfig.builder()
                .serviceName(serviceName)
                .serviceType(McpServiceConfig.ServiceType.NODEJS)
                .servicePath(packageName)
                .nodeExecutable("npx");
        
        // 添加参数
        List<String> argsList = new ArrayList<>();
        if (yFlag != null) {
            argsList.add("-y");
        }
        if (args != null && !args.trim().isEmpty()) {
            argsList.addAll(Arrays.asList(args.trim().split("\\s+")));
        }
        builder.args(argsList);
        
        return builder.build();
    }
    
    /**
     * 解析uvx命令
     * 例如: uvx mcp-server-git --repository /path/to/repo
     */
    private McpServiceConfig parseUvxCommand(String command, String serviceName) {
        Matcher matcher = UVX_PATTERN.matcher(command);
        if (!matcher.matches()) {
            return null;
        }
        
        String packageName = matcher.group(1); // 包名
        String args = matcher.group(2); // 参数
        
        if (serviceName == null) {
            serviceName = extractServiceNameFromPackage(packageName);
        }
        
        McpServiceConfig.McpServiceConfigBuilder builder = McpServiceConfig.builder()
                .serviceName(serviceName)
                .serviceType(McpServiceConfig.ServiceType.PYTHON)
                .servicePath(packageName)
                .pythonExecutable("uvx");
        
        // 添加参数
        if (args != null && !args.trim().isEmpty()) {
            builder.args(Arrays.asList(args.trim().split("\\s+")));
        }
        
        return builder.build();
    }
    
    /**
     * 解析python命令
     * 例如: python -m mcp_server_git --repository /path/to/repo
     * 或: python script.py --arg1 value1
     */
    private McpServiceConfig parsePythonCommand(String command, String serviceName) {
        Matcher matcher = PYTHON_PATTERN.matcher(command);
        if (!matcher.matches()) {
            return null;
        }
        
        String moduleFlag = matcher.group(1); // -m 标志
        String pathOrModule = matcher.group(2); // 路径或模块名
        String args = matcher.group(3); // 参数
        
        if (serviceName == null) {
            serviceName = extractServiceNameFromPath(pathOrModule);
        }
        
        McpServiceConfig.McpServiceConfigBuilder builder = McpServiceConfig.builder()
                .serviceName(serviceName)
                .serviceType(McpServiceConfig.ServiceType.PYTHON)
                .servicePath(pathOrModule)
                .pythonExecutable("python");
        
        // 添加参数
        List<String> argsList = new ArrayList<>();
        if (moduleFlag != null) {
            argsList.add("-m");
        }
        if (args != null && !args.trim().isEmpty()) {
            argsList.addAll(Arrays.asList(args.trim().split("\\s+")));
        }
        builder.args(argsList);
        
        return builder.build();
    }
    
    /**
     * 解析node命令
     * 例如: node server.js --port 8080
     */
    private McpServiceConfig parseNodeCommand(String command, String serviceName) {
        Matcher matcher = NODE_PATTERN.matcher(command);
        if (!matcher.matches()) {
            return null;
        }
        
        String scriptPath = matcher.group(1); // 脚本路径
        String args = matcher.group(2); // 参数
        
        if (serviceName == null) {
            serviceName = extractServiceNameFromPath(scriptPath);
        }
        
        McpServiceConfig.McpServiceConfigBuilder builder = McpServiceConfig.builder()
                .serviceName(serviceName)
                .serviceType(McpServiceConfig.ServiceType.NODEJS)
                .servicePath(scriptPath)
                .nodeExecutable("node");
        
        // 添加参数
        if (args != null && !args.trim().isEmpty()) {
            builder.args(Arrays.asList(args.trim().split("\\s+")));
        }
        
        return builder.build();
    }
    
    /**
     * 解析java命令
     * 例如: java -Xmx512m -jar mcp-server.jar --port 8080
     */
    private McpServiceConfig parseJavaCommand(String command, String serviceName) {
        Matcher matcher = JAVA_PATTERN.matcher(command);
        if (!matcher.matches()) {
            return null;
        }
        
        String jvmArgs = matcher.group(1); // JVM参数
        String jarPath = matcher.group(2); // JAR路径
        String args = matcher.group(3); // 程序参数
        
        if (serviceName == null) {
            serviceName = extractServiceNameFromPath(jarPath);
        }
        
        McpServiceConfig.McpServiceConfigBuilder builder = McpServiceConfig.builder()
                .serviceName(serviceName)
                .serviceType(McpServiceConfig.ServiceType.JAVA)
                .servicePath(jarPath);
        
        // 添加JVM参数
        if (jvmArgs != null && !jvmArgs.trim().isEmpty()) {
            List<String> jvmArgsList = Arrays.asList(jvmArgs.trim().split("\\s+"));
            builder.jvmArgs(jvmArgsList);
        }
        
        // 添加程序参数
        if (args != null && !args.trim().isEmpty()) {
            builder.args(Arrays.asList(args.trim().split("\\s+")));
        }
        
        return builder.build();
    }
    
    /**
     * 解析可执行文件命令
     * 例如: /usr/local/bin/mcp-server --config /etc/mcp.conf
     */
    private McpServiceConfig parseExecutableCommand(String command, String serviceName) {
        String[] parts = command.trim().split("\\s+");
        if (parts.length == 0) {
            return null;
        }
        
        String executablePath = parts[0];
        
        if (serviceName == null) {
            serviceName = extractServiceNameFromPath(executablePath);
        }
        
        McpServiceConfig.McpServiceConfigBuilder builder = McpServiceConfig.builder()
                .serviceName(serviceName)
                .serviceType(McpServiceConfig.ServiceType.EXECUTABLE)
                .servicePath(executablePath);
        
        // 添加参数
        if (parts.length > 1) {
            List<String> args = Arrays.asList(Arrays.copyOfRange(parts, 1, parts.length));
            builder.args(args);
        }
        
        return builder.build();
    }
    
    /**
     * 从包名提取服务名
     */
    private String extractServiceNameFromPackage(String packageName) {
        if (packageName.startsWith("@")) {
            // @modelcontextprotocol/server-filesystem -> filesystem
            String[] parts = packageName.split("/");
            if (parts.length > 1) {
                String serverName = parts[1];
                if (serverName.startsWith("server-")) {
                    return serverName.substring(7); // 移除 "server-" 前缀
                }
                return serverName;
            }
        } else if (packageName.startsWith("mcp-server-")) {
            // mcp-server-git -> git
            return packageName.substring(11);
        }
        
        return packageName.replaceAll("[^a-zA-Z0-9-]", "-");
    }
    
    /**
     * 从文件路径提取服务名
     */
    private String extractServiceNameFromPath(String path) {
        String fileName = path;
        
        // 提取文件名
        int lastSlash = path.lastIndexOf('/');
        if (lastSlash >= 0) {
            fileName = path.substring(lastSlash + 1);
        }
        
        // 移除文件扩展名
        int lastDot = fileName.lastIndexOf('.');
        if (lastDot > 0) {
            fileName = fileName.substring(0, lastDot);
        }
        
        return fileName.replaceAll("[^a-zA-Z0-9-]", "-");
    }
}
