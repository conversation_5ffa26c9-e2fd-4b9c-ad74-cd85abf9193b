package com.nexus.common.mcp.config;

import com.nexus.common.mcp.process.McpServiceConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 标准MCP服务注册表
 * 
 * 提供常用MCP服务的预定义配置和管理功能
 * 支持官方MCP服务和社区服务的注册和发现
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Component
public class StandardMcpServiceRegistry {
    
    private final Map<String, McpServiceConfig> registeredServices = new HashMap<>();
    
    public StandardMcpServiceRegistry() {
        registerOfficialServices();
        registerCommunityServices();
    }
    
    /**
     * 注册官方MCP服务
     */
    private void registerOfficialServices() {
        log.info("注册官方MCP服务");
        
        // 文件系统服务
        register("filesystem", McpServiceConfig.createNodeService("filesystem", "@modelcontextprotocol/server-filesystem")
                .addTag("category", "filesystem")
                .addTag("official", "true")
                .addTag("description", "安全的文件操作，支持可配置的访问控制"));
        
        // Git服务
        register("git", McpServiceConfig.createPythonService("git", "mcp-server-git")
                .addTag("category", "version-control")
                .addTag("official", "true")
                .addTag("description", "读取、搜索和操作Git仓库的工具"));
        
        // GitHub服务
        register("github", McpServiceConfig.createNodeService("github", "@modelcontextprotocol/server-github")
                .addTag("category", "api")
                .addTag("official", "true")
                .addTag("description", "仓库管理、文件操作和GitHub API集成"));
        
        // 内存服务
        register("memory", McpServiceConfig.createNodeService("memory", "@modelcontextprotocol/server-memory")
                .addTag("category", "storage")
                .addTag("official", "true")
                .addTag("description", "基于知识图谱的持久化内存系统"));
        
        // 时间服务
        register("time", McpServiceConfig.createNodeService("time", "@modelcontextprotocol/server-time")
                .addTag("category", "utility")
                .addTag("official", "true")
                .addTag("description", "时间和时区转换能力"));
        
        // 网页抓取服务
        register("fetch", McpServiceConfig.createNodeService("fetch", "@modelcontextprotocol/server-fetch")
                .addTag("category", "web")
                .addTag("official", "true")
                .addTag("description", "网页内容获取和转换，优化LLM使用"));
        
        // 顺序思考服务
        register("sequential-thinking", McpServiceConfig.createNodeService("sequential-thinking", "@modelcontextprotocol/server-sequentialthinking")
                .addTag("category", "reasoning")
                .addTag("official", "true")
                .addTag("description", "通过思维序列进行动态和反思性问题解决"));
    }
    
    /**
     * 注册社区MCP服务
     */
    private void registerCommunityServices() {
        log.info("注册社区MCP服务");
        
        // Brave搜索服务
        register("brave-search", McpServiceConfig.createPythonService("brave-search", "mcp-server-brave-search")
                .addTag("category", "search")
                .addTag("community", "true")
                .addTag("description", "使用Brave搜索API进行网络和本地搜索"));
        
        // PostgreSQL服务
        register("postgres", McpServiceConfig.createNodeService("postgres", "@modelcontextprotocol/server-postgres")
                .addTag("category", "database")
                .addTag("community", "true")
                .addTag("description", "只读数据库访问和模式检查"));
        
        // SQLite服务
        register("sqlite", McpServiceConfig.createPythonService("sqlite", "mcp-server-sqlite")
                .addTag("category", "database")
                .addTag("community", "true")
                .addTag("description", "数据库交互和商业智能能力"));
        
        // Puppeteer服务
        register("puppeteer", McpServiceConfig.createNodeService("puppeteer", "@modelcontextprotocol/server-puppeteer")
                .addTag("category", "automation")
                .addTag("community", "true")
                .addTag("description", "浏览器自动化和网页抓取"));
    }
    
    /**
     * 注册MCP服务
     */
    public void register(String serviceName, McpServiceConfig config) {
        registeredServices.put(serviceName, config);
        log.debug("注册MCP服务: {} -> {}", serviceName, config.getServicePath());
    }
    
    /**
     * 获取MCP服务配置
     */
    public Optional<McpServiceConfig> getServiceConfig(String serviceName) {
        return Optional.ofNullable(registeredServices.get(serviceName));
    }
    
    /**
     * 获取所有注册的服务
     */
    public Map<String, McpServiceConfig> getAllServices() {
        return new HashMap<>(registeredServices);
    }
    
    /**
     * 按类别获取服务
     */
    public Map<String, McpServiceConfig> getServicesByCategory(String category) {
        Map<String, McpServiceConfig> result = new HashMap<>();
        
        registeredServices.forEach((name, config) -> {
            if (category.equals(config.getTags().get("category"))) {
                result.put(name, config);
            }
        });
        
        return result;
    }
    
    /**
     * 获取官方服务
     */
    public Map<String, McpServiceConfig> getOfficialServices() {
        Map<String, McpServiceConfig> result = new HashMap<>();
        
        registeredServices.forEach((name, config) -> {
            if ("true".equals(config.getTags().get("official"))) {
                result.put(name, config);
            }
        });
        
        return result;
    }
    
    /**
     * 获取社区服务
     */
    public Map<String, McpServiceConfig> getCommunityServices() {
        Map<String, McpServiceConfig> result = new HashMap<>();
        
        registeredServices.forEach((name, config) -> {
            if ("true".equals(config.getTags().get("community"))) {
                result.put(name, config);
            }
        });
        
        return result;
    }
    
    /**
     * 检查服务是否已注册
     */
    public boolean isServiceRegistered(String serviceName) {
        return registeredServices.containsKey(serviceName);
    }
    
    /**
     * 移除服务注册
     */
    public boolean unregister(String serviceName) {
        McpServiceConfig removed = registeredServices.remove(serviceName);
        if (removed != null) {
            log.debug("移除MCP服务注册: {}", serviceName);
            return true;
        }
        return false;
    }
    
    /**
     * 获取服务统计信息
     */
    public ServiceRegistryStats getStats() {
        int totalServices = registeredServices.size();
        int officialServices = getOfficialServices().size();
        int communityServices = getCommunityServices().size();
        
        return new ServiceRegistryStats(totalServices, officialServices, communityServices);
    }
    
    /**
     * 服务注册表统计信息
     */
    public static class ServiceRegistryStats {
        private final int totalServices;
        private final int officialServices;
        private final int communityServices;
        
        public ServiceRegistryStats(int totalServices, int officialServices, int communityServices) {
            this.totalServices = totalServices;
            this.officialServices = officialServices;
            this.communityServices = communityServices;
        }
        
        public int getTotalServices() { return totalServices; }
        public int getOfficialServices() { return officialServices; }
        public int getCommunityServices() { return communityServices; }
    }
}
