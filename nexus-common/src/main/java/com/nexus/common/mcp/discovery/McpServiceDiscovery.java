package com.nexus.common.mcp.discovery;

import com.nexus.common.mcp.notification.McpNotificationListener;
import com.nexus.common.mcp.notification.McpNotificationManager;
import com.nexus.common.mcp.notification.McpNotificationType;
import com.nexus.common.mcp.notification.ServiceStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * MCP服务发现
 * 
 * 负责MCP服务的注册、发现、健康检查和路由管理
 * 支持动态服务注册、智能路由和负载均衡
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class McpServiceDiscovery implements McpNotificationListener {
    
    private final McpNotificationManager notificationManager;
    
    // 服务注册表
    private final Map<String, McpServiceInstance> serviceRegistry = new ConcurrentHashMap<>();
    
    // 工具到服务的映射
    private final Map<String, Set<String>> toolToServicesMap = new ConcurrentHashMap<>();
    
    // 资源到服务的映射
    private final Map<String, Set<String>> resourceToServicesMap = new ConcurrentHashMap<>();
    
    // 健康检查执行器
    private final ScheduledExecutorService healthCheckExecutor = Executors.newScheduledThreadPool(2);
    
    @PostConstruct
    public void init() {
        // 注册为通知监听器
        notificationManager.registerListener("*", this);
        
        // 启动健康检查
        startHealthCheck();
        
        log.info("MCP服务发现已启动");
    }
    
    @PreDestroy
    public void destroy() {
        healthCheckExecutor.shutdown();
        log.info("MCP服务发现已关闭");
    }
    
    /**
     * 注册MCP服务
     */
    public void registerService(McpServiceInstance serviceInstance) {
        String serviceId = serviceInstance.getServiceId();
        
        log.info("注册MCP服务: {} (类型: {})", serviceId, serviceInstance.getServiceType());
        
        // 更新服务注册表
        serviceRegistry.put(serviceId, serviceInstance);
        
        // 更新工具映射
        updateToolMappings(serviceInstance);
        
        // 更新资源映射
        updateResourceMappings(serviceInstance);
        
        log.info("MCP服务注册成功: {}", serviceId);
    }
    
    /**
     * 注销MCP服务
     */
    public void unregisterService(String serviceId) {
        log.info("注销MCP服务: {}", serviceId);
        
        McpServiceInstance instance = serviceRegistry.remove(serviceId);
        if (instance != null) {
            // 清理工具映射
            cleanupToolMappings(instance);
            
            // 清理资源映射
            cleanupResourceMappings(instance);
            
            log.info("MCP服务注销成功: {}", serviceId);
        } else {
            log.warn("尝试注销不存在的服务: {}", serviceId);
        }
    }
    
    /**
     * 查找提供指定工具的服务
     */
    public List<McpServiceInstance> findServicesByTool(String toolName) {
        Set<String> serviceIds = toolToServicesMap.get(toolName);
        if (serviceIds == null || serviceIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        return serviceIds.stream()
                .map(serviceRegistry::get)
                .filter(Objects::nonNull)
                .filter(instance -> instance.getStatus() == ServiceStatus.RUNNING)
                .collect(Collectors.toList());
    }
    
    /**
     * 查找提供指定资源的服务
     */
    public List<McpServiceInstance> findServicesByResource(String resourcePattern) {
        Set<String> serviceIds = resourceToServicesMap.get(resourcePattern);
        if (serviceIds == null || serviceIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        return serviceIds.stream()
                .map(serviceRegistry::get)
                .filter(Objects::nonNull)
                .filter(instance -> instance.getStatus() == ServiceStatus.RUNNING)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取所有健康的服务
     */
    public List<McpServiceInstance> getHealthyServices() {
        return serviceRegistry.values().stream()
                .filter(instance -> instance.getStatus() == ServiceStatus.RUNNING)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取指定类型的服务
     */
    public List<McpServiceInstance> getServicesByType(McpServiceType serviceType) {
        return serviceRegistry.values().stream()
                .filter(instance -> instance.getServiceType() == serviceType)
                .filter(instance -> instance.getStatus() == ServiceStatus.RUNNING)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取服务实例
     */
    public Optional<McpServiceInstance> getService(String serviceId) {
        return Optional.ofNullable(serviceRegistry.get(serviceId));
    }
    
    /**
     * 获取所有服务
     */
    public Map<String, McpServiceInstance> getAllServices() {
        return new HashMap<>(serviceRegistry);
    }
    
    /**
     * 更新工具映射
     */
    private void updateToolMappings(McpServiceInstance serviceInstance) {
        Set<String> tools = serviceInstance.getAvailableTools();
        if (tools != null) {
            for (String tool : tools) {
                toolToServicesMap.computeIfAbsent(tool, k -> ConcurrentHashMap.newKeySet())
                        .add(serviceInstance.getServiceId());
            }
        }
    }
    
    /**
     * 更新资源映射
     */
    private void updateResourceMappings(McpServiceInstance serviceInstance) {
        Set<String> resources = serviceInstance.getAvailableResources();
        if (resources != null) {
            for (String resource : resources) {
                resourceToServicesMap.computeIfAbsent(resource, k -> ConcurrentHashMap.newKeySet())
                        .add(serviceInstance.getServiceId());
            }
        }
    }
    
    /**
     * 清理工具映射
     */
    private void cleanupToolMappings(McpServiceInstance serviceInstance) {
        Set<String> tools = serviceInstance.getAvailableTools();
        if (tools != null) {
            for (String tool : tools) {
                Set<String> serviceIds = toolToServicesMap.get(tool);
                if (serviceIds != null) {
                    serviceIds.remove(serviceInstance.getServiceId());
                    if (serviceIds.isEmpty()) {
                        toolToServicesMap.remove(tool);
                    }
                }
            }
        }
    }
    
    /**
     * 清理资源映射
     */
    private void cleanupResourceMappings(McpServiceInstance serviceInstance) {
        Set<String> resources = serviceInstance.getAvailableResources();
        if (resources != null) {
            for (String resource : resources) {
                Set<String> serviceIds = resourceToServicesMap.get(resource);
                if (serviceIds != null) {
                    serviceIds.remove(serviceInstance.getServiceId());
                    if (serviceIds.isEmpty()) {
                        resourceToServicesMap.remove(resource);
                    }
                }
            }
        }
    }
    
    /**
     * 启动健康检查
     */
    private void startHealthCheck() {
        healthCheckExecutor.scheduleWithFixedDelay(
                this::performHealthCheck,
                30, // 初始延迟30秒
                60, // 每60秒检查一次
                TimeUnit.SECONDS
        );
    }
    
    /**
     * 执行健康检查
     */
    private void performHealthCheck() {
        log.debug("执行MCP服务健康检查");
        
        for (McpServiceInstance instance : serviceRegistry.values()) {
            try {
                // 检查服务是否仍然健康
                if (isServiceHealthy(instance)) {
                    // 更新最后健康检查时间
                    instance.updateLastHealthCheck();
                } else {
                    // 标记服务为不健康
                    if (instance.getStatus() == ServiceStatus.RUNNING) {
                        instance.setStatus(ServiceStatus.FAILED);
                        log.warn("服务健康检查失败: {}", instance.getServiceId());
                    }
                }
            } catch (Exception e) {
                log.error("健康检查异常: {} - {}", instance.getServiceId(), e.getMessage());
            }
        }
    }
    
    /**
     * 检查服务是否健康
     */
    private boolean isServiceHealthy(McpServiceInstance instance) {
        // 这里可以实现具体的健康检查逻辑
        // 例如：检查进程是否存活、发送ping请求等
        
        // 简化实现：检查最后更新时间
        long lastUpdate = instance.getLastUpdateTime().getTime();
        long now = System.currentTimeMillis();
        long timeout = 5 * 60 * 1000; // 5分钟超时
        
        return (now - lastUpdate) < timeout;
    }
    
    @Override
    public void onNotification(String serviceId, McpNotificationType type, Map<String, Object> data) {
        switch (type) {
            case SERVICE_STATUS_CHANGED:
                handleServiceStatusChanged(serviceId, data);
                break;
            case TOOLS_LIST_CHANGED:
                handleToolsListChanged(serviceId);
                break;
            case RESOURCES_LIST_CHANGED:
                handleResourcesListChanged(serviceId);
                break;
            default:
                // 忽略其他类型的通知
                break;
        }
    }
    
    /**
     * 处理服务状态变更
     */
    private void handleServiceStatusChanged(String serviceId, Map<String, Object> data) {
        McpServiceInstance instance = serviceRegistry.get(serviceId);
        if (instance != null && data != null) {
            Object statusObj = data.get("status");
            if (statusObj instanceof ServiceStatus) {
                instance.setStatus((ServiceStatus) statusObj);
                instance.updateLastUpdate();
                log.debug("更新服务状态: {} -> {}", serviceId, statusObj);
            }
        }
    }
    
    /**
     * 处理工具列表变更
     */
    private void handleToolsListChanged(String serviceId) {
        McpServiceInstance instance = serviceRegistry.get(serviceId);
        if (instance != null) {
            // 清理旧的工具映射
            cleanupToolMappings(instance);
            
            // 这里需要重新获取工具列表
            // 简化实现：标记需要刷新
            instance.markToolsNeedRefresh();
            
            log.debug("工具列表变更: {}", serviceId);
        }
    }
    
    /**
     * 处理资源列表变更
     */
    private void handleResourcesListChanged(String serviceId) {
        McpServiceInstance instance = serviceRegistry.get(serviceId);
        if (instance != null) {
            // 清理旧的资源映射
            cleanupResourceMappings(instance);
            
            // 这里需要重新获取资源列表
            // 简化实现：标记需要刷新
            instance.markResourcesNeedRefresh();
            
            log.debug("资源列表变更: {}", serviceId);
        }
    }
    
    /**
     * 获取发现统计信息
     */
    public DiscoveryStats getStats() {
        int totalServices = serviceRegistry.size();
        int healthyServices = (int) serviceRegistry.values().stream()
                .mapToLong(instance -> instance.getStatus() == ServiceStatus.RUNNING ? 1 : 0)
                .sum();
        int totalTools = toolToServicesMap.size();
        int totalResources = resourceToServicesMap.size();
        
        return new DiscoveryStats(totalServices, healthyServices, totalTools, totalResources);
    }
    
    /**
     * 发现统计信息
     */
    public static class DiscoveryStats {
        private final int totalServices;
        private final int healthyServices;
        private final int totalTools;
        private final int totalResources;
        
        public DiscoveryStats(int totalServices, int healthyServices, int totalTools, int totalResources) {
            this.totalServices = totalServices;
            this.healthyServices = healthyServices;
            this.totalTools = totalTools;
            this.totalResources = totalResources;
        }
        
        // Getters
        public int getTotalServices() { return totalServices; }
        public int getHealthyServices() { return healthyServices; }
        public int getTotalTools() { return totalTools; }
        public int getTotalResources() { return totalResources; }
    }
}
