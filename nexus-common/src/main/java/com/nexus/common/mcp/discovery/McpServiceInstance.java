package com.nexus.common.mcp.discovery;

import com.nexus.common.mcp.notification.ServiceStatus;
import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.Map;
import java.util.Set;

/**
 * MCP服务实例
 * 
 * 表示一个MCP服务的实例信息
 * 包含服务的基本信息、状态、能力等
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Builder
public class McpServiceInstance {
    
    /**
     * 服务ID（唯一标识）
     */
    private String serviceId;
    
    /**
     * 服务名称
     */
    private String serviceName;
    
    /**
     * 服务类型
     */
    private McpServiceType serviceType;
    
    /**
     * 服务版本
     */
    private String version;
    
    /**
     * 服务描述
     */
    private String description;
    
    /**
     * 服务状态
     */
    private ServiceStatus status;
    
    /**
     * 服务地址（对于HTTP/gRPC服务）
     */
    private String address;
    
    /**
     * 服务端口（对于HTTP/gRPC服务）
     */
    private Integer port;
    
    /**
     * 协议类型（HTTP、gRPC、STDIO等）
     */
    private String protocol;
    
    /**
     * 可用的工具列表
     */
    private Set<String> availableTools;
    
    /**
     * 可用的资源模式列表
     */
    private Set<String> availableResources;
    
    /**
     * 服务标签
     */
    private Map<String, String> tags;
    
    /**
     * 服务元数据
     */
    private Map<String, Object> metadata;
    
    /**
     * 注册时间
     */
    @Builder.Default
    private Date registrationTime = new Date();
    
    /**
     * 最后更新时间
     */
    @Builder.Default
    private Date lastUpdateTime = new Date();
    
    /**
     * 最后健康检查时间
     */
    @Builder.Default
    private Date lastHealthCheckTime = new Date();
    
    /**
     * 健康检查失败次数
     */
    @Builder.Default
    private int healthCheckFailureCount = 0;
    
    /**
     * 工具列表是否需要刷新
     */
    @Builder.Default
    private boolean toolsNeedRefresh = false;
    
    /**
     * 资源列表是否需要刷新
     */
    @Builder.Default
    private boolean resourcesNeedRefresh = false;
    
    /**
     * 服务权重（用于负载均衡）
     */
    @Builder.Default
    private int weight = 100;
    
    /**
     * 更新最后更新时间
     */
    public void updateLastUpdate() {
        this.lastUpdateTime = new Date();
    }
    
    /**
     * 更新最后健康检查时间
     */
    public void updateLastHealthCheck() {
        this.lastHealthCheckTime = new Date();
        this.healthCheckFailureCount = 0; // 重置失败计数
    }
    
    /**
     * 增加健康检查失败次数
     */
    public void incrementHealthCheckFailure() {
        this.healthCheckFailureCount++;
    }
    
    /**
     * 标记工具列表需要刷新
     */
    public void markToolsNeedRefresh() {
        this.toolsNeedRefresh = true;
    }
    
    /**
     * 标记资源列表需要刷新
     */
    public void markResourcesNeedRefresh() {
        this.resourcesNeedRefresh = true;
    }
    
    /**
     * 清除刷新标记
     */
    public void clearRefreshFlags() {
        this.toolsNeedRefresh = false;
        this.resourcesNeedRefresh = false;
    }
    
    /**
     * 检查服务是否健康
     */
    public boolean isHealthy() {
        return status == ServiceStatus.RUNNING && healthCheckFailureCount < 3;
    }
    
    /**
     * 检查服务是否可用
     */
    public boolean isAvailable() {
        return isHealthy() && !isOverloaded();
    }
    
    /**
     * 检查服务是否过载
     */
    public boolean isOverloaded() {
        // 这里可以实现过载检测逻辑
        // 例如：检查CPU使用率、内存使用率、请求队列长度等
        return false; // 简化实现
    }
    
    /**
     * 获取服务运行时间（毫秒）
     */
    public long getUptime() {
        if (status == ServiceStatus.RUNNING) {
            return System.currentTimeMillis() - registrationTime.getTime();
        }
        return 0;
    }
    
    /**
     * 获取格式化的运行时间
     */
    public String getFormattedUptime() {
        long uptimeMs = getUptime();
        long seconds = uptimeMs / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;
        
        if (days > 0) {
            return String.format("%dd %dh %dm", days, hours % 24, minutes % 60);
        } else if (hours > 0) {
            return String.format("%dh %dm", hours, minutes % 60);
        } else if (minutes > 0) {
            return String.format("%dm %ds", minutes, seconds % 60);
        } else {
            return String.format("%ds", seconds);
        }
    }
    
    /**
     * 获取服务端点URL
     */
    public String getEndpointUrl() {
        if (address != null && port != null) {
            String scheme = "https".equalsIgnoreCase(protocol) ? "https" : "http";
            return String.format("%s://%s:%d", scheme, address, port);
        }
        return null;
    }
    
    /**
     * 检查是否提供指定工具
     */
    public boolean providesTool(String toolName) {
        return availableTools != null && availableTools.contains(toolName);
    }
    
    /**
     * 检查是否提供指定资源
     */
    public boolean providesResource(String resourcePattern) {
        if (availableResources == null) {
            return false;
        }
        
        // 简单的模式匹配
        for (String pattern : availableResources) {
            if (resourcePattern.matches(pattern) || pattern.equals("*")) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 添加工具
     */
    public void addTool(String toolName) {
        if (availableTools != null) {
            availableTools.add(toolName);
        }
    }
    
    /**
     * 移除工具
     */
    public void removeTool(String toolName) {
        if (availableTools != null) {
            availableTools.remove(toolName);
        }
    }
    
    /**
     * 添加资源模式
     */
    public void addResource(String resourcePattern) {
        if (availableResources != null) {
            availableResources.add(resourcePattern);
        }
    }
    
    /**
     * 移除资源模式
     */
    public void removeResource(String resourcePattern) {
        if (availableResources != null) {
            availableResources.remove(resourcePattern);
        }
    }
    
    /**
     * 添加标签
     */
    public void addTag(String key, String value) {
        if (tags != null) {
            tags.put(key, value);
        }
    }
    
    /**
     * 获取标签值
     */
    public String getTag(String key) {
        return tags != null ? tags.get(key) : null;
    }
    
    /**
     * 检查是否有指定标签
     */
    public boolean hasTag(String key, String value) {
        String tagValue = getTag(key);
        return tagValue != null && tagValue.equals(value);
    }
    
    @Override
    public String toString() {
        return String.format("McpServiceInstance{id='%s', name='%s', type=%s, status=%s, uptime='%s'}", 
                serviceId, serviceName, serviceType, status, getFormattedUptime());
    }
}
