package com.nexus.common.mcp.discovery;

/**
 * MCP服务类型枚举
 * 
 * 定义不同类型的MCP服务
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public enum McpServiceType {
    
    /**
     * 标准MCP服务（使用STDIO协议）
     */
    STANDARD_MCP("standard_mcp", "标准MCP服务"),
    
    /**
     * HTTP MCP服务
     */
    HTTP_MCP("http_mcp", "HTTP MCP服务"),
    
    /**
     * gRPC MCP服务
     */
    GRPC_MCP("grpc_mcp", "gRPC MCP服务"),
    
    /**
     * WebSocket MCP服务
     */
    WEBSOCKET_MCP("websocket_mcp", "WebSocket MCP服务"),
    
    /**
     * 文件系统服务
     */
    FILESYSTEM("filesystem", "文件系统服务"),
    
    /**
     * 数据库服务
     */
    DATABASE("database", "数据库服务"),
    
    /**
     * 版本控制服务
     */
    VERSION_CONTROL("version_control", "版本控制服务"),
    
    /**
     * API集成服务
     */
    API_INTEGRATION("api_integration", "API集成服务"),
    
    /**
     * 搜索服务
     */
    SEARCH("search", "搜索服务"),
    
    /**
     * 内存存储服务
     */
    MEMORY_STORAGE("memory_storage", "内存存储服务"),
    
    /**
     * 时间工具服务
     */
    TIME_UTILITY("time_utility", "时间工具服务"),
    
    /**
     * 网络工具服务
     */
    NETWORK_UTILITY("network_utility", "网络工具服务"),
    
    /**
     * 自动化服务
     */
    AUTOMATION("automation", "自动化服务"),
    
    /**
     * 自定义服务
     */
    CUSTOM("custom", "自定义服务");
    
    private final String code;
    private final String description;
    
    McpServiceType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    /**
     * 获取服务类型代码
     */
    public String getCode() {
        return code;
    }
    
    /**
     * 获取服务类型描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码查找服务类型
     */
    public static McpServiceType fromCode(String code) {
        for (McpServiceType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return CUSTOM; // 默认返回自定义类型
    }
    
    /**
     * 检查是否为标准MCP服务
     */
    public boolean isStandardMcp() {
        return this == STANDARD_MCP;
    }
    
    /**
     * 检查是否为网络服务
     */
    public boolean isNetworkService() {
        return this == HTTP_MCP || this == GRPC_MCP || this == WEBSOCKET_MCP;
    }
    
    /**
     * 检查是否为官方服务类型
     */
    public boolean isOfficialType() {
        return this == FILESYSTEM || 
               this == DATABASE || 
               this == VERSION_CONTROL || 
               this == API_INTEGRATION || 
               this == SEARCH || 
               this == MEMORY_STORAGE || 
               this == TIME_UTILITY || 
               this == NETWORK_UTILITY || 
               this == AUTOMATION;
    }
}
