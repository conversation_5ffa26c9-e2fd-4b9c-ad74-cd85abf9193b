package com.nexus.common.mcp.notification;

import java.util.Map;

/**
 * MCP通知监听器接口
 * 
 * 用于接收MCP协议相关的通知事件
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface McpNotificationListener {
    
    /**
     * 处理MCP通知
     * 
     * @param serviceId 服务ID
     * @param type 通知类型
     * @param data 通知数据（可选）
     */
    void onNotification(String serviceId, McpNotificationType type, Map<String, Object> data);
    
    /**
     * 获取监听器名称
     */
    default String getListenerName() {
        return this.getClass().getSimpleName();
    }
    
    /**
     * 检查是否支持指定类型的通知
     */
    default boolean supportsNotificationType(McpNotificationType type) {
        return true; // 默认支持所有类型
    }
}
