package com.nexus.common.mcp.notification;

import com.nexus.common.mcp.protocol.JsonRpcNotification;
import com.nexus.common.mcp.transport.StdioConnection;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * MCP通知管理器
 * 
 * 负责管理MCP协议的通知机制：
 * - 服务状态变更通知
 * - 资源列表变化通知
 * - 工具列表变化通知
 * - 提示列表变化通知
 * - 进度通知
 * - 取消通知
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class McpNotificationManager {
    
    // 通知监听器注册表
    private final Map<String, List<McpNotificationListener>> listenerRegistry = new ConcurrentHashMap<>();
    
    // 连接到通知发送器的映射
    private final Map<String, StdioConnection> connectionRegistry = new ConcurrentHashMap<>();
    
    // 异步通知执行器
    private final ExecutorService notificationExecutor = Executors.newCachedThreadPool(r -> {
        Thread thread = new Thread(r, "mcp-notification-" + System.currentTimeMillis());
        thread.setDaemon(true);
        return thread;
    });
    
    /**
     * 注册通知监听器
     */
    public void registerListener(String serviceId, McpNotificationListener listener) {
        listenerRegistry.computeIfAbsent(serviceId, k -> new CopyOnWriteArrayList<>()).add(listener);
        log.debug("注册MCP通知监听器: {} -> {}", serviceId, listener.getClass().getSimpleName());
    }
    
    /**
     * 移除通知监听器
     */
    public void unregisterListener(String serviceId, McpNotificationListener listener) {
        List<McpNotificationListener> listeners = listenerRegistry.get(serviceId);
        if (listeners != null) {
            listeners.remove(listener);
            if (listeners.isEmpty()) {
                listenerRegistry.remove(serviceId);
            }
        }
        log.debug("移除MCP通知监听器: {} -> {}", serviceId, listener.getClass().getSimpleName());
    }
    
    /**
     * 注册MCP连接
     */
    public void registerConnection(String serviceId, StdioConnection connection) {
        connectionRegistry.put(serviceId, connection);
        log.debug("注册MCP连接: {}", serviceId);
    }
    
    /**
     * 移除MCP连接
     */
    public void unregisterConnection(String serviceId) {
        connectionRegistry.remove(serviceId);
        log.debug("移除MCP连接: {}", serviceId);
    }
    
    /**
     * 发送资源列表变更通知
     */
    public void notifyResourcesListChanged(String serviceId) {
        log.info("发送资源列表变更通知: {}", serviceId);
        
        JsonRpcNotification notification = JsonRpcNotification.createResourcesListChangedNotification();
        
        // 发送到连接
        sendNotificationToConnection(serviceId, notification);
        
        // 通知监听器
        notifyListeners(serviceId, McpNotificationType.RESOURCES_LIST_CHANGED, null);
    }
    
    /**
     * 发送工具列表变更通知
     */
    public void notifyToolsListChanged(String serviceId) {
        log.info("发送工具列表变更通知: {}", serviceId);
        
        JsonRpcNotification notification = JsonRpcNotification.createToolsListChangedNotification();
        
        // 发送到连接
        sendNotificationToConnection(serviceId, notification);
        
        // 通知监听器
        notifyListeners(serviceId, McpNotificationType.TOOLS_LIST_CHANGED, null);
    }
    
    /**
     * 发送提示列表变更通知
     */
    public void notifyPromptsListChanged(String serviceId) {
        log.info("发送提示列表变更通知: {}", serviceId);
        
        JsonRpcNotification notification = JsonRpcNotification.createPromptsListChangedNotification();
        
        // 发送到连接
        sendNotificationToConnection(serviceId, notification);
        
        // 通知监听器
        notifyListeners(serviceId, McpNotificationType.PROMPTS_LIST_CHANGED, null);
    }
    
    /**
     * 发送进度通知
     */
    public void notifyProgress(String serviceId, String progressToken, Object progress) {
        log.debug("发送进度通知: {} -> {}", serviceId, progressToken);
        
        JsonRpcNotification notification = JsonRpcNotification.createProgressNotification(progressToken, progress);
        
        // 发送到连接
        sendNotificationToConnection(serviceId, notification);
        
        // 通知监听器
        Map<String, Object> data = Map.of(
                "progressToken", progressToken,
                "progress", progress
        );
        notifyListeners(serviceId, McpNotificationType.PROGRESS, data);
    }
    
    /**
     * 发送取消通知
     */
    public void notifyCancelled(String serviceId, String requestId, String reason) {
        log.info("发送取消通知: {} -> {} (原因: {})", serviceId, requestId, reason);
        
        JsonRpcNotification notification = JsonRpcNotification.createCancelledNotification(requestId, reason);
        
        // 发送到连接
        sendNotificationToConnection(serviceId, notification);
        
        // 通知监听器
        Map<String, Object> data = Map.of(
                "requestId", requestId,
                "reason", reason
        );
        notifyListeners(serviceId, McpNotificationType.CANCELLED, data);
    }
    
    /**
     * 发送服务状态变更通知
     */
    public void notifyServiceStatusChanged(String serviceId, ServiceStatus status, String message) {
        log.info("发送服务状态变更通知: {} -> {} ({})", serviceId, status, message);
        
        // 通知监听器
        Map<String, Object> data = new HashMap<>();
        data.put("status", status);
        data.put("message", message);
        data.put("timestamp", System.currentTimeMillis());
        notifyListeners(serviceId, McpNotificationType.SERVICE_STATUS_CHANGED, data);
    }
    
    /**
     * 发送自定义通知
     */
    public void sendCustomNotification(String serviceId, String method, Object params) {
        log.debug("发送自定义通知: {} -> {}", serviceId, method);
        
        JsonRpcNotification notification = JsonRpcNotification.createNotification(method, params);
        
        // 发送到连接
        sendNotificationToConnection(serviceId, notification);
        
        // 通知监听器
        Map<String, Object> data = Map.of(
                "method", method,
                "params", params
        );
        notifyListeners(serviceId, McpNotificationType.CUSTOM, data);
    }
    
    /**
     * 发送通知到连接
     */
    private void sendNotificationToConnection(String serviceId, JsonRpcNotification notification) {
        StdioConnection connection = connectionRegistry.get(serviceId);
        if (connection != null && connection.isAlive()) {
            try {
                connection.sendNotification(notification);
            } catch (Exception e) {
                log.error("发送通知到连接失败: {} - {}", serviceId, e.getMessage());
            }
        } else {
            log.debug("连接不可用，跳过通知发送: {}", serviceId);
        }
    }
    
    /**
     * 通知监听器
     */
    private void notifyListeners(String serviceId, McpNotificationType type, Map<String, Object> data) {
        List<McpNotificationListener> listeners = listenerRegistry.get(serviceId);
        if (listeners != null && !listeners.isEmpty()) {
            notificationExecutor.submit(() -> {
                for (McpNotificationListener listener : listeners) {
                    try {
                        listener.onNotification(serviceId, type, data);
                    } catch (Exception e) {
                        log.error("通知监听器失败: {} - {}", listener.getClass().getSimpleName(), e.getMessage());
                    }
                }
            });
        }
    }
    
    /**
     * 获取通知统计信息
     */
    public NotificationStats getStats() {
        int totalListeners = listenerRegistry.values().stream()
                .mapToInt(List::size)
                .sum();
        
        int activeConnections = (int) connectionRegistry.values().stream()
                .mapToLong(conn -> conn.isAlive() ? 1 : 0)
                .sum();
        
        return new NotificationStats(
                listenerRegistry.size(),
                totalListeners,
                connectionRegistry.size(),
                activeConnections
        );
    }
    
    /**
     * 关闭通知管理器
     */
    public void shutdown() {
        log.info("关闭MCP通知管理器");
        
        listenerRegistry.clear();
        connectionRegistry.clear();
        
        notificationExecutor.shutdown();
        
        log.info("MCP通知管理器已关闭");
    }
    
    /**
     * 通知统计信息
     */
    public static class NotificationStats {
        private final int registeredServices;
        private final int totalListeners;
        private final int totalConnections;
        private final int activeConnections;
        
        public NotificationStats(int registeredServices, int totalListeners, int totalConnections, int activeConnections) {
            this.registeredServices = registeredServices;
            this.totalListeners = totalListeners;
            this.totalConnections = totalConnections;
            this.activeConnections = activeConnections;
        }
        
        // Getters
        public int getRegisteredServices() { return registeredServices; }
        public int getTotalListeners() { return totalListeners; }
        public int getTotalConnections() { return totalConnections; }
        public int getActiveConnections() { return activeConnections; }
    }
}
