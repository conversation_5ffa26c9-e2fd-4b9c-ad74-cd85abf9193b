package com.nexus.common.mcp.notification;

/**
 * MCP通知类型枚举
 * 
 * 定义MCP协议支持的各种通知类型
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public enum McpNotificationType {
    
    /**
     * 资源列表变更通知
     * 当MCP服务的资源列表发生变化时发送
     */
    RESOURCES_LIST_CHANGED("notifications/resources/list_changed"),
    
    /**
     * 工具列表变更通知
     * 当MCP服务的工具列表发生变化时发送
     */
    TOOLS_LIST_CHANGED("notifications/tools/list_changed"),
    
    /**
     * 提示列表变更通知
     * 当MCP服务的提示列表发生变化时发送
     */
    PROMPTS_LIST_CHANGED("notifications/prompts/list_changed"),
    
    /**
     * 进度通知
     * 用于报告长时间运行操作的进度
     */
    PROGRESS("notifications/progress"),
    
    /**
     * 取消通知
     * 用于通知操作已被取消
     */
    CANCELLED("notifications/cancelled"),
    
    /**
     * 初始化完成通知
     * 在MCP协议初始化完成后发送
     */
    INITIALIZED("notifications/initialized"),
    
    /**
     * 服务状态变更通知
     * 当MCP服务状态发生变化时发送（非标准MCP通知）
     */
    SERVICE_STATUS_CHANGED("notifications/service/status_changed"),
    
    /**
     * 连接状态变更通知
     * 当MCP连接状态发生变化时发送（非标准MCP通知）
     */
    CONNECTION_STATUS_CHANGED("notifications/connection/status_changed"),
    
    /**
     * 自定义通知
     * 用于发送自定义的通知消息
     */
    CUSTOM("notifications/custom");
    
    private final String method;
    
    McpNotificationType(String method) {
        this.method = method;
    }
    
    /**
     * 获取通知方法名
     */
    public String getMethod() {
        return method;
    }
    
    /**
     * 根据方法名查找通知类型
     */
    public static McpNotificationType fromMethod(String method) {
        for (McpNotificationType type : values()) {
            if (type.method.equals(method)) {
                return type;
            }
        }
        return CUSTOM; // 默认返回自定义类型
    }
    
    /**
     * 检查是否为标准MCP通知
     */
    public boolean isStandardMcpNotification() {
        return this != SERVICE_STATUS_CHANGED && 
               this != CONNECTION_STATUS_CHANGED && 
               this != CUSTOM;
    }
}
