package com.nexus.common.mcp.notification;

/**
 * 服务状态枚举
 * 
 * 定义MCP服务的各种状态
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public enum ServiceStatus {
    
    /**
     * 启动中
     */
    STARTING("starting", "服务正在启动"),
    
    /**
     * 运行中
     */
    RUNNING("running", "服务正在运行"),
    
    /**
     * 停止中
     */
    STOPPING("stopping", "服务正在停止"),
    
    /**
     * 已停止
     */
    STOPPED("stopped", "服务已停止"),
    
    /**
     * 失败
     */
    FAILED("failed", "服务运行失败"),
    
    /**
     * 重启中
     */
    RESTARTING("restarting", "服务正在重启"),
    
    /**
     * 暂停
     */
    PAUSED("paused", "服务已暂停"),
    
    /**
     * 未知状态
     */
    UNKNOWN("unknown", "服务状态未知");
    
    private final String code;
    private final String description;
    
    ServiceStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    /**
     * 获取状态代码
     */
    public String getCode() {
        return code;
    }
    
    /**
     * 获取状态描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码查找状态
     */
    public static ServiceStatus fromCode(String code) {
        for (ServiceStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return UNKNOWN;
    }
    
    /**
     * 检查是否为活跃状态
     */
    public boolean isActive() {
        return this == STARTING || this == RUNNING || this == RESTARTING;
    }
    
    /**
     * 检查是否为终止状态
     */
    public boolean isTerminated() {
        return this == STOPPED || this == FAILED;
    }
    
    /**
     * 检查是否为过渡状态
     */
    public boolean isTransitional() {
        return this == STARTING || this == STOPPING || this == RESTARTING;
    }
}
