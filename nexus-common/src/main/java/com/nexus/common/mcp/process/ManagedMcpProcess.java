package com.nexus.common.mcp.process;

import com.nexus.common.mcp.transport.StdioConnection;
import com.nexus.common.mcp.transport.StdioTransport;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 托管的MCP进程
 * 
 * 封装MCP服务进程的状态和操作
 * 提供进程生命周期管理、连接管理、统计信息等功能
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Getter
public class ManagedMcpProcess {
    
    private final String serviceId;
    private final Process process;
    private final McpServiceConfig config;
    private final StdioTransport stdioTransport;
    private final long startTime;
    
    private final AtomicInteger restartCount = new AtomicInteger(0);
    private final AtomicLong lastHealthCheckTime = new AtomicLong(0);
    
    private StdioConnection connection;
    private volatile ProcessState state = ProcessState.STARTING;
    
    public ManagedMcpProcess(String serviceId, Process process, McpServiceConfig config, StdioTransport stdioTransport) {
        this.serviceId = serviceId;
        this.process = process;
        this.config = config;
        this.stdioTransport = stdioTransport;
        this.startTime = System.currentTimeMillis();
        
        // 初始化连接
        initializeConnection();
    }
    
    /**
     * 初始化STDIO连接
     */
    private void initializeConnection() {
        try {
            // 等待进程稳定启动
            Thread.sleep(1000);

            // 检查进程是否仍然存活
            if (!process.isAlive()) {
                throw new McpProcessException("MCP process died during startup");
            }

            this.connection = stdioTransport.connect(process);
            this.state = ProcessState.RUNNING;
            log.info("STDIO connection established for MCP service: {}", serviceId);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            this.state = ProcessState.FAILED;
            log.error("Interrupted while waiting for MCP service startup: {}", serviceId, e);
            throw new McpProcessException("Startup interrupted", e);
        } catch (Exception e) {
            this.state = ProcessState.FAILED;
            log.error("Failed to establish STDIO connection for MCP service: {}", serviceId, e);
            throw new McpProcessException("Failed to initialize connection", e);
        }
    }
    
    /**
     * 检查进程是否存活
     */
    public boolean isAlive() {
        boolean processAlive = process.isAlive();
        boolean connectionAlive = connection != null && connection.isAlive();
        
        if (processAlive && connectionAlive) {
            state = ProcessState.RUNNING;
            return true;
        } else {
            if (state == ProcessState.RUNNING) {
                state = ProcessState.FAILED;
            }
            return false;
        }
    }
    
    /**
     * 获取进程ID
     */
    public long getProcessId() {
        // Java 8兼容：使用hashCode代替pid()
        return Math.abs(process.hashCode()) % 100000;
    }
    
    /**
     * 获取运行时间（毫秒）
     */
    public long getUptime() {
        return System.currentTimeMillis() - startTime;
    }
    
    /**
     * 获取运行时间（格式化）
     */
    public String getFormattedUptime() {
        long uptimeMs = getUptime();
        long seconds = uptimeMs / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;
        
        if (days > 0) {
            return String.format("%dd %dh %dm %ds", days, hours % 24, minutes % 60, seconds % 60);
        } else if (hours > 0) {
            return String.format("%dh %dm %ds", hours, minutes % 60, seconds % 60);
        } else if (minutes > 0) {
            return String.format("%dm %ds", minutes, seconds % 60);
        } else {
            return String.format("%ds", seconds);
        }
    }
    
    /**
     * 获取STDIO连接
     */
    public StdioConnection getConnection() {
        if (connection == null || !connection.isAlive()) {
            throw new IllegalStateException("STDIO connection is not available");
        }
        return connection;
    }
    
    /**
     * 检查是否应该自动重启
     */
    public boolean shouldAutoRestart() {
        return config.isAutoRestart() && 
               restartCount.get() < config.getMaxRestartCount() &&
               state != ProcessState.STOPPING &&
               state != ProcessState.STOPPED;
    }
    
    /**
     * 增加重启计数
     */
    public void incrementRestartCount() {
        restartCount.incrementAndGet();
    }
    
    /**
     * 重置重启计数
     */
    public void resetRestartCount() {
        restartCount.set(0);
    }
    
    /**
     * 更新健康检查时间
     */
    public void updateHealthCheckTime() {
        lastHealthCheckTime.set(System.currentTimeMillis());
    }
    
    /**
     * 获取最后健康检查时间
     */
    public long getLastHealthCheckTime() {
        return lastHealthCheckTime.get();
    }
    
    /**
     * 获取进程统计信息
     */
    public ProcessStats getStats() {
        return ProcessStats.builder()
                .serviceId(serviceId)
                .processId(getProcessId())
                .state(state)
                .uptime(getUptime())
                .restartCount(restartCount.get())
                .lastHealthCheckTime(lastHealthCheckTime.get())
                .memoryUsage(getMemoryUsage())
                .connectionStats(connection != null ? connection.getStats() : null)
                .build();
    }
    
    /**
     * 获取内存使用情况（占位符实现）
     */
    private long getMemoryUsage() {
        // 这里可以通过JMX或其他方式获取进程内存使用情况
        // 目前返回占位符值
        return 0;
    }
    
    /**
     * 停止进程
     */
    public void stop() {
        log.info("Stopping MCP service: {}", serviceId);
        state = ProcessState.STOPPING;
        
        try {
            // 关闭STDIO连接
            if (connection != null) {
                connection.close();
                connection = null;
            }
            
            // 优雅停止进程
            if (process.isAlive()) {
                process.destroy();
                
                // 等待进程退出
                boolean exited = process.waitFor(config.getShutdownTimeoutMs(), TimeUnit.MILLISECONDS);
                
                if (!exited) {
                    log.warn("Process did not exit gracefully, forcing termination: {}", serviceId);
                    process.destroyForcibly();
                    process.waitFor(5, TimeUnit.SECONDS);
                }
            }
            
            state = ProcessState.STOPPED;
            log.info("MCP service stopped: {}", serviceId);
            
        } catch (Exception e) {
            state = ProcessState.FAILED;
            log.error("Error stopping MCP service: {}", serviceId, e);
            
            // 强制终止
            if (process.isAlive()) {
                process.destroyForcibly();
            }
        }
    }
    
    /**
     * 强制终止进程
     */
    public void forceStop() {
        log.warn("Force stopping MCP service: {}", serviceId);
        state = ProcessState.STOPPING;
        
        try {
            if (connection != null) {
                connection.close();
                connection = null;
            }
            
            if (process.isAlive()) {
                process.destroyForcibly();
                process.waitFor(5, TimeUnit.SECONDS);
            }
            
            state = ProcessState.STOPPED;
            log.info("MCP service force stopped: {}", serviceId);
            
        } catch (Exception e) {
            state = ProcessState.FAILED;
            log.error("Error force stopping MCP service: {}", serviceId, e);
        }
    }
    
    /**
     * 进程状态枚举
     */
    public enum ProcessState {
        STARTING,   // 启动中
        RUNNING,    // 运行中
        STOPPING,   // 停止中
        STOPPED,    // 已停止
        FAILED      // 失败
    }
    
    /**
     * 进程统计信息
     */
    public static class ProcessStats {
        private final String serviceId;
        private final long processId;
        private final ProcessState state;
        private final long uptime;
        private final int restartCount;
        private final long lastHealthCheckTime;
        private final long memoryUsage;
        private final StdioConnection.ConnectionStats connectionStats;
        
        private ProcessStats(String serviceId, long processId, ProcessState state, long uptime, 
                           int restartCount, long lastHealthCheckTime, long memoryUsage,
                           StdioConnection.ConnectionStats connectionStats) {
            this.serviceId = serviceId;
            this.processId = processId;
            this.state = state;
            this.uptime = uptime;
            this.restartCount = restartCount;
            this.lastHealthCheckTime = lastHealthCheckTime;
            this.memoryUsage = memoryUsage;
            this.connectionStats = connectionStats;
        }
        
        public static ProcessStatsBuilder builder() {
            return new ProcessStatsBuilder();
        }
        
        // Getters
        public String getServiceId() { return serviceId; }
        public long getProcessId() { return processId; }
        public ProcessState getState() { return state; }
        public long getUptime() { return uptime; }
        public int getRestartCount() { return restartCount; }
        public long getLastHealthCheckTime() { return lastHealthCheckTime; }
        public long getMemoryUsage() { return memoryUsage; }
        public StdioConnection.ConnectionStats getConnectionStats() { return connectionStats; }
        
        public static class ProcessStatsBuilder {
            private String serviceId;
            private long processId;
            private ProcessState state;
            private long uptime;
            private int restartCount;
            private long lastHealthCheckTime;
            private long memoryUsage;
            private StdioConnection.ConnectionStats connectionStats;
            
            public ProcessStatsBuilder serviceId(String serviceId) {
                this.serviceId = serviceId;
                return this;
            }
            
            public ProcessStatsBuilder processId(long processId) {
                this.processId = processId;
                return this;
            }
            
            public ProcessStatsBuilder state(ProcessState state) {
                this.state = state;
                return this;
            }
            
            public ProcessStatsBuilder uptime(long uptime) {
                this.uptime = uptime;
                return this;
            }
            
            public ProcessStatsBuilder restartCount(int restartCount) {
                this.restartCount = restartCount;
                return this;
            }
            
            public ProcessStatsBuilder lastHealthCheckTime(long lastHealthCheckTime) {
                this.lastHealthCheckTime = lastHealthCheckTime;
                return this;
            }
            
            public ProcessStatsBuilder memoryUsage(long memoryUsage) {
                this.memoryUsage = memoryUsage;
                return this;
            }
            
            public ProcessStatsBuilder connectionStats(StdioConnection.ConnectionStats connectionStats) {
                this.connectionStats = connectionStats;
                return this;
            }
            
            public ProcessStats build() {
                return new ProcessStats(serviceId, processId, state, uptime, restartCount, 
                                      lastHealthCheckTime, memoryUsage, connectionStats);
            }
        }
    }
}
