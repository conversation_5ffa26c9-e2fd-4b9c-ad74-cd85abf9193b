package com.nexus.common.mcp.process;

/**
 * MCP进程异常
 * 
 * 用于表示MCP进程管理相关的错误
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public class McpProcessException extends RuntimeException {
    
    public McpProcessException(String message) {
        super(message);
    }
    
    public McpProcessException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public McpProcessException(Throwable cause) {
        super(cause);
    }
}
