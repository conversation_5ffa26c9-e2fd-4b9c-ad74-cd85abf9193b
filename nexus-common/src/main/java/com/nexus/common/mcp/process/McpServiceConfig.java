package com.nexus.common.mcp.process;

import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MCP服务配置
 *
 * 定义MCP服务启动和运行的配置参数
 *
 * 常见的MCP服务启动方式：
 * 1. Python: python -m mcp_server_name --arg1 value1
 * 2. Node.js: node server.js --config config.json
 * 3. Java: java -jar mcp-server.jar --port 8080
 * 4. 可执行文件: ./mcp-server --config /path/to/config
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Builder
public class McpServiceConfig {

    /**
     * 服务名称（唯一标识）
     */
    private String serviceName;

    /**
     * 服务类型
     */
    @Builder.Default
    private ServiceType serviceType = ServiceType.EXECUTABLE;

    /**
     * 服务类型枚举
     */
    public enum ServiceType {
        PYTHON,     // Python MCP服务
        NODEJS,     // Node.js MCP服务
        JAVA,       // Java MCP服务
        EXECUTABLE  // 可执行文件MCP服务
    }

    /**
     * 服务路径
     * - 对于Python: 模块名或脚本路径
     * - 对于Node.js: 脚本文件路径
     * - 对于Java: JAR文件路径
     * - 对于可执行文件: 可执行文件路径
     */
    private String servicePath;

    /**
     * 工作目录
     */
    private String workingDirectory;

    /**
     * 程序参数（传递给MCP服务的参数）
     */
    @Builder.Default
    private List<String> args = new ArrayList<>();

    /**
     * JVM参数（仅适用于Java服务）
     */
    @Builder.Default
    private List<String> jvmArgs = new ArrayList<>();

    /**
     * Python解释器路径（仅适用于Python服务）
     */
    private String pythonExecutable;

    /**
     * Node.js可执行文件路径（仅适用于Node.js服务）
     */
    private String nodeExecutable;
    
    /**
     * 环境变量
     */
    @Builder.Default
    private Map<String, String> environmentVariables = new HashMap<>();
    
    /**
     * 启动超时时间（毫秒）
     */
    @Builder.Default
    private long startupTimeoutMs = 30000;
    
    /**
     * 关闭超时时间（毫秒）
     */
    @Builder.Default
    private long shutdownTimeoutMs = 10000;
    
    /**
     * 是否自动重启
     */
    @Builder.Default
    private boolean autoRestart = true;
    
    /**
     * 最大重启次数
     */
    @Builder.Default
    private int maxRestartCount = 3;
    
    /**
     * 健康检查间隔（秒）
     */
    @Builder.Default
    private int healthCheckIntervalSeconds = 30;
    
    /**
     * 服务描述
     */
    private String description;
    
    /**
     * 服务版本
     */
    private String version;
    
    /**
     * 服务标签
     */
    @Builder.Default
    private Map<String, String> tags = new HashMap<>();
    
    /**
     * 验证配置是否有效
     */
    public boolean isValid() {
        return serviceName != null && !serviceName.trim().isEmpty() &&
               servicePath != null && !servicePath.trim().isEmpty() &&
               startupTimeoutMs > 0 &&
               shutdownTimeoutMs > 0 &&
               maxRestartCount >= 0 &&
               healthCheckIntervalSeconds > 0;
    }
    
    /**
     * 添加程序参数
     */
    public McpServiceConfig addArg(String arg) {
        if (args == null) {
            args = new ArrayList<>();
        }
        args.add(arg);
        return this;
    }
    
    /**
     * 添加JVM参数
     */
    public McpServiceConfig addJvmArg(String jvmArg) {
        if (jvmArgs == null) {
            jvmArgs = new ArrayList<>();
        }
        jvmArgs.add(jvmArg);
        return this;
    }
    
    /**
     * 添加环境变量
     */
    public McpServiceConfig addEnvironmentVariable(String key, String value) {
        if (environmentVariables == null) {
            environmentVariables = new HashMap<>();
        }
        environmentVariables.put(key, value);
        return this;
    }
    
    /**
     * 添加标签
     */
    public McpServiceConfig addTag(String key, String value) {
        if (tags == null) {
            tags = new HashMap<>();
        }
        tags.put(key, value);
        return this;
    }
    
    /**
     * 创建默认配置
     */
    public static McpServiceConfig createDefault(String serviceName, String servicePath) {
        return McpServiceConfig.builder()
                .serviceName(serviceName)
                .servicePath(servicePath)
                .build();
    }
    
    /**
     * 创建Java服务配置
     */
    public static McpServiceConfig createJavaService(String serviceName, String jarPath) {
        return McpServiceConfig.builder()
                .serviceName(serviceName)
                .serviceType(ServiceType.JAVA)
                .servicePath(jarPath)
                .jvmArgs(List.of("-Xmx512m", "-Xms256m"))
                .build();
    }
    
    /**
     * 创建Python服务配置（使用uvx）
     */
    public static McpServiceConfig createPythonService(String serviceName, String packageName) {
        return McpServiceConfig.builder()
                .serviceName(serviceName)
                .serviceType(ServiceType.PYTHON)
                .servicePath(packageName) // 例如: "mcp-server-git"
                .pythonExecutable("uvx") // 默认使用uvx
                .build();
    }

    /**
     * 创建Python脚本服务配置
     */
    public static McpServiceConfig createPythonScriptService(String serviceName, String scriptPath) {
        return McpServiceConfig.builder()
                .serviceName(serviceName)
                .serviceType(ServiceType.PYTHON)
                .servicePath(scriptPath)
                .pythonExecutable("python") // 使用python解释器
                .build();
    }

    /**
     * 创建Node.js服务配置（使用npx）
     */
    public static McpServiceConfig createNodeService(String serviceName, String packageName) {
        return McpServiceConfig.builder()
                .serviceName(serviceName)
                .serviceType(ServiceType.NODEJS)
                .servicePath(packageName) // 例如: "@modelcontextprotocol/server-filesystem"
                .nodeExecutable("npx")
                .args(Arrays.asList("-y")) // npx -y 参数
                .build();
    }

    /**
     * 创建Node.js脚本服务配置
     */
    public static McpServiceConfig createNodeScriptService(String serviceName, String scriptPath) {
        return McpServiceConfig.builder()
                .serviceName(serviceName)
                .serviceType(ServiceType.NODEJS)
                .servicePath(scriptPath)
                .nodeExecutable("node")
                .build();
    }
    
    /**
     * 创建常用MCP服务的预定义配置
     */

    /**
     * 文件系统服务 - 提供文件操作能力
     */
    public static McpServiceConfig createFilesystemService(String allowedPath) {
        return createNodeService("filesystem", "@modelcontextprotocol/server-filesystem")
                .addArg(allowedPath)
                .addTag("category", "filesystem")
                .addTag("official", "true");
    }

    /**
     * Git服务 - 提供Git仓库操作能力
     */
    public static McpServiceConfig createGitService(String repositoryPath) {
        return createPythonService("git", "mcp-server-git")
                .addArg("--repository")
                .addArg(repositoryPath)
                .addTag("category", "version-control")
                .addTag("official", "true");
    }

    /**
     * GitHub服务 - 提供GitHub API集成
     */
    public static McpServiceConfig createGitHubService(String token) {
        return createNodeService("github", "@modelcontextprotocol/server-github")
                .addEnvironmentVariable("GITHUB_PERSONAL_ACCESS_TOKEN", token)
                .addTag("category", "api")
                .addTag("official", "true");
    }

    /**
     * 内存服务 - 提供知识图谱持久化内存
     */
    public static McpServiceConfig createMemoryService() {
        return createNodeService("memory", "@modelcontextprotocol/server-memory")
                .addTag("category", "storage")
                .addTag("official", "true");
    }

    /**
     * 时间服务 - 提供时间和时区转换能力
     */
    public static McpServiceConfig createTimeService() {
        return createNodeService("time", "@modelcontextprotocol/server-time")
                .addTag("category", "utility")
                .addTag("official", "true");
    }

    /**
     * 网页抓取服务 - 提供网页内容获取和转换
     */
    public static McpServiceConfig createFetchService() {
        return createNodeService("fetch", "@modelcontextprotocol/server-fetch")
                .addTag("category", "web")
                .addTag("official", "true");
    }

    /**
     * 复制配置
     */
    public McpServiceConfig copy() {
        return McpServiceConfig.builder()
                .serviceName(serviceName)
                .serviceType(serviceType)
                .servicePath(servicePath)
                .workingDirectory(workingDirectory)
                .args(new ArrayList<>(args))
                .jvmArgs(new ArrayList<>(jvmArgs))
                .pythonExecutable(pythonExecutable)
                .nodeExecutable(nodeExecutable)
                .environmentVariables(new HashMap<>(environmentVariables))
                .startupTimeoutMs(startupTimeoutMs)
                .shutdownTimeoutMs(shutdownTimeoutMs)
                .autoRestart(autoRestart)
                .maxRestartCount(maxRestartCount)
                .healthCheckIntervalSeconds(healthCheckIntervalSeconds)
                .description(description)
                .version(version)
                .tags(new HashMap<>(tags))
                .build();
    }
    
    @Override
    public String toString() {
        return "McpServiceConfig{" +
                "serviceName='" + serviceName + '\'' +
                ", servicePath='" + servicePath + '\'' +
                ", workingDirectory='" + workingDirectory + '\'' +
                ", args=" + args +
                ", jvmArgs=" + jvmArgs +
                ", environmentVariables=" + environmentVariables +
                ", startupTimeoutMs=" + startupTimeoutMs +
                ", shutdownTimeoutMs=" + shutdownTimeoutMs +
                ", autoRestart=" + autoRestart +
                ", maxRestartCount=" + maxRestartCount +
                ", healthCheckIntervalSeconds=" + healthCheckIntervalSeconds +
                ", description='" + description + '\'' +
                ", version='" + version + '\'' +
                ", tags=" + tags +
                '}';
    }
}
