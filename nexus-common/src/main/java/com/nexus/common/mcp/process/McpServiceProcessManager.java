package com.nexus.common.mcp.process;

import com.nexus.common.mcp.transport.StdioConnection;
import com.nexus.common.mcp.transport.StdioTransport;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.io.File;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * MCP服务进程管理器
 * 
 * 负责MCP服务进程的完整生命周期管理：
 * - 进程启动和停止
 * - 健康检查和监控
 * - 自动重启和故障恢复
 * - 资源清理和管理
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class McpServiceProcessManager {
    
    private final StdioTransport stdioTransport;
    private final ConcurrentHashMap<String, ManagedMcpProcess> runningProcesses = new ConcurrentHashMap<>();
    private final ScheduledExecutorService healthCheckExecutor = Executors.newScheduledThreadPool(2);
    private final ExecutorService processExecutor = Executors.newCachedThreadPool();
    
    /**
     * 启动MCP服务进程
     */
    public ManagedMcpProcess startMcpService(McpServiceConfig config) {
        String serviceId = generateServiceId(config);
        
        if (runningProcesses.containsKey(serviceId)) {
            throw new McpProcessException("MCP service already running: " + serviceId);
        }
        
        log.info("Starting MCP service: {} with config: {}", serviceId, config);
        
        try {
            // 验证配置
            validateConfig(config);
            
            // 构建启动命令
            List<String> command = buildStartCommand(config);
            
            // 创建进程构建器
            ProcessBuilder processBuilder = createProcessBuilder(command, config);
            
            // 启动进程
            Process process = processBuilder.start();
            
            // 等待进程启动
            if (!waitForProcessStart(process, config.getStartupTimeoutMs())) {
                process.destroyForcibly();
                throw new McpProcessException("MCP service failed to start within timeout: " + config.getStartupTimeoutMs() + "ms");
            }
            
            // 创建托管进程对象
            ManagedMcpProcess managedProcess = new ManagedMcpProcess(
                serviceId, 
                process, 
                config,
                stdioTransport
            );
            
            // 注册进程
            runningProcesses.put(serviceId, managedProcess);
            
            // 启动健康检查
            scheduleHealthCheck(managedProcess);
            
            log.info("MCP service started successfully: {}", serviceId);
            return managedProcess;
            
        } catch (Exception e) {
            log.error("Failed to start MCP service: {}", serviceId, e);
            throw new McpProcessException("Failed to start MCP service: " + serviceId, e);
        }
    }
    
    /**
     * 停止MCP服务进程
     */
    public boolean stopMcpService(String serviceId) {
        ManagedMcpProcess managedProcess = runningProcesses.remove(serviceId);
        
        if (managedProcess == null) {
            log.warn("MCP service not found: {}", serviceId);
            return false;
        }
        
        log.info("Stopping MCP service: {}", serviceId);
        
        try {
            managedProcess.stop();
            log.info("MCP service stopped successfully: {}", serviceId);
            return true;
        } catch (Exception e) {
            log.error("Failed to stop MCP service: {}", serviceId, e);
            return false;
        }
    }
    
    /**
     * 重启MCP服务进程
     */
    public ManagedMcpProcess restartMcpService(String serviceId) {
        ManagedMcpProcess oldProcess = runningProcesses.get(serviceId);
        
        if (oldProcess == null) {
            throw new McpProcessException("MCP service not found: " + serviceId);
        }
        
        log.info("Restarting MCP service: {}", serviceId);
        
        McpServiceConfig config = oldProcess.getConfig();
        
        // 停止旧进程
        stopMcpService(serviceId);
        
        // 等待一段时间
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 启动新进程
        return startMcpService(config);
    }
    
    /**
     * 获取运行中的MCP服务
     */
    public ManagedMcpProcess getMcpService(String serviceId) {
        return runningProcesses.get(serviceId);
    }
    
    /**
     * 获取所有运行中的MCP服务
     */
    public Map<String, ManagedMcpProcess> getAllMcpServices() {
        return new HashMap<>(runningProcesses);
    }
    
    /**
     * 检查MCP服务是否运行
     */
    public boolean isServiceRunning(String serviceId) {
        ManagedMcpProcess process = runningProcesses.get(serviceId);
        return process != null && process.isAlive();
    }
    
    /**
     * 获取管理器统计信息
     */
    public ProcessManagerStats getStats() {
        int totalProcesses = runningProcesses.size();
        int aliveProcesses = (int) runningProcesses.values().stream()
                .mapToLong(p -> p.isAlive() ? 1 : 0)
                .sum();
        
        return new ProcessManagerStats(totalProcesses, aliveProcesses);
    }
    
    /**
     * 验证配置
     */
    private void validateConfig(McpServiceConfig config) {
        if (config.getServicePath() == null || config.getServicePath().trim().isEmpty()) {
            throw new IllegalArgumentException("Service path cannot be null or empty");
        }

        // 对于NODEJS类型，servicePath可能是npm包名，不需要验证文件存在性
        if (config.getServiceType() != McpServiceConfig.ServiceType.NODEJS) {
            File serviceFile = new File(config.getServicePath());
            if (!serviceFile.exists()) {
                throw new IllegalArgumentException("Service file does not exist: " + config.getServicePath());
            }
        }

        if (config.getStartupTimeoutMs() <= 0) {
            throw new IllegalArgumentException("Startup timeout must be positive");
        }
    }
    
    /**
     * 构建启动命令
     */
    private List<String> buildStartCommand(McpServiceConfig config) {
        List<String> command = new ArrayList<>();

        // 根据服务类型确定启动方式
        switch (config.getServiceType()) {
            case PYTHON:
                buildPythonCommand(command, config);
                break;
            case NODEJS:
                buildNodeJsCommand(command, config);
                break;
            case JAVA:
                buildJavaCommand(command, config);
                break;
            case EXECUTABLE:
                buildExecutableCommand(command, config);
                break;
            default:
                // 回退到基于文件扩展名的判断（向后兼容）
                buildLegacyCommand(command, config);
                break;
        }

        // 添加程序参数
        command.addAll(config.getArgs());

        return command;
    }

    /**
     * 构建Python服务启动命令
     */
    private void buildPythonCommand(List<String> command, McpServiceConfig config) {
        String pythonExecutable = config.getPythonExecutable();
        if (pythonExecutable == null) {
            pythonExecutable = "python"; // 默认使用python
        }

        command.add(pythonExecutable);

        // 如果使用uvx，添加包名；如果使用python，添加脚本路径或模块
        if ("uvx".equals(pythonExecutable)) {
            // uvx mcp-server-git
            command.add(config.getServicePath());
        } else {
            // python script.py 或 python -m module_name
            if (config.getServicePath().endsWith(".py")) {
                command.add(config.getServicePath());
            } else {
                // 假设是模块名
                command.add("-m");
                command.add(config.getServicePath());
            }
        }
    }

    /**
     * 构建Node.js服务启动命令
     */
    private void buildNodeJsCommand(List<String> command, McpServiceConfig config) {
        String nodeExecutable = config.getNodeExecutable();
        if (nodeExecutable == null) {
            nodeExecutable = "node"; // 默认使用node
        }

        // 如果使用npx，在Windows上需要通过cmd来执行
        if ("npx".equals(nodeExecutable)) {
            // 检测操作系统
            String os = System.getProperty("os.name").toLowerCase();
            if (os.contains("win")) {
                // Windows: cmd /c npx -y @modelcontextprotocol/server-memory
                command.add("cmd");
                command.add("/c");
                command.add("npx");
                command.add("-y");
                command.add(config.getServicePath());
            } else {
                // Unix/Linux: npx -y @modelcontextprotocol/server-memory
                command.add("npx");
                command.add("-y");
                command.add(config.getServicePath());
            }
        } else {
            // node script.js
            command.add(nodeExecutable);
            command.add(config.getServicePath());
        }
    }

    /**
     * 构建Java服务启动命令
     */
    private void buildJavaCommand(List<String> command, McpServiceConfig config) {
        command.add("java");

        // 添加JVM参数
        command.addAll(config.getJvmArgs());

        // 添加-jar参数和JAR文件路径
        command.add("-jar");
        command.add(config.getServicePath());
    }

    /**
     * 构建可执行文件启动命令
     */
    private void buildExecutableCommand(List<String> command, McpServiceConfig config) {
        command.add(config.getServicePath());
    }

    /**
     * 构建传统启动命令（向后兼容）
     */
    private void buildLegacyCommand(List<String> command, McpServiceConfig config) {
        String servicePath = config.getServicePath();

        if (servicePath.endsWith(".jar")) {
            command.add("java");
            command.addAll(config.getJvmArgs());
            command.add("-jar");
            command.add(servicePath);
        } else if (servicePath.endsWith(".py")) {
            command.add("python");
            command.add(servicePath);
        } else if (servicePath.endsWith(".js")) {
            command.add("node");
            command.add(servicePath);
        } else if (servicePath.endsWith(".exe") || new File(servicePath).canExecute()) {
            command.add(servicePath);
        } else {
            throw new IllegalArgumentException("Unsupported service type: " + servicePath);
        }
    }
    
    /**
     * 创建进程构建器
     */
    private ProcessBuilder createProcessBuilder(List<String> command, McpServiceConfig config) {
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        
        // 设置工作目录
        if (config.getWorkingDirectory() != null) {
            processBuilder.directory(new File(config.getWorkingDirectory()));
        }
        
        // 设置环境变量
        Map<String, String> env = processBuilder.environment();
        env.putAll(config.getEnvironmentVariables());
        
        // 重定向错误流
        processBuilder.redirectErrorStream(false);
        
        return processBuilder;
    }
    
    /**
     * 等待进程启动
     */
    private boolean waitForProcessStart(Process process, long timeoutMs) {
        long startTime = System.currentTimeMillis();
        
        while (System.currentTimeMillis() - startTime < timeoutMs) {
            if (!process.isAlive()) {
                return false; // 进程已经退出
            }
            
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }
        
        return process.isAlive();
    }
    
    /**
     * 调度健康检查
     */
    private void scheduleHealthCheck(ManagedMcpProcess managedProcess) {
        healthCheckExecutor.scheduleWithFixedDelay(
            () -> performHealthCheck(managedProcess),
            30, // 初始延迟30秒
            30, // 每30秒检查一次
            TimeUnit.SECONDS
        );
    }
    
    /**
     * 执行健康检查
     */
    private void performHealthCheck(ManagedMcpProcess managedProcess) {
        try {
            if (!managedProcess.isAlive()) {
                log.warn("MCP service process died: {}", managedProcess.getServiceId());
                
                // 检查是否需要自动重启
                if (managedProcess.shouldAutoRestart()) {
                    log.info("Attempting to restart MCP service: {}", managedProcess.getServiceId());
                    
                    try {
                        restartMcpService(managedProcess.getServiceId());
                        log.info("MCP service restarted successfully: {}", managedProcess.getServiceId());
                    } catch (Exception e) {
                        log.error("Failed to restart MCP service: {}", managedProcess.getServiceId(), e);
                    }
                } else {
                    log.info("Auto-restart disabled or max restart count reached for: {}", managedProcess.getServiceId());
                    runningProcesses.remove(managedProcess.getServiceId());
                }
            }
        } catch (Exception e) {
            log.error("Error during health check for: {}", managedProcess.getServiceId(), e);
        }
    }
    
    /**
     * 生成服务ID
     */
    private String generateServiceId(McpServiceConfig config) {
        return config.getServiceName() + "-" + System.currentTimeMillis();
    }
    
    /**
     * 关闭管理器
     */
    @PreDestroy
    public void shutdown() {
        log.info("Shutting down MCP service process manager");
        
        // 停止所有运行中的服务
        runningProcesses.values().forEach(process -> {
            try {
                process.stop();
            } catch (Exception e) {
                log.error("Error stopping MCP service: {}", process.getServiceId(), e);
            }
        });
        
        runningProcesses.clear();
        
        // 关闭执行器
        healthCheckExecutor.shutdown();
        processExecutor.shutdown();
        
        try {
            if (!healthCheckExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                healthCheckExecutor.shutdownNow();
            }
            if (!processExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                processExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            healthCheckExecutor.shutdownNow();
            processExecutor.shutdownNow();
        }
        
        log.info("MCP service process manager shutdown completed");
    }
    
    /**
     * 进程管理器统计信息
     */
    public static class ProcessManagerStats {
        private final int totalProcesses;
        private final int aliveProcesses;
        
        public ProcessManagerStats(int totalProcesses, int aliveProcesses) {
            this.totalProcesses = totalProcesses;
            this.aliveProcesses = aliveProcesses;
        }
        
        public int getTotalProcesses() { return totalProcesses; }
        public int getAliveProcesses() { return aliveProcesses; }
        public int getDeadProcesses() { return totalProcesses - aliveProcesses; }
    }
}
