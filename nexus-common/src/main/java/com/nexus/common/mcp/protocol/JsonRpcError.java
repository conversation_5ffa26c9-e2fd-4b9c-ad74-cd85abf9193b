package com.nexus.common.mcp.protocol;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * JSON-RPC 2.0 错误对象
 * 
 * 错误对象包含错误的详细信息
 * 必须包含：code, message
 * 可选包含：data
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class JsonRpcError {
    
    // 标准错误代码
    public static final int PARSE_ERROR = -32700;
    public static final int INVALID_REQUEST = -32600;
    public static final int METHOD_NOT_FOUND = -32601;
    public static final int INVALID_PARAMS = -32602;
    public static final int INTERNAL_ERROR = -32603;
    
    // 服务器错误代码范围：-32000 到 -32099
    public static final int SERVER_ERROR_MIN = -32099;
    public static final int SERVER_ERROR_MAX = -32000;
    
    /**
     * 错误代码
     * 必须是整数
     */
    @JsonProperty("code")
    @NotNull(message = "Error code cannot be null")
    private Integer code;
    
    /**
     * 错误消息
     * 简短描述错误的字符串
     */
    @JsonProperty("message")
    @NotBlank(message = "Error message cannot be blank")
    private String message;
    
    /**
     * 错误数据
     * 包含错误附加信息的原始值或结构化值
     * 可以省略
     */
    @JsonProperty("data")
    private Object data;
    
    /**
     * 验证错误对象是否有效
     */
    public boolean isValid() {
        return code != null && 
               message != null && 
               !message.trim().isEmpty();
    }
    
    /**
     * 检查是否为标准错误代码
     */
    public boolean isStandardError() {
        return code != null && (
            code == PARSE_ERROR ||
            code == INVALID_REQUEST ||
            code == METHOD_NOT_FOUND ||
            code == INVALID_PARAMS ||
            code == INTERNAL_ERROR ||
            (code >= SERVER_ERROR_MIN && code <= SERVER_ERROR_MAX)
        );
    }
    
    /**
     * 创建解析错误
     */
    public static JsonRpcError parseError(String message) {
        return JsonRpcError.builder()
                .code(PARSE_ERROR)
                .message(message != null ? message : "Parse error")
                .build();
    }
    
    /**
     * 创建无效请求错误
     */
    public static JsonRpcError invalidRequest(String message) {
        return JsonRpcError.builder()
                .code(INVALID_REQUEST)
                .message(message != null ? message : "Invalid Request")
                .build();
    }
    
    /**
     * 创建方法未找到错误
     */
    public static JsonRpcError methodNotFound(String message) {
        return JsonRpcError.builder()
                .code(METHOD_NOT_FOUND)
                .message(message != null ? message : "Method not found")
                .build();
    }
    
    /**
     * 创建无效参数错误
     */
    public static JsonRpcError invalidParams(String message) {
        return JsonRpcError.builder()
                .code(INVALID_PARAMS)
                .message(message != null ? message : "Invalid params")
                .build();
    }
    
    /**
     * 创建内部错误
     */
    public static JsonRpcError internalError(String message) {
        return JsonRpcError.builder()
                .code(INTERNAL_ERROR)
                .message(message != null ? message : "Internal error")
                .build();
    }
    
    /**
     * 创建自定义错误
     */
    public static JsonRpcError customError(int code, String message, Object data) {
        return JsonRpcError.builder()
                .code(code)
                .message(message)
                .data(data)
                .build();
    }
    
    /**
     * 创建服务器错误
     */
    public static JsonRpcError serverError(int code, String message) {
        if (code < SERVER_ERROR_MIN || code > SERVER_ERROR_MAX) {
            throw new IllegalArgumentException("Server error code must be between " +
                SERVER_ERROR_MIN + " and " + SERVER_ERROR_MAX);
        }

        return JsonRpcError.builder()
                .code(code)
                .message(message)
                .build();
    }

    /**
     * 创建通用错误
     */
    public static JsonRpcError createError(int code, String message, Object data) {
        return JsonRpcError.builder()
                .code(code)
                .message(message)
                .data(data)
                .build();
    }

    /**
     * 创建解析错误
     */
    public static JsonRpcError createParseError() {
        return JsonRpcError.builder()
                .code(PARSE_ERROR)
                .message("Parse error")
                .build();
    }

    /**
     * 创建无效请求错误
     */
    public static JsonRpcError createInvalidRequest() {
        return JsonRpcError.builder()
                .code(INVALID_REQUEST)
                .message("Invalid Request")
                .build();
    }

    /**
     * 创建方法未找到错误
     */
    public static JsonRpcError createMethodNotFound() {
        return JsonRpcError.builder()
                .code(METHOD_NOT_FOUND)
                .message("Method not found")
                .build();
    }

    /**
     * 创建无效参数错误
     */
    public static JsonRpcError createInvalidParams() {
        return JsonRpcError.builder()
                .code(INVALID_PARAMS)
                .message("Invalid params")
                .build();
    }

    /**
     * 创建内部错误
     */
    public static JsonRpcError createInternalError() {
        return JsonRpcError.builder()
                .code(INTERNAL_ERROR)
                .message("Internal error")
                .build();
    }
    
    /**
     * 获取错误类型描述
     */
    public String getErrorType() {
        if (code == null) {
            return "UNKNOWN";
        }
        
        switch (code) {
            case PARSE_ERROR:
                return "PARSE_ERROR";
            case INVALID_REQUEST:
                return "INVALID_REQUEST";
            case METHOD_NOT_FOUND:
                return "METHOD_NOT_FOUND";
            case INVALID_PARAMS:
                return "INVALID_PARAMS";
            case INTERNAL_ERROR:
                return "INTERNAL_ERROR";
            default:
                if (code >= SERVER_ERROR_MIN && code <= SERVER_ERROR_MAX) {
                    return "SERVER_ERROR";
                } else {
                    return "CUSTOM_ERROR";
                }
        }
    }
}
