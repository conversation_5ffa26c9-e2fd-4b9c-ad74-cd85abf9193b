package com.nexus.common.mcp.protocol;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotNull;

/**
 * JSON-RPC 2.0 错误响应消息
 * 
 * 错误响应消息用于服务器向客户端返回方法调用的错误结果
 * 必须包含：jsonrpc, id, error
 * 不能包含：result
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class JsonRpcErrorResponse extends JsonRpcMessage {
    
    /**
     * 响应ID，必须与对应请求的ID相同
     * 如果请求ID无法确定，则为null
     */
    @JsonProperty("id")
    private String id;
    
    /**
     * 错误信息对象
     */
    @JsonProperty("error")
    @NotNull(message = "Error object cannot be null")
    private JsonRpcError error;
    
    @Override
    public MessageType getMessageType() {
        return MessageType.ERROR;
    }
    
    @Override
    public boolean isValid() {
        return super.isValid() && 
               error != null && 
               error.isValid();
    }
    
    /**
     * 创建错误响应
     */
    public static JsonRpcErrorResponse createErrorResponse(String id, JsonRpcError error) {
        return JsonRpcErrorResponse.builder()
                .id(id)
                .error(error)
                .build();
    }
    
    /**
     * 创建解析错误响应
     */
    public static JsonRpcErrorResponse createParseError() {
        return JsonRpcErrorResponse.builder()
                .id(null)
                .error(JsonRpcError.parseError("Invalid JSON was received by the server"))
                .build();
    }
    
    /**
     * 创建无效请求错误响应
     */
    public static JsonRpcErrorResponse createInvalidRequest(String id) {
        return JsonRpcErrorResponse.builder()
                .id(id)
                .error(JsonRpcError.invalidRequest("The JSON sent is not a valid Request object"))
                .build();
    }
    
    /**
     * 创建方法未找到错误响应
     */
    public static JsonRpcErrorResponse createMethodNotFound(String id, String method) {
        return JsonRpcErrorResponse.builder()
                .id(id)
                .error(JsonRpcError.methodNotFound("Method not found: " + method))
                .build();
    }
    
    /**
     * 创建无效参数错误响应
     */
    public static JsonRpcErrorResponse createInvalidParams(String id, String message) {
        return JsonRpcErrorResponse.builder()
                .id(id)
                .error(JsonRpcError.invalidParams(message))
                .build();
    }
    
    /**
     * 创建内部错误响应
     */
    public static JsonRpcErrorResponse createInternalError(String id, String message) {
        return JsonRpcErrorResponse.builder()
                .id(id)
                .error(JsonRpcError.internalError(message))
                .build();
    }
    
    /**
     * 创建自定义错误响应
     */
    public static JsonRpcErrorResponse createCustomError(String id, int code, String message, Object data) {
        return JsonRpcErrorResponse.builder()
                .id(id)
                .error(JsonRpcError.customError(code, message, data))
                .build();
    }
    
    /**
     * 检查是否为特定类型的错误
     */
    public boolean isParseError() {
        return error != null && error.getCode() == JsonRpcError.PARSE_ERROR;
    }
    
    public boolean isInvalidRequest() {
        return error != null && error.getCode() == JsonRpcError.INVALID_REQUEST;
    }
    
    public boolean isMethodNotFound() {
        return error != null && error.getCode() == JsonRpcError.METHOD_NOT_FOUND;
    }
    
    public boolean isInvalidParams() {
        return error != null && error.getCode() == JsonRpcError.INVALID_PARAMS;
    }
    
    public boolean isInternalError() {
        return error != null && error.getCode() == JsonRpcError.INTERNAL_ERROR;
    }
}
