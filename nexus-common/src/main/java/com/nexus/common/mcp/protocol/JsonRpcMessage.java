package com.nexus.common.mcp.protocol;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.SuperBuilder;

/**
 * JSON-RPC 2.0 消息基类
 * 
 * 根据JSON-RPC 2.0规范实现：
 * - 所有消息必须包含jsonrpc字段，值为"2.0"
 * - 请求和响应必须包含id字段
 * - 通知不包含id字段
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public abstract class JsonRpcMessage {

    /**
     * 默认构造函数（Jackson需要）
     */
    public JsonRpcMessage() {
    }

    /**
     * JSON-RPC协议版本，必须为"2.0"
     */
    @JsonProperty("jsonrpc")
    private final String jsonrpc = "2.0";
    
    /**
     * 验证消息格式是否正确
     */
    public boolean isValid() {
        return "2.0".equals(jsonrpc);
    }
    
    /**
     * 获取消息类型
     */
    public abstract MessageType getMessageType();
    
    /**
     * 消息类型枚举
     */
    public enum MessageType {
        REQUEST,
        RESPONSE, 
        NOTIFICATION,
        ERROR
    }
}
