package com.nexus.common.mcp.protocol;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;

/**
 * JSON-RPC 2.0 通知消息
 * 
 * 通知消息是不需要响应的请求
 * 必须包含：jsonrpc, method
 * 可选包含：params
 * 不能包含：id
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class JsonRpcNotification extends JsonRpcMessage {
    
    /**
     * 要调用的方法名
     */
    @JsonProperty("method")
    @NotBlank(message = "Method name cannot be blank")
    private String method;
    
    /**
     * 方法参数
     * 可以是结构化值（对象或数组）或null
     */
    @JsonProperty("params")
    private Object params;
    
    @Override
    public MessageType getMessageType() {
        return MessageType.NOTIFICATION;
    }
    
    @Override
    public boolean isValid() {
        return super.isValid() && 
               method != null && 
               !method.trim().isEmpty();
    }
    
    /**
     * 创建通知消息
     */
    public static JsonRpcNotification createNotification(String method, Object params) {
        return JsonRpcNotification.builder()
                .method(method)
                .params(params)
                .build();
    }
    
    /**
     * 创建标准MCP初始化完成通知
     */
    public static JsonRpcNotification createInitializedNotification() {
        return JsonRpcNotification.builder()
                .method("notifications/initialized")
                .build();
    }
    
    /**
     * 创建标准MCP取消通知
     */
    public static JsonRpcNotification createCancelledNotification(String requestId, String reason) {
        CancelledParams params = new CancelledParams(requestId, reason);
        return JsonRpcNotification.builder()
                .method("notifications/cancelled")
                .params(params)
                .build();
    }
    
    /**
     * 创建标准MCP进度通知
     */
    public static JsonRpcNotification createProgressNotification(String progressToken, Object progress) {
        ProgressParams params = new ProgressParams(progressToken, progress);
        return JsonRpcNotification.builder()
                .method("notifications/progress")
                .params(params)
                .build();
    }
    
    /**
     * 创建标准MCP资源列表变更通知
     */
    public static JsonRpcNotification createResourcesListChangedNotification() {
        return JsonRpcNotification.builder()
                .method("notifications/resources/list_changed")
                .build();
    }
    
    /**
     * 创建标准MCP工具列表变更通知
     */
    public static JsonRpcNotification createToolsListChangedNotification() {
        return JsonRpcNotification.builder()
                .method("notifications/tools/list_changed")
                .build();
    }
    
    /**
     * 创建标准MCP提示列表变更通知
     */
    public static JsonRpcNotification createPromptsListChangedNotification() {
        return JsonRpcNotification.builder()
                .method("notifications/prompts/list_changed")
                .build();
    }
    
    /**
     * 取消通知参数
     */
    public static class CancelledParams {
        @JsonProperty("requestId")
        private String requestId;
        
        @JsonProperty("reason")
        private String reason;
        
        public CancelledParams(String requestId, String reason) {
            this.requestId = requestId;
            this.reason = reason;
        }
        
        // Getters and setters
        public String getRequestId() { return requestId; }
        public void setRequestId(String requestId) { this.requestId = requestId; }
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
    }
    
    /**
     * 进度通知参数
     */
    public static class ProgressParams {
        @JsonProperty("progressToken")
        private String progressToken;
        
        @JsonProperty("progress")
        private Object progress;
        
        public ProgressParams(String progressToken, Object progress) {
            this.progressToken = progressToken;
            this.progress = progress;
        }
        
        // Getters and setters
        public String getProgressToken() { return progressToken; }
        public void setProgressToken(String progressToken) { this.progressToken = progressToken; }
        public Object getProgress() { return progress; }
        public void setProgress(Object progress) { this.progress = progress; }
    }
}
