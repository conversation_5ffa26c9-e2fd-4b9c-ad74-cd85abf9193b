package com.nexus.common.mcp.protocol;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * JSON-RPC 2.0 协议处理器
 * 
 * 负责JSON-RPC消息的序列化、反序列化、验证和ID管理
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JsonRpcProtocolHandler {
    
    private final ObjectMapper objectMapper;
    private final AtomicLong requestIdGenerator = new AtomicLong(1);
    
    /**
     * 生成唯一的请求ID
     */
    public String generateRequestId() {
        return String.valueOf(requestIdGenerator.getAndIncrement());
    }
    
    /**
     * 序列化JSON-RPC消息为JSON字符串
     */
    public String serialize(JsonRpcMessage message) throws JsonProcessingException {
        if (message == null) {
            throw new IllegalArgumentException("Message cannot be null");
        }

        if (!message.isValid()) {
            throw new IllegalArgumentException("Invalid JSON-RPC message: " + message);
        }

        // 为MCP通信创建纯净的JSON-RPC对象，不包含额外字段
        if (message instanceof JsonRpcRequest) {
            JsonRpcRequest request = (JsonRpcRequest) message;
            Map<String, Object> pureRequest = new LinkedHashMap<>();
            pureRequest.put("jsonrpc", "2.0");
            pureRequest.put("id", request.getId());
            pureRequest.put("method", request.getMethod());

            // 只有当params不为null时才包含params字段
            if (request.getParams() != null) {
                pureRequest.put("params", request.getParams());
            }

            return objectMapper.writeValueAsString(pureRequest);
        }

        // 对于其他类型的消息，使用原来的方法
        return objectMapper.writeValueAsString(message);
    }
    
    /**
     * 反序列化JSON字符串为JSON-RPC消息
     */
    public JsonRpcMessage deserialize(String json) throws JsonProcessingException {
        if (json == null || json.trim().isEmpty()) {
            throw new IllegalArgumentException("JSON string cannot be null or empty");
        }
        
        try {
            JsonNode node = objectMapper.readTree(json);
            return parseJsonRpcMessage(node);
        } catch (JsonProcessingException e) {
            log.error("Failed to parse JSON: {}", json, e);
            throw e;
        }
    }
    
    /**
     * 解析JSON节点为JSON-RPC消息
     */
    private JsonRpcMessage parseJsonRpcMessage(JsonNode node) throws JsonProcessingException {
        // 验证基本结构
        if (!node.has("jsonrpc") || !"2.0".equals(node.get("jsonrpc").asText())) {
            throw new IllegalArgumentException("Invalid or missing jsonrpc field");
        }
        
        // 根据消息类型解析
        if (node.has("method")) {
            // 这是请求或通知
            if (node.has("id")) {
                // 这是请求
                return objectMapper.treeToValue(node, JsonRpcRequest.class);
            } else {
                // 这是通知
                return objectMapper.treeToValue(node, JsonRpcNotification.class);
            }
        } else if (node.has("result")) {
            // 这是成功响应
            return objectMapper.treeToValue(node, JsonRpcResponse.class);
        } else if (node.has("error")) {
            // 这是错误响应
            return objectMapper.treeToValue(node, JsonRpcErrorResponse.class);
        } else {
            throw new IllegalArgumentException("Invalid JSON-RPC message structure");
        }
    }
    
    /**
     * 创建标准MCP工具调用请求
     */
    public JsonRpcRequest createToolCallRequest(String toolName, Object arguments) {
        return JsonRpcRequest.createToolCallRequest(
            generateRequestId(), 
            toolName, 
            arguments
        );
    }
    
    /**
     * 创建标准MCP资源读取请求
     */
    public JsonRpcRequest createResourceReadRequest(String resourceUri) {
        return JsonRpcRequest.createResourceReadRequest(
            generateRequestId(), 
            resourceUri
        );
    }
    
    /**
     * 创建标准MCP初始化请求
     */
    public JsonRpcRequest createInitializeRequest(Object initParams) {
        return JsonRpcRequest.createInitializeRequest(
            generateRequestId(), 
            initParams
        );
    }
    
    /**
     * 创建成功响应
     */
    public JsonRpcResponse createSuccessResponse(String requestId, Object result) {
        return JsonRpcResponse.createSuccessResponse(requestId, result);
    }
    
    /**
     * 创建错误响应
     */
    public JsonRpcErrorResponse createErrorResponse(String requestId, int errorCode, String errorMessage) {
        JsonRpcError error = JsonRpcError.customError(errorCode, errorMessage, null);
        return JsonRpcErrorResponse.createErrorResponse(requestId, error);
    }
    
    /**
     * 创建错误响应（使用异常）
     */
    public JsonRpcErrorResponse createErrorResponse(String requestId, Exception exception) {
        JsonRpcError error = JsonRpcError.internalError(exception.getMessage());
        return JsonRpcErrorResponse.createErrorResponse(requestId, error);
    }
    
    /**
     * 验证消息格式
     */
    public boolean isValidMessage(String json) {
        try {
            JsonRpcMessage message = deserialize(json);
            return message.isValid();
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 检查是否为请求消息
     */
    public boolean isRequest(String json) {
        try {
            JsonNode node = objectMapper.readTree(json);
            return node.has("method") && node.has("id");
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 检查是否为响应消息
     */
    public boolean isResponse(String json) {
        try {
            JsonNode node = objectMapper.readTree(json);
            return node.has("id") && (node.has("result") || node.has("error"));
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 检查是否为通知消息
     */
    public boolean isNotification(String json) {
        try {
            JsonNode node = objectMapper.readTree(json);
            return node.has("method") && !node.has("id");
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 提取消息ID
     */
    public String extractMessageId(String json) {
        try {
            JsonNode node = objectMapper.readTree(json);
            if (node.has("id")) {
                return node.get("id").asText();
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 提取方法名
     */
    public String extractMethod(String json) {
        try {
            JsonNode node = objectMapper.readTree(json);
            if (node.has("method")) {
                return node.get("method").asText();
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 创建响应Map（Java 8兼容版本）
     */
    private Map<String, Object> createResponseMap(Object... keyValues) {
        Map<String, Object> map = new HashMap<>();
        for (int i = 0; i < keyValues.length; i += 2) {
            if (i + 1 < keyValues.length) {
                map.put((String) keyValues[i], keyValues[i + 1]);
            }
        }
        return map;
    }
}
