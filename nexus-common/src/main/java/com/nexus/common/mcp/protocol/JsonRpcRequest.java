package com.nexus.common.mcp.protocol;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * JSON-RPC 2.0 请求消息
 * 
 * 请求消息用于客户端向服务器发送方法调用请求
 * 必须包含：jsonrpc, id, method
 * 可选包含：params
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class JsonRpcRequest extends JsonRpcMessage {
    
    /**
     * 请求ID，用于匹配请求和响应
     * 可以是字符串、数字或null
     * 不能是小数、数组或对象
     */
    @JsonProperty("id")
    @NotNull(message = "Request ID cannot be null")
    private String id;
    
    /**
     * 要调用的方法名
     */
    @JsonProperty("method")
    @NotBlank(message = "Method name cannot be blank")
    private String method;
    
    /**
     * 方法参数
     * 可以是结构化值（对象或数组）或null
     */
    @JsonProperty("params")
    private Object params;
    
    @Override
    public MessageType getMessageType() {
        return MessageType.REQUEST;
    }
    
    @Override
    public boolean isValid() {
        return super.isValid() && 
               id != null && 
               method != null && 
               !method.trim().isEmpty();
    }
    
    /**
     * 创建标准MCP工具调用请求
     */
    public static JsonRpcRequest createToolCallRequest(String id, String toolName, Object arguments) {
        return JsonRpcRequest.builder()
                .id(id)
                .method("tools/call")
                .params(new ToolCallParams(toolName, arguments))
                .build();
    }
    
    /**
     * 创建标准MCP资源读取请求
     */
    public static JsonRpcRequest createResourceReadRequest(String id, String resourceUri) {
        return JsonRpcRequest.builder()
                .id(id)
                .method("resources/read")
                .params(new ResourceReadParams(resourceUri))
                .build();
    }
    
    /**
     * 创建标准MCP初始化请求
     */
    public static JsonRpcRequest createInitializeRequest(String id, Object initParams) {
        return JsonRpcRequest.builder()
                .id(id)
                .method("initialize")
                .params(initParams)
                .build();
    }

    /**
     * 创建通用请求
     */
    public static JsonRpcRequest createRequest(String method, Object params) {
        return JsonRpcRequest.builder()
                .id(String.valueOf(System.currentTimeMillis() % 1000000)) // 使用简单的数字ID
                .method(method)
                .params(params)
                .build();
    }
    
    /**
     * 工具调用参数
     */
    public static class ToolCallParams {
        @JsonProperty("name")
        private String name;
        
        @JsonProperty("arguments")
        private Object arguments;
        
        public ToolCallParams(String name, Object arguments) {
            this.name = name;
            this.arguments = arguments;
        }
        
        // Getters and setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public Object getArguments() { return arguments; }
        public void setArguments(Object arguments) { this.arguments = arguments; }
    }
    
    /**
     * 资源读取参数
     */
    public static class ResourceReadParams {
        @JsonProperty("uri")
        private String uri;
        
        public ResourceReadParams(String uri) {
            this.uri = uri;
        }
        
        // Getters and setters
        public String getUri() { return uri; }
        public void setUri(String uri) { this.uri = uri; }
    }
}
