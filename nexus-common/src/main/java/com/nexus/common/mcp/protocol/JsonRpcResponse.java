package com.nexus.common.mcp.protocol;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotNull;

/**
 * JSON-RPC 2.0 成功响应消息
 * 
 * 成功响应消息用于服务器向客户端返回方法调用的成功结果
 * 必须包含：jsonrpc, id, result
 * 不能包含：error
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class JsonRpcResponse extends JsonRpcMessage {

    /**
     * 默认构造函数（Jackson需要）
     */
    public JsonRpcResponse() {
        super();
    }

    /**
     * 响应ID，必须与对应请求的ID相同
     */
    @JsonProperty("id")
    @NotNull(message = "Response ID cannot be null")
    private String id;
    
    /**
     * 方法调用的成功结果
     * 当方法调用成功时，此字段必须存在
     * 当方法调用失败时，此字段不能存在
     */
    @JsonProperty("result")
    private Object result;

    /**
     * 方法调用的错误信息
     * 当方法调用失败时，此字段必须存在
     * 当方法调用成功时，此字段不能存在
     */
    @JsonProperty("error")
    private JsonRpcError error;
    
    @Override
    public MessageType getMessageType() {
        return MessageType.RESPONSE;
    }
    
    @Override
    public boolean isValid() {
        return super.isValid() &&
               id != null &&
               (result != null || error != null) &&
               !(result != null && error != null); // 不能同时有result和error
    }
    
    /**
     * 创建成功响应
     */
    public static JsonRpcResponse createSuccessResponse(String id, Object result) {
        return JsonRpcResponse.builder()
                .id(id)
                .result(result)
                .build();
    }
    
    /**
     * 创建工具调用成功响应
     */
    public static JsonRpcResponse createToolCallResponse(String id, Object toolResult) {
        return JsonRpcResponse.builder()
                .id(id)
                .result(toolResult)
                .build();
    }
    
    /**
     * 创建资源读取成功响应
     */
    public static JsonRpcResponse createResourceReadResponse(String id, Object resourceContent) {
        return JsonRpcResponse.builder()
                .id(id)
                .result(resourceContent)
                .build();
    }
    
    /**
     * 创建初始化成功响应
     */
    public static JsonRpcResponse createInitializeResponse(String id, Object initResult) {
        return JsonRpcResponse.builder()
                .id(id)
                .result(initResult)
                .build();
    }

    /**
     * 创建错误响应
     */
    public static JsonRpcResponse createErrorResponse(String id, JsonRpcError error) {
        return JsonRpcResponse.builder()
                .id(id)
                .error(error)
                .build();
    }
    
    /**
     * 检查响应是否表示成功
     */
    public boolean isSuccess() {
        return result != null;
    }
    
    /**
     * 获取结果的类型安全方法
     */
    @SuppressWarnings("unchecked")
    public <T> T getResultAs(Class<T> type) {
        if (result == null) {
            return null;
        }
        
        if (type.isInstance(result)) {
            return (T) result;
        }
        
        throw new ClassCastException("Result cannot be cast to " + type.getName());
    }
}
