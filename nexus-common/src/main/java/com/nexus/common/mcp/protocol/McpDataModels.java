package com.nexus.common.mcp.protocol;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;

/**
 * MCP协议数据模型
 * 
 * 定义MCP协议中使用的各种数据结构
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public class McpDataModels {
    
    /**
     * 初始化参数
     */
    @Data
    @Builder
    public static class InitializeParams {
        private String protocolVersion;
        private ClientCapabilities capabilities;
        private ClientInfo clientInfo;
    }
    
    /**
     * 初始化结果
     */
    @Data
    @Builder
    public static class InitializeResult {
        private String protocolVersion;
        private ServerCapabilities capabilities;
        private ServerInfo serverInfo;
    }
    
    /**
     * 初始化结果包装
     */
    @Data
    @Builder
    public static class InitializationResult {
        private boolean success;
        private String errorMessage;
        private ServerInfo serverInfo;
        private ServerCapabilities serverCapabilities;
        private String protocolVersion;
    }
    
    /**
     * 客户端能力
     */
    @Data
    @Builder
    public static class ClientCapabilities {
        private RootsCapability roots;
        private SamplingCapability sampling;
    }
    
    /**
     * 服务器能力
     */
    @Data
    @Builder
    public static class ServerCapabilities {
        private ToolsCapability tools;
        private ResourcesCapability resources;
        private PromptsCapability prompts;
        private LoggingCapability logging;
    }
    
    /**
     * 客户端信息
     */
    @Data
    @Builder
    public static class ClientInfo {
        private String name;
        private String version;
    }
    
    /**
     * 服务器信息
     */
    @Data
    @Builder
    public static class ServerInfo {
        private String name;
        private String version;
    }
    
    /**
     * Roots能力
     */
    @Data
    @Builder
    public static class RootsCapability {
        private boolean listChanged;
    }
    
    /**
     * 采样能力
     */
    @Data
    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class SamplingCapability {
        // 采样相关能力配置
        // 为了避免Jackson序列化空Bean的问题，添加一个可选字段
        @Builder.Default
        private Boolean enabled = null;
    }
    
    /**
     * 工具能力
     */
    @Data
    @Builder
    public static class ToolsCapability {
        private boolean listChanged;
    }
    
    /**
     * 资源能力
     */
    @Data
    @Builder
    public static class ResourcesCapability {
        private boolean subscribe;
        private boolean listChanged;
    }
    
    /**
     * 提示能力
     */
    @Data
    @Builder
    public static class PromptsCapability {
        private boolean listChanged;
    }
    
    /**
     * 日志能力
     */
    @Data
    @Builder
    public static class LoggingCapability {
        // 日志相关能力配置
    }
    
    /**
     * MCP连接接口
     */
    public interface McpConnection {
        JsonRpcMessage sendRequest(JsonRpcRequest request, long timeoutMs);
        void sendNotification(JsonRpcNotification notification);
        boolean isAlive();
        void close();
    }
    
    /**
     * MCP初始化异常
     */
    public static class McpInitializationException extends RuntimeException {
        public McpInitializationException(String message) {
            super(message);
        }

        public McpInitializationException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    /**
     * 工具定义
     */
    @Data
    @Builder
    public static class Tool {
        private String name;
        private String description;
        private java.util.Map<String, Object> inputSchema;
    }

    /**
     * 资源定义
     */
    @Data
    @Builder
    public static class Resource {
        private String uri;
        private String name;
        private String description;
        private String mimeType;
        private java.util.Map<String, Object> annotations;
    }

    /**
     * 初始化请求
     */
    @Data
    @Builder
    public static class InitializeRequest {
        private String protocolVersion;
        private ClientCapabilities capabilities;
        private ClientInfo clientInfo;
    }
}
