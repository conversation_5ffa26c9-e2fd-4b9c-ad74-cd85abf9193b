package com.nexus.common.mcp.protocol;

import com.nexus.common.mcp.protocol.McpDataModels.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * MCP协议初始化处理器
 * 
 * 负责标准MCP协议的初始化握手流程：
 * 1. 发送initialize请求
 * 2. 接收initialize响应
 * 3. 发送initialized通知
 * 4. 能力协商和版本检查
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class McpInitializationHandler {
    
    private final JsonRpcProtocolHandler protocolHandler;
    
    /**
     * 执行客户端初始化握手
     */
    public InitializationResult performClientInitialization(
            McpDataModels.McpConnection connection,
            ClientCapabilities clientCapabilities,
            ClientInfo clientInfo) {
        
        log.info("Starting MCP client initialization");
        
        try {
            // 1. 发送initialize请求
            InitializeParams initParams = InitializeParams.builder()
                    .protocolVersion("2024-11-05")
                    .capabilities(clientCapabilities)
                    .clientInfo(clientInfo)
                    .build();
            
            JsonRpcRequest initRequest = protocolHandler.createInitializeRequest(initParams);
            log.info("Sending initialize request: {}", initRequest);
            JsonRpcMessage response = connection.sendRequest(initRequest, 60000); // 增加超时到60秒
            
            if (response instanceof JsonRpcErrorResponse) {
                JsonRpcErrorResponse errorResponse = (JsonRpcErrorResponse) response;
                throw new McpInitializationException("Initialize request failed: " + errorResponse.getError().getMessage());
            }
            
            if (!(response instanceof JsonRpcResponse)) {
                throw new McpInitializationException("Unexpected response type: " + response.getClass().getSimpleName());
            }
            
            // 2. 解析initialize响应
            JsonRpcResponse successResponse = (JsonRpcResponse) response;
            InitializeResult initResult = parseInitializeResult(successResponse);
            
            // 3. 验证协议版本兼容性
            validateProtocolVersion(initResult.getProtocolVersion());
            
            // 4. 发送initialized通知
            JsonRpcNotification initializedNotification = JsonRpcNotification.createInitializedNotification();
            connection.sendNotification(initializedNotification);

            // 5. 等待服务器完成初始化处理
            try {
                Thread.sleep(5000); // 等待5秒让服务器完成初始化处理
                log.debug("Completed post-initialization wait period");
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("Interrupted while waiting for server post-initialization");
            }

            log.info("MCP client initialization completed successfully");
            
            return InitializationResult.builder()
                    .success(true)
                    .serverInfo(initResult.getServerInfo())
                    .serverCapabilities(initResult.getCapabilities())
                    .protocolVersion(initResult.getProtocolVersion())
                    .build();
            
        } catch (Exception e) {
            log.error("MCP client initialization failed", e);
            return InitializationResult.builder()
                    .success(false)
                    .errorMessage(e.getMessage())
                    .build();
        }
    }
    
    /**
     * 处理服务器端初始化请求
     */
    public JsonRpcMessage handleServerInitialization(
            JsonRpcRequest initRequest,
            ServerCapabilities serverCapabilities,
            ServerInfo serverInfo) {
        
        log.info("Handling MCP server initialization request");
        
        try {
            // 1. 解析客户端初始化参数
            InitializeParams initParams = parseInitializeParams(initRequest);
            
            // 2. 验证协议版本
            validateProtocolVersion(initParams.getProtocolVersion());
            
            // 3. 记录客户端信息和能力
            log.info("Client info: {}", initParams.getClientInfo());
            log.info("Client capabilities: {}", initParams.getCapabilities());
            
            // 4. 构建初始化结果
            InitializeResult initResult = InitializeResult.builder()
                    .protocolVersion("2024-11-05")
                    .capabilities(serverCapabilities)
                    .serverInfo(serverInfo)
                    .build();
            
            // 5. 返回成功响应
            return protocolHandler.createSuccessResponse(initRequest.getId(), initResult);
            
        } catch (Exception e) {
            log.error("Failed to handle server initialization", e);
            return protocolHandler.createErrorResponse(initRequest.getId(), e);
        }
    }
    
    /**
     * 解析初始化参数
     */
    private InitializeParams parseInitializeParams(JsonRpcRequest request) {
        if (request.getParams() == null) {
            throw new McpInitializationException("Initialize params cannot be null");
        }
        
        try {
            // 这里需要根据实际的JSON结构进行解析
            // 简化实现，实际应该使用ObjectMapper进行转换
            if (request.getParams() instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> paramsMap = (Map<String, Object>) request.getParams();
                
                return InitializeParams.builder()
                        .protocolVersion((String) paramsMap.get("protocolVersion"))
                        .capabilities(parseClientCapabilities(paramsMap.get("capabilities")))
                        .clientInfo(parseClientInfo(paramsMap.get("clientInfo")))
                        .build();
            }
            
            throw new McpInitializationException("Invalid initialize params format");
            
        } catch (Exception e) {
            throw new McpInitializationException("Failed to parse initialize params", e);
        }
    }
    
    /**
     * 解析初始化结果
     */
    private InitializeResult parseInitializeResult(JsonRpcResponse response) {
        if (response.getResult() == null) {
            throw new McpInitializationException("Initialize result cannot be null");
        }
        
        try {
            // 简化实现，实际应该使用ObjectMapper进行转换
            if (response.getResult() instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> resultMap = (Map<String, Object>) response.getResult();
                
                return InitializeResult.builder()
                        .protocolVersion((String) resultMap.get("protocolVersion"))
                        .capabilities(parseServerCapabilities(resultMap.get("capabilities")))
                        .serverInfo(parseServerInfo(resultMap.get("serverInfo")))
                        .build();
            }
            
            throw new McpInitializationException("Invalid initialize result format");
            
        } catch (Exception e) {
            throw new McpInitializationException("Failed to parse initialize result", e);
        }
    }
    
    /**
     * 解析客户端能力
     */
    private ClientCapabilities parseClientCapabilities(Object capabilitiesObj) {
        if (capabilitiesObj instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> capMap = (Map<String, Object>) capabilitiesObj;
            
            return ClientCapabilities.builder()
                    .roots(parseRootsCapability(capMap.get("roots")))
                    .sampling(parseSamplingCapability(capMap.get("sampling")))
                    .build();
        }
        
        return ClientCapabilities.builder().build();
    }
    
    /**
     * 解析服务器能力
     */
    private ServerCapabilities parseServerCapabilities(Object capabilitiesObj) {
        if (capabilitiesObj instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> capMap = (Map<String, Object>) capabilitiesObj;
            
            return ServerCapabilities.builder()
                    .tools(parseToolsCapability(capMap.get("tools")))
                    .resources(parseResourcesCapability(capMap.get("resources")))
                    .prompts(parsePromptsCapability(capMap.get("prompts")))
                    .logging(parseLoggingCapability(capMap.get("logging")))
                    .build();
        }
        
        return ServerCapabilities.builder().build();
    }
    
    /**
     * 解析客户端信息
     */
    private ClientInfo parseClientInfo(Object clientInfoObj) {
        if (clientInfoObj instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> infoMap = (Map<String, Object>) clientInfoObj;
            
            return ClientInfo.builder()
                    .name((String) infoMap.get("name"))
                    .version((String) infoMap.get("version"))
                    .build();
        }
        
        return ClientInfo.builder()
                .name("Unknown Client")
                .version("1.0.0")
                .build();
    }
    
    /**
     * 解析服务器信息
     */
    private ServerInfo parseServerInfo(Object serverInfoObj) {
        if (serverInfoObj instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> infoMap = (Map<String, Object>) serverInfoObj;
            
            return ServerInfo.builder()
                    .name((String) infoMap.get("name"))
                    .version((String) infoMap.get("version"))
                    .build();
        }
        
        return ServerInfo.builder()
                .name("Unknown Server")
                .version("1.0.0")
                .build();
    }
    
    /**
     * 验证协议版本兼容性
     */
    private void validateProtocolVersion(String protocolVersion) {
        if (protocolVersion == null || protocolVersion.trim().isEmpty()) {
            throw new McpInitializationException("Protocol version cannot be null or empty");
        }
        
        // 目前支持的协议版本
        if (!"2024-11-05".equals(protocolVersion)) {
            log.warn("Unsupported protocol version: {}, using default", protocolVersion);
        }
    }
    
    // 占位符方法，用于解析各种能力
    private RootsCapability parseRootsCapability(Object obj) {
        return RootsCapability.builder().listChanged(true).build();
    }
    
    private SamplingCapability parseSamplingCapability(Object obj) {
        return SamplingCapability.builder().build();
    }
    
    private ToolsCapability parseToolsCapability(Object obj) {
        return ToolsCapability.builder().listChanged(true).build();
    }
    
    private ResourcesCapability parseResourcesCapability(Object obj) {
        return ResourcesCapability.builder().subscribe(false).listChanged(true).build();
    }
    
    private PromptsCapability parsePromptsCapability(Object obj) {
        return PromptsCapability.builder().listChanged(true).build();
    }
    
    private LoggingCapability parseLoggingCapability(Object obj) {
        return LoggingCapability.builder().build();
    }
}
