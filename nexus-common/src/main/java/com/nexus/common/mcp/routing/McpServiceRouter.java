package com.nexus.common.mcp.routing;

import com.nexus.common.mcp.discovery.McpServiceDiscovery;
import com.nexus.common.mcp.discovery.McpServiceInstance;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * MCP服务路由器
 * 
 * 负责智能路由和负载均衡
 * 支持多种路由策略：轮询、随机、权重、最少连接等
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class McpServiceRouter {
    
    private final McpServiceDiscovery serviceDiscovery;
    
    // 轮询计数器
    private final Map<String, AtomicInteger> roundRobinCounters = new HashMap<>();
    
    /**
     * 为工具选择最佳服务
     */
    public Optional<McpServiceInstance> selectServiceForTool(String toolName, RoutingStrategy strategy) {
        List<McpServiceInstance> candidates = serviceDiscovery.findServicesByTool(toolName);
        
        if (candidates.isEmpty()) {
            log.debug("没有找到提供工具的服务: {}", toolName);
            return Optional.empty();
        }
        
        // 过滤健康的服务
        List<McpServiceInstance> healthyCandidates = candidates.stream()
                .filter(McpServiceInstance::isAvailable)
                .toList();
        
        if (healthyCandidates.isEmpty()) {
            log.warn("没有健康的服务提供工具: {}", toolName);
            return Optional.empty();
        }
        
        // 根据策略选择服务
        McpServiceInstance selected = selectService(healthyCandidates, strategy, toolName);
        
        log.debug("为工具 {} 选择服务: {} (策略: {})", toolName, selected.getServiceId(), strategy);
        return Optional.of(selected);
    }
    
    /**
     * 为资源选择最佳服务
     */
    public Optional<McpServiceInstance> selectServiceForResource(String resourcePattern, RoutingStrategy strategy) {
        List<McpServiceInstance> candidates = serviceDiscovery.findServicesByResource(resourcePattern);
        
        if (candidates.isEmpty()) {
            log.debug("没有找到提供资源的服务: {}", resourcePattern);
            return Optional.empty();
        }
        
        // 过滤健康的服务
        List<McpServiceInstance> healthyCandidates = candidates.stream()
                .filter(McpServiceInstance::isAvailable)
                .toList();
        
        if (healthyCandidates.isEmpty()) {
            log.warn("没有健康的服务提供资源: {}", resourcePattern);
            return Optional.empty();
        }
        
        // 根据策略选择服务
        McpServiceInstance selected = selectService(healthyCandidates, strategy, resourcePattern);
        
        log.debug("为资源 {} 选择服务: {} (策略: {})", resourcePattern, selected.getServiceId(), strategy);
        return Optional.of(selected);
    }
    
    /**
     * 根据策略选择服务
     */
    private McpServiceInstance selectService(List<McpServiceInstance> candidates, RoutingStrategy strategy, String key) {
        switch (strategy) {
            case ROUND_ROBIN:
                return selectByRoundRobin(candidates, key);
            case RANDOM:
                return selectByRandom(candidates);
            case WEIGHTED:
                return selectByWeight(candidates);
            case LEAST_CONNECTIONS:
                return selectByLeastConnections(candidates);
            case FASTEST_RESPONSE:
                return selectByFastestResponse(candidates);
            case HIGHEST_AVAILABILITY:
                return selectByHighestAvailability(candidates);
            default:
                return selectByRoundRobin(candidates, key);
        }
    }
    
    /**
     * 轮询选择
     */
    private McpServiceInstance selectByRoundRobin(List<McpServiceInstance> candidates, String key) {
        AtomicInteger counter = roundRobinCounters.computeIfAbsent(key, k -> new AtomicInteger(0));
        int index = counter.getAndIncrement() % candidates.size();
        return candidates.get(index);
    }
    
    /**
     * 随机选择
     */
    private McpServiceInstance selectByRandom(List<McpServiceInstance> candidates) {
        int index = ThreadLocalRandom.current().nextInt(candidates.size());
        return candidates.get(index);
    }
    
    /**
     * 权重选择
     */
    private McpServiceInstance selectByWeight(List<McpServiceInstance> candidates) {
        int totalWeight = candidates.stream()
                .mapToInt(McpServiceInstance::getWeight)
                .sum();
        
        if (totalWeight <= 0) {
            return selectByRandom(candidates);
        }
        
        int randomWeight = ThreadLocalRandom.current().nextInt(totalWeight);
        int currentWeight = 0;
        
        for (McpServiceInstance candidate : candidates) {
            currentWeight += candidate.getWeight();
            if (randomWeight < currentWeight) {
                return candidate;
            }
        }
        
        // 回退到最后一个
        return candidates.get(candidates.size() - 1);
    }
    
    /**
     * 最少连接选择
     */
    private McpServiceInstance selectByLeastConnections(List<McpServiceInstance> candidates) {
        // 简化实现：选择健康检查失败次数最少的服务
        return candidates.stream()
                .min(Comparator.comparingInt(McpServiceInstance::getHealthCheckFailureCount))
                .orElse(candidates.get(0));
    }
    
    /**
     * 最快响应选择
     */
    private McpServiceInstance selectByFastestResponse(List<McpServiceInstance> candidates) {
        // 简化实现：选择运行时间最长的服务（假设更稳定）
        return candidates.stream()
                .max(Comparator.comparingLong(McpServiceInstance::getUptime))
                .orElse(candidates.get(0));
    }
    
    /**
     * 最高可用性选择
     */
    private McpServiceInstance selectByHighestAvailability(List<McpServiceInstance> candidates) {
        // 选择健康检查失败次数最少且权重最高的服务
        return candidates.stream()
                .min(Comparator
                        .comparingInt(McpServiceInstance::getHealthCheckFailureCount)
                        .thenComparing(instance -> -instance.getWeight()))
                .orElse(candidates.get(0));
    }
    
    /**
     * 获取路由统计信息
     */
    public RoutingStats getStats() {
        Map<String, McpServiceInstance> allServices = serviceDiscovery.getAllServices();
        
        int totalServices = allServices.size();
        int availableServices = (int) allServices.values().stream()
                .mapToLong(instance -> instance.isAvailable() ? 1 : 0)
                .sum();
        
        int totalRoutes = roundRobinCounters.size();
        
        return new RoutingStats(totalServices, availableServices, totalRoutes);
    }
    
    /**
     * 清理路由计数器
     */
    public void cleanupCounters() {
        // 清理不再使用的计数器
        Set<String> activeTools = new HashSet<>();
        Set<String> activeResources = new HashSet<>();
        
        serviceDiscovery.getAllServices().values().forEach(instance -> {
            if (instance.getAvailableTools() != null) {
                activeTools.addAll(instance.getAvailableTools());
            }
            if (instance.getAvailableResources() != null) {
                activeResources.addAll(instance.getAvailableResources());
            }
        });
        
        Set<String> activeKeys = new HashSet<>();
        activeKeys.addAll(activeTools);
        activeKeys.addAll(activeResources);
        
        roundRobinCounters.keySet().retainAll(activeKeys);
        
        log.debug("清理路由计数器，保留 {} 个活跃路由", activeKeys.size());
    }
    
    /**
     * 路由统计信息
     */
    public static class RoutingStats {
        private final int totalServices;
        private final int availableServices;
        private final int totalRoutes;
        
        public RoutingStats(int totalServices, int availableServices, int totalRoutes) {
            this.totalServices = totalServices;
            this.availableServices = availableServices;
            this.totalRoutes = totalRoutes;
        }
        
        // Getters
        public int getTotalServices() { return totalServices; }
        public int getAvailableServices() { return availableServices; }
        public int getTotalRoutes() { return totalRoutes; }
        public int getUnavailableServices() { return totalServices - availableServices; }
    }
}
