package com.nexus.common.mcp.routing;

/**
 * 路由策略枚举
 * 
 * 定义MCP服务路由的各种策略
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public enum RoutingStrategy {
    
    /**
     * 轮询策略
     * 按顺序依次选择服务
     */
    ROUND_ROBIN("round_robin", "轮询"),
    
    /**
     * 随机策略
     * 随机选择服务
     */
    RANDOM("random", "随机"),
    
    /**
     * 权重策略
     * 根据服务权重选择
     */
    WEIGHTED("weighted", "权重"),
    
    /**
     * 最少连接策略
     * 选择连接数最少的服务
     */
    LEAST_CONNECTIONS("least_connections", "最少连接"),
    
    /**
     * 最快响应策略
     * 选择响应时间最快的服务
     */
    FASTEST_RESPONSE("fastest_response", "最快响应"),
    
    /**
     * 最高可用性策略
     * 选择可用性最高的服务
     */
    HIGHEST_AVAILABILITY("highest_availability", "最高可用性");
    
    private final String code;
    private final String description;
    
    RoutingStrategy(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    /**
     * 获取策略代码
     */
    public String getCode() {
        return code;
    }
    
    /**
     * 获取策略描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码查找策略
     */
    public static RoutingStrategy fromCode(String code) {
        for (RoutingStrategy strategy : values()) {
            if (strategy.code.equals(code)) {
                return strategy;
            }
        }
        return ROUND_ROBIN; // 默认返回轮询策略
    }
    
    /**
     * 检查是否为负载均衡策略
     */
    public boolean isLoadBalancing() {
        return this == ROUND_ROBIN || 
               this == RANDOM || 
               this == WEIGHTED || 
               this == LEAST_CONNECTIONS;
    }
    
    /**
     * 检查是否为性能优化策略
     */
    public boolean isPerformanceOptimized() {
        return this == FASTEST_RESPONSE || 
               this == HIGHEST_AVAILABILITY;
    }
}
