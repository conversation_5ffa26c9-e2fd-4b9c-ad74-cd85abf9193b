package com.nexus.common.mcp.transport;

/**
 * MCP传输层异常
 * 
 * 用于表示MCP传输层相关的错误
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public class McpTransportException extends RuntimeException {
    
    public McpTransportException(String message) {
        super(message);
    }
    
    public McpTransportException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public McpTransportException(Throwable cause) {
        super(cause);
    }
}
