package com.nexus.common.mcp.transport;

import com.nexus.common.mcp.protocol.*;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * STDIO连接实现
 * 
 * 管理与MCP服务进程的STDIO通信
 * 支持异步消息发送/接收、请求-响应匹配、连接生命周期管理
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
public class StdioConnection implements AutoCloseable {
    
    private final Process process;
    private final JsonRpcProtocolHandler protocolHandler;
    private final ExecutorService executorService;
    
    private final BufferedReader reader;
    private final PrintWriter writer;
    private final BufferedReader errorReader;
    
    private final ConcurrentHashMap<String, CompletableFuture<JsonRpcMessage>> pendingRequests;
    private final BlockingQueue<JsonRpcMessage> incomingMessages;
    private final AtomicBoolean running;
    private final AtomicBoolean started;
    
    private final AtomicLong messagesSent = new AtomicLong(0);
    private final AtomicLong messagesReceived = new AtomicLong(0);
    
    private Future<?> readerTask;
    private Future<?> errorReaderTask;
    
    public StdioConnection(Process process, JsonRpcProtocolHandler protocolHandler, ExecutorService executorService) {
        this.process = process;
        this.protocolHandler = protocolHandler;
        this.executorService = executorService;
        
        this.reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        this.writer = new PrintWriter(process.getOutputStream(), true);
        this.errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        
        this.pendingRequests = new ConcurrentHashMap<>();
        this.incomingMessages = new LinkedBlockingQueue<>();
        this.running = new AtomicBoolean(false);
        this.started = new AtomicBoolean(false);
    }
    
    /**
     * 启动连接
     */
    public void start() {
        if (started.getAndSet(true)) {
            throw new IllegalStateException("Connection already started");
        }
        
        if (!process.isAlive()) {
            throw new IllegalStateException("Process is not alive");
        }
        
        running.set(true);
        
        // 启动消息读取线程
        readerTask = executorService.submit(this::readMessages);
        
        // 启动错误流读取线程
        errorReaderTask = executorService.submit(this::readErrorStream);
        
        // Java 8兼容：使用hashCode代替pid()
        log.info("STDIO connection started for process: {}", process.hashCode());
    }
    
    /**
     * 发送消息
     */
    public void sendMessage(JsonRpcMessage message) {
        if (!running.get()) {
            throw new IllegalStateException("Connection is not running");
        }
        
        try {
            String json = protocolHandler.serialize(message);
            
            synchronized (writer) {
                writer.println(json);
                writer.flush();
            }
            
            messagesSent.incrementAndGet();
            log.info("Sent message: {}", json);
            
        } catch (Exception e) {
            log.error("Failed to send message: {}", message, e);
            throw new McpTransportException("Failed to send message", e);
        }
    }
    
    /**
     * 发送请求并等待响应
     */
    public JsonRpcMessage sendRequest(JsonRpcRequest request, long timeoutMs) {
        if (request.getId() == null) {
            throw new IllegalArgumentException("Request ID cannot be null");
        }
        
        CompletableFuture<JsonRpcMessage> future = new CompletableFuture<>();
        pendingRequests.put(request.getId(), future);
        
        try {
            // 发送请求
            sendMessage(request);
            
            // 等待响应
            return future.get(timeoutMs, TimeUnit.MILLISECONDS);
            
        } catch (TimeoutException e) {
            pendingRequests.remove(request.getId());
            throw new McpTransportException("Request timeout after " + timeoutMs + "ms", e);
        } catch (Exception e) {
            pendingRequests.remove(request.getId());
            throw new McpTransportException("Failed to send request", e);
        }
    }
    
    /**
     * 发送请求并等待响应（默认30秒超时）
     */
    public JsonRpcMessage sendRequest(JsonRpcRequest request) {
        return sendRequest(request, 30000);
    }

    /**
     * 发送通知（无需响应）
     */
    public void sendNotification(JsonRpcNotification notification) {
        sendMessage(notification);
    }
    
    /**
     * 接收消息（阻塞）
     */
    public JsonRpcMessage receiveMessage() throws InterruptedException {
        return incomingMessages.take();
    }
    
    /**
     * 接收消息（带超时）
     */
    public JsonRpcMessage receiveMessage(long timeoutMs) throws InterruptedException {
        return incomingMessages.poll(timeoutMs, TimeUnit.MILLISECONDS);
    }
    
    /**
     * 检查连接是否活跃
     */
    public boolean isAlive() {
        return running.get() && process.isAlive();
    }
    
    /**
     * 获取进程ID
     */
    public long getProcessId() {
        // Java 8兼容：使用hashCode代替pid()
        return Math.abs(process.hashCode()) % 100000;
    }
    
    /**
     * 获取统计信息
     */
    public ConnectionStats getStats() {
        return new ConnectionStats(
            messagesSent.get(),
            messagesReceived.get(),
            pendingRequests.size(),
            incomingMessages.size(),
            isAlive()
        );
    }
    
    /**
     * 读取消息的后台线程
     */
    private void readMessages() {
        log.info("Started message reader thread for process: {}", process.hashCode());
        
        try {
            String line;
            while (running.get() && (line = reader.readLine()) != null) {
                try {
                    processIncomingMessage(line);
                } catch (Exception e) {
                    log.error("Error processing incoming message: {}", line, e);
                }
            }
        } catch (IOException e) {
            if (running.get()) {
                log.error("Error reading from MCP process", e);
            }
        } finally {
            log.info("Message reader thread stopped for process: {}", process.hashCode());
        }
    }
    
    /**
     * 读取错误流的后台线程
     */
    private void readErrorStream() {
        log.info("Started error reader thread for process: {}", process.hashCode());
        
        try {
            String line;
            while (running.get() && (line = errorReader.readLine()) != null) {
                log.warn("MCP process error: {}", line);
            }
        } catch (IOException e) {
            if (running.get()) {
                log.error("Error reading error stream from MCP process", e);
            }
        } finally {
            log.info("Error reader thread stopped for process: {}", process.hashCode());
        }
    }
    
    /**
     * 处理接收到的消息
     */
    private void processIncomingMessage(String json) {
        try {
            JsonRpcMessage message = protocolHandler.deserialize(json);
            messagesReceived.incrementAndGet();
            
            log.info("Received message: {}", json);
            
            if (message instanceof JsonRpcResponse || message instanceof JsonRpcErrorResponse) {
                // 这是响应消息，匹配对应的请求
                handleResponse(message, json);
            } else {
                // 这是请求或通知，放入接收队列
                incomingMessages.offer(message);
            }
            
        } catch (Exception e) {
            log.error("Failed to process incoming message: {}", json, e);
        }
    }
    
    /**
     * 处理响应消息
     */
    private void handleResponse(JsonRpcMessage message, String json) {
        String requestId = null;
        
        if (message instanceof JsonRpcResponse) {
            requestId = ((JsonRpcResponse) message).getId();
        } else if (message instanceof JsonRpcErrorResponse) {
            requestId = ((JsonRpcErrorResponse) message).getId();
        }
        
        if (requestId != null) {
            CompletableFuture<JsonRpcMessage> future = pendingRequests.remove(requestId);
            if (future != null) {
                future.complete(message);
            } else {
                log.warn("Received response for unknown request ID: {}", requestId);
            }
        } else {
            log.warn("Received response without request ID: {}", json);
        }
    }
    
    @Override
    public void close() {
        if (!running.getAndSet(false)) {
            return; // 已经关闭
        }
        
        log.info("Closing STDIO connection for process: {}", process.hashCode());
        
        // 取消所有待处理的请求
        pendingRequests.values().forEach(future -> 
            future.completeExceptionally(new McpTransportException("Connection closed")));
        pendingRequests.clear();
        
        // 停止读取线程
        if (readerTask != null) {
            readerTask.cancel(true);
        }
        if (errorReaderTask != null) {
            errorReaderTask.cancel(true);
        }
        
        // 关闭流
        try {
            writer.close();
            reader.close();
            errorReader.close();
        } catch (IOException e) {
            log.warn("Error closing streams", e);
        }
        
        // 终止进程
        if (process.isAlive()) {
            process.destroy();
            
            try {
                if (!process.waitFor(5, TimeUnit.SECONDS)) {
                    process.destroyForcibly();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                process.destroyForcibly();
            }
        }
        
        log.info("STDIO connection closed for process: {}", process.hashCode());
    }
    
    /**
     * 连接统计信息
     */
    public static class ConnectionStats {
        private final long messagesSent;
        private final long messagesReceived;
        private final int pendingRequests;
        private final int queuedMessages;
        private final boolean alive;
        
        public ConnectionStats(long messagesSent, long messagesReceived, int pendingRequests, int queuedMessages, boolean alive) {
            this.messagesSent = messagesSent;
            this.messagesReceived = messagesReceived;
            this.pendingRequests = pendingRequests;
            this.queuedMessages = queuedMessages;
            this.alive = alive;
        }
        
        // Getters
        public long getMessagesSent() { return messagesSent; }
        public long getMessagesReceived() { return messagesReceived; }
        public int getPendingRequests() { return pendingRequests; }
        public int getQueuedMessages() { return queuedMessages; }
        public boolean isAlive() { return alive; }
    }
}
