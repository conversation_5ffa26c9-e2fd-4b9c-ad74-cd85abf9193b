package com.nexus.common.mcp.transport;

import com.nexus.common.mcp.protocol.JsonRpcMessage;
import com.nexus.common.mcp.protocol.JsonRpcProtocolHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * STDIO传输层实现
 * 
 * 实现标准MCP协议的STDIO传输机制
 * 支持进程间通信、消息序列化/反序列化、连接管理
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StdioTransport {
    
    private final JsonRpcProtocolHandler protocolHandler;
    private final ExecutorService executorService = Executors.newCachedThreadPool(r -> {
        Thread thread = new Thread(r, "stdio-transport-" + System.currentTimeMillis());
        thread.setDaemon(true);
        return thread;
    });
    
    /**
     * 连接到MCP服务进程
     */
    public StdioConnection connect(Process mcpProcess) {
        if (mcpProcess == null) {
            throw new IllegalArgumentException("MCP process cannot be null");
        }
        
        if (!mcpProcess.isAlive()) {
            throw new IllegalStateException("MCP process is not alive");
        }
        
        // Java 8兼容：使用hashCode代替pid()
        log.info("Establishing STDIO connection to MCP process: {}", mcpProcess.hashCode());
        
        try {
            StdioConnection connection = new StdioConnection(
                mcpProcess, 
                protocolHandler, 
                executorService
            );
            
            // 启动连接
            connection.start();
            
            log.info("STDIO connection established successfully");
            return connection;
            
        } catch (Exception e) {
            log.error("Failed to establish STDIO connection", e);
            throw new McpTransportException("Failed to connect to MCP process", e);
        }
    }
    
    /**
     * 连接到MCP服务进程（带超时）
     */
    public StdioConnection connect(Process mcpProcess, long timeoutMs) {
        CompletableFuture<StdioConnection> future = CompletableFuture.supplyAsync(() -> connect(mcpProcess));
        
        try {
            return future.get(timeoutMs, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            future.cancel(true);
            throw new McpTransportException("Connection timeout after " + timeoutMs + "ms", e);
        }
    }
    
    /**
     * 创建到外部进程的STDIO连接
     */
    public StdioConnection connectToExternalProcess(String command, String[] args) {
        try {
            ProcessBuilder processBuilder = new ProcessBuilder();
            processBuilder.command(command);
            if (args != null) {
                for (String arg : args) {
                    processBuilder.command().add(arg);
                }
            }
            
            // 重定向错误流到标准输出
            processBuilder.redirectErrorStream(false);
            
            // 启动进程
            Process process = processBuilder.start();
            
            // 等待进程启动
            Thread.sleep(1000);
            
            if (!process.isAlive()) {
                throw new McpTransportException("External process failed to start");
            }
            
            return connect(process);
            
        } catch (Exception e) {
            log.error("Failed to start external MCP process: {}", command, e);
            throw new McpTransportException("Failed to start external MCP process", e);
        }
    }
    
    /**
     * 验证进程是否支持MCP协议
     */
    public boolean isMcpCompatible(Process process) {
        try (StdioConnection connection = connect(process, 5000)) {
            // 尝试发送ping消息
            JsonRpcMessage pingRequest = protocolHandler.createInitializeRequest(null);
            connection.sendMessage(pingRequest);
            
            // 等待响应
            JsonRpcMessage response = connection.receiveMessage(3000);
            return response != null;
            
        } catch (Exception e) {
            log.debug("Process is not MCP compatible: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 关闭传输层
     */
    public void shutdown() {
        log.info("Shutting down STDIO transport");
        
        try {
            executorService.shutdown();
            if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            executorService.shutdownNow();
        }
        
        log.info("STDIO transport shutdown completed");
    }
    
    /**
     * 获取传输层统计信息
     */
    public TransportStats getStats() {
        return TransportStats.builder()
                .activeConnections(getActiveConnectionCount())
                .totalMessagesSent(getTotalMessagesSent())
                .totalMessagesReceived(getTotalMessagesReceived())
                .build();
    }
    
    private int getActiveConnectionCount() {
        // 实现活跃连接计数逻辑
        return 0; // 占位符
    }
    
    private long getTotalMessagesSent() {
        // 实现发送消息计数逻辑
        return 0; // 占位符
    }
    
    private long getTotalMessagesReceived() {
        // 实现接收消息计数逻辑
        return 0; // 占位符
    }
    
    /**
     * 传输层统计信息
     */
    public static class TransportStats {
        private final int activeConnections;
        private final long totalMessagesSent;
        private final long totalMessagesReceived;
        
        private TransportStats(int activeConnections, long totalMessagesSent, long totalMessagesReceived) {
            this.activeConnections = activeConnections;
            this.totalMessagesSent = totalMessagesSent;
            this.totalMessagesReceived = totalMessagesReceived;
        }
        
        public static TransportStatsBuilder builder() {
            return new TransportStatsBuilder();
        }
        
        // Getters
        public int getActiveConnections() { return activeConnections; }
        public long getTotalMessagesSent() { return totalMessagesSent; }
        public long getTotalMessagesReceived() { return totalMessagesReceived; }
        
        public static class TransportStatsBuilder {
            private int activeConnections;
            private long totalMessagesSent;
            private long totalMessagesReceived;
            
            public TransportStatsBuilder activeConnections(int activeConnections) {
                this.activeConnections = activeConnections;
                return this;
            }
            
            public TransportStatsBuilder totalMessagesSent(long totalMessagesSent) {
                this.totalMessagesSent = totalMessagesSent;
                return this;
            }
            
            public TransportStatsBuilder totalMessagesReceived(long totalMessagesReceived) {
                this.totalMessagesReceived = totalMessagesReceived;
                return this;
            }
            
            public TransportStats build() {
                return new TransportStats(activeConnections, totalMessagesSent, totalMessagesReceived);
            }
        }
    }
}
