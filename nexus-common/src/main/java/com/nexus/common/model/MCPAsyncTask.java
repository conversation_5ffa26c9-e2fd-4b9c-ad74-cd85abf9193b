package com.nexus.common.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * MCP异步任务模型
 * 用于RabbitMQ消息队列中的任务传递
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MCPAsyncTask {

    /**
     * 任务唯一标识符
     */
    private String taskId;

    /**
     * 关联ID，用于结果回调
     */
    private String correlationId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 服务ID
     */
    private String serviceId;

    /**
     * MCP方法名
     */
    private String method;

    /**
     * 工具名称（当method为tools/call时）
     */
    private String toolName;

    /**
     * 任务参数
     */
    private Map<String, Object> parameters;

    /**
     * 任务优先级（1-10，数字越大优先级越高）
     */
    @Builder.Default
    private int priority = 5;

    /**
     * 最大重试次数
     */
    @Builder.Default
    private int maxRetries = 3;

    /**
     * 当前重试次数
     */
    @Builder.Default
    private int currentRetries = 0;

    /**
     * 超时时间（秒）
     */
    @Builder.Default
    private int timeoutSeconds = 30;

    /**
     * 任务状态
     */
    @Builder.Default
    private TaskStatus status = TaskStatus.PENDING;

    /**
     * 任务创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 任务开始执行时间
     */
    private LocalDateTime startedAt;

    /**
     * 任务完成时间
     */
    private LocalDateTime completedAt;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 任务结果
     */
    private Map<String, Object> result;

    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        PENDING("待处理"),
        RUNNING("执行中"),
        COMPLETED("已完成"),
        FAILED("失败"),
        CANCELLED("已取消"),
        TIMEOUT("超时");

        private final String description;

        TaskStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 标记任务开始执行
     */
    public void markAsStarted() {
        this.status = TaskStatus.RUNNING;
        this.startedAt = LocalDateTime.now();
    }

    /**
     * 标记任务完成
     */
    public void markAsCompleted(Map<String, Object> taskResult) {
        this.status = TaskStatus.COMPLETED;
        this.completedAt = LocalDateTime.now();
        this.result = taskResult;
    }

    /**
     * 标记任务失败
     */
    public void markAsFailed(String error) {
        this.status = TaskStatus.FAILED;
        this.completedAt = LocalDateTime.now();
        this.errorMessage = error;
    }

    /**
     * 标记任务超时
     */
    public void markAsTimeout() {
        this.status = TaskStatus.TIMEOUT;
        this.completedAt = LocalDateTime.now();
        this.errorMessage = "任务执行超时";
    }

    /**
     * 增加重试次数
     */
    public void incrementRetries() {
        this.currentRetries++;
    }

    /**
     * 检查是否可以重试
     */
    public boolean canRetry() {
        return currentRetries < maxRetries;
    }

    /**
     * 检查任务是否已完成（成功或失败）
     */
    public boolean isCompleted() {
        return status == TaskStatus.COMPLETED || 
               status == TaskStatus.FAILED || 
               status == TaskStatus.CANCELLED || 
               status == TaskStatus.TIMEOUT;
    }

    /**
     * 检查任务是否成功
     */
    public boolean isSuccessful() {
        return status == TaskStatus.COMPLETED;
    }

    /**
     * 获取任务执行时长（毫秒）
     */
    public Long getExecutionTimeMs() {
        if (startedAt == null) {
            return null;
        }
        LocalDateTime endTime = completedAt != null ? completedAt : LocalDateTime.now();
        return java.time.Duration.between(startedAt, endTime).toMillis();
    }

    /**
     * 获取任务等待时长（毫秒）
     */
    public Long getWaitTimeMs() {
        if (createdAt == null || startedAt == null) {
            return null;
        }
        return java.time.Duration.between(createdAt, startedAt).toMillis();
    }

    /**
     * 重置任务状态以便重试
     */
    public void resetForRetry() {
        this.status = TaskStatus.PENDING;
        this.startedAt = null;
        this.completedAt = null;
        this.errorMessage = null;
        this.result = null;
    }
}
