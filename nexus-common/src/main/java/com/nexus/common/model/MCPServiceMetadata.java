package com.nexus.common.model;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;

import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MCP服务元数据
 * 描述一个MCP服务的基本信息和运行状态
 */
@Data
public class MCPServiceMetadata {
    
    public MCPServiceMetadata() {
        this.metadata = new HashMap<>();
        this.registrationTime = Instant.now();
    }

    private String serviceId;
    private String serviceName;
    private String serviceType;
    private String version;
    private String description;
    private boolean localExecution;
    private String agentId;
    private Map<String, String> metadata;
    private JsonNode schema;
    private Instant registrationTime;
    
    // 新增字段用于优化显示
    private String displayServiceType;      // 显示类型，如 "stdio/远端集成"
    private String deploymentType;          // 部署类型：REMOTE 或 LOCAL
    private String protocolType;            // 协议类型：line-delimited 或 binary-length-prefix
    private String realServiceName;         // 从MCP通信获取的真实服务名称
    private String serverInfo;              // 服务器信息JSON字符串
    private List<Map<String, Object>> tools; // 工具列表
    private int toolCount;                  // 工具数量
    private String connectionStatus;        // 连接状态
    private String statusMessage;           // 状态消息
    private long responseTimeMs;            // 响应时间
    private String endpoint;                // 服务端点
    private String classificationReason;    // 分类原因

    /**
     * 获取格式化的注册时间
     */
    public String getFormattedRegistrationTime() {
        if (registrationTime == null) {
            return "未知";
        }
        return DateTimeFormatter.ISO_INSTANT.format(registrationTime);
    }

    /**
     * 获取服务显示名称
     */
    public String getDisplayName() {
        if (realServiceName != null && !realServiceName.isEmpty()) {
            return realServiceName;
        }
        return serviceName != null ? serviceName : serviceId;
    }

    /**
     * 检查服务是否为本地服务
     */
    public boolean isLocalService() {
        return "LOCAL".equals(deploymentType) || localExecution;
    }

    /**
     * 检查服务是否为远程服务
     */
    public boolean isRemoteService() {
        return "REMOTE".equals(deploymentType) || !localExecution;
    }

    /**
     * 检查服务是否已连接
     */
    public boolean isConnected() {
        return "CONNECTED".equals(connectionStatus);
    }

    /**
     * 获取服务状态描述
     */
    public String getStatusDescription() {
        if (statusMessage != null && !statusMessage.isEmpty()) {
            return statusMessage;
        }
        return connectionStatus != null ? connectionStatus : "未知";
    }

    /**
     * 设置从MCP通信获取的信息
     */
    public void setMCPInfo(String realName, String serverInfoJson, List<Map<String, Object>> toolList, String protocol) {
        this.realServiceName = realName;
        this.serverInfo = serverInfoJson;
        this.tools = toolList;
        this.toolCount = toolList != null ? toolList.size() : 0;
        this.protocolType = protocol;
        
        // 如果没有设置服务名称，使用真实名称
        if ((this.serviceName == null || this.serviceName.equals("Stdio MCP Service")) && realName != null) {
            this.serviceName = realName;
        }
    }
    
    /**
     * 设置分类信息
     */
    public void setClassificationInfo(String deployType, String displayType, String reason) {
        this.deploymentType = deployType;
        this.displayServiceType = displayType;
        this.classificationReason = reason;
    }
    
    /**
     * 设置连接信息
     */
    public void setConnectionInfo(String status, String message, long responseTime) {
        this.connectionStatus = status;
        this.statusMessage = message;
        this.responseTimeMs = responseTime;
    }

    /**
     * 添加元数据
     */
    public void addMetadata(String key, String value) {
        if (this.metadata == null) {
            this.metadata = new HashMap<>();
        }
        this.metadata.put(key, value);
    }

    /**
     * 获取元数据
     */
    public String getMetadata(String key) {
        if (this.metadata == null) {
            return null;
        }
        return this.metadata.get(key);
    }

    /**
     * 服务分类结果
     */
    public static class ClassificationResult {
        private final String deploymentType;
        private final String reason;

        public ClassificationResult(String deploymentType, String reason) {
            this.deploymentType = deploymentType;
            this.reason = reason;
        }

        public String getDeploymentType() {
            return deploymentType;
        }

        public String getReason() {
            return reason;
        }
    }

    /**
     * 服务类型枚举
     */
    public enum ServiceType {
        STDIO("标准输入输出"),
        HTTP("HTTP协议"),
        WEBSOCKET("WebSocket协议"),
        GRPC("gRPC协议");

        private final String description;

        ServiceType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 连接状态枚举
     */
    public enum ConnectionStatus {
        CONNECTED("已连接"),
        DISCONNECTED("已断开"),
        CONNECTING("连接中"),
        ERROR("错误"),
        UNKNOWN("未知");

        private final String description;

        ConnectionStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
