package com.nexus.common.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * MCP任务执行结果
 * 用于封装异步任务的执行结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MCPTaskResult {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 关联ID
     */
    private String correlationId;

    /**
     * 执行是否成功
     */
    private boolean success;

    /**
     * 结果数据
     */
    private Map<String, Object> data;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 执行时长（毫秒）
     */
    private Long executionTimeMs;

    /**
     * 结果创建时间
     */
    private LocalDateTime timestamp;

    /**
     * 服务ID
     */
    private String serviceId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 额外的元数据
     */
    private Map<String, Object> metadata;

    /**
     * 创建成功结果
     */
    public static MCPTaskResult success(String taskId, String correlationId, Map<String, Object> data) {
        return MCPTaskResult.builder()
                .taskId(taskId)
                .correlationId(correlationId)
                .success(true)
                .data(data)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * 创建失败结果
     */
    public static MCPTaskResult failure(String taskId, String correlationId, String errorMessage) {
        return MCPTaskResult.builder()
                .taskId(taskId)
                .correlationId(correlationId)
                .success(false)
                .errorMessage(errorMessage)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * 创建失败结果（带错误代码）
     */
    public static MCPTaskResult failure(String taskId, String correlationId, String errorCode, String errorMessage) {
        return MCPTaskResult.builder()
                .taskId(taskId)
                .correlationId(correlationId)
                .success(false)
                .errorCode(errorCode)
                .errorMessage(errorMessage)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * 创建成功结果（简化版本，用于兼容）
     */
    public static MCPTaskResult success(String taskId, Map<String, Object> data) {
        return success(taskId, "", data);
    }

    /**
     * 创建失败结果（简化版本，用于兼容）
     */
    public static MCPTaskResult failure(String taskId, String errorMessage) {
        return failure(taskId, "", errorMessage);
    }

    /**
     * 检查结果是否成功
     */
    public boolean isSuccess() {
        return success;
    }

    /**
     * 检查结果是否失败
     */
    public boolean isFailure() {
        return !success;
    }

    /**
     * 获取错误描述
     */
    public String getErrorDescription() {
        if (errorCode != null && errorMessage != null) {
            return String.format("[%s] %s", errorCode, errorMessage);
        } else if (errorMessage != null) {
            return errorMessage;
        } else if (errorCode != null) {
            return errorCode;
        }
        return "未知错误";
    }

    /**
     * 添加元数据
     */
    public void addMetadata(String key, Object value) {
        if (this.metadata == null) {
            this.metadata = new java.util.HashMap<>();
        }
        this.metadata.put(key, value);
    }

    /**
     * 获取元数据
     */
    public Object getMetadata(String key) {
        if (this.metadata == null) {
            return null;
        }
        return this.metadata.get(key);
    }

    /**
     * 设置执行时长
     */
    public void setExecutionTime(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime != null && endTime != null) {
            this.executionTimeMs = java.time.Duration.between(startTime, endTime).toMillis();
        }
    }

    /**
     * 获取格式化的执行时长
     */
    public String getFormattedExecutionTime() {
        if (executionTimeMs == null) {
            return "未知";
        }
        if (executionTimeMs < 1000) {
            return executionTimeMs + "ms";
        } else {
            return String.format("%.2fs", executionTimeMs / 1000.0);
        }
    }
}
