package com.nexus.common.service;

import com.nexus.common.event.EmailNotificationEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import javax.mail.internet.MimeMessage;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 邮件服务
 * 提供邮件发送功能，支持HTML模板、重试机制
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "nexus.email.enabled", havingValue = "true", matchIfMissing = false)
public class EmailService {
    
    private final JavaMailSender mailSender;
    private final TemplateEngine templateEngine;
    
    @Value("${nexus.email.from:<EMAIL>}")
    private String fromEmail;
    
    @Value("${nexus.email.from-name:Nexus微服务平台}")
    private String fromName;
    
    @Value("${nexus.email.enabled:false}")
    private boolean emailEnabled;
    
    @Value("${nexus.email.mock:false}")
    private boolean mockMode;
    
    public EmailService(JavaMailSender mailSender, TemplateEngine templateEngine) {
        this.mailSender = mailSender;
        this.templateEngine = templateEngine;
    }
    
    /**
     * 发送邮件通知事件
     */
    public boolean sendEmailNotification(EmailNotificationEvent event) {
        if (!emailEnabled) {
            log.debug("邮件服务未启用，跳过发送: eventId={}", event.getEventId());
            return true; // 返回true避免重试
        }
        
        if (mockMode) {
            return sendMockEmail(event);
        }
        
        try {
            if (event.getTemplate() != null && !event.getTemplate().trim().isEmpty()) {
                return sendTemplateEmail(event);
            } else {
                return sendPlainEmail(event);
            }
        } catch (Exception e) {
            log.error("发送邮件失败: eventId={}, toEmail={} - {}", 
                    event.getEventId(), event.getToEmail(), e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 发送模板邮件
     */
    @Retryable(value = {Exception.class}, maxAttempts = 3, 
               backoff = @Backoff(delay = 2000, multiplier = 2))
    private boolean sendTemplateEmail(EmailNotificationEvent event) throws Exception {
        log.info("发送模板邮件: eventId={}, template={}, toEmail={}", 
                event.getEventId(), event.getTemplate(), event.getToEmail());
        
        // 创建Thymeleaf上下文
        Context context = new Context();
        if (event.getTemplateParams() != null) {
            event.getTemplateParams().forEach(context::setVariable);
        }
        
        // 添加通用变量
        context.setVariable("toName", event.getToName());
        context.setVariable("currentTime", LocalDateTime.now());
        context.setVariable("platformName", "Nexus微服务平台");
        
        // 渲染模板
        String htmlContent = templateEngine.process(event.getTemplate(), context);
        
        // 发送邮件
        return sendHtmlEmailInternal(
                event.getToEmail(),
                event.getToName(),
                event.getSubject(),
                htmlContent
        );
    }
    
    /**
     * 发送纯文本邮件
     */
    @Retryable(value = {Exception.class}, maxAttempts = 3, 
               backoff = @Backoff(delay = 2000, multiplier = 2))
    private boolean sendPlainEmail(EmailNotificationEvent event) throws Exception {
        log.info("发送纯文本邮件: eventId={}, toEmail={}", event.getEventId(), event.getToEmail());
        
        MimeMessage message = mailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
        
        // 设置发件人
        helper.setFrom(fromEmail, fromName);
        
        // 设置收件人
        helper.setTo(event.getToEmail());
        
        // 设置邮件内容
        helper.setSubject(event.getSubject());
        helper.setText(event.getContent(), event.getIsHtml());
        
        // 发送邮件
        mailSender.send(message);
        
        log.info("邮件发送成功: eventId={}, toEmail={}", event.getEventId(), event.getToEmail());
        return true;
    }
    
    /**
     * 发送HTML邮件
     */
    private boolean sendHtmlEmailInternal(String toEmail, String toName, String subject, String htmlContent) throws Exception {
        MimeMessage message = mailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
        
        // 设置发件人
        helper.setFrom(fromEmail, fromName);
        
        // 设置收件人
        helper.setTo(toEmail);
        
        // 设置邮件内容
        helper.setSubject(subject);
        helper.setText(htmlContent, true); // true表示HTML格式
        
        // 发送邮件
        mailSender.send(message);
        
        log.info("HTML邮件发送成功: toEmail={}", toEmail);
        return true;
    }
    
    /**
     * 模拟邮件发送（用于测试环境）
     */
    private boolean sendMockEmail(EmailNotificationEvent event) {
        log.info("=== 模拟邮件发送 ===");
        log.info("事件ID: {}", event.getEventId());
        log.info("收件人: {} <{}>", event.getToName(), event.getToEmail());
        log.info("主题: {}", event.getSubject());
        log.info("邮件类型: {}", event.getEmailType());
        
        if (event.getTemplate() != null) {
            log.info("模板: {}", event.getTemplate());
            log.info("模板参数: {}", event.getTemplateParams());
        } else {
            log.info("内容: {}", event.getContent());
            log.info("HTML格式: {}", event.getIsHtml());
        }
        
        log.info("=== 模拟邮件发送完成 ===");
        return true;
    }
    
    /**
     * 发送欢迎邮件
     */
    public boolean sendWelcomeEmail(String toEmail, String toName, String username, String apiKey) {
        try {
            // 准备模板参数
            Map<String, Object> templateParams = Map.of(
                    "username", username,
                    "apiKey", apiKey,
                    "loginUrl", "https://nexus.example.com/login",
                    "docsUrl", "https://nexus.example.com/docs",
                    "supportEmail", "<EMAIL>"
            );
            
            // 创建邮件事件
            EmailNotificationEvent event = new EmailNotificationEvent(
                    null, // userId
                    toEmail,
                    toName,
                    "欢迎加入Nexus微服务平台！",
                    "welcome_email",
                    templateParams,
                    EmailNotificationEvent.EmailType.WELCOME
            );
            
            return sendEmailNotification(event);
            
        } catch (Exception e) {
            log.error("发送欢迎邮件失败: toEmail={} - {}", toEmail, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 发送密码重置邮件
     */
    public boolean sendPasswordResetEmail(String toEmail, String toName, String resetToken, String resetUrl) {
        try {
            Map<String, Object> templateParams = Map.of(
                    "resetUrl", resetUrl,
                    "resetToken", resetToken,
                    "expiryHours", 24
            );
            
            EmailNotificationEvent event = new EmailNotificationEvent(
                    null,
                    toEmail,
                    toName,
                    "密码重置请求",
                    "password_reset",
                    templateParams,
                    EmailNotificationEvent.EmailType.PASSWORD_RESET
            );
            
            return sendEmailNotification(event);
            
        } catch (Exception e) {
            log.error("发送密码重置邮件失败: toEmail={} - {}", toEmail, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 简单邮件发送方法（兼容性方法）
     */
    public boolean sendEmail(String toEmail, String subject, String content) {
        try {
            // 创建邮件通知事件
            EmailNotificationEvent event = new EmailNotificationEvent(
                    null, // userId
                    toEmail,
                    null, // toName
                    subject,
                    content,
                    EmailNotificationEvent.EmailType.SYSTEM_NOTIFICATION,
                    false // isHtml
            );

            return sendEmailNotification(event);

        } catch (Exception e) {
            log.error("发送邮件失败: toEmail={}, subject={} - {}", toEmail, subject, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送HTML邮件（兼容性方法）
     */
    public boolean sendHtmlEmail(String toEmail, String toName, String subject, String htmlContent) {
        try {
            // 创建邮件通知事件
            EmailNotificationEvent event = new EmailNotificationEvent(
                    null, // userId
                    toEmail,
                    toName,
                    subject,
                    htmlContent,
                    EmailNotificationEvent.EmailType.SYSTEM_NOTIFICATION,
                    true // isHtml
            );

            return sendEmailNotification(event);

        } catch (Exception e) {
            log.error("发送HTML邮件失败: toEmail={}, subject={} - {}", toEmail, subject, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查邮件服务是否可用
     */
    public boolean isEmailServiceAvailable() {
        if (!emailEnabled) {
            return false;
        }

        if (mockMode) {
            return true;
        }

        try {
            // 尝试创建一个测试消息来检查邮件服务
            mailSender.createMimeMessage();
            return true;
        } catch (Exception e) {
            log.warn("邮件服务不可用: {}", e.getMessage());
            return false;
        }
    }
}
