package com.nexus.common.service;

import com.nexus.common.event.OperationLogEvent;
import com.nexus.common.event.ErrorLogEvent;
import com.nexus.common.event.AuditLogEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 日志处理服务
 * 负责日志分类、存储、索引和查询
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LogProcessingService {
    
    private final RedisTemplate<String, Object> redisTemplate;
    
    // Redis键前缀
    private static final String OPERATION_LOG_PREFIX = "logs:operation:";
    private static final String ERROR_LOG_PREFIX = "logs:error:";
    private static final String AUDIT_LOG_PREFIX = "logs:audit:";
    private static final String LOG_INDEX_PREFIX = "logs:index:";
    private static final String LOG_STATS_PREFIX = "logs:stats:";
    
    // 时间格式
    private static final DateTimeFormatter DAY_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter HOUR_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd-HH");
    
    // 默认过期时间
    private static final long LOG_EXPIRE_DAYS = 30; // 30天
    private static final long INDEX_EXPIRE_DAYS = 7; // 7天
    
    /**
     * 处理操作日志
     */
    public void processOperationLog(OperationLogEvent event) {
        try {
            log.debug("处理操作日志: operationId={}, userId={}, action={}", 
                    event.getOperationId(), event.getUserId(), event.getAction());
            
            String dayKey = DAY_FORMAT.format(event.getOperationTime());
            String hourKey = HOUR_FORMAT.format(event.getOperationTime());
            
            // 存储日志详情
            storeOperationLogDetails(event, dayKey);
            
            // 创建索引
            createOperationLogIndex(event, dayKey, hourKey);
            
            // 更新统计
            updateOperationLogStats(event, dayKey, hourKey);
            
            log.debug("操作日志处理完成: operationId={}", event.getOperationId());
            
        } catch (Exception e) {
            log.error("处理操作日志失败: operationId={} - {}", 
                    event.getOperationId(), e.getMessage(), e);
        }
    }
    
    /**
     * 存储操作日志详情
     */
    private void storeOperationLogDetails(OperationLogEvent event, String dayKey) {
        String logKey = OPERATION_LOG_PREFIX + dayKey + ":" + event.getOperationId();
        
        Map<String, Object> logData = new HashMap<>();
        logData.put("operationId", event.getOperationId());
        logData.put("userId", event.getUserId());
        logData.put("username", event.getUsername());
        logData.put("operationType", event.getOperationType().name());
        logData.put("action", event.getAction());
        logData.put("target", event.getTarget());
        logData.put("targetId", event.getTargetId());
        logData.put("description", event.getDescription());
        logData.put("operationTime", event.getOperationTime().toString());
        logData.put("result", event.getResult().name());
        logData.put("duration", event.getDuration());
        logData.put("clientIp", event.getClientIp());
        logData.put("userAgent", event.getUserAgent());
        logData.put("sessionId", event.getSessionId());
        logData.put("riskLevel", event.getRiskLevel().name());
        logData.put("isSuccessful", event.isSuccessful());
        logData.put("isHighRisk", event.isHighRisk());
        
        redisTemplate.opsForHash().putAll(logKey, logData);
        redisTemplate.expire(logKey, LOG_EXPIRE_DAYS, TimeUnit.DAYS);
    }
    
    /**
     * 创建操作日志索引
     */
    private void createOperationLogIndex(OperationLogEvent event, String dayKey, String hourKey) {
        // 按用户索引
        if (event.getUserId() != null) {
            String userIndexKey = LOG_INDEX_PREFIX + "operation:user:" + event.getUserId() + ":" + dayKey;
            redisTemplate.opsForList().rightPush(userIndexKey, event.getOperationId());
            redisTemplate.expire(userIndexKey, INDEX_EXPIRE_DAYS, TimeUnit.DAYS);
        }
        
        // 按操作类型索引
        String typeIndexKey = LOG_INDEX_PREFIX + "operation:type:" + 
                             event.getOperationType().name() + ":" + dayKey;
        redisTemplate.opsForList().rightPush(typeIndexKey, event.getOperationId());
        redisTemplate.expire(typeIndexKey, INDEX_EXPIRE_DAYS, TimeUnit.DAYS);
        
        // 按风险级别索引
        if (event.isHighRisk()) {
            String riskIndexKey = LOG_INDEX_PREFIX + "operation:highrisk:" + dayKey;
            redisTemplate.opsForList().rightPush(riskIndexKey, event.getOperationId());
            redisTemplate.expire(riskIndexKey, INDEX_EXPIRE_DAYS, TimeUnit.DAYS);
        }
        
        // 按小时索引（用于快速查询）
        String hourIndexKey = LOG_INDEX_PREFIX + "operation:hour:" + hourKey;
        redisTemplate.opsForList().rightPush(hourIndexKey, event.getOperationId());
        redisTemplate.expire(hourIndexKey, INDEX_EXPIRE_DAYS, TimeUnit.DAYS);
    }
    
    /**
     * 更新操作日志统计
     */
    private void updateOperationLogStats(OperationLogEvent event, String dayKey, String hourKey) {
        // 按天统计
        String dayStatsKey = LOG_STATS_PREFIX + "operation:day:" + dayKey;
        redisTemplate.opsForHash().increment(dayStatsKey, "total", 1);
        redisTemplate.opsForHash().increment(dayStatsKey, 
                "type_" + event.getOperationType().name(), 1);
        
        if (event.isSuccessful()) {
            redisTemplate.opsForHash().increment(dayStatsKey, "success", 1);
        } else {
            redisTemplate.opsForHash().increment(dayStatsKey, "failed", 1);
        }
        
        if (event.isHighRisk()) {
            redisTemplate.opsForHash().increment(dayStatsKey, "highRisk", 1);
        }
        
        redisTemplate.expire(dayStatsKey, LOG_EXPIRE_DAYS, TimeUnit.DAYS);
        
        // 按小时统计
        String hourStatsKey = LOG_STATS_PREFIX + "operation:hour:" + hourKey;
        redisTemplate.opsForHash().increment(hourStatsKey, "total", 1);
        redisTemplate.expire(hourStatsKey, INDEX_EXPIRE_DAYS, TimeUnit.DAYS);
    }
    
    /**
     * 处理错误日志
     */
    public void processErrorLog(ErrorLogEvent event) {
        try {
            log.debug("处理错误日志: errorId={}, errorLevel={}, errorType={}", 
                    event.getErrorId(), event.getErrorLevel(), event.getErrorType());
            
            String dayKey = DAY_FORMAT.format(event.getOccurredTime());
            String hourKey = HOUR_FORMAT.format(event.getOccurredTime());
            
            // 存储错误日志详情
            storeErrorLogDetails(event, dayKey);
            
            // 创建索引
            createErrorLogIndex(event, dayKey, hourKey);
            
            // 更新统计
            updateErrorLogStats(event, dayKey, hourKey);
            
            // 严重错误特殊处理
            if (event.needsImmediateAttention()) {
                handleSevereError(event);
            }
            
            log.debug("错误日志处理完成: errorId={}", event.getErrorId());
            
        } catch (Exception e) {
            log.error("处理错误日志失败: errorId={} - {}", 
                    event.getErrorId(), e.getMessage(), e);
        }
    }
    
    /**
     * 存储错误日志详情
     */
    private void storeErrorLogDetails(ErrorLogEvent event, String dayKey) {
        String logKey = ERROR_LOG_PREFIX + dayKey + ":" + event.getErrorId();
        
        Map<String, Object> logData = new HashMap<>();
        logData.put("errorId", event.getErrorId());
        logData.put("errorLevel", event.getErrorLevel().name());
        logData.put("errorType", event.getErrorType().name());
        logData.put("errorMessage", event.getErrorMessage());
        logData.put("errorCode", event.getErrorCode());
        logData.put("exceptionClass", event.getExceptionClass());
        logData.put("occurredTime", event.getOccurredTime().toString());
        logData.put("userId", event.getUserId());
        logData.put("sessionId", event.getSessionId());
        logData.put("requestId", event.getRequestId());
        logData.put("clientIp", event.getClientIp());
        logData.put("moduleName", event.getModuleName());
        logData.put("className", event.getClassName());
        logData.put("methodName", event.getMethodName());
        logData.put("lineNumber", event.getLineNumber());
        logData.put("requestUrl", event.getRequestUrl());
        logData.put("requestMethod", event.getRequestMethod());
        logData.put("resolved", event.getResolved());
        logData.put("isSevereError", event.isSevereError());
        logData.put("isSystemError", event.isSystemError());
        logData.put("needsImmediateAttention", event.needsImmediateAttention());
        
        redisTemplate.opsForHash().putAll(logKey, logData);
        redisTemplate.expire(logKey, LOG_EXPIRE_DAYS, TimeUnit.DAYS);
    }
    
    /**
     * 创建错误日志索引
     */
    private void createErrorLogIndex(ErrorLogEvent event, String dayKey, String hourKey) {
        // 按错误级别索引
        String levelIndexKey = LOG_INDEX_PREFIX + "error:level:" + 
                              event.getErrorLevel().name() + ":" + dayKey;
        redisTemplate.opsForList().rightPush(levelIndexKey, event.getErrorId());
        redisTemplate.expire(levelIndexKey, INDEX_EXPIRE_DAYS, TimeUnit.DAYS);
        
        // 按错误类型索引
        String typeIndexKey = LOG_INDEX_PREFIX + "error:type:" + 
                             event.getErrorType().name() + ":" + dayKey;
        redisTemplate.opsForList().rightPush(typeIndexKey, event.getErrorId());
        redisTemplate.expire(typeIndexKey, INDEX_EXPIRE_DAYS, TimeUnit.DAYS);
        
        // 严重错误索引
        if (event.isSevereError()) {
            String severeIndexKey = LOG_INDEX_PREFIX + "error:severe:" + dayKey;
            redisTemplate.opsForList().rightPush(severeIndexKey, event.getErrorId());
            redisTemplate.expire(severeIndexKey, INDEX_EXPIRE_DAYS, TimeUnit.DAYS);
        }
        
        // 按用户索引（如果有用户信息）
        if (event.getUserId() != null) {
            String userIndexKey = LOG_INDEX_PREFIX + "error:user:" + event.getUserId() + ":" + dayKey;
            redisTemplate.opsForList().rightPush(userIndexKey, event.getErrorId());
            redisTemplate.expire(userIndexKey, INDEX_EXPIRE_DAYS, TimeUnit.DAYS);
        }
    }
    
    /**
     * 更新错误日志统计
     */
    private void updateErrorLogStats(ErrorLogEvent event, String dayKey, String hourKey) {
        // 按天统计
        String dayStatsKey = LOG_STATS_PREFIX + "error:day:" + dayKey;
        redisTemplate.opsForHash().increment(dayStatsKey, "total", 1);
        redisTemplate.opsForHash().increment(dayStatsKey, 
                "level_" + event.getErrorLevel().name(), 1);
        redisTemplate.opsForHash().increment(dayStatsKey, 
                "type_" + event.getErrorType().name(), 1);
        
        if (event.isSevereError()) {
            redisTemplate.opsForHash().increment(dayStatsKey, "severe", 1);
        }
        
        if (event.isSystemError()) {
            redisTemplate.opsForHash().increment(dayStatsKey, "system", 1);
        }
        
        redisTemplate.expire(dayStatsKey, LOG_EXPIRE_DAYS, TimeUnit.DAYS);
        
        // 按小时统计
        String hourStatsKey = LOG_STATS_PREFIX + "error:hour:" + hourKey;
        redisTemplate.opsForHash().increment(hourStatsKey, "total", 1);
        redisTemplate.expire(hourStatsKey, INDEX_EXPIRE_DAYS, TimeUnit.DAYS);
    }
    
    /**
     * 处理严重错误
     */
    private void handleSevereError(ErrorLogEvent event) {
        // 记录到严重错误队列
        String severeErrorKey = "logs:severe_errors";
        Map<String, Object> severeErrorInfo = new HashMap<>();
        severeErrorInfo.put("errorId", event.getErrorId());
        severeErrorInfo.put("errorLevel", event.getErrorLevel().name());
        severeErrorInfo.put("errorType", event.getErrorType().name());
        severeErrorInfo.put("errorMessage", event.getErrorMessage());
        severeErrorInfo.put("occurredTime", event.getOccurredTime().toString());
        severeErrorInfo.put("needsImmediateAttention", event.needsImmediateAttention());
        
        redisTemplate.opsForList().rightPush(severeErrorKey, severeErrorInfo);
        
        // 限制队列长度（保留最近1000个严重错误）
        redisTemplate.opsForList().trim(severeErrorKey, -1000, -1);
        
        log.warn("检测到严重错误: errorId={}, errorLevel={}, errorMessage={}", 
                event.getErrorId(), event.getErrorLevel(), event.getErrorMessage());
    }
    
    /**
     * 处理审计日志
     */
    public void processAuditLog(AuditLogEvent event) {
        try {
            log.debug("处理审计日志: auditId={}, auditType={}, userId={}", 
                    event.getAuditId(), event.getAuditType(), event.getUserId());
            
            String dayKey = DAY_FORMAT.format(event.getAuditTime());
            String hourKey = HOUR_FORMAT.format(event.getAuditTime());
            
            // 存储审计日志详情
            storeAuditLogDetails(event, dayKey);
            
            // 创建索引
            createAuditLogIndex(event, dayKey, hourKey);
            
            // 更新统计
            updateAuditLogStats(event, dayKey, hourKey);
            
            // 高风险审计特殊处理
            if (event.isHighRisk()) {
                handleHighRiskAudit(event);
            }
            
            log.debug("审计日志处理完成: auditId={}", event.getAuditId());
            
        } catch (Exception e) {
            log.error("处理审计日志失败: auditId={} - {}", 
                    event.getAuditId(), e.getMessage(), e);
        }
    }
    
    /**
     * 存储审计日志详情
     */
    private void storeAuditLogDetails(AuditLogEvent event, String dayKey) {
        String logKey = AUDIT_LOG_PREFIX + dayKey + ":" + event.getAuditId();
        
        Map<String, Object> logData = new HashMap<>();
        logData.put("auditId", event.getAuditId());
        logData.put("auditType", event.getAuditType().name());
        logData.put("auditLevel", event.getAuditLevel().name());
        logData.put("userId", event.getUserId());
        logData.put("username", event.getUsername());
        logData.put("userRole", event.getUserRole());
        logData.put("action", event.getAction());
        logData.put("target", event.getTarget());
        logData.put("targetId", event.getTargetId());
        logData.put("description", event.getDescription());
        logData.put("auditTime", event.getAuditTime().toString());
        logData.put("result", event.getResult());
        logData.put("clientIp", event.getClientIp());
        logData.put("userAgent", event.getUserAgent());
        logData.put("sessionId", event.getSessionId());
        logData.put("resourceType", event.getResourceType());
        logData.put("resourceId", event.getResourceId());
        logData.put("permissions", event.getPermissions());
        logData.put("isSecurityRelated", event.isSecurityRelated());
        logData.put("isHighRisk", event.isHighRisk());
        
        redisTemplate.opsForHash().putAll(logKey, logData);
        redisTemplate.expire(logKey, LOG_EXPIRE_DAYS, TimeUnit.DAYS);
    }
    
    /**
     * 创建审计日志索引
     */
    private void createAuditLogIndex(AuditLogEvent event, String dayKey, String hourKey) {
        // 按审计类型索引
        String typeIndexKey = LOG_INDEX_PREFIX + "audit:type:" + 
                             event.getAuditType().name() + ":" + dayKey;
        redisTemplate.opsForList().rightPush(typeIndexKey, event.getAuditId());
        redisTemplate.expire(typeIndexKey, INDEX_EXPIRE_DAYS, TimeUnit.DAYS);
        
        // 按用户索引
        if (event.getUserId() != null) {
            String userIndexKey = LOG_INDEX_PREFIX + "audit:user:" + event.getUserId() + ":" + dayKey;
            redisTemplate.opsForList().rightPush(userIndexKey, event.getAuditId());
            redisTemplate.expire(userIndexKey, INDEX_EXPIRE_DAYS, TimeUnit.DAYS);
        }
        
        // 高风险审计索引
        if (event.isHighRisk()) {
            String riskIndexKey = LOG_INDEX_PREFIX + "audit:highrisk:" + dayKey;
            redisTemplate.opsForList().rightPush(riskIndexKey, event.getAuditId());
            redisTemplate.expire(riskIndexKey, INDEX_EXPIRE_DAYS, TimeUnit.DAYS);
        }
        
        // 安全相关审计索引
        if (event.isSecurityRelated()) {
            String securityIndexKey = LOG_INDEX_PREFIX + "audit:security:" + dayKey;
            redisTemplate.opsForList().rightPush(securityIndexKey, event.getAuditId());
            redisTemplate.expire(securityIndexKey, INDEX_EXPIRE_DAYS, TimeUnit.DAYS);
        }
    }
    
    /**
     * 更新审计日志统计
     */
    private void updateAuditLogStats(AuditLogEvent event, String dayKey, String hourKey) {
        // 按天统计
        String dayStatsKey = LOG_STATS_PREFIX + "audit:day:" + dayKey;
        redisTemplate.opsForHash().increment(dayStatsKey, "total", 1);
        redisTemplate.opsForHash().increment(dayStatsKey, 
                "type_" + event.getAuditType().name(), 1);
        redisTemplate.opsForHash().increment(dayStatsKey, 
                "level_" + event.getAuditLevel().name(), 1);
        
        if (event.isHighRisk()) {
            redisTemplate.opsForHash().increment(dayStatsKey, "highRisk", 1);
        }
        
        if (event.isSecurityRelated()) {
            redisTemplate.opsForHash().increment(dayStatsKey, "security", 1);
        }
        
        redisTemplate.expire(dayStatsKey, LOG_EXPIRE_DAYS, TimeUnit.DAYS);
        
        // 按小时统计
        String hourStatsKey = LOG_STATS_PREFIX + "audit:hour:" + hourKey;
        redisTemplate.opsForHash().increment(hourStatsKey, "total", 1);
        redisTemplate.expire(hourStatsKey, INDEX_EXPIRE_DAYS, TimeUnit.DAYS);
    }
    
    /**
     * 处理高风险审计
     */
    private void handleHighRiskAudit(AuditLogEvent event) {
        // 记录到高风险审计队列
        String highRiskAuditKey = "logs:high_risk_audits";
        Map<String, Object> riskAuditInfo = new HashMap<>();
        riskAuditInfo.put("auditId", event.getAuditId());
        riskAuditInfo.put("auditType", event.getAuditType().name());
        riskAuditInfo.put("auditLevel", event.getAuditLevel().name());
        riskAuditInfo.put("userId", event.getUserId());
        riskAuditInfo.put("username", event.getUsername());
        riskAuditInfo.put("action", event.getAction());
        riskAuditInfo.put("auditTime", event.getAuditTime().toString());
        riskAuditInfo.put("isSecurityRelated", event.isSecurityRelated());
        
        redisTemplate.opsForList().rightPush(highRiskAuditKey, riskAuditInfo);
        
        // 限制队列长度（保留最近500个高风险审计）
        redisTemplate.opsForList().trim(highRiskAuditKey, -500, -1);
        
        log.warn("检测到高风险审计: auditId={}, auditType={}, userId={}, action={}", 
                event.getAuditId(), event.getAuditType(), event.getUserId(), event.getAction());
    }
}
