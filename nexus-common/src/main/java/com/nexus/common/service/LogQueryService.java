package com.nexus.common.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 日志查询服务
 * 提供日志数据的查询和分析功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LogQueryService {
    
    private final RedisTemplate<String, Object> redisTemplate;
    
    // Redis键前缀
    private static final String OPERATION_LOG_PREFIX = "logs:operation:";
    private static final String ERROR_LOG_PREFIX = "logs:error:";
    private static final String AUDIT_LOG_PREFIX = "logs:audit:";
    private static final String LOG_INDEX_PREFIX = "logs:index:";
    private static final String LOG_STATS_PREFIX = "logs:stats:";
    
    // 时间格式
    private static final DateTimeFormatter DAY_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter HOUR_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd-HH");
    
    /**
     * 查询操作日志
     */
    public Map<String, Object> queryOperationLogs(String userId, String operationType, 
                                                  String dateRange, int limit) {
        try {
            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>> logs = new ArrayList<>();
            
            String dayKey = getDateKey(dateRange);
            List<Object> logIds = new ArrayList<>();
            
            // 根据查询条件获取日志ID列表
            if (userId != null && !userId.trim().isEmpty()) {
                String userIndexKey = LOG_INDEX_PREFIX + "operation:user:" + userId + ":" + dayKey;
                List<Object> userLogIds = redisTemplate.opsForList().range(userIndexKey, 0, -1);
                if (userLogIds != null) {
                    logIds.addAll(userLogIds);
                }
            } else if (operationType != null && !operationType.trim().isEmpty()) {
                String typeIndexKey = LOG_INDEX_PREFIX + "operation:type:" + operationType + ":" + dayKey;
                List<Object> typeLogIds = redisTemplate.opsForList().range(typeIndexKey, 0, -1);
                if (typeLogIds != null) {
                    logIds.addAll(typeLogIds);
                }
            } else {
                // 获取当天所有操作日志
                String hourIndexKey = LOG_INDEX_PREFIX + "operation:hour:" + 
                                     HOUR_FORMAT.format(LocalDateTime.now());
                List<Object> hourLogIds = redisTemplate.opsForList().range(hourIndexKey, 0, -1);
                if (hourLogIds != null) {
                    logIds.addAll(hourLogIds);
                }
            }
            
            // 限制返回数量
            int actualLimit = Math.min(limit, logIds.size());
            for (int i = 0; i < actualLimit; i++) {
                String logId = logIds.get(i).toString();
                String logKey = OPERATION_LOG_PREFIX + dayKey + ":" + logId;
                
                Map<Object, Object> logData = redisTemplate.opsForHash().entries(logKey);
                if (logData != null && !logData.isEmpty()) {
                    Map<String, Object> log = new HashMap<>();
                    logData.forEach((k, v) -> log.put(k.toString(), v));
                    logs.add(log);
                }
            }
            
            result.put("logs", logs);
            result.put("totalCount", logIds.size());
            result.put("returnedCount", logs.size());
            result.put("dateRange", dateRange);
            result.put("queryType", "operation");
            
            log.debug("查询操作日志完成: userId={}, operationType={}, count={}", 
                    userId, operationType, logs.size());
            
            return result;
            
        } catch (Exception e) {
            log.error("查询操作日志失败: userId={}, operationType={} - {}", 
                    userId, operationType, e.getMessage(), e);
            return new HashMap<>();
        }
    }
    
    /**
     * 查询错误日志
     */
    public Map<String, Object> queryErrorLogs(String errorLevel, String errorType, 
                                             String dateRange, int limit) {
        try {
            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>> logs = new ArrayList<>();
            
            String dayKey = getDateKey(dateRange);
            List<Object> logIds = new ArrayList<>();
            
            // 根据查询条件获取日志ID列表
            if (errorLevel != null && !errorLevel.trim().isEmpty()) {
                String levelIndexKey = LOG_INDEX_PREFIX + "error:level:" + errorLevel + ":" + dayKey;
                List<Object> levelLogIds = redisTemplate.opsForList().range(levelIndexKey, 0, -1);
                if (levelLogIds != null) {
                    logIds.addAll(levelLogIds);
                }
            } else if (errorType != null && !errorType.trim().isEmpty()) {
                String typeIndexKey = LOG_INDEX_PREFIX + "error:type:" + errorType + ":" + dayKey;
                List<Object> typeLogIds = redisTemplate.opsForList().range(typeIndexKey, 0, -1);
                if (typeLogIds != null) {
                    logIds.addAll(typeLogIds);
                }
            } else {
                // 获取严重错误
                String severeIndexKey = LOG_INDEX_PREFIX + "error:severe:" + dayKey;
                List<Object> severeLogIds = redisTemplate.opsForList().range(severeIndexKey, 0, -1);
                if (severeLogIds != null) {
                    logIds.addAll(severeLogIds);
                }
            }
            
            // 限制返回数量
            int actualLimit = Math.min(limit, logIds.size());
            for (int i = 0; i < actualLimit; i++) {
                String logId = logIds.get(i).toString();
                String logKey = ERROR_LOG_PREFIX + dayKey + ":" + logId;
                
                Map<Object, Object> logData = redisTemplate.opsForHash().entries(logKey);
                if (logData != null && !logData.isEmpty()) {
                    Map<String, Object> log = new HashMap<>();
                    logData.forEach((k, v) -> log.put(k.toString(), v));
                    logs.add(log);
                }
            }
            
            result.put("logs", logs);
            result.put("totalCount", logIds.size());
            result.put("returnedCount", logs.size());
            result.put("dateRange", dateRange);
            result.put("queryType", "error");
            
            log.debug("查询错误日志完成: errorLevel={}, errorType={}, count={}", 
                    errorLevel, errorType, logs.size());
            
            return result;
            
        } catch (Exception e) {
            log.error("查询错误日志失败: errorLevel={}, errorType={} - {}", 
                    errorLevel, errorType, e.getMessage(), e);
            return new HashMap<>();
        }
    }
    
    /**
     * 查询审计日志
     */
    public Map<String, Object> queryAuditLogs(String userId, String auditType, 
                                             String dateRange, int limit) {
        try {
            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>> logs = new ArrayList<>();
            
            String dayKey = getDateKey(dateRange);
            List<Object> logIds = new ArrayList<>();
            
            // 根据查询条件获取日志ID列表
            if (userId != null && !userId.trim().isEmpty()) {
                String userIndexKey = LOG_INDEX_PREFIX + "audit:user:" + userId + ":" + dayKey;
                List<Object> userLogIds = redisTemplate.opsForList().range(userIndexKey, 0, -1);
                if (userLogIds != null) {
                    logIds.addAll(userLogIds);
                }
            } else if (auditType != null && !auditType.trim().isEmpty()) {
                String typeIndexKey = LOG_INDEX_PREFIX + "audit:type:" + auditType + ":" + dayKey;
                List<Object> typeLogIds = redisTemplate.opsForList().range(typeIndexKey, 0, -1);
                if (typeLogIds != null) {
                    logIds.addAll(typeLogIds);
                }
            } else {
                // 获取高风险审计
                String riskIndexKey = LOG_INDEX_PREFIX + "audit:highrisk:" + dayKey;
                List<Object> riskLogIds = redisTemplate.opsForList().range(riskIndexKey, 0, -1);
                if (riskLogIds != null) {
                    logIds.addAll(riskLogIds);
                }
            }
            
            // 限制返回数量
            int actualLimit = Math.min(limit, logIds.size());
            for (int i = 0; i < actualLimit; i++) {
                String logId = logIds.get(i).toString();
                String logKey = AUDIT_LOG_PREFIX + dayKey + ":" + logId;
                
                Map<Object, Object> logData = redisTemplate.opsForHash().entries(logKey);
                if (logData != null && !logData.isEmpty()) {
                    Map<String, Object> log = new HashMap<>();
                    logData.forEach((k, v) -> log.put(k.toString(), v));
                    logs.add(log);
                }
            }
            
            result.put("logs", logs);
            result.put("totalCount", logIds.size());
            result.put("returnedCount", logs.size());
            result.put("dateRange", dateRange);
            result.put("queryType", "audit");
            
            log.debug("查询审计日志完成: userId={}, auditType={}, count={}", 
                    userId, auditType, logs.size());
            
            return result;
            
        } catch (Exception e) {
            log.error("查询审计日志失败: userId={}, auditType={} - {}", 
                    userId, auditType, e.getMessage(), e);
            return new HashMap<>();
        }
    }
    
    /**
     * 获取日志统计概览
     */
    public Map<String, Object> getLogStatisticsOverview(String dateRange) {
        try {
            Map<String, Object> overview = new HashMap<>();
            String dayKey = getDateKey(dateRange);
            
            // 操作日志统计
            String operationStatsKey = LOG_STATS_PREFIX + "operation:day:" + dayKey;
            Map<Object, Object> operationStats = redisTemplate.opsForHash().entries(operationStatsKey);
            if (operationStats != null && !operationStats.isEmpty()) {
                overview.put("operationLogs", operationStats);
            } else {
                overview.put("operationLogs", new HashMap<>());
            }
            
            // 错误日志统计
            String errorStatsKey = LOG_STATS_PREFIX + "error:day:" + dayKey;
            Map<Object, Object> errorStats = redisTemplate.opsForHash().entries(errorStatsKey);
            if (errorStats != null && !errorStats.isEmpty()) {
                overview.put("errorLogs", errorStats);
            } else {
                overview.put("errorLogs", new HashMap<>());
            }
            
            // 审计日志统计
            String auditStatsKey = LOG_STATS_PREFIX + "audit:day:" + dayKey;
            Map<Object, Object> auditStats = redisTemplate.opsForHash().entries(auditStatsKey);
            if (auditStats != null && !auditStats.isEmpty()) {
                overview.put("auditLogs", auditStats);
            } else {
                overview.put("auditLogs", new HashMap<>());
            }
            
            overview.put("dateRange", dateRange);
            overview.put("generatedAt", LocalDateTime.now().toString());
            
            log.debug("获取日志统计概览完成: dateRange={}", dateRange);
            return overview;
            
        } catch (Exception e) {
            log.error("获取日志统计概览失败: dateRange={} - {}", dateRange, e.getMessage(), e);
            return new HashMap<>();
        }
    }
    
    /**
     * 获取严重错误列表
     */
    public List<Map<String, Object>> getSevereErrors(int limit) {
        try {
            List<Map<String, Object>> severeErrors = new ArrayList<>();
            String severeErrorKey = "logs:severe_errors";
            
            List<Object> errors = redisTemplate.opsForList().range(severeErrorKey, -limit, -1);
            if (errors != null) {
                for (Object error : errors) {
                    if (error instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> errorMap = (Map<String, Object>) error;
                        severeErrors.add(errorMap);
                    }
                }
            }
            
            log.debug("获取严重错误列表完成: count={}", severeErrors.size());
            return severeErrors;
            
        } catch (Exception e) {
            log.error("获取严重错误列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 获取高风险审计列表
     */
    public List<Map<String, Object>> getHighRiskAudits(int limit) {
        try {
            List<Map<String, Object>> highRiskAudits = new ArrayList<>();
            String highRiskAuditKey = "logs:high_risk_audits";
            
            List<Object> audits = redisTemplate.opsForList().range(highRiskAuditKey, -limit, -1);
            if (audits != null) {
                for (Object audit : audits) {
                    if (audit instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> auditMap = (Map<String, Object>) audit;
                        highRiskAudits.add(auditMap);
                    }
                }
            }
            
            log.debug("获取高风险审计列表完成: count={}", highRiskAudits.size());
            return highRiskAudits;
            
        } catch (Exception e) {
            log.error("获取高风险审计列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 搜索日志
     */
    public Map<String, Object> searchLogs(String logType, String keyword, String dateRange, int limit) {
        try {
            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>> matchedLogs = new ArrayList<>();
            
            String dayKey = getDateKey(dateRange);
            String logPrefix;
            
            switch (logType.toLowerCase()) {
                case "operation":
                    logPrefix = OPERATION_LOG_PREFIX;
                    break;
                case "error":
                    logPrefix = ERROR_LOG_PREFIX;
                    break;
                case "audit":
                    logPrefix = AUDIT_LOG_PREFIX;
                    break;
                default:
                    throw new IllegalArgumentException("不支持的日志类型: " + logType);
            }
            
            // 简化的关键词搜索（实际应用中可能需要更复杂的搜索逻辑）
            String searchPattern = logPrefix + dayKey + ":*";
            java.util.Set<String> logKeys = redisTemplate.keys(searchPattern);
            
            if (logKeys != null) {
                int count = 0;
                for (String logKey : logKeys) {
                    if (count >= limit) break;
                    
                    Map<Object, Object> logData = redisTemplate.opsForHash().entries(logKey);
                    if (logData != null && !logData.isEmpty()) {
                        // 检查是否包含关键词
                        boolean matches = logData.values().stream()
                                .anyMatch(value -> value != null && 
                                         value.toString().toLowerCase().contains(keyword.toLowerCase()));
                        
                        if (matches) {
                            Map<String, Object> log = new HashMap<>();
                            logData.forEach((k, v) -> log.put(k.toString(), v));
                            matchedLogs.add(log);
                            count++;
                        }
                    }
                }
            }
            
            result.put("logs", matchedLogs);
            result.put("keyword", keyword);
            result.put("logType", logType);
            result.put("dateRange", dateRange);
            result.put("matchedCount", matchedLogs.size());
            
            log.debug("搜索日志完成: logType={}, keyword={}, count={}", 
                    logType, keyword, matchedLogs.size());
            
            return result;
            
        } catch (Exception e) {
            log.error("搜索日志失败: logType={}, keyword={} - {}", 
                    logType, keyword, e.getMessage(), e);
            return new HashMap<>();
        }
    }
    
    /**
     * 获取日期键
     */
    private String getDateKey(String dateRange) {
        if (dateRange != null && !dateRange.trim().isEmpty()) {
            return dateRange;
        }
        return DAY_FORMAT.format(LocalDateTime.now());
    }
}
