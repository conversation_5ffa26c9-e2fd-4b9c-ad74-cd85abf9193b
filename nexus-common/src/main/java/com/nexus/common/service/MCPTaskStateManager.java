package com.nexus.common.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * MCP任务状态管理器
 * 负责管理MCP任务的状态存储、查询和更新
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MCPTaskStateManager {
    
    private final RedisTemplate<String, Object> redisTemplate;
    
    // Redis键前缀
    private static final String TASK_STATE_PREFIX = "mcp:task:state:";
    private static final String TASK_RESULT_PREFIX = "mcp:task:result:";
    private static final String TASK_PROGRESS_PREFIX = "mcp:task:progress:";
    private static final String USER_TASKS_PREFIX = "mcp:user:tasks:";
    
    // 默认过期时间（24小时）
    private static final long DEFAULT_EXPIRE_HOURS = 24;
    
    /**
     * 任务状态枚举
     */
    public enum TaskState {
        CREATED("已创建"),
        QUEUED("已排队"),
        EXECUTING("执行中"),
        COMPLETED("已完成"),
        FAILED("已失败"),
        TIMEOUT("已超时"),
        CANCELLED("已取消");
        
        private final String description;
        
        TaskState(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 创建任务状态
     */
    public void createTaskState(String taskId, String userId, String toolName, 
                               String serviceName, Map<String, Object> parameters) {
        try {
            Map<String, Object> taskState = new HashMap<>();
            taskState.put("taskId", taskId);
            taskState.put("userId", userId);
            taskState.put("toolName", toolName);
            taskState.put("serviceName", serviceName);
            taskState.put("parameters", parameters);
            taskState.put("state", TaskState.CREATED.name());
            taskState.put("stateDescription", TaskState.CREATED.getDescription());
            taskState.put("createdAt", LocalDateTime.now().toString());
            taskState.put("updatedAt", LocalDateTime.now().toString());
            taskState.put("progress", 0);
            
            // 存储任务状态
            String stateKey = TASK_STATE_PREFIX + taskId;
            redisTemplate.opsForValue().set(stateKey, taskState, DEFAULT_EXPIRE_HOURS, TimeUnit.HOURS);
            
            // 添加到用户任务列表
            String userTasksKey = USER_TASKS_PREFIX + userId;
            redisTemplate.opsForSet().add(userTasksKey, taskId);
            redisTemplate.expire(userTasksKey, DEFAULT_EXPIRE_HOURS, TimeUnit.HOURS);
            
            log.debug("任务状态创建成功: taskId={}, state={}", taskId, TaskState.CREATED.name());
            
        } catch (Exception e) {
            log.error("创建任务状态失败: taskId={} - {}", taskId, e.getMessage(), e);
        }
    }
    
    /**
     * 更新任务状态
     */
    public void updateTaskState(String taskId, String state, String statusDescription, 
                               Map<String, Object> result) {
        try {
            String stateKey = TASK_STATE_PREFIX + taskId;
            
            // 获取现有状态
            @SuppressWarnings("unchecked")
            Map<String, Object> taskState = (Map<String, Object>) redisTemplate.opsForValue().get(stateKey);
            
            if (taskState == null) {
                log.warn("任务状态不存在，无法更新: taskId={}", taskId);
                return;
            }
            
            // 更新状态信息
            taskState.put("state", state);
            taskState.put("stateDescription", statusDescription);
            taskState.put("updatedAt", LocalDateTime.now().toString());
            
            // 根据状态设置进度
            if ("EXECUTING".equals(state)) {
                taskState.put("progress", 50);
                taskState.put("executingAt", LocalDateTime.now().toString());
            } else if ("COMPLETED".equals(state)) {
                taskState.put("progress", 100);
                taskState.put("completedAt", LocalDateTime.now().toString());
                
                // 存储结果
                if (result != null) {
                    String resultKey = TASK_RESULT_PREFIX + taskId;
                    redisTemplate.opsForValue().set(resultKey, result, DEFAULT_EXPIRE_HOURS, TimeUnit.HOURS);
                    taskState.put("hasResult", true);
                }
            } else if ("FAILED".equals(state) || "TIMEOUT".equals(state)) {
                taskState.put("failedAt", LocalDateTime.now().toString());
                
                // 存储错误信息
                if (result != null) {
                    String resultKey = TASK_RESULT_PREFIX + taskId;
                    redisTemplate.opsForValue().set(resultKey, result, DEFAULT_EXPIRE_HOURS, TimeUnit.HOURS);
                    taskState.put("hasError", true);
                }
            }
            
            // 更新状态
            redisTemplate.opsForValue().set(stateKey, taskState, DEFAULT_EXPIRE_HOURS, TimeUnit.HOURS);
            
            log.debug("任务状态更新成功: taskId={}, state={}", taskId, state);
            
        } catch (Exception e) {
            log.error("更新任务状态失败: taskId={}, state={} - {}", taskId, state, e.getMessage(), e);
        }
    }
    
    /**
     * 更新任务进度
     */
    public void updateTaskProgress(String taskId, int progress, String progressDescription) {
        try {
            String stateKey = TASK_STATE_PREFIX + taskId;
            
            // 获取现有状态
            @SuppressWarnings("unchecked")
            Map<String, Object> taskState = (Map<String, Object>) redisTemplate.opsForValue().get(stateKey);
            
            if (taskState == null) {
                log.warn("任务状态不存在，无法更新进度: taskId={}", taskId);
                return;
            }
            
            // 更新进度信息
            taskState.put("progress", Math.max(0, Math.min(100, progress)));
            taskState.put("progressDescription", progressDescription);
            taskState.put("updatedAt", LocalDateTime.now().toString());
            
            // 更新状态
            redisTemplate.opsForValue().set(stateKey, taskState, DEFAULT_EXPIRE_HOURS, TimeUnit.HOURS);
            
            // 存储进度历史
            String progressKey = TASK_PROGRESS_PREFIX + taskId;
            Map<String, Object> progressInfo = new HashMap<>();
            progressInfo.put("progress", progress);
            progressInfo.put("description", progressDescription);
            progressInfo.put("timestamp", LocalDateTime.now().toString());
            
            redisTemplate.opsForList().rightPush(progressKey, progressInfo);
            redisTemplate.expire(progressKey, DEFAULT_EXPIRE_HOURS, TimeUnit.HOURS);
            
            log.debug("任务进度更新成功: taskId={}, progress={}%", taskId, progress);
            
        } catch (Exception e) {
            log.error("更新任务进度失败: taskId={}, progress={} - {}", taskId, progress, e.getMessage(), e);
        }
    }
    
    /**
     * 获取任务状态
     */
    public Map<String, Object> getTaskState(String taskId) {
        try {
            String stateKey = TASK_STATE_PREFIX + taskId;
            
            @SuppressWarnings("unchecked")
            Map<String, Object> taskState = (Map<String, Object>) redisTemplate.opsForValue().get(stateKey);
            
            if (taskState == null) {
                log.debug("任务状态不存在: taskId={}", taskId);
                return null;
            }
            
            // 添加额外信息
            taskState.put("exists", true);
            taskState.put("queriedAt", LocalDateTime.now().toString());
            
            return taskState;
            
        } catch (Exception e) {
            log.error("获取任务状态失败: taskId={} - {}", taskId, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 获取任务结果
     */
    public Map<String, Object> getTaskResult(String taskId) {
        try {
            String resultKey = TASK_RESULT_PREFIX + taskId;
            
            @SuppressWarnings("unchecked")
            Map<String, Object> result = (Map<String, Object>) redisTemplate.opsForValue().get(resultKey);
            
            if (result == null) {
                log.debug("任务结果不存在: taskId={}", taskId);
                return null;
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("获取任务结果失败: taskId={} - {}", taskId, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 获取任务进度历史
     */
    public java.util.List<Object> getTaskProgressHistory(String taskId) {
        try {
            String progressKey = TASK_PROGRESS_PREFIX + taskId;
            return redisTemplate.opsForList().range(progressKey, 0, -1);
            
        } catch (Exception e) {
            log.error("获取任务进度历史失败: taskId={} - {}", taskId, e.getMessage(), e);
            return new java.util.ArrayList<>();
        }
    }
    
    /**
     * 获取用户的所有任务
     */
    public java.util.Set<Object> getUserTasks(String userId) {
        try {
            String userTasksKey = USER_TASKS_PREFIX + userId;
            return redisTemplate.opsForSet().members(userTasksKey);
            
        } catch (Exception e) {
            log.error("获取用户任务失败: userId={} - {}", userId, e.getMessage(), e);
            return new java.util.HashSet<>();
        }
    }
    
    /**
     * 删除任务状态和相关数据
     */
    public void deleteTaskState(String taskId, String userId) {
        try {
            // 删除任务状态
            String stateKey = TASK_STATE_PREFIX + taskId;
            redisTemplate.delete(stateKey);
            
            // 删除任务结果
            String resultKey = TASK_RESULT_PREFIX + taskId;
            redisTemplate.delete(resultKey);
            
            // 删除进度历史
            String progressKey = TASK_PROGRESS_PREFIX + taskId;
            redisTemplate.delete(progressKey);
            
            // 从用户任务列表中移除
            if (userId != null) {
                String userTasksKey = USER_TASKS_PREFIX + userId;
                redisTemplate.opsForSet().remove(userTasksKey, taskId);
            }
            
            log.debug("任务状态删除成功: taskId={}", taskId);
            
        } catch (Exception e) {
            log.error("删除任务状态失败: taskId={} - {}", taskId, e.getMessage(), e);
        }
    }
    
    /**
     * 检查任务是否存在
     */
    public boolean taskExists(String taskId) {
        try {
            String stateKey = TASK_STATE_PREFIX + taskId;
            return Boolean.TRUE.equals(redisTemplate.hasKey(stateKey));
            
        } catch (Exception e) {
            log.error("检查任务存在性失败: taskId={} - {}", taskId, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 获取任务完整信息（状态+结果+进度历史）
     */
    public Map<String, Object> getTaskFullInfo(String taskId) {
        try {
            Map<String, Object> fullInfo = new HashMap<>();
            
            // 获取任务状态
            Map<String, Object> taskState = getTaskState(taskId);
            if (taskState != null) {
                fullInfo.put("state", taskState);
                
                // 获取任务结果
                Map<String, Object> result = getTaskResult(taskId);
                if (result != null) {
                    fullInfo.put("result", result);
                }
                
                // 获取进度历史
                java.util.List<Object> progressHistory = getTaskProgressHistory(taskId);
                if (!progressHistory.isEmpty()) {
                    fullInfo.put("progressHistory", progressHistory);
                }
                
                fullInfo.put("hasFullInfo", true);
            } else {
                fullInfo.put("exists", false);
            }
            
            return fullInfo;
            
        } catch (Exception e) {
            log.error("获取任务完整信息失败: taskId={} - {}", taskId, e.getMessage(), e);
            return Map.of("exists", false, "error", e.getMessage());
        }
    }
}
