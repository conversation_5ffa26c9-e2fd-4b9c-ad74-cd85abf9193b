package com.nexus.common.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 消息处理监控服务
 * 提供消息处理的指标收集和监控功能
 */
@Slf4j
@Service
public class MessageMonitoringService {
    
    /**
     * 消息发送统计
     */
    private final ConcurrentMap<String, MessageStats> sendStats = new ConcurrentHashMap<>();
    
    /**
     * 消息消费统计
     */
    private final ConcurrentMap<String, MessageStats> consumeStats = new ConcurrentHashMap<>();
    
    /**
     * 消息处理时间统计
     */
    private final ConcurrentMap<String, ProcessingTimeStats> processingTimeStats = new ConcurrentHashMap<>();
    
    /**
     * 消息统计信息
     */
    public static class MessageStats {
        private final AtomicLong totalCount = new AtomicLong(0);
        private final AtomicLong successCount = new AtomicLong(0);
        private final AtomicLong failureCount = new AtomicLong(0);
        private final AtomicLong retryCount = new AtomicLong(0);
        private volatile LocalDateTime lastProcessTime;
        
        public void incrementTotal() { totalCount.incrementAndGet(); }
        public void incrementSuccess() { successCount.incrementAndGet(); }
        public void incrementFailure() { failureCount.incrementAndGet(); }
        public void incrementRetry() { retryCount.incrementAndGet(); }
        public void updateLastProcessTime() { lastProcessTime = LocalDateTime.now(); }
        
        public long getTotalCount() { return totalCount.get(); }
        public long getSuccessCount() { return successCount.get(); }
        public long getFailureCount() { return failureCount.get(); }
        public long getRetryCount() { return retryCount.get(); }
        public LocalDateTime getLastProcessTime() { return lastProcessTime; }
        
        public double getSuccessRate() {
            long total = totalCount.get();
            return total > 0 ? (double) successCount.get() / total * 100 : 0.0;
        }
        
        public double getFailureRate() {
            long total = totalCount.get();
            return total > 0 ? (double) failureCount.get() / total * 100 : 0.0;
        }
    }
    
    /**
     * 处理时间统计
     */
    public static class ProcessingTimeStats {
        private final AtomicLong totalTime = new AtomicLong(0);
        private final AtomicLong count = new AtomicLong(0);
        private volatile long minTime = Long.MAX_VALUE;
        private volatile long maxTime = 0;
        
        public void addTime(long time) {
            totalTime.addAndGet(time);
            count.incrementAndGet();
            
            // 更新最小值
            long currentMin = minTime;
            while (time < currentMin && !compareAndSetMin(currentMin, time)) {
                currentMin = minTime;
            }
            
            // 更新最大值
            long currentMax = maxTime;
            while (time > currentMax && !compareAndSetMax(currentMax, time)) {
                currentMax = maxTime;
            }
        }
        
        private boolean compareAndSetMin(long expect, long update) {
            if (minTime == expect) {
                minTime = update;
                return true;
            }
            return false;
        }
        
        private boolean compareAndSetMax(long expect, long update) {
            if (maxTime == expect) {
                maxTime = update;
                return true;
            }
            return false;
        }
        
        public double getAverageTime() {
            long c = count.get();
            return c > 0 ? (double) totalTime.get() / c : 0.0;
        }
        
        public long getMinTime() { return minTime == Long.MAX_VALUE ? 0 : minTime; }
        public long getMaxTime() { return maxTime; }
        public long getTotalTime() { return totalTime.get(); }
        public long getCount() { return count.get(); }
    }
    
    /**
     * 记录消息发送
     */
    public void recordMessageSent(String eventType, boolean success, long duration) {
        MessageStats stats = sendStats.computeIfAbsent(eventType, k -> new MessageStats());
        stats.incrementTotal();
        stats.updateLastProcessTime();
        
        if (success) {
            stats.incrementSuccess();
        } else {
            stats.incrementFailure();
        }
        
        // 记录处理时间
        ProcessingTimeStats timeStats = processingTimeStats.computeIfAbsent(
                "send." + eventType, k -> new ProcessingTimeStats());
        timeStats.addTime(duration);
        
        log.debug("记录消息发送: eventType={}, success={}, duration={}ms", eventType, success, duration);
    }
    
    /**
     * 记录消息消费
     */
    public void recordMessageConsumed(String eventType, boolean success, long duration) {
        MessageStats stats = consumeStats.computeIfAbsent(eventType, k -> new MessageStats());
        stats.incrementTotal();
        stats.updateLastProcessTime();
        
        if (success) {
            stats.incrementSuccess();
        } else {
            stats.incrementFailure();
        }
        
        // 记录处理时间
        ProcessingTimeStats timeStats = processingTimeStats.computeIfAbsent(
                "consume." + eventType, k -> new ProcessingTimeStats());
        timeStats.addTime(duration);
        
        log.debug("记录消息消费: eventType={}, success={}, duration={}ms", eventType, success, duration);
    }
    
    /**
     * 记录消息重试
     */
    public void recordMessageRetry(String eventType) {
        MessageStats sendStat = sendStats.get(eventType);
        if (sendStat != null) {
            sendStat.incrementRetry();
        }
        
        MessageStats consumeStat = consumeStats.get(eventType);
        if (consumeStat != null) {
            consumeStat.incrementRetry();
        }
        
        log.debug("记录消息重试: eventType={}", eventType);
    }
    
    /**
     * 获取发送统计
     */
    public MessageStats getSendStats(String eventType) {
        return sendStats.get(eventType);
    }
    
    /**
     * 获取消费统计
     */
    public MessageStats getConsumeStats(String eventType) {
        return consumeStats.get(eventType);
    }
    
    /**
     * 获取处理时间统计
     */
    public ProcessingTimeStats getProcessingTimeStats(String key) {
        return processingTimeStats.get(key);
    }
    
    /**
     * 获取所有发送统计
     */
    public ConcurrentMap<String, MessageStats> getAllSendStats() {
        return new ConcurrentHashMap<>(sendStats);
    }
    
    /**
     * 获取所有消费统计
     */
    public ConcurrentMap<String, MessageStats> getAllConsumeStats() {
        return new ConcurrentHashMap<>(consumeStats);
    }
    
    /**
     * 获取所有处理时间统计
     */
    public ConcurrentMap<String, ProcessingTimeStats> getAllProcessingTimeStats() {
        return new ConcurrentHashMap<>(processingTimeStats);
    }
    
    /**
     * 重置统计数据
     */
    public void resetStats() {
        sendStats.clear();
        consumeStats.clear();
        processingTimeStats.clear();
        log.info("消息监控统计数据已重置");
    }
    
    /**
     * 重置特定事件类型的统计数据
     */
    public void resetStats(String eventType) {
        sendStats.remove(eventType);
        consumeStats.remove(eventType);
        processingTimeStats.remove("send." + eventType);
        processingTimeStats.remove("consume." + eventType);
        log.info("消息监控统计数据已重置: eventType={}", eventType);
    }
    
    /**
     * 打印统计报告
     */
    public void printStatsReport() {
        log.info("=== 消息处理统计报告 ===");
        
        log.info("发送统计:");
        sendStats.forEach((eventType, stats) -> {
            log.info("  {}: 总数={}, 成功={}, 失败={}, 重试={}, 成功率={:.2f}%, 最后处理时间={}", 
                    eventType, stats.getTotalCount(), stats.getSuccessCount(), 
                    stats.getFailureCount(), stats.getRetryCount(), stats.getSuccessRate(), 
                    stats.getLastProcessTime());
        });
        
        log.info("消费统计:");
        consumeStats.forEach((eventType, stats) -> {
            log.info("  {}: 总数={}, 成功={}, 失败={}, 重试={}, 成功率={:.2f}%, 最后处理时间={}", 
                    eventType, stats.getTotalCount(), stats.getSuccessCount(), 
                    stats.getFailureCount(), stats.getRetryCount(), stats.getSuccessRate(), 
                    stats.getLastProcessTime());
        });
        
        log.info("处理时间统计:");
        processingTimeStats.forEach((key, stats) -> {
            log.info("  {}: 平均={:.2f}ms, 最小={}ms, 最大={}ms, 总次数={}", 
                    key, stats.getAverageTime(), stats.getMinTime(), 
                    stats.getMaxTime(), stats.getCount());
        });
        
        log.info("=== 统计报告结束 ===");
    }
}
