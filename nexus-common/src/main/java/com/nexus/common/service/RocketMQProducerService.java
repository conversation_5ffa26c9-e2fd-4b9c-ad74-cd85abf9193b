package com.nexus.common.service;

import com.nexus.common.event.BaseEvent;
import com.nexus.common.util.MessageTraceUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 统一RocketMQ消息生产者服务
 * 提供可靠的消息发送功能，包括重试策略
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RocketMQProducerService {

    private final RocketMQTemplate rocketMQTemplate;

    @Autowired(required = false)
    private MessageMonitoringService monitoringService;

    @Value("${nexus.messaging.enable-confirm:true}")
    private boolean enableConfirm;

    @Value("${nexus.messaging.default-timeout:30000}")
    private long defaultTimeout;
    
    /**
     * 发送事件消息（同步）
     * 
     * @param event 事件对象
     * @return 发送结果
     */
    public boolean sendEvent(BaseEvent event) {
        return sendEvent(event, false);
    }
    
    /**
     * 发送事件消息（异步）
     * 
     * @param event 事件对象
     * @return 发送结果
     */
    public boolean sendEventAsync(BaseEvent event) {
        return sendEvent(event, true);
    }
    
    /**
     * 发送事件消息的核心方法
     * 
     * @param event 事件对象
     * @param async 是否异步发送
     * @return 发送结果
     */
    private boolean sendEvent(BaseEvent event, boolean async) {
        if (event == null) {
            log.warn("事件对象为空，跳过发送");
            return false;
        }

        // 设置事件基础信息
        if (event.getEventId() == null) {
            event.setEventId(UUID.randomUUID().toString());
        }
        if (event.getTimestamp() == null) {
            event.setTimestamp(LocalDateTime.now());
        }

        // 开始追踪
        MessageTraceUtil.startTrace(event.getEventId(), event.getEventType(),
                event.getUserId(), event.getSourceService());

        boolean success = false;
        long startTime = System.currentTimeMillis();

        try {
            // 构建Topic和Tag
            String topic = event.getTopicName();
            String tag = event.getTagName();
            String destination = topic + ":" + tag;

            // 设置消息Key（用于消息去重和查询）
            String messageKey = event.getEventId();

            // 发送消息
            if (async) {
                rocketMQTemplate.asyncSend(destination, event, new org.apache.rocketmq.client.producer.SendCallback() {
                    @Override
                    public void onSuccess(org.apache.rocketmq.client.producer.SendResult sendResult) {
                        log.info("异步事件消息发送成功: eventId={}, eventType={}, msgId={}",
                                event.getEventId(), event.getEventType(), sendResult.getMsgId());
                        MessageTraceUtil.recordEvent("MESSAGE_SENT_ASYNC", "异步消息发送成功");
                    }

                    @Override
                    public void onException(Throwable e) {
                        log.error("异步事件消息发送失败: eventId={}, eventType={} - {}",
                                event.getEventId(), event.getEventType(), e.getMessage(), e);
                        MessageTraceUtil.recordError("MESSAGE_SEND_ASYNC_FAILED", e.getMessage());
                    }
                });
            } else {
                org.apache.rocketmq.client.producer.SendResult sendResult = 
                    rocketMQTemplate.syncSend(destination, event);
                
                log.info("同步事件消息发送成功: eventId={}, eventType={}, msgId={}",
                        event.getEventId(), event.getEventType(), sendResult.getMsgId());
            }

            success = true;
            MessageTraceUtil.recordEvent("MESSAGE_SENT", "消息发送成功");

            return true;

        } catch (Exception e) {
            MessageTraceUtil.recordError("MESSAGE_SEND_FAILED", e.getMessage());

            log.error("发送事件消息失败: eventId={}, eventType={} - {}",
                    event.getEventId(), event.getEventType(), e.getMessage(), e);

            // 增加重试次数
            event.incrementRetryCount();

            // 记录重试
            if (monitoringService != null) {
                monitoringService.recordMessageRetry(event.getEventType());
            }

            return false;

        } finally {
            long duration = System.currentTimeMillis() - startTime;

            // 记录监控指标
            if (monitoringService != null) {
                if (success) {
                    monitoringService.recordMessageSent(event.getEventType(), success, duration);
                } else {
                    monitoringService.recordMessageSent(event.getEventType(), false, duration);
                }
            }

            MessageTraceUtil.endTrace();
        }
    }
    
    /**
     * 发送延迟消息
     * 
     * @param event 事件对象
     * @param delayLevel 延迟级别（1-18，对应不同的延迟时间）
     * @return 发送结果
     */
    public boolean sendDelayedEvent(BaseEvent event, int delayLevel) {
        if (event == null) {
            log.warn("事件对象为空，跳过发送");
            return false;
        }

        try {
            // 设置事件基础信息
            if (event.getEventId() == null) {
                event.setEventId(UUID.randomUUID().toString());
            }
            if (event.getTimestamp() == null) {
                event.setTimestamp(LocalDateTime.now());
            }

            // 构建Topic和Tag
            String topic = event.getTopicName();
            String tag = event.getTagName();
            String destination = topic + ":" + tag;

            // 发送延迟消息
            org.springframework.messaging.Message<BaseEvent> message =
                org.springframework.messaging.support.MessageBuilder.withPayload(event).build();
            org.apache.rocketmq.client.producer.SendResult sendResult =
                rocketMQTemplate.syncSend(destination, message, defaultTimeout, delayLevel);
            
            log.info("延迟事件消息发送成功: eventId={}, eventType={}, delayLevel={}, msgId={}", 
                    event.getEventId(), event.getEventType(), delayLevel, sendResult.getMsgId());
            
            return true;
            
        } catch (Exception e) {
            log.error("发送延迟事件消息失败: eventId={}, eventType={}, delayLevel={} - {}", 
                    event.getEventId(), event.getEventType(), delayLevel, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 发送顺序消息
     * 
     * @param event 事件对象
     * @param hashKey 用于选择队列的哈希键
     * @return 发送结果
     */
    public boolean sendOrderlyEvent(BaseEvent event, String hashKey) {
        if (event == null) {
            log.warn("事件对象为空，跳过发送");
            return false;
        }

        try {
            // 设置事件基础信息
            if (event.getEventId() == null) {
                event.setEventId(UUID.randomUUID().toString());
            }
            if (event.getTimestamp() == null) {
                event.setTimestamp(LocalDateTime.now());
            }

            // 构建Topic和Tag
            String topic = event.getTopicName();
            String tag = event.getTagName();
            String destination = topic + ":" + tag;

            // 发送顺序消息
            org.apache.rocketmq.client.producer.SendResult sendResult = 
                rocketMQTemplate.syncSendOrderly(destination, event, hashKey);
            
            log.info("顺序事件消息发送成功: eventId={}, eventType={}, hashKey={}, msgId={}", 
                    event.getEventId(), event.getEventType(), hashKey, sendResult.getMsgId());
            
            return true;
            
        } catch (Exception e) {
            log.error("发送顺序事件消息失败: eventId={}, eventType={}, hashKey={} - {}", 
                    event.getEventId(), event.getEventType(), hashKey, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 检查RocketMQ连接健康状态
     */
    public boolean isConnectionHealthy() {
        try {
            // RocketMQ的健康检查可以通过发送测试消息来实现
            // 这里简化处理，实际可以发送一个测试消息到测试Topic
            return rocketMQTemplate != null;
        } catch (Exception e) {
            log.error("RocketMQ连接健康检查失败: {}", e.getMessage());
            return false;
        }
    }
}
