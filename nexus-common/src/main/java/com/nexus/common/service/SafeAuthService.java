package com.nexus.common.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 安全的认证服务
 * 解决异步注册可能导致的登录问题
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SafeAuthService {
    
    private final UserInitializationService userInitializationService;
    
    /**
     * 安全的用户注册
     * 确保异步处理完成前用户状态的一致性
     */
    public RegisterResult registerUserSafely(RegisterRequest request) {
        try {
            // 1. 同步处理核心数据
            User user = createUserSynchronously(request);
            
            // 2. 标记初始化开始
            userInitializationService.markInitializationStarted(user.getId());
            
            // 3. 发送异步事件
            sendUserRegistrationEventAsync(user);
            
            // 4. 返回注册结果，包含初始化状态
            return RegisterResult.builder()
                    .success(true)
                    .userId(user.getId())
                    .username(user.getUsername())
                    .initializationStatus("PENDING")
                    .message("注册成功，正在初始化用户数据...")
                    .build();
                    
        } catch (Exception e) {
            log.error("用户注册失败: {}", e.getMessage(), e);
            return RegisterResult.builder()
                    .success(false)
                    .message("注册失败: " + e.getMessage())
                    .build();
        }
    }
    
    /**
     * 安全的用户登录
     * 检查用户初始化状态，确保数据完整性
     */
    public LoginResult loginUserSafely(LoginRequest request) {
        try {
            // 1. 基本登录验证
            User user = validateUserCredentials(request);
            if (user == null) {
                return LoginResult.builder()
                        .success(false)
                        .message("用户名或密码错误")
                        .build();
            }
            
            // 2. 检查用户初始化状态
            if (!userInitializationService.isUserReadyForLogin(user.getId())) {
                UserInitializationService.InitializationStatus status = 
                    userInitializationService.getInitializationStatus(user.getId());
                
                if (status == UserInitializationService.InitializationStatus.PENDING) {
                    return LoginResult.builder()
                            .success(false)
                            .message("账户正在初始化中，请稍后再试")
                            .retryAfterSeconds(5)
                            .build();
                } else if (status == UserInitializationService.InitializationStatus.FAILED) {
                    String reason = userInitializationService.getInitializationFailureReason(user.getId());
                    return LoginResult.builder()
                            .success(false)
                            .message("账户初始化失败，请联系客服。原因: " + reason)
                            .build();
                }
            }
            
            // 3. 正常登录流程
            String token = generateJwtToken(user);
            
            return LoginResult.builder()
                    .success(true)
                    .token(token)
                    .userId(user.getId())
                    .username(user.getUsername())
                    .message("登录成功")
                    .build();
                    
        } catch (Exception e) {
            log.error("用户登录失败: {}", e.getMessage(), e);
            return LoginResult.builder()
                    .success(false)
                    .message("登录失败: " + e.getMessage())
                    .build();
        }
    }
    
    /**
     * 带等待的安全登录
     * 如果用户初始化还在进行中，等待一段时间
     */
    public LoginResult loginUserSafelyWithWait(LoginRequest request, long waitTimeoutSeconds) {
        try {
            // 1. 基本登录验证
            User user = validateUserCredentials(request);
            if (user == null) {
                return LoginResult.builder()
                        .success(false)
                        .message("用户名或密码错误")
                        .build();
            }
            
            // 2. 检查初始化状态，如果需要则等待
            UserInitializationService.InitializationStatus status = 
                userInitializationService.getInitializationStatus(user.getId());
                
            if (status == UserInitializationService.InitializationStatus.PENDING) {
                log.info("用户初始化中，等待完成: userId={}, waitTimeout={}s", user.getId(), waitTimeoutSeconds);
                
                boolean initCompleted = userInitializationService.waitForInitializationComplete(
                    user.getId(), waitTimeoutSeconds);
                
                if (!initCompleted) {
                    return LoginResult.builder()
                            .success(false)
                            .message("账户初始化超时，请稍后再试")
                            .retryAfterSeconds(10)
                            .build();
                }
            } else if (status == UserInitializationService.InitializationStatus.FAILED) {
                String reason = userInitializationService.getInitializationFailureReason(user.getId());
                return LoginResult.builder()
                        .success(false)
                        .message("账户初始化失败，请联系客服。原因: " + reason)
                        .build();
            }
            
            // 3. 正常登录流程
            String token = generateJwtToken(user);
            
            return LoginResult.builder()
                    .success(true)
                    .token(token)
                    .userId(user.getId())
                    .username(user.getUsername())
                    .message("登录成功")
                    .build();
                    
        } catch (Exception e) {
            log.error("用户登录失败: {}", e.getMessage(), e);
            return LoginResult.builder()
                    .success(false)
                    .message("登录失败: " + e.getMessage())
                    .build();
        }
    }
    
    /**
     * 检查用户初始化状态的API
     */
    public InitializationStatusResult checkInitializationStatus(String userId) {
        UserInitializationService.InitializationStatus status = 
            userInitializationService.getInitializationStatus(userId);
            
        return InitializationStatusResult.builder()
                .userId(userId)
                .status(status.name())
                .description(status.getDescription())
                .canLogin(status == UserInitializationService.InitializationStatus.COMPLETED)
                .build();
    }
    
    /**
     * 强制完成用户初始化（管理员功能）
     */
    public void forceCompleteUserInitialization(String userId, String adminId, String reason) {
        log.warn("管理员强制完成用户初始化: userId={}, adminId={}, reason={}", userId, adminId, reason);
        
        String fullReason = String.format("管理员 %s 强制完成: %s", adminId, reason);
        userInitializationService.forceCompleteInitialization(userId, fullReason);
    }
    
    // 模拟方法，实际实现需要根据具体的User实体和业务逻辑
    private User createUserSynchronously(RegisterRequest request) {
        // 实际实现：创建用户、保存到数据库、设置基本状态
        return new User(); // 简化示例
    }
    
    private void sendUserRegistrationEventAsync(User user) {
        // 实际实现：发送用户注册事件到消息队列
    }
    
    private User validateUserCredentials(LoginRequest request) {
        // 实际实现：验证用户名密码
        return new User(); // 简化示例
    }
    
    private String generateJwtToken(User user) {
        // 实际实现：生成JWT token
        return "jwt-token"; // 简化示例
    }
    
    // 简化的数据类
    public static class User {
        private String id;
        private String username;
        // getters and setters
        public String getId() { return id; }
        public String getUsername() { return username; }
    }
    
    public static class RegisterRequest {
        private String username;
        private String email;
        private String password;
        // getters and setters
    }
    
    public static class LoginRequest {
        private String username;
        private String password;
        // getters and setters
    }
    
    @lombok.Builder
    @lombok.Data
    public static class RegisterResult {
        private boolean success;
        private String userId;
        private String username;
        private String initializationStatus;
        private String message;
    }
    
    @lombok.Builder
    @lombok.Data
    public static class LoginResult {
        private boolean success;
        private String token;
        private String userId;
        private String username;
        private String message;
        private Integer retryAfterSeconds;
    }
    
    @lombok.Builder
    @lombok.Data
    public static class InitializationStatusResult {
        private String userId;
        private String status;
        private String description;
        private boolean canLogin;
    }
}
