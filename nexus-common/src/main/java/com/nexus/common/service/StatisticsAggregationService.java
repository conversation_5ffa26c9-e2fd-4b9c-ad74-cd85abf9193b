package com.nexus.common.service;

import com.nexus.common.event.UserBehaviorStatisticsEvent;
import com.nexus.common.event.SystemPerformanceStatisticsEvent;
import com.nexus.common.event.BusinessMetricsStatisticsEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 统计数据聚合服务
 * 负责实时统计计算和数据汇总
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StatisticsAggregationService {
    
    private final RedisTemplate<String, Object> redisTemplate;
    
    // Redis键前缀
    private static final String USER_BEHAVIOR_PREFIX = "stats:user:behavior:";
    private static final String SYSTEM_PERFORMANCE_PREFIX = "stats:system:performance:";
    private static final String BUSINESS_METRICS_PREFIX = "stats:business:metrics:";
    private static final String AGGREGATED_STATS_PREFIX = "stats:aggregated:";
    
    // 时间格式
    private static final DateTimeFormatter HOUR_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd-HH");
    private static final DateTimeFormatter DAY_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter MONTH_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM");
    
    // 默认过期时间
    private static final long DEFAULT_EXPIRE_HOURS = 72; // 3天
    private static final long AGGREGATED_EXPIRE_DAYS = 30; // 30天
    
    /**
     * 聚合用户行为统计
     */
    public void aggregateUserBehaviorStatistics(UserBehaviorStatisticsEvent event) {
        try {
            log.debug("聚合用户行为统计: userId={}, behaviorType={}, action={}", 
                    event.getUserId(), event.getBehaviorType(), event.getAction());
            
            LocalDateTime behaviorTime = event.getBehaviorTime();
            String hourKey = HOUR_FORMAT.format(behaviorTime);
            String dayKey = DAY_FORMAT.format(behaviorTime);
            
            // 按小时聚合
            aggregateUserBehaviorByHour(event, hourKey);
            
            // 按天聚合
            aggregateUserBehaviorByDay(event, dayKey);
            
            // 实时统计
            updateRealTimeUserStats(event);
            
            log.debug("用户行为统计聚合完成: userId={}", event.getUserId());
            
        } catch (Exception e) {
            log.error("聚合用户行为统计失败: userId={} - {}", 
                    event.getUserId(), e.getMessage(), e);
        }
    }
    
    /**
     * 按小时聚合用户行为
     */
    private void aggregateUserBehaviorByHour(UserBehaviorStatisticsEvent event, String hourKey) {
        String baseKey = USER_BEHAVIOR_PREFIX + "hour:" + hourKey;
        
        // 总行为数
        redisTemplate.opsForValue().increment(baseKey + ":total", 1);
        
        // 按行为类型统计
        String behaviorTypeKey = baseKey + ":type:" + event.getBehaviorType().name();
        redisTemplate.opsForValue().increment(behaviorTypeKey, 1);
        
        // 按用户统计
        String userKey = baseKey + ":user:" + event.getUserId();
        redisTemplate.opsForValue().increment(userKey, 1);
        
        // 成功/失败统计
        if (event.isSuccessfulBehavior()) {
            redisTemplate.opsForValue().increment(baseKey + ":success", 1);
        } else if (event.isFailedBehavior()) {
            redisTemplate.opsForValue().increment(baseKey + ":failed", 1);
        }
        
        // 设置过期时间
        redisTemplate.expire(baseKey + ":total", DEFAULT_EXPIRE_HOURS, TimeUnit.HOURS);
        redisTemplate.expire(behaviorTypeKey, DEFAULT_EXPIRE_HOURS, TimeUnit.HOURS);
        redisTemplate.expire(userKey, DEFAULT_EXPIRE_HOURS, TimeUnit.HOURS);
    }
    
    /**
     * 按天聚合用户行为
     */
    private void aggregateUserBehaviorByDay(UserBehaviorStatisticsEvent event, String dayKey) {
        String baseKey = USER_BEHAVIOR_PREFIX + "day:" + dayKey;
        
        // 总行为数
        redisTemplate.opsForValue().increment(baseKey + ":total", 1);
        
        // 按行为类型统计
        String behaviorTypeKey = baseKey + ":type:" + event.getBehaviorType().name();
        redisTemplate.opsForValue().increment(behaviorTypeKey, 1);
        
        // 活跃用户统计
        String activeUsersKey = baseKey + ":active_users";
        redisTemplate.opsForSet().add(activeUsersKey, event.getUserId());
        
        // 设置过期时间
        redisTemplate.expire(baseKey + ":total", AGGREGATED_EXPIRE_DAYS, TimeUnit.DAYS);
        redisTemplate.expire(behaviorTypeKey, AGGREGATED_EXPIRE_DAYS, TimeUnit.DAYS);
        redisTemplate.expire(activeUsersKey, AGGREGATED_EXPIRE_DAYS, TimeUnit.DAYS);
    }
    
    /**
     * 更新实时用户统计
     */
    private void updateRealTimeUserStats(UserBehaviorStatisticsEvent event) {
        String userStatsKey = USER_BEHAVIOR_PREFIX + "realtime:user:" + event.getUserId();
        
        // 更新用户最后活跃时间
        redisTemplate.opsForHash().put(userStatsKey, "lastActiveTime", 
                event.getBehaviorTime().toString());
        
        // 更新用户行为计数
        redisTemplate.opsForHash().increment(userStatsKey, "totalBehaviors", 1);
        redisTemplate.opsForHash().increment(userStatsKey, 
                "behavior_" + event.getBehaviorType().name(), 1);
        
        // 设置过期时间
        redisTemplate.expire(userStatsKey, 7, TimeUnit.DAYS);
    }
    
    /**
     * 聚合系统性能统计
     */
    public void aggregateSystemPerformanceStatistics(SystemPerformanceStatisticsEvent event) {
        try {
            log.debug("聚合系统性能统计: serviceName={}, metricType={}, metricName={}", 
                    event.getServiceName(), event.getMetricType(), event.getMetricName());
            
            LocalDateTime measurementTime = event.getMeasurementTime();
            String hourKey = HOUR_FORMAT.format(measurementTime);
            String dayKey = DAY_FORMAT.format(measurementTime);
            
            // 按小时聚合
            aggregateSystemPerformanceByHour(event, hourKey);
            
            // 按天聚合
            aggregateSystemPerformanceByDay(event, dayKey);
            
            // 实时性能监控
            updateRealTimePerformanceStats(event);
            
            log.debug("系统性能统计聚合完成: serviceName={}", event.getServiceName());
            
        } catch (Exception e) {
            log.error("聚合系统性能统计失败: serviceName={} - {}", 
                    event.getServiceName(), e.getMessage(), e);
        }
    }
    
    /**
     * 按小时聚合系统性能
     */
    private void aggregateSystemPerformanceByHour(SystemPerformanceStatisticsEvent event, String hourKey) {
        String baseKey = SYSTEM_PERFORMANCE_PREFIX + "hour:" + hourKey + ":" + event.getServiceName();
        
        // 记录指标值
        String metricKey = baseKey + ":metric:" + event.getMetricName();
        redisTemplate.opsForList().rightPush(metricKey, event.getMetricValue());
        
        // 限制列表长度（保留最近100个值）
        redisTemplate.opsForList().trim(metricKey, -100, -1);
        
        // 计算平均值
        java.util.List<Object> values = redisTemplate.opsForList().range(metricKey, 0, -1);
        if (values != null && !values.isEmpty()) {
            double sum = values.stream()
                    .mapToDouble(v -> Double.parseDouble(v.toString()))
                    .sum();
            double average = sum / values.size();
            
            redisTemplate.opsForHash().put(baseKey + ":averages", event.getMetricName(), average);
        }
        
        // 告警检查
        if (event.needsAlert()) {
            String alertKey = baseKey + ":alerts:" + event.getMetricName();
            redisTemplate.opsForValue().increment(alertKey, 1);
            redisTemplate.expire(alertKey, DEFAULT_EXPIRE_HOURS, TimeUnit.HOURS);
        }
        
        // 设置过期时间
        redisTemplate.expire(metricKey, DEFAULT_EXPIRE_HOURS, TimeUnit.HOURS);
        redisTemplate.expire(baseKey + ":averages", DEFAULT_EXPIRE_HOURS, TimeUnit.HOURS);
    }
    
    /**
     * 按天聚合系统性能
     */
    private void aggregateSystemPerformanceByDay(SystemPerformanceStatisticsEvent event, String dayKey) {
        String baseKey = SYSTEM_PERFORMANCE_PREFIX + "day:" + dayKey + ":" + event.getServiceName();
        
        // 记录每日指标摘要
        String summaryKey = baseKey + ":summary:" + event.getMetricName();
        Map<String, Object> summary = new HashMap<>();
        summary.put("value", event.getMetricValue());
        summary.put("timestamp", event.getMeasurementTime().toString());
        summary.put("alertLevel", event.getAlertLevel());
        
        redisTemplate.opsForList().rightPush(summaryKey, summary);
        
        // 设置过期时间
        redisTemplate.expire(summaryKey, AGGREGATED_EXPIRE_DAYS, TimeUnit.DAYS);
    }
    
    /**
     * 更新实时性能统计
     */
    private void updateRealTimePerformanceStats(SystemPerformanceStatisticsEvent event) {
        String realtimeKey = SYSTEM_PERFORMANCE_PREFIX + "realtime:" + event.getServiceName();
        
        // 更新最新指标值
        redisTemplate.opsForHash().put(realtimeKey, event.getMetricName(), event.getMetricValue());
        redisTemplate.opsForHash().put(realtimeKey, event.getMetricName() + "_timestamp", 
                event.getMeasurementTime().toString());
        
        // 更新告警状态
        if (event.needsAlert()) {
            redisTemplate.opsForHash().put(realtimeKey, event.getMetricName() + "_alert", 
                    event.getAlertLevel());
        }
        
        // 设置过期时间
        redisTemplate.expire(realtimeKey, 1, TimeUnit.HOURS);
    }
    
    /**
     * 聚合业务指标统计
     */
    public void aggregateBusinessMetricsStatistics(BusinessMetricsStatisticsEvent event) {
        try {
            log.debug("聚合业务指标统计: metricType={}, metricName={}, dimensionValue={}", 
                    event.getMetricType(), event.getMetricName(), event.getDimensionValue());
            
            LocalDateTime statisticsTime = event.getStatisticsTime();
            String hourKey = HOUR_FORMAT.format(statisticsTime);
            String dayKey = DAY_FORMAT.format(statisticsTime);
            
            // 按小时聚合
            aggregateBusinessMetricsByHour(event, hourKey);
            
            // 按天聚合
            aggregateBusinessMetricsByDay(event, dayKey);
            
            // 实时业务指标
            updateRealTimeBusinessMetrics(event);
            
            log.debug("业务指标统计聚合完成: metricType={}", event.getMetricType());
            
        } catch (Exception e) {
            log.error("聚合业务指标统计失败: metricType={} - {}", 
                    event.getMetricType(), e.getMessage(), e);
        }
    }
    
    /**
     * 按小时聚合业务指标
     */
    private void aggregateBusinessMetricsByHour(BusinessMetricsStatisticsEvent event, String hourKey) {
        String baseKey = BUSINESS_METRICS_PREFIX + "hour:" + hourKey;
        
        // 按指标类型聚合
        String metricTypeKey = baseKey + ":type:" + event.getMetricType().name();
        redisTemplate.opsForHash().put(metricTypeKey, event.getMetricName(), event.getMetricValue());
        
        // 按维度聚合
        if (event.getDimensionValue() != null) {
            String dimensionKey = baseKey + ":dimension:" + event.getBusinessDimension() + 
                                 ":" + event.getDimensionValue();
            redisTemplate.opsForHash().put(dimensionKey, event.getMetricName(), event.getMetricValue());
            redisTemplate.expire(dimensionKey, DEFAULT_EXPIRE_HOURS, TimeUnit.HOURS);
        }
        
        // KPI指标特殊处理
        if (event.isKPI()) {
            String kpiKey = baseKey + ":kpi:" + event.getMetricName();
            redisTemplate.opsForValue().set(kpiKey, event.getMetricValue());
            redisTemplate.expire(kpiKey, DEFAULT_EXPIRE_HOURS, TimeUnit.HOURS);
        }
        
        // 设置过期时间
        redisTemplate.expire(metricTypeKey, DEFAULT_EXPIRE_HOURS, TimeUnit.HOURS);
    }
    
    /**
     * 按天聚合业务指标
     */
    private void aggregateBusinessMetricsByDay(BusinessMetricsStatisticsEvent event, String dayKey) {
        String baseKey = BUSINESS_METRICS_PREFIX + "day:" + dayKey;
        
        // 记录每日业务指标
        String dailyKey = baseKey + ":metrics:" + event.getMetricType().name();
        Map<String, Object> dailyMetric = new HashMap<>();
        dailyMetric.put("metricName", event.getMetricName());
        dailyMetric.put("metricValue", event.getMetricValue());
        dailyMetric.put("dimension", event.getBusinessDimension());
        dailyMetric.put("dimensionValue", event.getDimensionValue());
        dailyMetric.put("isKPI", event.isKPI());
        dailyMetric.put("timestamp", event.getStatisticsTime().toString());
        
        redisTemplate.opsForList().rightPush(dailyKey, dailyMetric);
        
        // 设置过期时间
        redisTemplate.expire(dailyKey, AGGREGATED_EXPIRE_DAYS, TimeUnit.DAYS);
    }
    
    /**
     * 更新实时业务指标
     */
    private void updateRealTimeBusinessMetrics(BusinessMetricsStatisticsEvent event) {
        String realtimeKey = BUSINESS_METRICS_PREFIX + "realtime:" + event.getMetricType().name();
        
        // 更新最新指标值
        redisTemplate.opsForHash().put(realtimeKey, event.getMetricName(), event.getMetricValue());
        redisTemplate.opsForHash().put(realtimeKey, event.getMetricName() + "_timestamp", 
                event.getStatisticsTime().toString());
        
        // 告警检查
        if (event.needsAlert()) {
            redisTemplate.opsForHash().put(realtimeKey, event.getMetricName() + "_alert", 
                    event.getAlertLevel());
        }
        
        // 设置过期时间
        redisTemplate.expire(realtimeKey, 2, TimeUnit.HOURS);
    }
}
