package com.nexus.common.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 统计查询服务
 * 提供统计数据的查询和分析功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StatisticsQueryService {
    
    private final RedisTemplate<String, Object> redisTemplate;
    
    // Redis键前缀
    private static final String USER_BEHAVIOR_PREFIX = "stats:user:behavior:";
    private static final String SYSTEM_PERFORMANCE_PREFIX = "stats:system:performance:";
    private static final String BUSINESS_METRICS_PREFIX = "stats:business:metrics:";
    
    // 时间格式
    private static final DateTimeFormatter HOUR_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd-HH");
    private static final DateTimeFormatter DAY_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    
    /**
     * 获取用户行为统计概览
     */
    public Map<String, Object> getUserBehaviorOverview(String timeRange) {
        try {
            Map<String, Object> overview = new HashMap<>();
            String timeKey = getTimeKey(timeRange);
            String baseKey = USER_BEHAVIOR_PREFIX + timeRange + ":" + timeKey;
            
            // 总行为数
            Object totalBehaviors = redisTemplate.opsForValue().get(baseKey + ":total");
            overview.put("totalBehaviors", totalBehaviors != null ? totalBehaviors : 0);
            
            // 成功/失败统计
            Object successCount = redisTemplate.opsForValue().get(baseKey + ":success");
            Object failedCount = redisTemplate.opsForValue().get(baseKey + ":failed");
            overview.put("successCount", successCount != null ? successCount : 0);
            overview.put("failedCount", failedCount != null ? failedCount : 0);
            
            // 按行为类型统计
            Map<String, Object> behaviorTypes = new HashMap<>();
            String[] types = {"LOGIN", "LOGOUT", "TOOL_CALL", "API_REQUEST", "PAGE_VIEW"};
            for (String type : types) {
                Object count = redisTemplate.opsForValue().get(baseKey + ":type:" + type);
                behaviorTypes.put(type, count != null ? count : 0);
            }
            overview.put("behaviorTypes", behaviorTypes);
            
            // 活跃用户数（仅日统计有效）
            if ("day".equals(timeRange)) {
                Set<Object> activeUsers = redisTemplate.opsForSet().members(baseKey + ":active_users");
                overview.put("activeUserCount", activeUsers != null ? activeUsers.size() : 0);
            }
            
            log.debug("获取用户行为统计概览完成: timeRange={}", timeRange);
            return overview;
            
        } catch (Exception e) {
            log.error("获取用户行为统计概览失败: timeRange={} - {}", timeRange, e.getMessage(), e);
            return new HashMap<>();
        }
    }
    
    /**
     * 获取系统性能统计概览
     */
    public Map<String, Object> getSystemPerformanceOverview(String serviceName, String timeRange) {
        try {
            Map<String, Object> overview = new HashMap<>();
            String timeKey = getTimeKey(timeRange);
            String baseKey = SYSTEM_PERFORMANCE_PREFIX + timeRange + ":" + timeKey + ":" + serviceName;
            
            // 获取平均值
            Map<Object, Object> averages = redisTemplate.opsForHash().entries(baseKey + ":averages");
            if (averages != null && !averages.isEmpty()) {
                overview.put("averages", averages);
            }
            
            // 获取告警统计
            Map<String, Object> alerts = new HashMap<>();
            Set<String> keys = redisTemplate.keys(baseKey + ":alerts:*");
            if (keys != null) {
                for (String key : keys) {
                    String metricName = key.substring(key.lastIndexOf(":") + 1);
                    Object alertCount = redisTemplate.opsForValue().get(key);
                    alerts.put(metricName, alertCount != null ? alertCount : 0);
                }
            }
            overview.put("alerts", alerts);
            
            // 获取实时状态
            String realtimeKey = SYSTEM_PERFORMANCE_PREFIX + "realtime:" + serviceName;
            Map<Object, Object> realtimeStats = redisTemplate.opsForHash().entries(realtimeKey);
            if (realtimeStats != null && !realtimeStats.isEmpty()) {
                overview.put("realtime", realtimeStats);
            }
            
            log.debug("获取系统性能统计概览完成: serviceName={}, timeRange={}", serviceName, timeRange);
            return overview;
            
        } catch (Exception e) {
            log.error("获取系统性能统计概览失败: serviceName={}, timeRange={} - {}", 
                    serviceName, timeRange, e.getMessage(), e);
            return new HashMap<>();
        }
    }
    
    /**
     * 获取统计摘要
     */
    public Map<String, Object> getStatisticsSummary() {
        try {
            Map<String, Object> summary = new HashMap<>();
            
            // 用户行为摘要
            Map<String, Object> userBehaviorSummary = getUserBehaviorOverview("hour");
            summary.put("userBehavior", userBehaviorSummary);
            
            // 系统性能摘要
            Map<String, Object> systemPerformanceSummary = new HashMap<>();
            Set<String> serviceKeys = redisTemplate.keys(SYSTEM_PERFORMANCE_PREFIX + "realtime:*");
            if (serviceKeys != null) {
                for (String key : serviceKeys) {
                    String serviceName = key.substring(key.lastIndexOf(":") + 1);
                    Map<Object, Object> serviceStats = redisTemplate.opsForHash().entries(key);
                    if (serviceStats != null && !serviceStats.isEmpty()) {
                        systemPerformanceSummary.put(serviceName, serviceStats);
                    }
                }
            }
            summary.put("systemPerformance", systemPerformanceSummary);
            
            summary.put("generatedAt", LocalDateTime.now().toString());
            summary.put("timeRange", "realtime");
            
            log.debug("获取统计摘要完成");
            return summary;
            
        } catch (Exception e) {
            log.error("获取统计摘要失败: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }
    
    /**
     * 获取时间键
     */
    private String getTimeKey(String timeRange) {
        LocalDateTime now = LocalDateTime.now();
        switch (timeRange.toLowerCase()) {
            case "hour":
                return HOUR_FORMAT.format(now);
            case "day":
                return DAY_FORMAT.format(now);
            default:
                return HOUR_FORMAT.format(now);
        }
    }
    
    /**
     * 清理过期统计数据
     */
    public void cleanupExpiredStatistics() {
        try {
            log.info("开始清理过期统计数据");
            
            // 清理过期的小时统计数据（保留72小时）
            LocalDateTime cutoffTime = LocalDateTime.now().minusHours(72);
            String cutoffHour = HOUR_FORMAT.format(cutoffTime);
            
            // 清理用户行为统计
            cleanupExpiredKeys(USER_BEHAVIOR_PREFIX + "hour:", cutoffHour);
            
            // 清理系统性能统计
            cleanupExpiredKeys(SYSTEM_PERFORMANCE_PREFIX + "hour:", cutoffHour);
            
            // 清理业务指标统计
            cleanupExpiredKeys(BUSINESS_METRICS_PREFIX + "hour:", cutoffHour);
            
            log.info("过期统计数据清理完成");
            
        } catch (Exception e) {
            log.error("清理过期统计数据失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 清理过期的键
     */
    private void cleanupExpiredKeys(String keyPrefix, String cutoffTime) {
        Set<String> keys = redisTemplate.keys(keyPrefix + "*");
        if (keys != null) {
            for (String key : keys) {
                // 提取时间部分并比较
                String[] parts = key.split(":");
                if (parts.length >= 3) {
                    String timeKey = parts[2];
                    if (timeKey.compareTo(cutoffTime) < 0) {
                        redisTemplate.delete(key);
                        log.debug("删除过期统计键: {}", key);
                    }
                }
            }
        }
    }
}
