package com.nexus.common.service;

import com.nexus.common.event.SubscriptionCreatedEvent;
import com.nexus.common.event.SubscriptionStatusChangedEvent;
import com.nexus.common.event.SubscriptionRenewedEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 订阅通知服务
 * 负责订阅相关的邮件通知模板和发送逻辑
 */
@Slf4j
@Service
public class SubscriptionNotificationService {

    @Autowired(required = false)
    private EmailService emailService;
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 发送订阅创建欢迎邮件
     */
    public void sendSubscriptionWelcomeEmail(SubscriptionCreatedEvent event) {
        try {
            if (event.getUserEmail() == null || event.getUserEmail().trim().isEmpty()) {
                log.warn("用户邮箱为空，跳过欢迎邮件发送: userId={}", event.getUserId());
                return;
            }
            
            String subject = buildWelcomeEmailSubject(event);
            String content = buildWelcomeEmailContent(event);

            if (emailService != null) {
                emailService.sendHtmlEmail(event.getUserEmail(), event.getUsername(), subject, content);
            } else {
                log.info("邮件服务未启用，跳过发送欢迎邮件: userId={}, email={}",
                        event.getUserId(), event.getUserEmail());
            }
            
            log.info("订阅欢迎邮件发送成功: userId={}, email={}", 
                    event.getUserId(), event.getUserEmail());
            
        } catch (Exception e) {
            log.error("发送订阅欢迎邮件失败: userId={} - {}", 
                    event.getUserId(), e.getMessage(), e);
        }
    }
    
    /**
     * 发送订阅状态变更通知邮件
     */
    public void sendStatusChangeNotification(SubscriptionStatusChangedEvent event) {
        try {
            if (event.getUserEmail() == null || event.getUserEmail().trim().isEmpty()) {
                log.warn("用户邮箱为空，跳过状态变更通知: userId={}", event.getUserId());
                return;
            }
            
            String subject = buildStatusChangeEmailSubject(event);
            String content = buildStatusChangeEmailContent(event);

            if (emailService != null) {
                emailService.sendEmail(event.getUserEmail(), subject, content);
            } else {
                log.info("邮件服务未启用，跳过发送状态变更通知: userId={}, email={}",
                        event.getUserId(), event.getUserEmail());
            }
            
            log.info("订阅状态变更通知发送成功: userId={}, email={}", 
                    event.getUserId(), event.getUserEmail());
            
        } catch (Exception e) {
            log.error("发送订阅状态变更通知失败: userId={} - {}", 
                    event.getUserId(), e.getMessage(), e);
        }
    }
    
    /**
     * 发送订阅续费确认邮件
     */
    public void sendRenewalConfirmationEmail(SubscriptionRenewedEvent event) {
        try {
            if (event.getUserEmail() == null || event.getUserEmail().trim().isEmpty()) {
                log.warn("用户邮箱为空，跳过续费确认邮件: userId={}", event.getUserId());
                return;
            }
            
            String subject = buildRenewalEmailSubject(event);
            String content = buildRenewalEmailContent(event);

            if (emailService != null) {
                emailService.sendEmail(event.getUserEmail(), subject, content);
            } else {
                log.info("邮件服务未启用，跳过发送续费确认邮件: userId={}, email={}",
                        event.getUserId(), event.getUserEmail());
            }
            
            log.info("订阅续费确认邮件发送成功: userId={}, email={}", 
                    event.getUserId(), event.getUserEmail());
            
        } catch (Exception e) {
            log.error("发送订阅续费确认邮件失败: userId={} - {}", 
                    event.getUserId(), e.getMessage(), e);
        }
    }
    
    /**
     * 发送订阅到期提醒邮件
     */
    public void sendExpirationReminderEmail(String userEmail, String username, String serviceName, 
                                           LocalDateTime expirationDate, Long subscriptionId) {
        try {
            if (userEmail == null || userEmail.trim().isEmpty()) {
                log.warn("用户邮箱为空，跳过到期提醒邮件: subscriptionId={}", subscriptionId);
                return;
            }
            
            String subject = "订阅即将到期提醒 - " + serviceName;
            String content = buildExpirationReminderContent(username, serviceName, expirationDate, subscriptionId);

            if (emailService != null) {
                emailService.sendEmail(userEmail, subject, content);
            } else {
                log.info("邮件服务未启用，跳过发送到期提醒邮件: subscriptionId={}, email={}",
                        subscriptionId, userEmail);
            }
            
            log.info("订阅到期提醒邮件发送成功: subscriptionId={}, email={}", subscriptionId, userEmail);
            
        } catch (Exception e) {
            log.error("发送订阅到期提醒邮件失败: subscriptionId={} - {}", subscriptionId, e.getMessage(), e);
        }
    }
    
    /**
     * 构建欢迎邮件主题
     */
    private String buildWelcomeEmailSubject(SubscriptionCreatedEvent event) {
        StringBuilder subject = new StringBuilder();
        subject.append("欢迎订阅 ").append(event.getServiceName());
        
        if (event.isTrialSubscription()) {
            subject.append(" - 免费试用");
        } else if (event.getSubscriptionType() != null) {
            subject.append(" - ").append(event.getSubscriptionType().getDescription());
        }
        
        return subject.toString();
    }
    
    /**
     * 构建欢迎邮件内容
     */
    private String buildWelcomeEmailContent(SubscriptionCreatedEvent event) {
        StringBuilder content = new StringBuilder();
        
        // 邮件头部
        content.append("<!DOCTYPE html>\n");
        content.append("<html>\n<head>\n");
        content.append("<meta charset=\"UTF-8\">\n");
        content.append("<title>欢迎订阅</title>\n");
        content.append("<style>\n");
        content.append("body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n");
        content.append(".container { max-width: 600px; margin: 0 auto; padding: 20px; }\n");
        content.append(".header { background-color: #4CAF50; color: white; padding: 20px; text-align: center; }\n");
        content.append(".content { padding: 20px; background-color: #f9f9f9; }\n");
        content.append(".info-table { width: 100%; border-collapse: collapse; margin: 20px 0; }\n");
        content.append(".info-table th, .info-table td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }\n");
        content.append(".info-table th { background-color: #f2f2f2; }\n");
        content.append(".footer { text-align: center; padding: 20px; color: #666; }\n");
        content.append("</style>\n");
        content.append("</head>\n<body>\n");
        
        content.append("<div class=\"container\">\n");
        
        // 邮件标题
        content.append("<div class=\"header\">\n");
        content.append("<h1>🎉 欢迎订阅我们的服务！</h1>\n");
        content.append("</div>\n");
        
        // 邮件内容
        content.append("<div class=\"content\">\n");
        content.append("<p>亲爱的 ").append(event.getUsername() != null ? event.getUsername() : "用户").append("，</p>\n");
        content.append("<p>感谢您订阅我们的服务！我们很高兴为您提供优质的服务体验。</p>\n");
        
        // 订阅详情表格
        content.append("<table class=\"info-table\">\n");
        content.append("<tr><th>订阅详情</th><th>信息</th></tr>\n");
        content.append("<tr><td>订阅ID</td><td>").append(event.getSubscriptionId()).append("</td></tr>\n");
        content.append("<tr><td>服务名称</td><td>").append(event.getServiceName()).append("</td></tr>\n");
        
        if (event.getSubscriptionType() != null) {
            content.append("<tr><td>订阅类型</td><td>").append(event.getSubscriptionType().getDescription()).append("</td></tr>\n");
        }
        
        if (event.getBillingCycle() != null) {
            content.append("<tr><td>计费周期</td><td>").append(event.getBillingCycle().getDescription()).append("</td></tr>\n");
        }
        
        if (event.getStartDate() != null) {
            content.append("<tr><td>开始时间</td><td>").append(event.getStartDate().format(DATE_FORMATTER)).append("</td></tr>\n");
        }
        
        if (event.getEndDate() != null) {
            content.append("<tr><td>结束时间</td><td>").append(event.getEndDate().format(DATE_FORMATTER)).append("</td></tr>\n");
        }
        
        if (event.isPaidSubscription()) {
            content.append("<tr><td>支付金额</td><td>").append(event.getActualPaymentAmount())
                   .append(" ").append(event.getCurrency()).append("</td></tr>\n");
        }
        
        if (event.hasDiscount()) {
            content.append("<tr><td>享受折扣</td><td>").append(event.getDiscountAmount())
                   .append(" ").append(event.getCurrency()).append("</td></tr>\n");
        }
        
        content.append("</table>\n");
        
        // 特殊提示
        if (event.isTrialSubscription()) {
            content.append("<div style=\"background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 5px;\">\n");
            content.append("<h3>🎁 试用提醒</h3>\n");
            content.append("<p>您当前使用的是试用版本，试用期结束后如需继续使用，请及时升级到付费版本。</p>\n");
            content.append("</div>\n");
        }
        
        if (event.isReferralSubscription()) {
            content.append("<div style=\"background-color: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; margin: 20px 0; border-radius: 5px;\">\n");
            content.append("<h3>🎯 推荐奖励</h3>\n");
            content.append("<p>感谢您通过推荐链接订阅我们的服务，您和推荐人都将获得相应的奖励！</p>\n");
            content.append("</div>\n");
        }
        
        // 使用指南
        content.append("<h3>📖 使用指南</h3>\n");
        content.append("<p>为了帮助您更好地使用我们的服务，我们为您准备了以下资源：</p>\n");
        content.append("<ul>\n");
        content.append("<li>📚 <a href=\"#\">用户手册</a> - 详细的功能介绍和使用说明</li>\n");
        content.append("<li>🎥 <a href=\"#\">视频教程</a> - 直观的操作演示</li>\n");
        content.append("<li>💬 <a href=\"#\">在线客服</a> - 7x24小时技术支持</li>\n");
        content.append("<li>📧 <a href=\"#\">联系我们</a> - <EMAIL></li>\n");
        content.append("</ul>\n");
        
        content.append("<p>如有任何问题或建议，请随时联系我们的客服团队。</p>\n");
        content.append("<p>再次感谢您的信任，祝您使用愉快！</p>\n");
        content.append("</div>\n");
        
        // 邮件尾部
        content.append("<div class=\"footer\">\n");
        content.append("<p>此邮件由系统自动发送，请勿直接回复。</p>\n");
        content.append("<p>&copy; 2024 Nexus团队. 保留所有权利.</p>\n");
        content.append("</div>\n");
        
        content.append("</div>\n");
        content.append("</body>\n</html>");
        
        return content.toString();
    }
    
    /**
     * 构建状态变更邮件主题
     */
    private String buildStatusChangeEmailSubject(SubscriptionStatusChangedEvent event) {
        StringBuilder subject = new StringBuilder();
        subject.append("订阅状态变更通知 - ").append(event.getServiceName());
        
        if (event.isCancellation()) {
            subject.append(" [已取消]");
        } else if (event.isExpiration()) {
            subject.append(" [已过期]");
        } else if (event.isActivation()) {
            subject.append(" [已激活]");
        } else if (event.isSuspension()) {
            subject.append(" [已暂停]");
        }
        
        return subject.toString();
    }
    
    /**
     * 构建状态变更邮件内容
     */
    private String buildStatusChangeEmailContent(SubscriptionStatusChangedEvent event) {
        StringBuilder content = new StringBuilder();
        
        content.append("亲爱的 ").append(event.getUsername() != null ? event.getUsername() : "用户").append("，\n\n");
        content.append("您的订阅状态已发生变更，详情如下：\n\n");
        
        content.append("订阅信息：\n");
        content.append("- 订阅ID：").append(event.getSubscriptionId()).append("\n");
        content.append("- 服务名称：").append(event.getServiceName()).append("\n");
        content.append("- 原状态：").append(event.getOldStatus()).append("\n");
        content.append("- 新状态：").append(event.getNewStatus()).append("\n");
        content.append("- 变更原因：").append(event.getChangeReason()).append("\n");
        content.append("- 变更时间：").append(event.getChangeTime().format(DATE_FORMATTER)).append("\n");
        
        // 根据不同状态提供不同的提示信息
        if (event.isCancellation()) {
            content.append("\n❌ 订阅取消通知\n");
            content.append("您的订阅已被取消。如果这不是您的操作，请立即联系客服。\n");
            content.append("如需重新订阅，请登录系统进行操作。\n");
        } else if (event.isExpiration()) {
            content.append("\n⏰ 订阅过期通知\n");
            content.append("您的订阅已过期，相关服务已停止。\n");
            content.append("如需继续使用，请及时续费。续费后服务将立即恢复。\n");
        } else if (event.isActivation()) {
            content.append("\n✅ 订阅激活通知\n");
            content.append("您的订阅已成功激活，现在可以正常使用所有服务功能了。\n");
            content.append("感谢您的信任，祝您使用愉快！\n");
        } else if (event.isSuspension()) {
            content.append("\n⏸️ 订阅暂停通知\n");
            content.append("您的订阅已被暂停，服务暂时不可用。\n");
            content.append("如有疑问，请联系客服了解详情。\n");
        }
        
        content.append("\n如有任何问题，请联系我们的客服团队：\n");
        content.append("📧 邮箱：<EMAIL>\n");
        content.append("📞 电话：400-123-4567\n");
        content.append("💬 在线客服：登录系统后点击客服按钮\n");
        
        content.append("\nNexus团队\n");
        content.append(LocalDateTime.now().format(DATE_FORMATTER));
        
        return content.toString();
    }
    
    /**
     * 构建续费邮件主题
     */
    private String buildRenewalEmailSubject(SubscriptionRenewedEvent event) {
        StringBuilder subject = new StringBuilder();
        subject.append("订阅续费成功 - ").append(event.getServiceName());
        
        if (event.isAutoRenewal()) {
            subject.append(" [自动续费]");
        } else {
            subject.append(" [手动续费]");
        }
        
        return subject.toString();
    }
    
    /**
     * 构建续费邮件内容
     */
    private String buildRenewalEmailContent(SubscriptionRenewedEvent event) {
        StringBuilder content = new StringBuilder();
        
        content.append("亲爱的 ").append(event.getUsername() != null ? event.getUsername() : "用户").append("，\n\n");
        content.append("🎉 您的订阅续费成功！感谢您继续选择我们的服务。\n\n");
        
        content.append("续费详情：\n");
        content.append("- 订阅ID：").append(event.getSubscriptionId()).append("\n");
        content.append("- 服务名称：").append(event.getServiceName()).append("\n");
        content.append("- 续费类型：").append(event.getRenewalType().getDescription()).append("\n");
        content.append("- 续费金额：").append(event.getActualPaymentAmount())
               .append(" ").append(event.getCurrency()).append("\n");
        content.append("- 原到期时间：").append(event.getOldEndDate().format(DATE_FORMATTER)).append("\n");
        content.append("- 新到期时间：").append(event.getNewEndDate().format(DATE_FORMATTER)).append("\n");
        content.append("- 续费时间：").append(event.getRenewalTime().format(DATE_FORMATTER)).append("\n");
        
        if (event.hasDiscount()) {
            content.append("- 享受折扣：").append(event.getDiscountAmount())
                   .append(" ").append(event.getCurrency()).append("\n");
        }
        
        long days = event.getRenewalDays();
        if (days > 0) {
            content.append("- 续费天数：").append(days).append("天\n");
        }
        
        if (event.isAutoRenewal()) {
            content.append("\n🔄 自动续费提醒\n");
            content.append("您的订阅已设置为自动续费，下次续费将在到期前自动进行。\n");
            content.append("如需取消自动续费，请登录系统进行设置。\n");
        }
        
        content.append("\n💡 温馨提示\n");
        content.append("- 续费后您可以继续享受所有服务功能\n");
        content.append("- 如需发票，请在系统中申请开具\n");
        content.append("- 续费记录可在\"我的订阅\"中查看\n");
        
        content.append("\n感谢您的信任与支持！\n\n");
        content.append("Nexus团队\n");
        content.append(LocalDateTime.now().format(DATE_FORMATTER));
        
        return content.toString();
    }
    
    /**
     * 构建到期提醒邮件内容
     */
    private String buildExpirationReminderContent(String username, String serviceName, 
                                                 LocalDateTime expirationDate, Long subscriptionId) {
        StringBuilder content = new StringBuilder();
        
        content.append("亲爱的 ").append(username != null ? username : "用户").append("，\n\n");
        content.append("⏰ 您的订阅即将到期，请及时续费以免影响服务使用。\n\n");
        
        content.append("订阅信息：\n");
        content.append("- 订阅ID：").append(subscriptionId).append("\n");
        content.append("- 服务名称：").append(serviceName).append("\n");
        content.append("- 到期时间：").append(expirationDate.format(DATE_FORMATTER)).append("\n");
        
        long daysUntilExpiration = java.time.Duration.between(LocalDateTime.now(), expirationDate).toDays();
        if (daysUntilExpiration > 0) {
            content.append("- 剩余天数：").append(daysUntilExpiration).append("天\n");
        } else {
            content.append("- 状态：已过期\n");
        }
        
        content.append("\n🚀 续费优势\n");
        content.append("- 继续享受所有高级功能\n");
        content.append("- 数据和设置完整保留\n");
        content.append("- 优先技术支持服务\n");
        content.append("- 新功能抢先体验\n");
        
        content.append("\n💰 续费方式\n");
        content.append("1. 登录系统，进入\"我的订阅\"页面\n");
        content.append("2. 选择续费时长和支付方式\n");
        content.append("3. 完成支付，立即生效\n");
        
        content.append("\n如有任何问题，请联系客服：\n");
        content.append("📧 <EMAIL>\n");
        content.append("📞 400-123-4567\n");
        
        content.append("\n感谢您的支持！\n\n");
        content.append("Nexus团队\n");
        content.append(LocalDateTime.now().format(DATE_FORMATTER));
        
        return content.toString();
    }
}
