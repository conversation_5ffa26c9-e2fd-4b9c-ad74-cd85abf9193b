package com.nexus.common.service;

import com.nexus.common.event.SubscriptionCreatedEvent;
import com.nexus.common.event.SubscriptionStatusChangedEvent;
import com.nexus.common.event.SubscriptionRenewedEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 订阅统计聚合服务
 * 负责订阅数据的统计聚合和分析功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SubscriptionStatisticsService {
    
    private final RedisTemplate<String, Object> redisTemplate;
    
    // Redis键前缀
    private static final String SUBSCRIPTION_STATS_PREFIX = "stats:subscription:";
    private static final String REVENUE_STATS_PREFIX = "stats:revenue:";
    private static final String USER_STATS_PREFIX = "stats:user:";
    
    // 时间格式
    private static final DateTimeFormatter DAY_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter MONTH_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM");
    private static final DateTimeFormatter YEAR_FORMAT = DateTimeFormatter.ofPattern("yyyy");
    
    // 默认过期时间
    private static final long DAILY_EXPIRE_DAYS = 90; // 90天
    private static final long MONTHLY_EXPIRE_DAYS = 365; // 1年
    private static final long YEARLY_EXPIRE_DAYS = 1825; // 5年
    
    /**
     * 聚合订阅创建统计
     */
    public void aggregateSubscriptionCreated(SubscriptionCreatedEvent event) {
        try {
            log.debug("聚合订阅创建统计: subscriptionId={}, userId={}, serviceName={}", 
                    event.getSubscriptionId(), event.getUserId(), event.getServiceName());
            
            LocalDateTime createTime = event.getStartDate() != null ? 
                    event.getStartDate() : LocalDateTime.now();
            
            String dayKey = DAY_FORMAT.format(createTime);
            String monthKey = MONTH_FORMAT.format(createTime);
            String yearKey = YEAR_FORMAT.format(createTime);
            
            // 按天统计
            aggregateSubscriptionCreatedByDay(event, dayKey);
            
            // 按月统计
            aggregateSubscriptionCreatedByMonth(event, monthKey);
            
            // 按年统计
            aggregateSubscriptionCreatedByYear(event, yearKey);
            
            // 用户统计
            updateUserSubscriptionStats(event);
            
            // 收入统计
            if (event.isPaidSubscription()) {
                aggregateRevenueStats(event, dayKey, monthKey, yearKey);
            }
            
            log.debug("订阅创建统计聚合完成: subscriptionId={}", event.getSubscriptionId());
            
        } catch (Exception e) {
            log.error("聚合订阅创建统计失败: subscriptionId={} - {}", 
                    event.getSubscriptionId(), e.getMessage(), e);
        }
    }
    
    /**
     * 聚合订阅状态变更统计
     */
    public void aggregateSubscriptionStatusChanged(SubscriptionStatusChangedEvent event) {
        try {
            log.debug("聚合订阅状态变更统计: subscriptionId={}, oldStatus={}, newStatus={}", 
                    event.getSubscriptionId(), event.getOldStatus(), event.getNewStatus());
            
            LocalDateTime changeTime = event.getChangeTime() != null ? 
                    event.getChangeTime() : LocalDateTime.now();
            
            String dayKey = DAY_FORMAT.format(changeTime);
            String monthKey = MONTH_FORMAT.format(changeTime);
            
            // 按天统计状态变更
            aggregateStatusChangeByDay(event, dayKey);
            
            // 按月统计状态变更
            aggregateStatusChangeByMonth(event, monthKey);
            
            // 特殊状态统计
            if (event.isCancellation()) {
                aggregateCancellationStats(event, dayKey, monthKey);
            } else if (event.isExpiration()) {
                aggregateExpirationStats(event, dayKey, monthKey);
            } else if (event.isActivation()) {
                aggregateActivationStats(event, dayKey, monthKey);
            }
            
            log.debug("订阅状态变更统计聚合完成: subscriptionId={}", event.getSubscriptionId());
            
        } catch (Exception e) {
            log.error("聚合订阅状态变更统计失败: subscriptionId={} - {}", 
                    event.getSubscriptionId(), e.getMessage(), e);
        }
    }
    
    /**
     * 聚合订阅续费统计
     */
    public void aggregateSubscriptionRenewed(SubscriptionRenewedEvent event) {
        try {
            log.debug("聚合订阅续费统计: subscriptionId={}, renewalType={}, amount={}", 
                    event.getSubscriptionId(), event.getRenewalType(), event.getRenewalAmount());
            
            LocalDateTime renewalTime = event.getRenewalTime() != null ? 
                    event.getRenewalTime() : LocalDateTime.now();
            
            String dayKey = DAY_FORMAT.format(renewalTime);
            String monthKey = MONTH_FORMAT.format(renewalTime);
            String yearKey = YEAR_FORMAT.format(renewalTime);
            
            // 按天统计续费
            aggregateRenewalByDay(event, dayKey);
            
            // 按月统计续费
            aggregateRenewalByMonth(event, monthKey);
            
            // 按年统计续费
            aggregateRenewalByYear(event, yearKey);
            
            // 续费收入统计
            aggregateRenewalRevenueStats(event, dayKey, monthKey, yearKey);
            
            // 用户续费统计
            updateUserRenewalStats(event);
            
            log.debug("订阅续费统计聚合完成: subscriptionId={}", event.getSubscriptionId());
            
        } catch (Exception e) {
            log.error("聚合订阅续费统计失败: subscriptionId={} - {}", 
                    event.getSubscriptionId(), e.getMessage(), e);
        }
    }
    
    /**
     * 按天聚合订阅创建
     */
    private void aggregateSubscriptionCreatedByDay(SubscriptionCreatedEvent event, String dayKey) {
        String baseKey = SUBSCRIPTION_STATS_PREFIX + "day:" + dayKey;
        
        // 总订阅数
        redisTemplate.opsForValue().increment(baseKey + ":total", 1);
        
        // 按订阅类型统计
        if (event.getSubscriptionType() != null) {
            String typeKey = baseKey + ":type:" + event.getSubscriptionType().name();
            redisTemplate.opsForValue().increment(typeKey, 1);
            redisTemplate.expire(typeKey, DAILY_EXPIRE_DAYS, TimeUnit.DAYS);
        }
        
        // 按计费周期统计
        if (event.getBillingCycle() != null) {
            String cycleKey = baseKey + ":cycle:" + event.getBillingCycle().name();
            redisTemplate.opsForValue().increment(cycleKey, 1);
            redisTemplate.expire(cycleKey, DAILY_EXPIRE_DAYS, TimeUnit.DAYS);
        }
        
        // 试用订阅统计
        if (event.isTrialSubscription()) {
            redisTemplate.opsForValue().increment(baseKey + ":trial", 1);
        }
        
        // 付费订阅统计
        if (event.isPaidSubscription()) {
            redisTemplate.opsForValue().increment(baseKey + ":paid", 1);
        }
        
        // 推荐订阅统计
        if (event.isReferralSubscription()) {
            redisTemplate.opsForValue().increment(baseKey + ":referral", 1);
        }
        
        // 设置过期时间
        redisTemplate.expire(baseKey + ":total", DAILY_EXPIRE_DAYS, TimeUnit.DAYS);
        redisTemplate.expire(baseKey + ":trial", DAILY_EXPIRE_DAYS, TimeUnit.DAYS);
        redisTemplate.expire(baseKey + ":paid", DAILY_EXPIRE_DAYS, TimeUnit.DAYS);
        redisTemplate.expire(baseKey + ":referral", DAILY_EXPIRE_DAYS, TimeUnit.DAYS);
    }
    
    /**
     * 按月聚合订阅创建
     */
    private void aggregateSubscriptionCreatedByMonth(SubscriptionCreatedEvent event, String monthKey) {
        String baseKey = SUBSCRIPTION_STATS_PREFIX + "month:" + monthKey;
        
        // 总订阅数
        redisTemplate.opsForValue().increment(baseKey + ":total", 1);
        
        // 按订阅类型统计
        if (event.getSubscriptionType() != null) {
            String typeKey = baseKey + ":type:" + event.getSubscriptionType().name();
            redisTemplate.opsForValue().increment(typeKey, 1);
            redisTemplate.expire(typeKey, MONTHLY_EXPIRE_DAYS, TimeUnit.DAYS);
        }
        
        // 新用户订阅统计
        String newUserKey = baseKey + ":new_users";
        redisTemplate.opsForSet().add(newUserKey, event.getUserId());
        redisTemplate.expire(newUserKey, MONTHLY_EXPIRE_DAYS, TimeUnit.DAYS);
        
        // 设置过期时间
        redisTemplate.expire(baseKey + ":total", MONTHLY_EXPIRE_DAYS, TimeUnit.DAYS);
    }
    
    /**
     * 按年聚合订阅创建
     */
    private void aggregateSubscriptionCreatedByYear(SubscriptionCreatedEvent event, String yearKey) {
        String baseKey = SUBSCRIPTION_STATS_PREFIX + "year:" + yearKey;
        
        // 总订阅数
        redisTemplate.opsForValue().increment(baseKey + ":total", 1);
        
        // 年度新用户统计
        String newUserKey = baseKey + ":new_users";
        redisTemplate.opsForSet().add(newUserKey, event.getUserId());
        redisTemplate.expire(newUserKey, YEARLY_EXPIRE_DAYS, TimeUnit.DAYS);
        
        // 设置过期时间
        redisTemplate.expire(baseKey + ":total", YEARLY_EXPIRE_DAYS, TimeUnit.DAYS);
    }
    
    /**
     * 更新用户订阅统计
     */
    private void updateUserSubscriptionStats(SubscriptionCreatedEvent event) {
        String userStatsKey = USER_STATS_PREFIX + "subscription:" + event.getUserId();
        
        // 用户总订阅数
        redisTemplate.opsForHash().increment(userStatsKey, "totalSubscriptions", 1);
        
        // 用户最新订阅时间
        redisTemplate.opsForHash().put(userStatsKey, "lastSubscriptionTime", 
                LocalDateTime.now().toString());
        
        // 按订阅类型统计
        if (event.getSubscriptionType() != null) {
            redisTemplate.opsForHash().increment(userStatsKey, 
                    "type_" + event.getSubscriptionType().name(), 1);
        }
        
        // 设置过期时间
        redisTemplate.expire(userStatsKey, YEARLY_EXPIRE_DAYS, TimeUnit.DAYS);
    }
    
    /**
     * 聚合收入统计
     */
    private void aggregateRevenueStats(SubscriptionCreatedEvent event, String dayKey, 
                                      String monthKey, String yearKey) {
        if (event.getActualPaymentAmount() == null) {
            return;
        }
        
        BigDecimal amount = event.getActualPaymentAmount();
        String currency = event.getCurrency() != null ? event.getCurrency() : "CNY";
        
        // 按天收入统计
        String dayRevenueKey = REVENUE_STATS_PREFIX + "day:" + dayKey + ":" + currency;
        redisTemplate.opsForValue().increment(dayRevenueKey, amount.doubleValue());
        redisTemplate.expire(dayRevenueKey, DAILY_EXPIRE_DAYS, TimeUnit.DAYS);
        
        // 按月收入统计
        String monthRevenueKey = REVENUE_STATS_PREFIX + "month:" + monthKey + ":" + currency;
        redisTemplate.opsForValue().increment(monthRevenueKey, amount.doubleValue());
        redisTemplate.expire(monthRevenueKey, MONTHLY_EXPIRE_DAYS, TimeUnit.DAYS);
        
        // 按年收入统计
        String yearRevenueKey = REVENUE_STATS_PREFIX + "year:" + yearKey + ":" + currency;
        redisTemplate.opsForValue().increment(yearRevenueKey, amount.doubleValue());
        redisTemplate.expire(yearRevenueKey, YEARLY_EXPIRE_DAYS, TimeUnit.DAYS);
        
        // 新订阅收入统计
        String newSubscriptionRevenueKey = REVENUE_STATS_PREFIX + "new_subscription:" + dayKey + ":" + currency;
        redisTemplate.opsForValue().increment(newSubscriptionRevenueKey, amount.doubleValue());
        redisTemplate.expire(newSubscriptionRevenueKey, DAILY_EXPIRE_DAYS, TimeUnit.DAYS);
    }
    
    /**
     * 按天聚合状态变更
     */
    private void aggregateStatusChangeByDay(SubscriptionStatusChangedEvent event, String dayKey) {
        String baseKey = SUBSCRIPTION_STATS_PREFIX + "status_change:day:" + dayKey;
        
        // 总状态变更数
        redisTemplate.opsForValue().increment(baseKey + ":total", 1);
        
        // 按新状态统计
        String newStatusKey = baseKey + ":new_status:" + event.getNewStatus();
        redisTemplate.opsForValue().increment(newStatusKey, 1);
        
        // 设置过期时间
        redisTemplate.expire(baseKey + ":total", DAILY_EXPIRE_DAYS, TimeUnit.DAYS);
        redisTemplate.expire(newStatusKey, DAILY_EXPIRE_DAYS, TimeUnit.DAYS);
    }
    
    /**
     * 按月聚合状态变更
     */
    private void aggregateStatusChangeByMonth(SubscriptionStatusChangedEvent event, String monthKey) {
        String baseKey = SUBSCRIPTION_STATS_PREFIX + "status_change:month:" + monthKey;
        
        // 总状态变更数
        redisTemplate.opsForValue().increment(baseKey + ":total", 1);
        
        // 按新状态统计
        String newStatusKey = baseKey + ":new_status:" + event.getNewStatus();
        redisTemplate.opsForValue().increment(newStatusKey, 1);
        
        // 设置过期时间
        redisTemplate.expire(baseKey + ":total", MONTHLY_EXPIRE_DAYS, TimeUnit.DAYS);
        redisTemplate.expire(newStatusKey, MONTHLY_EXPIRE_DAYS, TimeUnit.DAYS);
    }
    
    /**
     * 聚合取消统计
     */
    private void aggregateCancellationStats(SubscriptionStatusChangedEvent event, 
                                           String dayKey, String monthKey) {
        // 按天取消统计
        String dayCancelKey = SUBSCRIPTION_STATS_PREFIX + "cancellation:day:" + dayKey;
        redisTemplate.opsForValue().increment(dayCancelKey + ":total", 1);
        redisTemplate.expire(dayCancelKey + ":total", DAILY_EXPIRE_DAYS, TimeUnit.DAYS);
        
        // 按月取消统计
        String monthCancelKey = SUBSCRIPTION_STATS_PREFIX + "cancellation:month:" + monthKey;
        redisTemplate.opsForValue().increment(monthCancelKey + ":total", 1);
        redisTemplate.expire(monthCancelKey + ":total", MONTHLY_EXPIRE_DAYS, TimeUnit.DAYS);
        
        // 取消原因统计
        if (event.getChangeReason() != null) {
            String reasonKey = dayCancelKey + ":reason:" + event.getChangeReason();
            redisTemplate.opsForValue().increment(reasonKey, 1);
            redisTemplate.expire(reasonKey, DAILY_EXPIRE_DAYS, TimeUnit.DAYS);
        }
    }
    
    /**
     * 聚合过期统计
     */
    private void aggregateExpirationStats(SubscriptionStatusChangedEvent event, 
                                         String dayKey, String monthKey) {
        // 按天过期统计
        String dayExpireKey = SUBSCRIPTION_STATS_PREFIX + "expiration:day:" + dayKey;
        redisTemplate.opsForValue().increment(dayExpireKey + ":total", 1);
        redisTemplate.expire(dayExpireKey + ":total", DAILY_EXPIRE_DAYS, TimeUnit.DAYS);
        
        // 按月过期统计
        String monthExpireKey = SUBSCRIPTION_STATS_PREFIX + "expiration:month:" + monthKey;
        redisTemplate.opsForValue().increment(monthExpireKey + ":total", 1);
        redisTemplate.expire(monthExpireKey + ":total", MONTHLY_EXPIRE_DAYS, TimeUnit.DAYS);
    }
    
    /**
     * 聚合激活统计
     */
    private void aggregateActivationStats(SubscriptionStatusChangedEvent event, 
                                         String dayKey, String monthKey) {
        // 按天激活统计
        String dayActiveKey = SUBSCRIPTION_STATS_PREFIX + "activation:day:" + dayKey;
        redisTemplate.opsForValue().increment(dayActiveKey + ":total", 1);
        redisTemplate.expire(dayActiveKey + ":total", DAILY_EXPIRE_DAYS, TimeUnit.DAYS);
        
        // 按月激活统计
        String monthActiveKey = SUBSCRIPTION_STATS_PREFIX + "activation:month:" + monthKey;
        redisTemplate.opsForValue().increment(monthActiveKey + ":total", 1);
        redisTemplate.expire(monthActiveKey + ":total", MONTHLY_EXPIRE_DAYS, TimeUnit.DAYS);
    }
    
    /**
     * 按天聚合续费
     */
    private void aggregateRenewalByDay(SubscriptionRenewedEvent event, String dayKey) {
        String baseKey = SUBSCRIPTION_STATS_PREFIX + "renewal:day:" + dayKey;
        
        // 总续费数
        redisTemplate.opsForValue().increment(baseKey + ":total", 1);
        
        // 按续费类型统计
        String typeKey = baseKey + ":type:" + event.getRenewalType().name();
        redisTemplate.opsForValue().increment(typeKey, 1);
        
        // 设置过期时间
        redisTemplate.expire(baseKey + ":total", DAILY_EXPIRE_DAYS, TimeUnit.DAYS);
        redisTemplate.expire(typeKey, DAILY_EXPIRE_DAYS, TimeUnit.DAYS);
    }
    
    /**
     * 按月聚合续费
     */
    private void aggregateRenewalByMonth(SubscriptionRenewedEvent event, String monthKey) {
        String baseKey = SUBSCRIPTION_STATS_PREFIX + "renewal:month:" + monthKey;
        
        // 总续费数
        redisTemplate.opsForValue().increment(baseKey + ":total", 1);
        
        // 续费用户统计
        String renewalUserKey = baseKey + ":users";
        redisTemplate.opsForSet().add(renewalUserKey, event.getUserId());
        
        // 设置过期时间
        redisTemplate.expire(baseKey + ":total", MONTHLY_EXPIRE_DAYS, TimeUnit.DAYS);
        redisTemplate.expire(renewalUserKey, MONTHLY_EXPIRE_DAYS, TimeUnit.DAYS);
    }
    
    /**
     * 按年聚合续费
     */
    private void aggregateRenewalByYear(SubscriptionRenewedEvent event, String yearKey) {
        String baseKey = SUBSCRIPTION_STATS_PREFIX + "renewal:year:" + yearKey;
        
        // 总续费数
        redisTemplate.opsForValue().increment(baseKey + ":total", 1);
        
        // 设置过期时间
        redisTemplate.expire(baseKey + ":total", YEARLY_EXPIRE_DAYS, TimeUnit.DAYS);
    }
    
    /**
     * 聚合续费收入统计
     */
    private void aggregateRenewalRevenueStats(SubscriptionRenewedEvent event, String dayKey, 
                                             String monthKey, String yearKey) {
        if (event.getRenewalAmount() == null) {
            return;
        }
        
        BigDecimal amount = event.getActualPaymentAmount();
        String currency = event.getCurrency() != null ? event.getCurrency() : "CNY";
        
        // 按天续费收入统计
        String dayRevenueKey = REVENUE_STATS_PREFIX + "renewal:day:" + dayKey + ":" + currency;
        redisTemplate.opsForValue().increment(dayRevenueKey, amount.doubleValue());
        redisTemplate.expire(dayRevenueKey, DAILY_EXPIRE_DAYS, TimeUnit.DAYS);
        
        // 按月续费收入统计
        String monthRevenueKey = REVENUE_STATS_PREFIX + "renewal:month:" + monthKey + ":" + currency;
        redisTemplate.opsForValue().increment(monthRevenueKey, amount.doubleValue());
        redisTemplate.expire(monthRevenueKey, MONTHLY_EXPIRE_DAYS, TimeUnit.DAYS);
        
        // 按年续费收入统计
        String yearRevenueKey = REVENUE_STATS_PREFIX + "renewal:year:" + yearKey + ":" + currency;
        redisTemplate.opsForValue().increment(yearRevenueKey, amount.doubleValue());
        redisTemplate.expire(yearRevenueKey, YEARLY_EXPIRE_DAYS, TimeUnit.DAYS);
    }
    
    /**
     * 更新用户续费统计
     */
    private void updateUserRenewalStats(SubscriptionRenewedEvent event) {
        String userStatsKey = USER_STATS_PREFIX + "renewal:" + event.getUserId();
        
        // 用户总续费次数
        redisTemplate.opsForHash().increment(userStatsKey, "totalRenewals", 1);
        
        // 用户最新续费时间
        redisTemplate.opsForHash().put(userStatsKey, "lastRenewalTime", 
                LocalDateTime.now().toString());
        
        // 按续费类型统计
        redisTemplate.opsForHash().increment(userStatsKey, 
                "type_" + event.getRenewalType().name(), 1);
        
        // 用户续费总金额
        if (event.getActualPaymentAmount() != null) {
            redisTemplate.opsForHash().increment(userStatsKey, "totalRenewalAmount", 
                    event.getActualPaymentAmount().doubleValue());
        }
        
        // 设置过期时间
        redisTemplate.expire(userStatsKey, YEARLY_EXPIRE_DAYS, TimeUnit.DAYS);
    }
    
    /**
     * 获取订阅统计概览
     */
    public Map<String, Object> getSubscriptionStatisticsOverview(String dateRange) {
        try {
            Map<String, Object> overview = new HashMap<>();
            String timeKey = getTimeKey(dateRange);
            String baseKey = SUBSCRIPTION_STATS_PREFIX + dateRange + ":" + timeKey;
            
            // 总订阅数
            Object totalSubscriptions = redisTemplate.opsForValue().get(baseKey + ":total");
            overview.put("totalSubscriptions", totalSubscriptions != null ? totalSubscriptions : 0);
            
            // 试用订阅数
            Object trialSubscriptions = redisTemplate.opsForValue().get(baseKey + ":trial");
            overview.put("trialSubscriptions", trialSubscriptions != null ? trialSubscriptions : 0);
            
            // 付费订阅数
            Object paidSubscriptions = redisTemplate.opsForValue().get(baseKey + ":paid");
            overview.put("paidSubscriptions", paidSubscriptions != null ? paidSubscriptions : 0);
            
            // 推荐订阅数
            Object referralSubscriptions = redisTemplate.opsForValue().get(baseKey + ":referral");
            overview.put("referralSubscriptions", referralSubscriptions != null ? referralSubscriptions : 0);
            
            overview.put("dateRange", dateRange);
            overview.put("generatedAt", LocalDateTime.now().toString());
            
            log.debug("获取订阅统计概览完成: dateRange={}", dateRange);
            return overview;
            
        } catch (Exception e) {
            log.error("获取订阅统计概览失败: dateRange={} - {}", dateRange, e.getMessage(), e);
            return new HashMap<>();
        }
    }
    
    /**
     * 获取时间键
     */
    private String getTimeKey(String dateRange) {
        LocalDateTime now = LocalDateTime.now();
        switch (dateRange.toLowerCase()) {
            case "day":
                return DAY_FORMAT.format(now);
            case "month":
                return MONTH_FORMAT.format(now);
            case "year":
                return YEAR_FORMAT.format(now);
            default:
                return DAY_FORMAT.format(now);
        }
    }
}
