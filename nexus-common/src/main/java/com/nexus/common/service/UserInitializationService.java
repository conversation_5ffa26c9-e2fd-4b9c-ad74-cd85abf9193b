package com.nexus.common.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;

/**
 * 用户初始化服务
 * 确保用户注册后的数据完整性和一致性
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserInitializationService {
    
    private final RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 用户初始化状态枚举
     */
    public enum InitializationStatus {
        PENDING("初始化中"),
        COMPLETED("初始化完成"),
        FAILED("初始化失败");
        
        private final String description;
        
        InitializationStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 标记用户初始化开始
     */
    public void markInitializationStarted(String userId) {
        String key = "user:init:status:" + userId;
        redisTemplate.opsForValue().set(key, InitializationStatus.PENDING, Duration.ofMinutes(10));
        log.info("标记用户初始化开始: userId={}", userId);
    }
    
    /**
     * 标记用户初始化完成
     */
    public void markInitializationCompleted(String userId) {
        String key = "user:init:status:" + userId;
        redisTemplate.opsForValue().set(key, InitializationStatus.COMPLETED, Duration.ofHours(1));
        log.info("标记用户初始化完成: userId={}", userId);
    }
    
    /**
     * 标记用户初始化失败
     */
    public void markInitializationFailed(String userId, String reason) {
        String key = "user:init:status:" + userId;
        redisTemplate.opsForValue().set(key, InitializationStatus.FAILED, Duration.ofHours(1));
        
        // 记录失败原因
        String reasonKey = "user:init:failure:" + userId;
        redisTemplate.opsForValue().set(reasonKey, reason, Duration.ofHours(1));
        
        log.error("标记用户初始化失败: userId={}, reason={}", userId, reason);
    }
    
    /**
     * 检查用户初始化状态
     */
    public InitializationStatus getInitializationStatus(String userId) {
        String key = "user:init:status:" + userId;
        Object status = redisTemplate.opsForValue().get(key);
        
        if (status == null) {
            // 如果没有初始化状态记录，认为是已完成（兼容老数据）
            return InitializationStatus.COMPLETED;
        }
        
        return (InitializationStatus) status;
    }
    
    /**
     * 等待用户初始化完成
     */
    public boolean waitForInitializationComplete(String userId, long timeoutSeconds) {
        long startTime = System.currentTimeMillis();
        long timeoutMillis = timeoutSeconds * 1000;
        
        while (System.currentTimeMillis() - startTime < timeoutMillis) {
            InitializationStatus status = getInitializationStatus(userId);
            
            if (status == InitializationStatus.COMPLETED) {
                return true;
            } else if (status == InitializationStatus.FAILED) {
                log.warn("用户初始化失败: userId={}", userId);
                return false;
            }
            
            try {
                Thread.sleep(100); // 等待100ms后重试
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }
        
        log.warn("等待用户初始化超时: userId={}, timeoutSeconds={}", userId, timeoutSeconds);
        return false;
    }
    
    /**
     * 异步等待用户初始化完成
     */
    public CompletableFuture<Boolean> waitForInitializationCompleteAsync(String userId, long timeoutSeconds) {
        return CompletableFuture.supplyAsync(() -> {
            return waitForInitializationComplete(userId, timeoutSeconds);
        });
    }
    
    /**
     * 检查用户是否可以安全登录
     */
    public boolean isUserReadyForLogin(String userId) {
        InitializationStatus status = getInitializationStatus(userId);
        
        // 只有初始化完成的用户才能安全登录
        if (status == InitializationStatus.COMPLETED) {
            return true;
        }
        
        // 如果初始化还在进行中，检查是否超时
        if (status == InitializationStatus.PENDING) {
            log.warn("用户初始化仍在进行中: userId={}", userId);
            return false;
        }
        
        // 如果初始化失败，不允许登录
        if (status == InitializationStatus.FAILED) {
            log.error("用户初始化失败，不允许登录: userId={}", userId);
            return false;
        }
        
        return true;
    }
    
    /**
     * 强制完成用户初始化（紧急情况使用）
     */
    public void forceCompleteInitialization(String userId, String reason) {
        markInitializationCompleted(userId);
        
        // 记录强制完成的原因
        String forceKey = "user:init:forced:" + userId;
        redisTemplate.opsForValue().set(forceKey, reason, Duration.ofDays(1));
        
        log.warn("强制完成用户初始化: userId={}, reason={}", userId, reason);
    }
    
    /**
     * 重试用户初始化
     */
    public void retryInitialization(String userId) {
        // 清除之前的状态
        String statusKey = "user:init:status:" + userId;
        String reasonKey = "user:init:failure:" + userId;
        
        redisTemplate.delete(statusKey);
        redisTemplate.delete(reasonKey);
        
        // 重新标记为初始化中
        markInitializationStarted(userId);
        
        log.info("重试用户初始化: userId={}", userId);
    }
    
    /**
     * 获取初始化失败原因
     */
    public String getInitializationFailureReason(String userId) {
        String reasonKey = "user:init:failure:" + userId;
        Object reason = redisTemplate.opsForValue().get(reasonKey);
        return reason != null ? reason.toString() : "未知原因";
    }
    
    /**
     * 清理用户初始化状态（用户删除时调用）
     */
    public void cleanupInitializationStatus(String userId) {
        String statusKey = "user:init:status:" + userId;
        String reasonKey = "user:init:failure:" + userId;
        String forceKey = "user:init:forced:" + userId;
        
        redisTemplate.delete(statusKey);
        redisTemplate.delete(reasonKey);
        redisTemplate.delete(forceKey);
        
        log.info("清理用户初始化状态: userId={}", userId);
    }
}
