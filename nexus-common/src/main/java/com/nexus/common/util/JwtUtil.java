package com.nexus.common.util;

import com.nexus.common.dto.UserDTO;
import com.nexus.common.exception.NexusException;
import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.crypto.SecretKey;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * JWT工具类
 * 提供JWT token的生成、解析和验证功能
 */
@Component
public class JwtUtil {

    private static final Logger logger = LoggerFactory.getLogger(JwtUtil.class);

    @Value("${nexus.jwt.secret:nexus-default-secret-key-for-jwt-token-generation-and-validation}")
    private String jwtSecret;

    @Value("${nexus.jwt.expiration:86400}") // 默认24小时
    private int jwtExpirationInSeconds;

    @Value("${nexus.jwt.refresh-expiration:604800}") // 默认7天
    private int refreshExpirationInSeconds;

    private SecretKey key;

    @PostConstruct
    public void init() {
        // 确保密钥长度足够（至少256位）
        if (jwtSecret.length() < 32) {
            jwtSecret = jwtSecret + "0123456789abcdef0123456789abcdef";
        }
        this.key = Keys.hmacShaKeyFor(jwtSecret.getBytes());
    }

    /**
     * 生成访问令牌
     */
    public String generateAccessToken(String userId, String username, String role) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        claims.put("username", username);
        claims.put("role", role);
        claims.put("type", "access");

        return createToken(claims, userId, jwtExpirationInSeconds);
    }

    /**
     * 生成刷新令牌
     */
    public String generateRefreshToken(String userId) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        claims.put("type", "refresh");

        return createToken(claims, userId, refreshExpirationInSeconds);
    }

    /**
     * 生成API密钥令牌（长期有效）
     */
    public String generateApiKeyToken(String userId, String username, String role, int expirationDays) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        claims.put("username", username);
        claims.put("role", role);
        claims.put("type", "api_key");

        return createToken(claims, userId, expirationDays * 24 * 3600);
    }

    /**
     * 创建JWT令牌
     */
    private String createToken(Map<String, Object> claims, String subject, int expirationInSeconds) {
        Date now = new Date();
        Date expiration = new Date(now.getTime() + expirationInSeconds * 1000L);

        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuedAt(now)
                .setExpiration(expiration)
                .signWith(key, SignatureAlgorithm.HS256)
                .compact();
    }

    /**
     * 从令牌中提取用户ID
     */
    public String extractUserId(String token) {
        return extractClaim(token, claims -> claims.get("userId", String.class));
    }

    /**
     * 从令牌中提取用户名
     */
    public String extractUsername(String token) {
        return extractClaim(token, claims -> claims.get("username", String.class));
    }

    /**
     * 从令牌中提取用户角色
     */
    public String extractRole(String token) {
        return extractClaim(token, claims -> claims.get("role", String.class));
    }

    /**
     * 从令牌中提取令牌类型
     */
    public String extractTokenType(String token) {
        return extractClaim(token, claims -> claims.get("type", String.class));
    }

    /**
     * 从令牌中提取过期时间
     */
    public Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }

    /**
     * 从令牌中提取指定声明
     */
    public <T> T extractClaim(String token, ClaimsResolver<T> claimsResolver) {
        final Claims claims = extractAllClaims(token);
        return claimsResolver.resolve(claims);
    }

    /**
     * 提取所有声明
     */
    private Claims extractAllClaims(String token) {
        try {
            return Jwts.parserBuilder()
                    .setSigningKey(key)
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
        } catch (ExpiredJwtException e) {
            logger.warn("JWT token expired: {}", e.getMessage());
            throw new NexusException.AuthenticationException("Token已过期");
        } catch (UnsupportedJwtException e) {
            logger.warn("Unsupported JWT token: {}", e.getMessage());
            throw new NexusException.AuthenticationException("不支持的Token格式");
        } catch (MalformedJwtException e) {
            logger.warn("Malformed JWT token: {}", e.getMessage());
            throw new NexusException.AuthenticationException("Token格式错误");
        } catch (SecurityException e) {
            logger.warn("Invalid JWT signature: {}", e.getMessage());
            throw new NexusException.AuthenticationException("Token签名无效");
        } catch (IllegalArgumentException e) {
            logger.warn("JWT token compact of handler are invalid: {}", e.getMessage());
            throw new NexusException.AuthenticationException("Token参数无效");
        }
    }

    /**
     * 检查令牌是否过期
     */
    public Boolean isTokenExpired(String token) {
        try {
            return extractExpiration(token).before(new Date());
        } catch (Exception e) {
            return true;
        }
    }

    /**
     * 验证令牌
     */
    public Boolean validateToken(String token, String userId) {
        try {
            final String tokenUserId = extractUserId(token);
            return (userId.equals(tokenUserId) && !isTokenExpired(token));
        } catch (Exception e) {
            logger.warn("Token validation failed: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 验证访问令牌
     */
    public Boolean validateAccessToken(String token) {
        try {
            String tokenType = extractTokenType(token);
            return "access".equals(tokenType) && !isTokenExpired(token);
        } catch (Exception e) {
            logger.warn("Access token validation failed: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 验证刷新令牌
     */
    public Boolean validateRefreshToken(String token) {
        try {
            String tokenType = extractTokenType(token);
            return "refresh".equals(tokenType) && !isTokenExpired(token);
        } catch (Exception e) {
            logger.warn("Refresh token validation failed: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 验证API密钥令牌
     */
    public Boolean validateApiKeyToken(String token) {
        try {
            String tokenType = extractTokenType(token);
            return "api_key".equals(tokenType) && !isTokenExpired(token);
        } catch (Exception e) {
            logger.warn("API key token validation failed: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取令牌剩余有效时间（秒）
     */
    public Long getTokenRemainingTime(String token) {
        try {
            Date expiration = extractExpiration(token);
            Date now = new Date();
            return Math.max(0, (expiration.getTime() - now.getTime()) / 1000);
        } catch (Exception e) {
            return 0L;
        }
    }

    /**
     * 检查令牌是否即将过期（1小时内）
     */
    public Boolean isTokenExpiringSoon(String token) {
        try {
            Long remainingTime = getTokenRemainingTime(token);
            return remainingTime != null && remainingTime < 3600; // 1小时
        } catch (Exception e) {
            return true;
        }
    }

    /**
     * 从HTTP请求中获取当前用户信息
     */
    public UserDTO getCurrentUserFromRequest(HttpServletRequest request) {
        try {
            String token = extractTokenFromRequest(request);
            if (token == null) {
                return null;
            }

            Claims claims = extractAllClaims(token);
            if (claims == null) {
                return null;
            }

            // 构建UserDTO
            UserDTO user = new UserDTO();
            user.setId(Long.valueOf(claims.getSubject()));
            user.setUsername(claims.get("username", String.class));
            user.setEmail(claims.get("email", String.class));

            return user;
        } catch (Exception e) {
            logger.warn("从请求中获取用户信息失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从HTTP请求中提取JWT令牌
     */
    private String extractTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }

    /**
     * 声明解析器接口
     */
    @FunctionalInterface
    public interface ClaimsResolver<T> {
        T resolve(Claims claims);
    }
}
