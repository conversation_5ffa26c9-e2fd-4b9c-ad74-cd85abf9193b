package com.nexus.common.util;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import java.util.UUID;

/**
 * 消息链路追踪工具类
 * 提供消息处理的链路追踪功能
 */
@Slf4j
public class MessageTraceUtil {
    
    // MDC键名常量
    public static final String TRACE_ID = "traceId";
    public static final String SPAN_ID = "spanId";
    public static final String EVENT_ID = "eventId";
    public static final String EVENT_TYPE = "eventType";
    public static final String USER_ID = "userId";
    public static final String SOURCE_SERVICE = "sourceService";
    public static final String CONSUMER_NAME = "consumerName";
    
    /**
     * 开始消息追踪
     */
    public static void startTrace(String eventId, String eventType, String userId, String sourceService) {
        String traceId = generateTraceId();
        String spanId = generateSpanId();
        
        MDC.put(TRACE_ID, traceId);
        MDC.put(SPAN_ID, spanId);
        MDC.put(EVENT_ID, eventId);
        MDC.put(EVENT_TYPE, eventType);
        
        if (userId != null) {
            MDC.put(USER_ID, userId);
        }
        if (sourceService != null) {
            MDC.put(SOURCE_SERVICE, sourceService);
        }
        
        log.debug("开始消息追踪: traceId={}, spanId={}, eventId={}, eventType={}", 
                traceId, spanId, eventId, eventType);
    }
    
    /**
     * 开始消费者追踪
     */
    public static void startConsumerTrace(String consumerName) {
        String spanId = generateSpanId();
        MDC.put(SPAN_ID, spanId);
        MDC.put(CONSUMER_NAME, consumerName);
        
        log.debug("开始消费者追踪: consumerName={}, spanId={}", consumerName, spanId);
    }
    
    /**
     * 继承父级追踪上下文
     */
    public static void inheritTrace(String parentTraceId, String parentSpanId) {
        if (parentTraceId != null) {
            MDC.put(TRACE_ID, parentTraceId);
        }
        
        // 生成新的spanId作为子span
        String spanId = generateSpanId();
        MDC.put(SPAN_ID, spanId);
        
        log.debug("继承追踪上下文: parentTraceId={}, parentSpanId={}, newSpanId={}", 
                parentTraceId, parentSpanId, spanId);
    }
    
    /**
     * 添加追踪标签
     */
    public static void addTag(String key, String value) {
        if (key != null && value != null) {
            MDC.put(key, value);
        }
    }
    
    /**
     * 记录追踪事件
     */
    public static void recordEvent(String event, String description) {
        log.info("追踪事件: event={}, description={}", event, description);
    }
    
    /**
     * 记录错误
     */
    public static void recordError(String error, String description) {
        log.error("追踪错误: error={}, description={}", error, description);
    }
    
    /**
     * 记录警告
     */
    public static void recordWarning(String warning, String description) {
        log.warn("追踪警告: warning={}, description={}", warning, description);
    }
    
    /**
     * 获取当前追踪ID
     */
    public static String getCurrentTraceId() {
        return MDC.get(TRACE_ID);
    }
    
    /**
     * 获取当前SpanID
     */
    public static String getCurrentSpanId() {
        return MDC.get(SPAN_ID);
    }
    
    /**
     * 获取当前事件ID
     */
    public static String getCurrentEventId() {
        return MDC.get(EVENT_ID);
    }
    
    /**
     * 获取当前事件类型
     */
    public static String getCurrentEventType() {
        return MDC.get(EVENT_TYPE);
    }
    
    /**
     * 获取当前用户ID
     */
    public static String getCurrentUserId() {
        return MDC.get(USER_ID);
    }
    
    /**
     * 获取当前源服务
     */
    public static String getCurrentSourceService() {
        return MDC.get(SOURCE_SERVICE);
    }
    
    /**
     * 获取当前消费者名称
     */
    public static String getCurrentConsumerName() {
        return MDC.get(CONSUMER_NAME);
    }
    
    /**
     * 结束追踪
     */
    public static void endTrace() {
        String traceId = getCurrentTraceId();
        String spanId = getCurrentSpanId();
        String eventId = getCurrentEventId();
        
        log.debug("结束消息追踪: traceId={}, spanId={}, eventId={}", traceId, spanId, eventId);
        
        // 清理MDC
        clearTrace();
    }
    
    /**
     * 清理追踪上下文
     */
    public static void clearTrace() {
        MDC.remove(TRACE_ID);
        MDC.remove(SPAN_ID);
        MDC.remove(EVENT_ID);
        MDC.remove(EVENT_TYPE);
        MDC.remove(USER_ID);
        MDC.remove(SOURCE_SERVICE);
        MDC.remove(CONSUMER_NAME);
    }
    
    /**
     * 清理所有MDC
     */
    public static void clearAll() {
        MDC.clear();
    }
    
    /**
     * 生成追踪ID
     */
    private static String generateTraceId() {
        return UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 生成SpanID
     */
    private static String generateSpanId() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }
    
    /**
     * 创建追踪上下文快照
     */
    public static TraceContext createSnapshot() {
        return new TraceContext(
                getCurrentTraceId(),
                getCurrentSpanId(),
                getCurrentEventId(),
                getCurrentEventType(),
                getCurrentUserId(),
                getCurrentSourceService(),
                getCurrentConsumerName()
        );
    }
    
    /**
     * 恢复追踪上下文
     */
    public static void restoreSnapshot(TraceContext context) {
        if (context != null) {
            if (context.getTraceId() != null) {
                MDC.put(TRACE_ID, context.getTraceId());
            }
            if (context.getSpanId() != null) {
                MDC.put(SPAN_ID, context.getSpanId());
            }
            if (context.getEventId() != null) {
                MDC.put(EVENT_ID, context.getEventId());
            }
            if (context.getEventType() != null) {
                MDC.put(EVENT_TYPE, context.getEventType());
            }
            if (context.getUserId() != null) {
                MDC.put(USER_ID, context.getUserId());
            }
            if (context.getSourceService() != null) {
                MDC.put(SOURCE_SERVICE, context.getSourceService());
            }
            if (context.getConsumerName() != null) {
                MDC.put(CONSUMER_NAME, context.getConsumerName());
            }
        }
    }
    
    /**
     * 追踪上下文快照
     */
    public static class TraceContext {
        private final String traceId;
        private final String spanId;
        private final String eventId;
        private final String eventType;
        private final String userId;
        private final String sourceService;
        private final String consumerName;
        
        public TraceContext(String traceId, String spanId, String eventId, String eventType, 
                           String userId, String sourceService, String consumerName) {
            this.traceId = traceId;
            this.spanId = spanId;
            this.eventId = eventId;
            this.eventType = eventType;
            this.userId = userId;
            this.sourceService = sourceService;
            this.consumerName = consumerName;
        }
        
        public String getTraceId() { return traceId; }
        public String getSpanId() { return spanId; }
        public String getEventId() { return eventId; }
        public String getEventType() { return eventType; }
        public String getUserId() { return userId; }
        public String getSourceService() { return sourceService; }
        public String getConsumerName() { return consumerName; }
    }
}
