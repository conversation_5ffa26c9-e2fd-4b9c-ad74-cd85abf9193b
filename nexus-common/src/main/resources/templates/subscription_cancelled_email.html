<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订阅已取消 - Nexus微服务平台</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #ffc107;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #ffc107;
            margin-bottom: 10px;
        }
        .cancelled-title {
            color: #ffc107;
            font-size: 24px;
            margin-bottom: 20px;
        }
        .content {
            margin-bottom: 30px;
        }
        .subscription-info {
            background-color: #fff3cd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #ffc107;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #ffeaa7;
        }
        .info-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .info-label {
            font-weight: bold;
            color: #856404;
            flex: 1;
        }
        .info-value {
            flex: 2;
            text-align: right;
            color: #333;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            background-color: #ffc107;
            color: #212529;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 5px;
            font-weight: bold;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .button.secondary {
            background-color: #6c757d;
        }
        .button.secondary:hover {
            background-color: #5a6268;
        }
        .button.success {
            background-color: #28a745;
        }
        .button.success:hover {
            background-color: #218838;
        }
        .feedback-section {
            background-color: #e7f3ff;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        .alternatives {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #dee2e6;
        }
        .alternative-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background-color: white;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        .alternative-item:last-child {
            margin-bottom: 0;
        }
        .alternative-icon {
            font-size: 20px;
            margin-right: 15px;
            color: #007bff;
        }
        .alternative-content {
            flex: 1;
        }
        .alternative-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            text-align: center;
            color: #6c757d;
            font-size: 14px;
        }
        .sad-emoji {
            font-size: 48px;
            text-align: center;
            margin: 20px 0;
        }
        @media (max-width: 600px) {
            .info-row {
                flex-direction: column;
            }
            .info-value {
                text-align: left;
                margin-top: 5px;
            }
            .alternative-item {
                flex-direction: column;
                text-align: center;
            }
            .alternative-icon {
                margin-right: 0;
                margin-bottom: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">😔 Nexus</div>
            <div style="color: #6c757d;">微服务平台</div>
        </div>
        
        <h1 class="cancelled-title">订阅已取消</h1>
        
        <div class="sad-emoji">😢</div>
        
        <div class="content">
            <p>我们很遗憾地确认，您的服务订阅已按照您的要求取消。感谢您曾经选择我们的服务！</p>
            
            <div class="subscription-info">
                <h3 style="margin-top: 0; color: #856404;">📋 取消订阅详情</h3>
                
                <div class="info-row">
                    <span class="info-label">订阅ID：</span>
                    <span class="info-value" th:text="${subscriptionId}">12345</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">服务名称：</span>
                    <span class="info-value" th:text="${serviceName}">MCP服务</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">当前状态：</span>
                    <span class="info-value">
                        <span class="status-badge">已取消</span>
                    </span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">取消时间：</span>
                    <span class="info-value" th:text="${#temporals.format(changeTime, 'yyyy-MM-dd HH:mm:ss')}">2024-01-01 12:00:00</span>
                </div>
                
                <div class="info-row" th:if="${changeReason}">
                    <span class="info-label">取消原因：</span>
                    <span class="info-value" th:text="${changeReason}">用户取消</span>
                </div>
            </div>
            
            <h3>📋 取消后的影响</h3>
            <p>订阅取消后，以下变化将立即生效：</p>
            <ul>
                <li>❌ 无法继续访问服务API接口</li>
                <li>❌ 所有高级功能将被禁用</li>
                <li>❌ 技术支持服务将停止</li>
                <li>⚠️ 您的数据将保留30天，之后将被永久删除</li>
                <li>💰 未使用的费用将按照退款政策处理</li>
            </ul>
            
            <div class="feedback-section">
                <h3 style="margin-top: 0; color: #0056b3;">💬 我们想听听您的意见</h3>
                <p>您的反馈对我们非常重要！请告诉我们取消订阅的原因，帮助我们改进服务：</p>
                <div style="text-align: center; margin: 15px 0;">
                    <a href="https://nexus.example.com/feedback?type=cancellation&subscriptionId=${subscriptionId}" class="button">提供反馈</a>
                </div>
            </div>
            
            <h3>🔄 其他选择</h3>
            <p>如果您改变主意，我们随时欢迎您回来：</p>
            
            <div class="alternatives">
                <div class="alternative-item">
                    <div class="alternative-icon">🔄</div>
                    <div class="alternative-content">
                        <div class="alternative-title">重新订阅</div>
                        <div>随时可以重新订阅相同或其他服务</div>
                    </div>
                </div>
                
                <div class="alternative-item">
                    <div class="alternative-icon">📉</div>
                    <div class="alternative-content">
                        <div class="alternative-title">降级套餐</div>
                        <div>选择更经济的套餐继续使用基础功能</div>
                    </div>
                </div>
                
                <div class="alternative-item">
                    <div class="alternative-icon">⏸️</div>
                    <div class="alternative-content">
                        <div class="alternative-title">暂停服务</div>
                        <div>暂时暂停服务而不是完全取消</div>
                    </div>
                </div>
                
                <div class="alternative-item">
                    <div class="alternative-icon">🎁</div>
                    <div class="alternative-content">
                        <div class="alternative-title">免费试用</div>
                        <div>体验我们的其他服务，享受免费试用期</div>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="https://nexus.example.com/subscription/new" class="button success">重新订阅</a>
                <a href="https://nexus.example.com/services" class="button secondary">浏览服务</a>
            </div>
            
            <h3>📞 需要帮助？</h3>
            <p>如果您有任何疑问或需要帮助，我们的客服团队随时为您服务：</p>
            <ul>
                <li>📧 发送邮件至 <a href="mailto:<EMAIL>"><EMAIL></a></li>
                <li>📞 拨打客服热线 400-123-4567</li>
                <li>💬 访问在线客服系统</li>
            </ul>
        </div>
        
        <div class="footer">
            <p><strong>感谢您曾经选择Nexus微服务平台！</strong></p>
            <p>我们希望未来有机会再次为您服务。</p>
            <hr style="margin: 20px 0;">
            <p>&copy; <span th:text="${#temporals.year(changeTime)}">2024</span> Nexus微服务平台. 保留所有权利.</p>
            <p><small>此邮件由系统自动发送，请勿回复。</small></p>
        </div>
    </div>
</body>
</html>
