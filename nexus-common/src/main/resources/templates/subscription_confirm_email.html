<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订阅确认 - Nexus微服务平台</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #28a745;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #28a745;
            margin-bottom: 10px;
        }
        .confirm-title {
            color: #28a745;
            font-size: 24px;
            margin-bottom: 20px;
        }
        .content {
            margin-bottom: 30px;
        }
        .subscription-info {
            background-color: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #d4edda;
        }
        .info-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .info-label {
            font-weight: bold;
            color: #155724;
            flex: 1;
        }
        .info-value {
            flex: 2;
            text-align: right;
            color: #333;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            background-color: #28a745;
            color: white;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 5px;
            font-weight: bold;
        }
        .button:hover {
            background-color: #218838;
        }
        .button.secondary {
            background-color: #6c757d;
        }
        .button.secondary:hover {
            background-color: #5a6268;
        }
        .features {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }
        .feature {
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        .feature-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }
        .feature-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: #28a745;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            text-align: center;
            color: #6c757d;
            font-size: 14px;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            color: #856404;
        }
        @media (max-width: 600px) {
            .features {
                grid-template-columns: 1fr;
            }
            .info-row {
                flex-direction: column;
            }
            .info-value {
                text-align: left;
                margin-top: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🎉 Nexus</div>
            <div style="color: #6c757d;">微服务平台</div>
        </div>
        
        <h1 class="confirm-title">订阅确认成功！</h1>
        
        <div class="content">
            <p>恭喜您成功订阅了我们的服务！您现在可以开始使用以下服务的所有功能。</p>
            
            <div class="subscription-info">
                <h3 style="margin-top: 0; color: #155724;">📋 订阅详情</h3>
                
                <div class="info-row">
                    <span class="info-label">订阅ID：</span>
                    <span class="info-value" th:text="${subscriptionId}">12345</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">服务名称：</span>
                    <span class="info-value" th:text="${serviceName}">MCP服务</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">订阅状态：</span>
                    <span class="info-value">
                        <span class="status-badge">已激活</span>
                    </span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">开始时间：</span>
                    <span class="info-value" th:text="${#temporals.format(startDate, 'yyyy-MM-dd HH:mm:ss')}">2024-01-01 12:00:00</span>
                </div>
                
                <div class="info-row" th:if="${endDate}">
                    <span class="info-label">到期时间：</span>
                    <span class="info-value" th:text="${#temporals.format(endDate, 'yyyy-MM-dd HH:mm:ss')}">2024-02-01 12:00:00</span>
                </div>
                
                <div class="info-row" th:if="${callLimit}">
                    <span class="info-label">调用限制：</span>
                    <span class="info-value">
                        <span th:text="${callLimit}">1000</span> 次/月
                    </span>
                </div>
                
                <div class="info-row" th:if="${notes}">
                    <span class="info-label">备注：</span>
                    <span class="info-value" th:text="${notes}">特殊说明</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">订阅时间：</span>
                    <span class="info-value" th:text="${#temporals.format(subscriptionTime, 'yyyy-MM-dd HH:mm:ss')}">2024-01-01 12:00:00</span>
                </div>
            </div>
            
            <div class="features">
                <div class="feature">
                    <div class="feature-icon">🚀</div>
                    <div class="feature-title">即时访问</div>
                    <div>立即开始使用服务</div>
                </div>
                <div class="feature">
                    <div class="feature-icon">📊</div>
                    <div class="feature-title">使用监控</div>
                    <div>实时查看使用情况</div>
                </div>
                <div class="feature">
                    <div class="feature-icon">🔧</div>
                    <div class="feature-title">技术支持</div>
                    <div>7x24小时技术支持</div>
                </div>
                <div class="feature">
                    <div class="feature-icon">📈</div>
                    <div class="feature-title">性能保障</div>
                    <div>高可用性能保证</div>
                </div>
            </div>
            
            <h3>🚀 快速开始</h3>
            <p>现在您可以：</p>
            <ul>
                <li>访问服务管理面板查看详细信息</li>
                <li>查看API文档了解接口使用方法</li>
                <li>监控您的服务使用情况和性能指标</li>
                <li>联系技术支持获取帮助</li>
            </ul>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="https://nexus.example.com/dashboard" class="button">访问控制台</a>
                <a href="https://nexus.example.com/docs" class="button secondary">查看文档</a>
            </div>
            
            <div class="warning" th:if="${endDate}">
                <strong>⚠️ 重要提醒：</strong>
                您的订阅将在 <span th:text="${#temporals.format(endDate, 'yyyy年MM月dd日')}">2024年02月01日</span> 到期。
                请及时续费以避免服务中断。
            </div>
        </div>
        
        <div class="footer">
            <p>如果您有任何问题，请随时联系我们：</p>
            <p>📧 <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p>🌐 <a href="https://nexus.example.com">https://nexus.example.com</a></p>
            <hr style="margin: 20px 0;">
            <p>&copy; <span th:text="${#temporals.year(subscriptionTime)}">2024</span> Nexus微服务平台. 保留所有权利.</p>
            <p><small>此邮件由系统自动发送，请勿回复。</small></p>
        </div>
    </div>
</body>
</html>
