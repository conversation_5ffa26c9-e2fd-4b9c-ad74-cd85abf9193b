<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订阅已过期 - Nexus微服务平台</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #dc3545;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #dc3545;
            margin-bottom: 10px;
        }
        .expired-title {
            color: #dc3545;
            font-size: 24px;
            margin-bottom: 20px;
        }
        .content {
            margin-bottom: 30px;
        }
        .subscription-info {
            background-color: #f8d7da;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #dc3545;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #f1b0b7;
        }
        .info-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .info-label {
            font-weight: bold;
            color: #721c24;
            flex: 1;
        }
        .info-value {
            flex: 2;
            text-align: right;
            color: #333;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            background-color: #dc3545;
            color: white;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #dc3545;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 5px;
            font-weight: bold;
        }
        .button:hover {
            background-color: #c82333;
        }
        .button.primary {
            background-color: #007bff;
        }
        .button.primary:hover {
            background-color: #0056b3;
        }
        .button.secondary {
            background-color: #6c757d;
        }
        .button.secondary:hover {
            background-color: #5a6268;
        }
        .alert {
            background-color: #f8d7da;
            border: 1px solid #f1b0b7;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
            color: #721c24;
        }
        .alert-icon {
            font-size: 24px;
            margin-right: 10px;
        }
        .renewal-options {
            background-color: #e7f3ff;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        .option {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background-color: white;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        .option:last-child {
            margin-bottom: 0;
        }
        .option-icon {
            font-size: 20px;
            margin-right: 15px;
            color: #007bff;
        }
        .option-content {
            flex: 1;
        }
        .option-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            text-align: center;
            color: #6c757d;
            font-size: 14px;
        }
        @media (max-width: 600px) {
            .info-row {
                flex-direction: column;
            }
            .info-value {
                text-align: left;
                margin-top: 5px;
            }
            .option {
                flex-direction: column;
                text-align: center;
            }
            .option-icon {
                margin-right: 0;
                margin-bottom: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">⚠️ Nexus</div>
            <div style="color: #6c757d;">微服务平台</div>
        </div>
        
        <h1 class="expired-title">订阅已过期</h1>
        
        <div class="content">
            <div class="alert">
                <span class="alert-icon">🚨</span>
                <strong>重要通知：</strong>您的服务订阅已于 
                <span th:text="${#temporals.format(changeTime, 'yyyy年MM月dd日 HH:mm')}">2024年01月01日 12:00</span> 过期。
                服务访问已被暂停，请及时续费以恢复服务。
            </div>
            
            <div class="subscription-info">
                <h3 style="margin-top: 0; color: #721c24;">📋 过期订阅详情</h3>
                
                <div class="info-row">
                    <span class="info-label">订阅ID：</span>
                    <span class="info-value" th:text="${subscriptionId}">12345</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">服务名称：</span>
                    <span class="info-value" th:text="${serviceName}">MCP服务</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">当前状态：</span>
                    <span class="info-value">
                        <span class="status-badge">已过期</span>
                    </span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">过期时间：</span>
                    <span class="info-value" th:text="${#temporals.format(changeTime, 'yyyy-MM-dd HH:mm:ss')}">2024-01-01 12:00:00</span>
                </div>
                
                <div class="info-row" th:if="${changeReason}">
                    <span class="info-label">过期原因：</span>
                    <span class="info-value" th:text="${changeReason}">自动过期</span>
                </div>
            </div>
            
            <h3>🔄 续费选项</h3>
            <p>选择以下任一方式快速恢复您的服务：</p>
            
            <div class="renewal-options">
                <div class="option">
                    <div class="option-icon">💳</div>
                    <div class="option-content">
                        <div class="option-title">在线续费</div>
                        <div>登录控制台，选择续费套餐，支持多种支付方式</div>
                    </div>
                </div>
                
                <div class="option">
                    <div class="option-icon">📞</div>
                    <div class="option-content">
                        <div class="option-title">联系客服</div>
                        <div>致电客服热线，专业顾问为您提供续费服务</div>
                    </div>
                </div>
                
                <div class="option">
                    <div class="option-icon">🎯</div>
                    <div class="option-content">
                        <div class="option-title">升级套餐</div>
                        <div>升级到更高级套餐，享受更多功能和优惠</div>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="https://nexus.example.com/subscription/renew?id=${subscriptionId}" class="button primary">立即续费</a>
                <a href="https://nexus.example.com/contact" class="button secondary">联系客服</a>
            </div>
            
            <h3>📋 过期影响</h3>
            <p>订阅过期后，以下功能将受到影响：</p>
            <ul>
                <li>❌ 无法访问服务API接口</li>
                <li>❌ 无法使用高级功能</li>
                <li>❌ 技术支持服务暂停</li>
                <li>⚠️ 数据保留30天后将被清理</li>
            </ul>
            
            <div class="alert">
                <span class="alert-icon">💡</span>
                <strong>温馨提示：</strong>
                续费后服务将立即恢复，所有数据和配置都会保持不变。
                为避免服务中断，建议开启自动续费功能。
            </div>
        </div>
        
        <div class="footer">
            <p>如果您有任何问题，请随时联系我们：</p>
            <p>📧 <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p>📞 客服热线：400-123-4567</p>
            <p>🌐 <a href="https://nexus.example.com">https://nexus.example.com</a></p>
            <hr style="margin: 20px 0;">
            <p>&copy; <span th:text="${#temporals.year(changeTime)}">2024</span> Nexus微服务平台. 保留所有权利.</p>
            <p><small>此邮件由系统自动发送，请勿回复。</small></p>
        </div>
    </div>
</body>
</html>
