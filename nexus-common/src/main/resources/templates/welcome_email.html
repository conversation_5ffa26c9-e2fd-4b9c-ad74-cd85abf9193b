<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>欢迎加入Nexus微服务平台</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        .welcome-title {
            color: #007bff;
            font-size: 24px;
            margin-bottom: 20px;
        }
        .content {
            margin-bottom: 30px;
        }
        .highlight {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        .api-key {
            font-family: 'Courier New', monospace;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #dee2e6;
            word-break: break-all;
            font-size: 14px;
        }
        .button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 5px;
            font-weight: bold;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            text-align: center;
            color: #6c757d;
            font-size: 14px;
        }
        .features {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .feature {
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            text-align: center;
        }
        .feature-icon {
            font-size: 24px;
            margin-bottom: 10px;
        }
        @media (max-width: 600px) {
            .features {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🚀 Nexus</div>
            <div style="color: #6c757d;">微服务平台</div>
        </div>
        
        <h1 class="welcome-title">欢迎加入，<span th:text="${username}">用户</span>！</h1>
        
        <div class="content">
            <p>恭喜您成功注册Nexus微服务平台！我们很高兴您选择我们的服务。</p>
            
            <div class="highlight">
                <h3>🎉 您的账户已激活</h3>
                <p><strong>用户名：</strong> <span th:text="${username}">username</span></p>
                <p><strong>邮箱：</strong> <span th:text="${email}"><EMAIL></span></p>
                <p><strong>注册时间：</strong> <span th:text="${#temporals.format(registrationTime, 'yyyy-MM-dd HH:mm:ss')}">2024-01-01 12:00:00</span></p>
            </div>
            
            <h3>🔑 您的API密钥</h3>
            <p>以下是您的专属API密钥，请妥善保管：</p>
            <div class="api-key" th:text="${apiKey}">your-api-key-here</div>
            <p><small>⚠️ 请勿将API密钥分享给他人，如有泄露请及时联系我们重新生成。</small></p>
            
            <div class="features">
                <div class="feature">
                    <div class="feature-icon">🛠️</div>
                    <h4>MCP服务管理</h4>
                    <p>管理本地和远程MCP服务</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">📊</div>
                    <h4>实时监控</h4>
                    <p>监控服务状态和性能指标</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">🔐</div>
                    <h4>安全认证</h4>
                    <p>JWT和API密钥双重认证</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">⚡</div>
                    <h4>高性能</h4>
                    <p>基于Spring Cloud微服务架构</p>
                </div>
            </div>
            
            <h3>🚀 快速开始</h3>
            <p>现在您可以：</p>
            <ul>
                <li>登录平台开始使用服务</li>
                <li>查看API文档了解接口详情</li>
                <li>注册您的MCP服务</li>
                <li>监控服务运行状态</li>
            </ul>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="https://nexus.example.com/login" class="button">立即登录</a>
                <a href="https://nexus.example.com/docs" class="button" style="background-color: #28a745;">查看文档</a>
            </div>
        </div>
        
        <div class="footer">
            <p>如果您有任何问题，请随时联系我们：</p>
            <p>📧 <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p>🌐 <a href="https://nexus.example.com">https://nexus.example.com</a></p>
            <hr style="margin: 20px 0;">
            <p>&copy; <span th:text="${#temporals.year(currentTime)}">2024</span> Nexus微服务平台. 保留所有权利.</p>
            <p><small>此邮件由系统自动发送，请勿回复。</small></p>
        </div>
    </div>
</body>
</html>
