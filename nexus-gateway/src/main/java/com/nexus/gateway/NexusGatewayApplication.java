package com.nexus.gateway;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * Nexus API网关启动类
 * 
 * 功能：
 * - 统一API入口和路由管理
 * - 请求认证和授权
 * - 限流和熔断保护
 * - 请求日志和监控
 * - 跨域处理和安全防护
 * - 负载均衡和服务发现
 */
@SpringBootApplication(scanBasePackages = {
    "com.nexus.gateway",  // 当前服务包
    "com.nexus.common"    // 公共组件包
})
@EnableDiscoveryClient
public class NexusGatewayApplication {

    public static void main(String[] args) {
        SpringApplication.run(NexusGatewayApplication.class, args);
    }
}
