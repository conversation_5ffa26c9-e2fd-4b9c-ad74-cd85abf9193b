package com.nexus.gateway.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 降级控制器
 * 提供服务降级和健康检查端点
 */
@Slf4j
@RestController
public class FallbackController {

    /**
     * 通用降级端点
     */
    @GetMapping("/fallback")
    public ResponseEntity<Map<String, Object>> fallback() {
        log.warn("触发服务降级");
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("error", "Service Unavailable");
        response.put("message", "服务暂时不可用，请稍后重试");
        response.put("code", HttpStatus.SERVICE_UNAVAILABLE.value());
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response);
    }

    /**
     * 网关健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "nexus-gateway");
        health.put("timestamp", System.currentTimeMillis());
        health.put("version", "1.0.0");
        
        return ResponseEntity.ok(health);
    }

    /**
     * 网关信息
     */
    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> info() {
        Map<String, Object> info = new HashMap<>();
        info.put("name", "Nexus API Gateway");
        info.put("description", "统一API网关服务");
        info.put("version", "1.0.0");
        info.put("timestamp", System.currentTimeMillis());
        
        Map<String, Object> features = new HashMap<>();
        features.put("authentication", "JWT认证");
        features.put("authorization", "订阅权限验证");
        features.put("rateLimit", "Redis限流");
        features.put("circuitBreaker", "熔断保护");
        features.put("loadBalancer", "负载均衡");
        info.put("features", features);
        
        return ResponseEntity.ok(info);
    }
}
