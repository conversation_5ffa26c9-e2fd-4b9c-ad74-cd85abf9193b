package com.nexus.gateway.exception;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.reactive.error.ErrorWebExceptionHandler;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;

/**
 * 全局异常处理器
 * 处理网关中的异常并返回统一的错误响应
 */
@Slf4j
@Component
@Order(-1)
public class GlobalExceptionHandler implements ErrorWebExceptionHandler {

    @Override
    public Mono<Void> handle(ServerWebExchange exchange, Throwable ex) {
        ServerHttpResponse response = exchange.getResponse();
        
        if (response.isCommitted()) {
            return Mono.error(ex);
        }

        // 设置响应头
        response.getHeaders().add("Content-Type", MediaType.APPLICATION_JSON_VALUE);

        HttpStatus status;
        String error;
        String message;

        if (ex instanceof ResponseStatusException) {
            ResponseStatusException rse = (ResponseStatusException) ex;
            status = rse.getStatus();
            error = status.getReasonPhrase();
            message = rse.getReason() != null ? rse.getReason() : "请求处理失败";
        } else if (ex instanceof org.springframework.cloud.gateway.support.TimeoutException) {
            status = HttpStatus.GATEWAY_TIMEOUT;
            error = "Gateway Timeout";
            message = "请求超时，请稍后重试";
        } else if (ex instanceof org.springframework.web.server.ServerWebInputException) {
            status = HttpStatus.BAD_REQUEST;
            error = "Bad Request";
            message = "请求参数错误";
        } else if (ex instanceof java.net.ConnectException) {
            status = HttpStatus.SERVICE_UNAVAILABLE;
            error = "Service Unavailable";
            message = "服务暂时不可用，请稍后重试";
        } else {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            error = "Internal Server Error";
            message = "服务器内部错误";
        }

        response.setStatusCode(status);

        String body = buildErrorResponse(error, message, status.value());
        DataBuffer buffer = response.bufferFactory().wrap(body.getBytes(StandardCharsets.UTF_8));

        log.error("网关异常处理: {} - {} - {}", 
                exchange.getRequest().getURI().getPath(), 
                status.value(), 
                ex.getMessage(), ex);

        return response.writeWith(Mono.just(buffer));
    }

    /**
     * 构建错误响应
     */
    private String buildErrorResponse(String error, String message, int code) {
        return String.format(
                "{\"success\":false,\"error\":\"%s\",\"message\":\"%s\",\"code\":%d,\"timestamp\":%d}",
                error, message, code, System.currentTimeMillis()
        );
    }
}
