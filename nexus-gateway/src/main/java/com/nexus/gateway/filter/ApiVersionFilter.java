package com.nexus.gateway.filter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * API版本管理过滤器
 * 支持URL路径版本和Header版本控制
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component
@Slf4j
public class ApiVersionFilter extends AbstractGatewayFilterFactory<ApiVersionFilter.Config> {

    private static final String API_VERSION_HEADER = "X-API-Version";
    private static final String DEFAULT_VERSION = "v1";
    private static final Pattern VERSION_PATTERN = Pattern.compile("/api/(v\\d+)/");

    public ApiVersionFilter() {
        super(Config.class);
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            ServerHttpRequest request = exchange.getRequest();
            String path = request.getURI().getPath();
            
            log.debug("API版本过滤器处理请求: {}", path);

            try {
                // 1. 从URL路径提取版本
                String pathVersion = extractVersionFromPath(path);
                
                // 2. 从Header提取版本
                String headerVersion = extractVersionFromHeader(request);
                
                // 3. 确定最终使用的版本
                String finalVersion = determineVersion(pathVersion, headerVersion, config);
                
                // 4. 验证版本是否支持
                if (!isVersionSupported(finalVersion, config)) {
                    return handleUnsupportedVersion(exchange, finalVersion);
                }
                
                // 5. 检查版本是否已弃用
                if (isVersionDeprecated(finalVersion, config)) {
                    log.warn("使用已弃用的API版本: {} - 路径: {}", finalVersion, path);
                    // 添加弃用警告头
                    exchange.getResponse().getHeaders().add("X-API-Deprecated", "true");
                    exchange.getResponse().getHeaders().add("X-API-Deprecation-Message", 
                            "此API版本已弃用，请升级到最新版本");
                }
                
                // 6. 重写请求路径（如果需要）
                ServerHttpRequest modifiedRequest = rewritePathIfNeeded(request, finalVersion, config);
                
                // 7. 添加版本信息到请求头
                modifiedRequest = modifiedRequest.mutate()
                        .header("X-Resolved-API-Version", finalVersion)
                        .header("X-Original-Path", path)
                        .build();
                
                ServerWebExchange modifiedExchange = exchange.mutate()
                        .request(modifiedRequest)
                        .build();
                
                log.debug("API版本解析完成: 路径版本={}, Header版本={}, 最终版本={}", 
                         pathVersion, headerVersion, finalVersion);
                
                return chain.filter(modifiedExchange);
                
            } catch (Exception e) {
                log.error("API版本过滤器处理失败: {} - {}", path, e.getMessage(), e);
                return handleError(exchange, "API版本处理失败");
            }
        };
    }

    /**
     * 从URL路径提取版本
     */
    private String extractVersionFromPath(String path) {
        Matcher matcher = VERSION_PATTERN.matcher(path);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    /**
     * 从Header提取版本
     */
    private String extractVersionFromHeader(ServerHttpRequest request) {
        List<String> versionHeaders = request.getHeaders().get(API_VERSION_HEADER);
        if (versionHeaders != null && !versionHeaders.isEmpty()) {
            String version = versionHeaders.get(0);
            // 确保版本格式正确
            if (version.matches("v\\d+")) {
                return version;
            } else if (version.matches("\\d+")) {
                return "v" + version;
            }
        }
        return null;
    }

    /**
     * 确定最终使用的版本
     */
    private String determineVersion(String pathVersion, String headerVersion, Config config) {
        // 优先级：路径版本 > Header版本 > 默认版本
        if (pathVersion != null) {
            return pathVersion;
        }
        if (headerVersion != null) {
            return headerVersion;
        }
        return config.getDefaultVersion() != null ? config.getDefaultVersion() : DEFAULT_VERSION;
    }

    /**
     * 检查版本是否支持
     */
    private boolean isVersionSupported(String version, Config config) {
        if (config.getSupportedVersions() == null || config.getSupportedVersions().isEmpty()) {
            return true; // 如果没有配置支持的版本，则认为都支持
        }
        return config.getSupportedVersions().contains(version);
    }

    /**
     * 检查版本是否已弃用
     */
    private boolean isVersionDeprecated(String version, Config config) {
        if (config.getDeprecatedVersions() == null) {
            return false;
        }
        return config.getDeprecatedVersions().contains(version);
    }

    /**
     * 重写请求路径（如果需要）
     */
    private ServerHttpRequest rewritePathIfNeeded(ServerHttpRequest request, String version, Config config) {
        String originalPath = request.getURI().getPath();
        
        // 如果配置了路径重写规则
        if (config.isRewritePath()) {
            // 如果原路径没有版本，添加版本
            if (!VERSION_PATTERN.matcher(originalPath).find()) {
                String newPath = "/api/" + version + originalPath.replaceFirst("^/api", "");
                return request.mutate()
                        .path(newPath)
                        .build();
            }
        }
        
        return request;
    }

    /**
     * 处理不支持的版本
     */
    private Mono<Void> handleUnsupportedVersion(ServerWebExchange exchange, String version) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.BAD_REQUEST);
        response.getHeaders().add("Content-Type", "application/json");
        
        String body = String.format(
                "{\"success\":false,\"error\":\"Unsupported API Version\",\"message\":\"API版本 %s 不受支持\",\"code\":400,\"timestamp\":%d}",
                version, System.currentTimeMillis()
        );
        
        org.springframework.core.io.buffer.DataBuffer buffer = 
                response.bufferFactory().wrap(body.getBytes(StandardCharsets.UTF_8));
        
        log.warn("不支持的API版本: {}", version);
        return response.writeWith(reactor.core.publisher.Mono.just(buffer));
    }

    /**
     * 处理错误
     */
    private Mono<Void> handleError(ServerWebExchange exchange, String message) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.INTERNAL_SERVER_ERROR);
        response.getHeaders().add("Content-Type", "application/json");
        
        String body = String.format(
                "{\"success\":false,\"error\":\"Internal Server Error\",\"message\":\"%s\",\"code\":500,\"timestamp\":%d}",
                message, System.currentTimeMillis()
        );
        
        org.springframework.core.io.buffer.DataBuffer buffer = 
                response.bufferFactory().wrap(body.getBytes(StandardCharsets.UTF_8));
        
        return response.writeWith(reactor.core.publisher.Mono.just(buffer));
    }

    @Override
    public List<String> shortcutFieldOrder() {
        return Arrays.asList("supportedVersions", "deprecatedVersions", "defaultVersion", "rewritePath");
    }

    /**
     * 配置类
     */
    public static class Config {
        private List<String> supportedVersions;
        private List<String> deprecatedVersions;
        private String defaultVersion = DEFAULT_VERSION;
        private boolean rewritePath = false;

        public List<String> getSupportedVersions() {
            return supportedVersions;
        }

        public void setSupportedVersions(List<String> supportedVersions) {
            this.supportedVersions = supportedVersions;
        }

        public List<String> getDeprecatedVersions() {
            return deprecatedVersions;
        }

        public void setDeprecatedVersions(List<String> deprecatedVersions) {
            this.deprecatedVersions = deprecatedVersions;
        }

        public String getDefaultVersion() {
            return defaultVersion;
        }

        public void setDefaultVersion(String defaultVersion) {
            this.defaultVersion = defaultVersion;
        }

        public boolean isRewritePath() {
            return rewritePath;
        }

        public void setRewritePath(boolean rewritePath) {
            this.rewritePath = rewritePath;
        }
    }
}
