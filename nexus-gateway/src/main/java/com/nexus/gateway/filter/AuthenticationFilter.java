package com.nexus.gateway.filter;

import com.nexus.gateway.service.JwtService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 认证过滤器
 * 验证JWT令牌并提取用户信息
 */
@Slf4j
@Component
public class AuthenticationFilter extends AbstractGatewayFilterFactory<AuthenticationFilter.Config> {

    private final JwtService jwtService;
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    @Value("${nexus.gateway.auth.skip-auth-paths:/auth/**,/actuator/**,/fallback,/favicon.ico}")
    private List<String> skipAuthPaths;
    
    @Value("${nexus.gateway.security.excluded-paths:}")
    private List<String> excludedPaths;

    public AuthenticationFilter(JwtService jwtService) {
        super(Config.class);
        this.jwtService = jwtService;
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            ServerHttpRequest request = exchange.getRequest();
            String path = request.getURI().getPath();

            // 检查是否需要跳过认证
            if (shouldSkipAuth(path)) {
                log.debug("跳过认证检查: {}", path);
                return chain.filter(exchange);
            }
            
            log.debug("开始认证检查: {}", path);
            log.debug("排除路径配置: {}", excludedPaths);

            // 提取Authorization头
            String authHeader = request.getHeaders().getFirst(HttpHeaders.AUTHORIZATION);
            log.debug("Authorization头: {}", authHeader);
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                log.warn("缺少或无效的Authorization头: {}", path);
                return handleUnauthorized(exchange, "缺少认证令牌");
            }

            // 提取JWT令牌
            String token = authHeader.substring(7);
            
            try {
                // 验证JWT令牌
                if (!jwtService.validateToken(token)) {
                    log.warn("JWT令牌验证失败: {}", path);
                    return handleUnauthorized(exchange, "无效的认证令牌");
                }

                // 提取用户信息
                String userId = jwtService.extractUserId(token);
                String username = jwtService.extractUsername(token);
                
                if (userId == null || username == null) {
                    log.warn("无法从JWT令牌提取用户信息: {}", path);
                    return handleUnauthorized(exchange, "令牌信息不完整");
                }

                // 将用户信息添加到请求头中，传递给下游服务
                ServerHttpRequest modifiedRequest = request.mutate()
                        .header("X-User-ID", userId)
                        .header("X-Username", username)
                        .header("X-Auth-Token", token)
                        .build();

                ServerWebExchange modifiedExchange = exchange.mutate()
                        .request(modifiedRequest)
                        .build();

                log.debug("认证成功: 用户ID {}, 用户名 {}, 路径 {}", userId, username, path);
                return chain.filter(modifiedExchange);

            } catch (Exception e) {
                log.error("认证过程中发生异常: {} - {}", path, e.getMessage());
                return handleUnauthorized(exchange, "认证失败");
            }
        };
    }

    /**
     * 检查是否应该跳过认证
     */
    private boolean shouldSkipAuth(String path) {
        // 检查旧的skipAuthPaths配置
        if (skipAuthPaths != null && skipAuthPaths.stream()
                .anyMatch(pattern -> pathMatcher.match(pattern, path))) {
            return true;
        }
        
        // 检查新的excludedPaths配置
        if (excludedPaths != null && excludedPaths.stream()
                .anyMatch(pattern -> pathMatcher.match(pattern, path))) {
            return true;
        }
        
        return false;
    }

    /**
     * 处理未授权请求
     */
    private Mono<Void> handleUnauthorized(ServerWebExchange exchange, String message) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.UNAUTHORIZED);
        response.getHeaders().add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);

        String body = String.format(
                "{\"success\":false,\"error\":\"Unauthorized\",\"message\":\"%s\",\"code\":401,\"timestamp\":%d}",
                message, System.currentTimeMillis()
        );

        DataBuffer buffer = response.bufferFactory().wrap(body.getBytes(StandardCharsets.UTF_8));
        return response.writeWith(Mono.just(buffer));
    }

    /**
     * 配置类
     */
    public static class Config {
        // 可以添加配置参数
    }
}
