package com.nexus.gateway.filter;

import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerConfig;
import io.github.resilience4j.circuitbreaker.CircuitBreakerRegistry;
import io.github.resilience4j.reactor.circuitbreaker.operator.CircuitBreakerOperator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 熔断器过滤器
 * 基于Resilience4j实现的熔断保护机制
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component
@Slf4j
public class CircuitBreakerFilter extends AbstractGatewayFilterFactory<CircuitBreakerFilter.Config> {

    private final CircuitBreakerRegistry circuitBreakerRegistry;
    private final ConcurrentHashMap<String, CircuitBreaker> circuitBreakerCache = new ConcurrentHashMap<>();

    public CircuitBreakerFilter(CircuitBreakerRegistry circuitBreakerRegistry) {
        super(Config.class);
        this.circuitBreakerRegistry = circuitBreakerRegistry;
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            if (!config.isEnabled()) {
                return chain.filter(exchange);
            }

            ServerHttpRequest request = exchange.getRequest();
            String circuitBreakerName = buildCircuitBreakerName(request, config);
            
            // 获取或创建熔断器
            CircuitBreaker circuitBreaker = getOrCreateCircuitBreaker(circuitBreakerName, config);
            
            log.debug("应用熔断器: {} - 状态: {}", circuitBreakerName, circuitBreaker.getState());

            return chain.filter(exchange)
                    .transformDeferred(CircuitBreakerOperator.of(circuitBreaker))
                    .onErrorResume(throwable -> handleCircuitBreakerError(exchange, throwable, circuitBreakerName, config))
                    .doOnSuccess(aVoid -> {
                        log.debug("熔断器请求成功: {}", circuitBreakerName);
                    })
                    .doOnError(throwable -> {
                        log.warn("熔断器请求失败: {} - {}", circuitBreakerName, throwable.getMessage());
                    });
        };
    }

    /**
     * 获取或创建熔断器
     */
    private CircuitBreaker getOrCreateCircuitBreaker(String name, Config config) {
        return circuitBreakerCache.computeIfAbsent(name, key -> {
            // 检查注册表中是否已存在
            if (circuitBreakerRegistry.getAllCircuitBreakers().toJavaStream()
                    .anyMatch(cb -> cb.getName().equals(key))) {
                return circuitBreakerRegistry.circuitBreaker(key);
            }
            
            // 创建新的熔断器配置
            CircuitBreakerConfig circuitBreakerConfig = CircuitBreakerConfig.custom()
                    .failureRateThreshold(config.getFailureRateThreshold())
                    .slowCallRateThreshold(config.getSlowCallRateThreshold())
                    .slowCallDurationThreshold(Duration.ofMillis(config.getSlowCallDurationThreshold()))
                    .minimumNumberOfCalls(config.getMinimumNumberOfCalls())
                    .slidingWindowSize(config.getSlidingWindowSize())
                    .slidingWindowType(CircuitBreakerConfig.SlidingWindowType.COUNT_BASED)
                    .waitDurationInOpenState(Duration.ofMillis(config.getWaitDurationInOpenState()))
                    .permittedNumberOfCallsInHalfOpenState(config.getPermittedNumberOfCallsInHalfOpenState())
                    .automaticTransitionFromOpenToHalfOpenEnabled(config.isAutomaticTransitionFromOpenToHalfOpenEnabled())
                    .recordExceptions(getRecordExceptions(config))
                    .ignoreExceptions(getIgnoreExceptions(config))
                    .build();

            CircuitBreaker circuitBreaker = circuitBreakerRegistry.circuitBreaker(key, circuitBreakerConfig);
            
            // 添加事件监听器
            addEventListeners(circuitBreaker);
            
            log.info("创建熔断器: {} - 配置: 失败率阈值={}%, 慢调用阈值={}ms, 最小调用数={}", 
                    key, config.getFailureRateThreshold(), config.getSlowCallDurationThreshold(), 
                    config.getMinimumNumberOfCalls());
            
            return circuitBreaker;
        });
    }

    /**
     * 构建熔断器名称
     */
    private String buildCircuitBreakerName(ServerHttpRequest request, Config config) {
        String path = request.getURI().getPath();
        
        // 根据配置的粒度级别构建名称
        switch (config.getGranularity()) {
            case "service":
                return extractServiceName(path);
            case "path":
                return extractPathPattern(path);
            case "method_path":
                return (request.getMethod() != null ? request.getMethod().name() : "UNKNOWN") + "_" + extractPathPattern(path);
            default:
                return config.getName() != null ? config.getName() : "default";
        }
    }

    /**
     * 提取服务名称
     */
    private String extractServiceName(String path) {
        if (path.startsWith("/api/")) {
            String[] parts = path.split("/");
            if (parts.length >= 3) {
                return parts[2]; // /api/{service}/...
            }
        }
        return "unknown-service";
    }

    /**
     * 提取路径模式
     */
    private String extractPathPattern(String path) {
        return path.replaceAll("/\\d+", "/{id}")
                   .replaceAll("/[a-f0-9-]{36}", "/{uuid}")
                   .replaceAll("/[a-zA-Z0-9_-]{20,}", "/{token}");
    }

    /**
     * 处理熔断器错误
     */
    private Mono<Void> handleCircuitBreakerError(ServerWebExchange exchange, Throwable throwable, 
                                                String circuitBreakerName, Config config) {
        
        log.warn("熔断器触发: {} - 错误: {}", circuitBreakerName, throwable.getMessage());

        // 判断是熔断器开启还是其他错误
        if (throwable instanceof io.github.resilience4j.circuitbreaker.CallNotPermittedException) {
            // 熔断器开启
            return handleCircuitBreakerOpen(exchange, circuitBreakerName, config);
        } else {
            // 其他错误，返回服务不可用
            return handleServiceUnavailable(exchange, throwable.getMessage());
        }
    }

    /**
     * 处理熔断器开启状态
     */
    private Mono<Void> handleCircuitBreakerOpen(ServerWebExchange exchange, String circuitBreakerName, Config config) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.SERVICE_UNAVAILABLE);
        response.getHeaders().add("Content-Type", "application/json");
        response.getHeaders().add("X-Circuit-Breaker", circuitBreakerName);
        response.getHeaders().add("X-Circuit-Breaker-State", "OPEN");
        
        String fallbackResponse = config.getFallbackResponse();
        if (fallbackResponse == null) {
            fallbackResponse = String.format(
                    "{\"success\":false,\"error\":\"Circuit Breaker Open\",\"message\":\"服务熔断保护已触发，请稍后重试\",\"circuitBreaker\":\"%s\",\"code\":503,\"timestamp\":%d}",
                    circuitBreakerName, System.currentTimeMillis()
            );
        }
        
        org.springframework.core.io.buffer.DataBuffer buffer = 
                response.bufferFactory().wrap(fallbackResponse.getBytes(StandardCharsets.UTF_8));
        
        return response.writeWith(Mono.just(buffer));
    }

    /**
     * 处理服务不可用
     */
    private Mono<Void> handleServiceUnavailable(ServerWebExchange exchange, String errorMessage) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.SERVICE_UNAVAILABLE);
        response.getHeaders().add("Content-Type", "application/json");
        
        String body = String.format(
                "{\"success\":false,\"error\":\"Service Unavailable\",\"message\":\"服务暂时不可用: %s\",\"code\":503,\"timestamp\":%d}",
                errorMessage, System.currentTimeMillis()
        );
        
        org.springframework.core.io.buffer.DataBuffer buffer = 
                response.bufferFactory().wrap(body.getBytes(StandardCharsets.UTF_8));
        
        return response.writeWith(Mono.just(buffer));
    }

    /**
     * 添加事件监听器
     */
    private void addEventListeners(CircuitBreaker circuitBreaker) {
        circuitBreaker.getEventPublisher()
                .onStateTransition(event -> {
                    log.info("熔断器状态转换: {} - {} -> {}", 
                            circuitBreaker.getName(), event.getStateTransition().getFromState(), 
                            event.getStateTransition().getToState());
                })
                .onFailureRateExceeded(event -> {
                    log.warn("熔断器失败率超过阈值: {} - 失败率: {}%", 
                            circuitBreaker.getName(), event.getFailureRate());
                })
                .onSlowCallRateExceeded(event -> {
                    log.warn("熔断器慢调用率超过阈值: {} - 慢调用率: {}%", 
                            circuitBreaker.getName(), event.getSlowCallRate());
                })
                .onCallNotPermitted(event -> {
                    log.debug("熔断器拒绝调用: {}", circuitBreaker.getName());
                });
    }

    /**
     * 获取需要记录的异常类型
     */
    @SuppressWarnings("unchecked")
    private Class<? extends Throwable>[] getRecordExceptions(Config config) {
        // 默认记录所有异常
        return new Class[]{Exception.class};
    }

    /**
     * 获取需要忽略的异常类型
     */
    @SuppressWarnings("unchecked")
    private Class<? extends Throwable>[] getIgnoreExceptions(Config config) {
        // 默认不忽略任何异常
        return new Class[0];
    }

    @Override
    public List<String> shortcutFieldOrder() {
        return Arrays.asList("name", "enabled", "failureRateThreshold", "slowCallDurationThreshold", 
                           "minimumNumberOfCalls", "granularity");
    }

    /**
     * 配置类
     */
    public static class Config {
        private String name;
        private boolean enabled = true;
        private float failureRateThreshold = 50.0f;
        private float slowCallRateThreshold = 100.0f;
        private long slowCallDurationThreshold = 2000L;
        private int minimumNumberOfCalls = 10;
        private int slidingWindowSize = 100;
        private long waitDurationInOpenState = 10000L;
        private int permittedNumberOfCallsInHalfOpenState = 3;
        private boolean automaticTransitionFromOpenToHalfOpenEnabled = true;
        private String granularity = "service"; // service, path, method_path
        private String fallbackResponse;

        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }
        
        public float getFailureRateThreshold() { return failureRateThreshold; }
        public void setFailureRateThreshold(float failureRateThreshold) { this.failureRateThreshold = failureRateThreshold; }
        
        public float getSlowCallRateThreshold() { return slowCallRateThreshold; }
        public void setSlowCallRateThreshold(float slowCallRateThreshold) { this.slowCallRateThreshold = slowCallRateThreshold; }
        
        public long getSlowCallDurationThreshold() { return slowCallDurationThreshold; }
        public void setSlowCallDurationThreshold(long slowCallDurationThreshold) { this.slowCallDurationThreshold = slowCallDurationThreshold; }
        
        public int getMinimumNumberOfCalls() { return minimumNumberOfCalls; }
        public void setMinimumNumberOfCalls(int minimumNumberOfCalls) { this.minimumNumberOfCalls = minimumNumberOfCalls; }
        
        public int getSlidingWindowSize() { return slidingWindowSize; }
        public void setSlidingWindowSize(int slidingWindowSize) { this.slidingWindowSize = slidingWindowSize; }
        
        public long getWaitDurationInOpenState() { return waitDurationInOpenState; }
        public void setWaitDurationInOpenState(long waitDurationInOpenState) { this.waitDurationInOpenState = waitDurationInOpenState; }
        
        public int getPermittedNumberOfCallsInHalfOpenState() { return permittedNumberOfCallsInHalfOpenState; }
        public void setPermittedNumberOfCallsInHalfOpenState(int permittedNumberOfCallsInHalfOpenState) { this.permittedNumberOfCallsInHalfOpenState = permittedNumberOfCallsInHalfOpenState; }
        
        public boolean isAutomaticTransitionFromOpenToHalfOpenEnabled() { return automaticTransitionFromOpenToHalfOpenEnabled; }
        public void setAutomaticTransitionFromOpenToHalfOpenEnabled(boolean automaticTransitionFromOpenToHalfOpenEnabled) { this.automaticTransitionFromOpenToHalfOpenEnabled = automaticTransitionFromOpenToHalfOpenEnabled; }
        
        public String getGranularity() { return granularity; }
        public void setGranularity(String granularity) { this.granularity = granularity; }
        
        public String getFallbackResponse() { return fallbackResponse; }
        public void setFallbackResponse(String fallbackResponse) { this.fallbackResponse = fallbackResponse; }
    }
}
