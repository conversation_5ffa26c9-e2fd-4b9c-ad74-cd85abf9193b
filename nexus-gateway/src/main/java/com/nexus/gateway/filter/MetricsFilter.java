package com.nexus.gateway.filter;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 监控指标收集过滤器
 * 收集请求数量、响应时间、错误率等指标
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component
@Slf4j
public class MetricsFilter extends AbstractGatewayFilterFactory<MetricsFilter.Config> {

    private final MeterRegistry meterRegistry;
    
    private static final String TIMER_START_TIME = "timer.start.time";
    private static final String METRICS_PREFIX = "nexus.gateway";

    public MetricsFilter(MeterRegistry meterRegistry) {
        super(Config.class);
        this.meterRegistry = meterRegistry;
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            if (!config.isEnabled()) {
                return chain.filter(exchange);
            }

            ServerHttpRequest request = exchange.getRequest();
            long startTime = System.nanoTime();
            
            // 保存开始时间
            exchange.getAttributes().put(TIMER_START_TIME, startTime);

            // 增加请求计数
            incrementRequestCounter(request, config);

            return chain.filter(exchange)
                    .doOnSuccess(aVoid -> recordMetrics(exchange, startTime, config, true))
                    .doOnError(throwable -> recordMetrics(exchange, startTime, config, false));
        };
    }

    /**
     * 增加请求计数器
     */
    private void incrementRequestCounter(ServerHttpRequest request, Config config) {
        try {
            String method = request.getMethod().name();
            String path = extractPathPattern(request.getURI().getPath());
            
            Counter.builder(METRICS_PREFIX + ".requests.total")
                    .description("Total number of requests")
                    .tag("method", method)
                    .tag("path", path)
                    .register(meterRegistry)
                    .increment();

            log.debug("请求计数器增加: method={}, path={}", method, path);
        } catch (Exception e) {
            log.error("增加请求计数器失败", e);
        }
    }

    /**
     * 记录指标
     */
    private void recordMetrics(ServerWebExchange exchange, long startTime, Config config, boolean success) {
        try {
            ServerHttpRequest request = exchange.getRequest();
            ServerHttpResponse response = exchange.getResponse();
            
            long endTime = System.nanoTime();
            long duration = endTime - startTime;
            
            String method = request.getMethod().name();
            String path = extractPathPattern(request.getURI().getPath());
            String status = response.getStatusCode() != null ? 
                    response.getStatusCode().toString() : "UNKNOWN";
            
            // 记录响应时间
            recordResponseTime(method, path, status, duration, config);
            
            // 记录响应状态
            recordResponseStatus(method, path, status, config);
            
            // 记录错误率
            if (!success || isErrorStatus(response.getStatusCode())) {
                recordError(method, path, status, config);
            }
            
            // 记录请求大小（如果配置启用）
            if (config.isIncludeRequestSize()) {
                recordRequestSize(request, method, path, config);
            }
            
            // 记录响应大小（如果配置启用）
            if (config.isIncludeResponseSize()) {
                recordResponseSize(response, method, path, config);
            }

            log.debug("指标记录完成: method={}, path={}, status={}, duration={}ms", 
                     method, path, status, TimeUnit.NANOSECONDS.toMillis(duration));
                     
        } catch (Exception e) {
            log.error("记录指标失败", e);
        }
    }

    /**
     * 记录响应时间
     */
    private void recordResponseTime(String method, String path, String status, long durationNanos, Config config) {
        Timer.builder(METRICS_PREFIX + ".request.duration")
                .description("Request duration in seconds")
                .tag("method", method)
                .tag("path", path)
                .tag("status", status)
                .register(meterRegistry)
                .record(durationNanos, TimeUnit.NANOSECONDS);
    }

    /**
     * 记录响应状态
     */
    private void recordResponseStatus(String method, String path, String status, Config config) {
        Counter.builder(METRICS_PREFIX + ".responses.total")
                .description("Total number of responses")
                .tag("method", method)
                .tag("path", path)
                .tag("status", status)
                .register(meterRegistry)
                .increment();
    }

    /**
     * 记录错误
     */
    private void recordError(String method, String path, String status, Config config) {
        Counter.builder(METRICS_PREFIX + ".errors.total")
                .description("Total number of errors")
                .tag("method", method)
                .tag("path", path)
                .tag("status", status)
                .register(meterRegistry)
                .increment();
    }

    /**
     * 记录请求大小
     */
    private void recordRequestSize(ServerHttpRequest request, String method, String path, Config config) {
        String contentLength = request.getHeaders().getFirst("Content-Length");
        if (contentLength != null) {
            try {
                long size = Long.parseLong(contentLength);
                Timer.builder(METRICS_PREFIX + ".request.size")
                        .description("Request size in bytes")
                        .tag("method", method)
                        .tag("path", path)
                        .register(meterRegistry)
                        .record(size, TimeUnit.NANOSECONDS); // 这里用纳秒作为单位记录字节数
            } catch (NumberFormatException e) {
                log.debug("无法解析Content-Length: {}", contentLength);
            }
        }
    }

    /**
     * 记录响应大小
     */
    private void recordResponseSize(ServerHttpResponse response, String method, String path, Config config) {
        String contentLength = response.getHeaders().getFirst("Content-Length");
        if (contentLength != null) {
            try {
                long size = Long.parseLong(contentLength);
                Timer.builder(METRICS_PREFIX + ".response.size")
                        .description("Response size in bytes")
                        .tag("method", method)
                        .tag("path", path)
                        .register(meterRegistry)
                        .record(size, TimeUnit.NANOSECONDS); // 这里用纳秒作为单位记录字节数
            } catch (NumberFormatException e) {
                log.debug("无法解析Content-Length: {}", contentLength);
            }
        }
    }

    /**
     * 提取路径模式（用于指标标签）
     */
    private String extractPathPattern(String path) {
        if (path == null) {
            return "unknown";
        }
        
        // 简化路径，移除具体的ID等参数
        String pattern = path
                .replaceAll("/\\d+", "/{id}")
                .replaceAll("/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}", "/{uuid}")
                .replaceAll("/[a-zA-Z0-9_-]{20,}", "/{token}");
        
        // 限制路径长度，避免标签值过长
        if (pattern.length() > 100) {
            pattern = pattern.substring(0, 97) + "...";
        }
        
        return pattern;
    }

    /**
     * 检查是否为错误状态码
     */
    private boolean isErrorStatus(HttpStatus status) {
        if (status == null) {
            return true;
        }
        return status.is4xxClientError() || status.is5xxServerError();
    }

    @Override
    public List<String> shortcutFieldOrder() {
        return Arrays.asList("enabled", "includeRequestSize", "includeResponseSize");
    }

    /**
     * 配置类
     */
    public static class Config {
        private boolean enabled = true;
        private boolean includeRequestSize = false;
        private boolean includeResponseSize = false;

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public boolean isIncludeRequestSize() {
            return includeRequestSize;
        }

        public void setIncludeRequestSize(boolean includeRequestSize) {
            this.includeRequestSize = includeRequestSize;
        }

        public boolean isIncludeResponseSize() {
            return includeResponseSize;
        }

        public void setIncludeResponseSize(boolean includeResponseSize) {
            this.includeResponseSize = includeResponseSize;
        }
    }
}
