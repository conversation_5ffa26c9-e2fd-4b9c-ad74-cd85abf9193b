package com.nexus.gateway.filter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.http.server.reactive.ServerHttpResponseDecorator;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * 请求日志记录过滤器
 * 记录请求和响应的详细信息，支持配置化控制
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component
@Slf4j
public class RequestLoggingFilter extends AbstractGatewayFilterFactory<RequestLoggingFilter.Config> {

    private static final String REQUEST_ID_HEADER = "X-Request-ID";
    private static final String START_TIME_ATTR = "startTime";
    private static final int MAX_LOG_LENGTH = 1000;

    public RequestLoggingFilter() {
        super(Config.class);
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            if (!config.isEnabled()) {
                return chain.filter(exchange);
            }

            // 生成请求ID
            String requestId = generateRequestId();
            long startTime = System.currentTimeMillis();
            
            // 保存开始时间
            exchange.getAttributes().put(START_TIME_ATTR, startTime);

            ServerHttpRequest request = exchange.getRequest();
            
            // 记录请求信息
            logRequest(request, requestId, config);

            // 如果需要记录请求体
            if (config.isIncludeRequestBody() && hasBody(request)) {
                return handleRequestWithBody(exchange, chain, config, requestId, startTime);
            } else {
                return handleRequestWithoutBody(exchange, chain, config, requestId, startTime);
            }
        };
    }

    /**
     * 处理有请求体的请求
     */
    private Mono<Void> handleRequestWithBody(ServerWebExchange exchange, 
                                           org.springframework.cloud.gateway.filter.GatewayFilterChain chain,
                                           Config config, String requestId, long startTime) {
        
        ServerHttpRequest request = exchange.getRequest();
        
        return DataBufferUtils.join(request.getBody())
                .map(dataBuffer -> {
                    byte[] bytes = new byte[dataBuffer.readableByteCount()];
                    dataBuffer.read(bytes);
                    DataBufferUtils.release(dataBuffer);
                    return bytes;
                })
                .defaultIfEmpty(new byte[0])
                .flatMap(bodyBytes -> {
                    // 记录请求体
                    if (bodyBytes.length > 0) {
                        String requestBody = new String(bodyBytes, StandardCharsets.UTF_8);
                        logRequestBody(requestId, requestBody, config);
                    }

                    // 重新包装请求
                    ServerHttpRequest wrappedRequest = new ServerHttpRequestDecorator(request) {
                        @Override
                        public Flux<DataBuffer> getBody() {
                            return Flux.just(exchange.getResponse().bufferFactory().wrap(bodyBytes));
                        }
                    };

                    // 添加请求ID到头部
                    wrappedRequest = wrappedRequest.mutate()
                            .header(REQUEST_ID_HEADER, requestId)
                            .build();

                    ServerWebExchange wrappedExchange = exchange.mutate()
                            .request(wrappedRequest)
                            .build();

                    return handleResponse(wrappedExchange, chain, config, requestId, startTime);
                });
    }

    /**
     * 处理无请求体的请求
     */
    private Mono<Void> handleRequestWithoutBody(ServerWebExchange exchange,
                                              org.springframework.cloud.gateway.filter.GatewayFilterChain chain,
                                              Config config, String requestId, long startTime) {
        
        // 添加请求ID到头部
        ServerHttpRequest wrappedRequest = exchange.getRequest().mutate()
                .header(REQUEST_ID_HEADER, requestId)
                .build();

        ServerWebExchange wrappedExchange = exchange.mutate()
                .request(wrappedRequest)
                .build();

        return handleResponse(wrappedExchange, chain, config, requestId, startTime);
    }

    /**
     * 处理响应
     */
    private Mono<Void> handleResponse(ServerWebExchange exchange,
                                    org.springframework.cloud.gateway.filter.GatewayFilterChain chain,
                                    Config config, String requestId, long startTime) {
        
        ServerHttpResponse response = exchange.getResponse();
        
        if (config.isIncludeResponseBody()) {
            ServerHttpResponseDecorator decoratedResponse = new ServerHttpResponseDecorator(response) {
                @Override
                public Mono<Void> writeWith(org.reactivestreams.Publisher<? extends DataBuffer> body) {
                    if (body instanceof Flux) {
                        Flux<? extends DataBuffer> fluxBody = (Flux<? extends DataBuffer>) body;
                        return super.writeWith(fluxBody.buffer().map(dataBuffers -> {
                            // 合并所有数据缓冲区
                            DataBuffer joinedBuffer = response.bufferFactory().join(dataBuffers);
                            byte[] content = new byte[joinedBuffer.readableByteCount()];
                            joinedBuffer.read(content);
                            DataBufferUtils.release(joinedBuffer);

                            // 记录响应体
                            String responseBody = new String(content, StandardCharsets.UTF_8);
                            logResponse(exchange.getRequest(), response, requestId, startTime, responseBody, config);

                            return response.bufferFactory().wrap(content);
                        }));
                    }
                    return super.writeWith(body);
                }
            };

            return chain.filter(exchange.mutate().response(decoratedResponse).build());
        } else {
            return chain.filter(exchange)
                    .then(Mono.fromRunnable(() -> {
                        logResponse(exchange.getRequest(), response, requestId, startTime, null, config);
                    }));
        }
    }

    /**
     * 记录请求信息
     */
    private void logRequest(ServerHttpRequest request, String requestId, Config config) {
        try {
            StringBuilder logBuilder = new StringBuilder();
            logBuilder.append("\n=== 请求开始 ===");
            logBuilder.append("\n请求ID: ").append(requestId);
            logBuilder.append("\n时间: ").append(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            logBuilder.append("\n方法: ").append(request.getMethod());
            logBuilder.append("\n路径: ").append(request.getURI().getPath());
            logBuilder.append("\n查询参数: ").append(request.getURI().getQuery());
            logBuilder.append("\n客户端IP: ").append(getClientIpAddress(request));
            
            if (config.isIncludeHeaders()) {
                logBuilder.append("\n请求头:");
                request.getHeaders().forEach((name, values) -> {
                    if (!isSensitiveHeader(name)) {
                        logBuilder.append("\n  ").append(name).append(": ").append(String.join(", ", values));
                    }
                });
            }
            
            log.info(logBuilder.toString());
        } catch (Exception e) {
            log.error("记录请求信息失败: requestId={}", requestId, e);
        }
    }

    /**
     * 记录请求体
     */
    private void logRequestBody(String requestId, String requestBody, Config config) {
        try {
            String truncatedBody = truncateIfNeeded(requestBody, config.getMaxLogLength());
            log.info("\n请求体 [{}]: {}", requestId, truncatedBody);
        } catch (Exception e) {
            log.error("记录请求体失败: requestId={}", requestId, e);
        }
    }

    /**
     * 记录响应信息
     */
    private void logResponse(ServerHttpRequest request, ServerHttpResponse response, 
                           String requestId, long startTime, String responseBody, Config config) {
        try {
            long duration = System.currentTimeMillis() - startTime;
            
            StringBuilder logBuilder = new StringBuilder();
            logBuilder.append("\n=== 响应结束 ===");
            logBuilder.append("\n请求ID: ").append(requestId);
            logBuilder.append("\n状态码: ").append(response.getStatusCode());
            logBuilder.append("\n处理时间: ").append(duration).append("ms");
            
            if (config.isIncludeHeaders()) {
                logBuilder.append("\n响应头:");
                response.getHeaders().forEach((name, values) -> {
                    if (!isSensitiveHeader(name)) {
                        logBuilder.append("\n  ").append(name).append(": ").append(String.join(", ", values));
                    }
                });
            }
            
            if (responseBody != null && config.isIncludeResponseBody()) {
                String truncatedBody = truncateIfNeeded(responseBody, config.getMaxLogLength());
                logBuilder.append("\n响应体: ").append(truncatedBody);
            }
            
            log.info(logBuilder.toString());
        } catch (Exception e) {
            log.error("记录响应信息失败: requestId={}", requestId, e);
        }
    }

    /**
     * 生成请求ID
     */
    private String generateRequestId() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 8);
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(ServerHttpRequest request) {
        String xForwardedFor = request.getHeaders().getFirst("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeaders().getFirst("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddress() != null ? 
                request.getRemoteAddress().getAddress().getHostAddress() : "unknown";
    }

    /**
     * 检查是否有请求体
     */
    private boolean hasBody(ServerHttpRequest request) {
        String contentLength = request.getHeaders().getFirst(HttpHeaders.CONTENT_LENGTH);
        return contentLength != null && !contentLength.equals("0");
    }

    /**
     * 检查是否为敏感头部
     */
    private boolean isSensitiveHeader(String headerName) {
        String lowerName = headerName.toLowerCase();
        return lowerName.contains("authorization") || 
               lowerName.contains("cookie") || 
               lowerName.contains("token") ||
               lowerName.contains("password");
    }

    /**
     * 截断过长的内容
     */
    private String truncateIfNeeded(String content, int maxLength) {
        if (content == null) {
            return null;
        }
        if (content.length() <= maxLength) {
            return content;
        }
        return content.substring(0, maxLength) + "... (truncated)";
    }

    @Override
    public List<String> shortcutFieldOrder() {
        return Arrays.asList("enabled", "includeHeaders", "includeRequestBody", "includeResponseBody", "maxLogLength");
    }

    /**
     * 配置类
     */
    public static class Config {
        private boolean enabled = true;
        private boolean includeHeaders = true;
        private boolean includeRequestBody = false;
        private boolean includeResponseBody = false;
        private int maxLogLength = MAX_LOG_LENGTH;

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public boolean isIncludeHeaders() {
            return includeHeaders;
        }

        public void setIncludeHeaders(boolean includeHeaders) {
            this.includeHeaders = includeHeaders;
        }

        public boolean isIncludeRequestBody() {
            return includeRequestBody;
        }

        public void setIncludeRequestBody(boolean includeRequestBody) {
            this.includeRequestBody = includeRequestBody;
        }

        public boolean isIncludeResponseBody() {
            return includeResponseBody;
        }

        public void setIncludeResponseBody(boolean includeResponseBody) {
            this.includeResponseBody = includeResponseBody;
        }

        public int getMaxLogLength() {
            return maxLogLength;
        }

        public void setMaxLogLength(int maxLogLength) {
            this.maxLogLength = maxLogLength;
        }
    }
}
