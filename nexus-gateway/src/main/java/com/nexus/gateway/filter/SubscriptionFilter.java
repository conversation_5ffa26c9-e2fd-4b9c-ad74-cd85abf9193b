package com.nexus.gateway.filter;

import com.nexus.gateway.service.SubscriptionValidationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;

/**
 * 订阅验证过滤器
 * 验证用户是否有访问特定服务的订阅权限
 */
@Slf4j
@Component
public class SubscriptionFilter extends AbstractGatewayFilterFactory<SubscriptionFilter.Config> {

    private final SubscriptionValidationService subscriptionValidationService;

    public SubscriptionFilter(SubscriptionValidationService subscriptionValidationService) {
        super(Config.class);
        this.subscriptionValidationService = subscriptionValidationService;
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            ServerHttpRequest request = exchange.getRequest();
            String path = request.getURI().getPath();

            // 提取用户ID（由认证过滤器添加）
            String userId = request.getHeaders().getFirst("X-User-ID");
            if (userId == null) {
                log.warn("缺少用户ID信息: {}", path);
                return handleForbidden(exchange, "用户信息缺失");
            }

            // 根据路径确定服务类型
            String serviceType = determineServiceType(path);
            if (serviceType == null) {
                log.debug("无需订阅验证的路径: {}", path);
                return chain.filter(exchange);
            }

            // 验证订阅权限
            return subscriptionValidationService.validateSubscription(userId, serviceType, path)
                    .flatMap(validationResult -> {
                        if (validationResult.isValid()) {
                            log.debug("订阅验证通过: 用户ID {}, 服务类型 {}, 路径 {}", 
                                    userId, serviceType, path);
                            
                            // 添加订阅信息到请求头
                            ServerHttpRequest modifiedRequest = request.mutate()
                                    .header("X-Subscription-ID", validationResult.getSubscriptionId())
                                    .header("X-Service-Type", serviceType)
                                    .build();

                            ServerWebExchange modifiedExchange = exchange.mutate()
                                    .request(modifiedRequest)
                                    .build();

                            // 记录API调用
                            return subscriptionValidationService.recordApiCall(userId, serviceType)
                                    .then(chain.filter(modifiedExchange))
                                    .onErrorResume(error -> {
                                        log.error("记录API调用失败: {}", error.getMessage());
                                        // 即使记录失败也继续处理请求
                                        return chain.filter(modifiedExchange);
                                    });
                        } else {
                            log.warn("订阅验证失败: 用户ID {}, 服务类型 {}, 原因 {}", 
                                    userId, serviceType, validationResult.getReason());
                            return handleForbidden(exchange, validationResult.getReason());
                        }
                    })
                    .onErrorResume(error -> {
                        log.error("订阅验证过程中发生异常: {} - {}", path, error.getMessage());
                        return handleForbidden(exchange, "订阅验证失败");
                    });
        };
    }

    /**
     * 根据路径确定服务类型
     */
    private String determineServiceType(String path) {
        if (path.startsWith("/api/v1/unified-mcp")) {
            // UnifiedMCP接口需要订阅验证
            if (path.contains("/tools/")) {
                return "MCP_TOOLS";
            } else if (path.contains("/resources/")) {
                return "MCP_RESOURCES";
            } else {
                return "MCP_SERVICE";
            }
        }
        
        // 其他路径可能不需要订阅验证
        return null;
    }

    /**
     * 处理禁止访问请求
     */
    private Mono<Void> handleForbidden(ServerWebExchange exchange, String message) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.FORBIDDEN);
        response.getHeaders().add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);

        String body = String.format(
                "{\"success\":false,\"error\":\"Forbidden\",\"message\":\"%s\",\"code\":403,\"timestamp\":%d}",
                message, System.currentTimeMillis()
        );

        DataBuffer buffer = response.bufferFactory().wrap(body.getBytes(StandardCharsets.UTF_8));
        return response.writeWith(Mono.just(buffer));
    }

    /**
     * 配置类
     */
    public static class Config {
        // 可以添加配置参数
    }
}
