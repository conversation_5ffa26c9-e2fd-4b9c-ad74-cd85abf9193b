package com.nexus.gateway.service;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.Date;

/**
 * JWT服务
 * 负责JWT令牌的验证和解析
 */
@Slf4j
@Service
public class JwtService {

    private final SecretKey secretKey;
    private final long jwtExpiration;

    public JwtService(@Value("${nexus.auth.jwt.secret:nexus-microservices-jwt-secret-key-2024}") String jwtSecret,
                     @Value("${nexus.auth.jwt.expiration:86400}") long jwtExpiration) {
        log.info("JWT Service初始化 - 密钥: {}...", jwtSecret.substring(0, Math.min(jwtSecret.length(), 20))); // 记录前20个字符用于调试
        // 确保密钥长度足够（至少256位）
        if (jwtSecret.length() < 32) {
            jwtSecret = jwtSecret + "0123456789abcdef0123456789abcdef";
            log.info("JWT密钥长度不足，已补足到32位以上");
        }
        this.secretKey = Keys.hmacShaKeyFor(jwtSecret.getBytes(StandardCharsets.UTF_8));
        this.jwtExpiration = jwtExpiration * 1000; // 转换为毫秒
        log.info("JWT Service初始化完成 - 过期时间: {}ms", this.jwtExpiration);
    }

    /**
     * 验证JWT令牌
     */
    public boolean validateToken(String token) {
        log.debug("开始验证JWT令牌: {}...", token.substring(0, Math.min(token.length(), 20))); // 记录前20个字符用于调试
        try {
            Jwts.parserBuilder()
                    .setSigningKey(secretKey)
                    .build()
                    .parseClaimsJws(token);
            log.debug("JWT令牌验证成功");
            return true;
        } catch (ExpiredJwtException e) {
            log.warn("JWT令牌已过期: {}", e.getMessage());
            return false;
        } catch (UnsupportedJwtException e) {
            log.warn("不支持的JWT令牌: {}", e.getMessage());
            return false;
        } catch (MalformedJwtException e) {
            log.warn("格式错误的JWT令牌: {}", e.getMessage());
            return false;
        } catch (SecurityException e) {
            log.warn("JWT令牌签名验证失败: {}", e.getMessage());
            return false;
        } catch (IllegalArgumentException e) {
            log.warn("JWT令牌参数错误: {}", e.getMessage());
            return false;
        } catch (Exception e) {
            log.error("JWT令牌验证异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 提取用户ID
     */
    public String extractUserId(String token) {
        try {
            Claims claims = extractClaims(token);
            return claims.get("userId", String.class);
        } catch (Exception e) {
            log.error("提取用户ID失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 提取用户名
     */
    public String extractUsername(String token) {
        try {
            Claims claims = extractClaims(token);
            return claims.getSubject();
        } catch (Exception e) {
            log.error("提取用户名失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 提取用户角色
     */
    public String extractRole(String token) {
        try {
            Claims claims = extractClaims(token);
            return claims.get("role", String.class);
        } catch (Exception e) {
            log.error("提取用户角色失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 提取令牌过期时间
     */
    public Date extractExpiration(String token) {
        try {
            Claims claims = extractClaims(token);
            return claims.getExpiration();
        } catch (Exception e) {
            log.error("提取令牌过期时间失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 检查令牌是否过期
     */
    public boolean isTokenExpired(String token) {
        try {
            Date expiration = extractExpiration(token);
            return expiration != null && expiration.before(new Date());
        } catch (Exception e) {
            log.error("检查令牌过期状态失败: {}", e.getMessage());
            return true;
        }
    }

    /**
     * 提取所有声明
     */
    private Claims extractClaims(String token) {
        return Jwts.parserBuilder()
                .setSigningKey(secretKey)
                .build()
                .parseClaimsJws(token)
                .getBody();
    }

    /**
     * 生成JWT令牌（用于测试或其他场景）
     */
    public String generateToken(String username, String userId, String role) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + jwtExpiration);

        return Jwts.builder()
                .setSubject(username)
                .claim("userId", userId)
                .claim("role", role)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(secretKey, SignatureAlgorithm.HS256)
                .compact();
    }

    /**
     * 刷新JWT令牌
     */
    public String refreshToken(String token) {
        try {
            Claims claims = extractClaims(token);
            String username = claims.getSubject();
            String userId = claims.get("userId", String.class);
            String role = claims.get("role", String.class);

            return generateToken(username, userId, role);
        } catch (Exception e) {
            log.error("刷新令牌失败: {}", e.getMessage());
            throw new RuntimeException("无法刷新令牌", e);
        }
    }

    /**
     * 获取令牌剩余有效时间（秒）
     */
    public long getTokenRemainingTime(String token) {
        try {
            Date expiration = extractExpiration(token);
            if (expiration == null) {
                return 0;
            }
            
            long remainingTime = expiration.getTime() - System.currentTimeMillis();
            return Math.max(0, remainingTime / 1000);
        } catch (Exception e) {
            log.error("获取令牌剩余时间失败: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * 验证令牌并提取用户信息
     */
    public UserInfo validateAndExtractUserInfo(String token) {
        if (!validateToken(token)) {
            return null;
        }

        try {
            String userId = extractUserId(token);
            String username = extractUsername(token);
            String role = extractRole(token);
            Date expiration = extractExpiration(token);

            if (userId == null || username == null) {
                return null;
            }

            return UserInfo.builder()
                    .userId(userId)
                    .username(username)
                    .role(role)
                    .expiration(expiration)
                    .build();
        } catch (Exception e) {
            log.error("提取用户信息失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 用户信息类
     */
    @lombok.Data
    @lombok.Builder
    public static class UserInfo {
        private String userId;
        private String username;
        private String role;
        private Date expiration;
        
        public boolean isExpired() {
            return expiration != null && expiration.before(new Date());
        }
        
        public long getRemainingTime() {
            if (expiration == null) {
                return 0;
            }
            long remainingTime = expiration.getTime() - System.currentTimeMillis();
            return Math.max(0, remainingTime / 1000);
        }
    }
}
