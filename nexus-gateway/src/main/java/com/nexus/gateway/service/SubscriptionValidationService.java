package com.nexus.gateway.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Map;

/**
 * 订阅验证服务
 * 负责验证用户的订阅权限和记录API调用
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SubscriptionValidationService {

    private final WebClient.Builder webClientBuilder;
    private final ReactiveRedisTemplate<String, Object> redisTemplate;

    private static final String SUBSCRIPTION_CACHE_PREFIX = "subscription:";
    private static final Duration CACHE_TTL = Duration.ofMinutes(5);

    /**
     * 验证用户订阅
     */
    public Mono<ValidationResult> validateSubscription(String userId, String serviceType, String path) {
        log.debug("验证用户订阅: 用户ID {}, 服务类型 {}, 路径 {}", userId, serviceType, path);

        String cacheKey = SUBSCRIPTION_CACHE_PREFIX + userId + ":" + serviceType;

        // 先从缓存中查找
        return redisTemplate.opsForValue().get(cacheKey)
                .cast(ValidationResult.class)
                .doOnNext(cached -> log.debug("从缓存获取订阅验证结果: {}", cached))
                .switchIfEmpty(
                        // 缓存未命中，调用订阅服务验证
                        validateSubscriptionFromService(userId, serviceType)
                                .doOnNext(result -> {
                                    // 缓存验证结果
                                    if (result.isValid()) {
                                        redisTemplate.opsForValue()
                                                .set(cacheKey, result, CACHE_TTL)
                                                .subscribe();
                                    }
                                })
                );
    }

    /**
     * 从订阅服务验证订阅
     */
    private Mono<ValidationResult> validateSubscriptionFromService(String userId, String serviceType) {
        WebClient webClient = webClientBuilder.build();

        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .scheme("lb")
                        .host("nexus-subscription-service")
                        .path("/api/v1/subscriptions/check")
                        .queryParam("userId", userId)
                        .queryParam("serviceType", serviceType)
                        .build())
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .bodyToMono(Map.class)
                .map(response -> {
                    Boolean success = (Boolean) response.get("success");
                    if (Boolean.TRUE.equals(success)) {
                        Map<String, Object> data = (Map<String, Object>) response.get("data");
                        Boolean hasValidSubscription = (Boolean) data.get("hasValidSubscription");
                        
                        if (Boolean.TRUE.equals(hasValidSubscription)) {
                            String subscriptionId = data.get("subscriptionId") != null ? 
                                    data.get("subscriptionId").toString() : "unknown";
                            return ValidationResult.valid(subscriptionId);
                        } else {
                            return ValidationResult.invalid("用户没有有效的订阅");
                        }
                    } else {
                        String message = (String) response.get("message");
                        return ValidationResult.invalid(message != null ? message : "订阅验证失败");
                    }
                })
                .onErrorReturn(ValidationResult.invalid("订阅服务不可用"))
                .doOnError(error -> log.error("调用订阅服务验证失败: {}", error.getMessage()));
    }

    /**
     * 记录API调用
     */
    public Mono<Void> recordApiCall(String userId, String serviceType) {
        log.debug("记录API调用: 用户ID {}, 服务类型 {}", userId, serviceType);

        WebClient webClient = webClientBuilder.build();

        return webClient.post()
                .uri(uriBuilder -> uriBuilder
                        .scheme("lb")
                        .host("nexus-subscription-service")
                        .path("/api/v1/subscriptions/record-call")
                        .queryParam("userId", userId)
                        .queryParam("serviceType", serviceType)
                        .build())
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .bodyToMono(Map.class)
                .then()
                .onErrorResume(error -> {
                    log.error("记录API调用失败: {}", error.getMessage());
                    return Mono.empty(); // 记录失败不影响主流程
                });
    }

    /**
     * 检查工具权限
     */
    public Mono<ValidationResult> checkToolPermission(String userId, String toolName) {
        log.debug("检查工具权限: 用户ID {}, 工具 {}", userId, toolName);

        String cacheKey = "tool_permission:" + userId + ":" + toolName;

        return redisTemplate.opsForValue().get(cacheKey)
                .cast(ValidationResult.class)
                .switchIfEmpty(
                        checkToolPermissionFromService(userId, toolName)
                                .doOnNext(result -> {
                                    if (result.isValid()) {
                                        redisTemplate.opsForValue()
                                                .set(cacheKey, result, Duration.ofMinutes(2))
                                                .subscribe();
                                    }
                                })
                );
    }

    /**
     * 从订阅服务检查工具权限
     */
    private Mono<ValidationResult> checkToolPermissionFromService(String userId, String toolName) {
        WebClient webClient = webClientBuilder.build();

        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .scheme("lb")
                        .host("nexus-subscription-service")
                        .path("/api/v1/subscriptions/permissions/check-tool")
                        .queryParam("userId", userId)
                        .queryParam("toolName", toolName)
                        .build())
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .bodyToMono(Map.class)
                .map(response -> {
                    Boolean success = (Boolean) response.get("success");
                    if (Boolean.TRUE.equals(success)) {
                        Map<String, Object> data = (Map<String, Object>) response.get("data");
                        Boolean allowed = (Boolean) data.get("allowed");
                        
                        if (Boolean.TRUE.equals(allowed)) {
                            String permissionId = data.get("permissionId") != null ? 
                                    data.get("permissionId").toString() : "unknown";
                            return ValidationResult.valid(permissionId);
                        } else {
                            String reason = (String) data.get("reason");
                            return ValidationResult.invalid(reason != null ? reason : "没有工具访问权限");
                        }
                    } else {
                        String message = (String) response.get("message");
                        return ValidationResult.invalid(message != null ? message : "权限检查失败");
                    }
                })
                .onErrorReturn(ValidationResult.invalid("权限服务不可用"));
    }

    /**
     * 检查资源权限
     */
    public Mono<ValidationResult> checkResourcePermission(String userId, String resourceName) {
        log.debug("检查资源权限: 用户ID {}, 资源 {}", userId, resourceName);

        String cacheKey = "resource_permission:" + userId + ":" + resourceName;

        return redisTemplate.opsForValue().get(cacheKey)
                .cast(ValidationResult.class)
                .switchIfEmpty(
                        checkResourcePermissionFromService(userId, resourceName)
                                .doOnNext(result -> {
                                    if (result.isValid()) {
                                        redisTemplate.opsForValue()
                                                .set(cacheKey, result, Duration.ofMinutes(2))
                                                .subscribe();
                                    }
                                })
                );
    }

    /**
     * 从订阅服务检查资源权限
     */
    private Mono<ValidationResult> checkResourcePermissionFromService(String userId, String resourceName) {
        WebClient webClient = webClientBuilder.build();

        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .scheme("lb")
                        .host("nexus-subscription-service")
                        .path("/api/v1/subscriptions/permissions/check-resource")
                        .queryParam("userId", userId)
                        .queryParam("resourceName", resourceName)
                        .build())
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .bodyToMono(Map.class)
                .map(response -> {
                    Boolean success = (Boolean) response.get("success");
                    if (Boolean.TRUE.equals(success)) {
                        Map<String, Object> data = (Map<String, Object>) response.get("data");
                        Boolean allowed = (Boolean) data.get("allowed");
                        
                        if (Boolean.TRUE.equals(allowed)) {
                            String permissionId = data.get("permissionId") != null ? 
                                    data.get("permissionId").toString() : "unknown";
                            return ValidationResult.valid(permissionId);
                        } else {
                            String reason = (String) data.get("reason");
                            return ValidationResult.invalid(reason != null ? reason : "没有资源访问权限");
                        }
                    } else {
                        String message = (String) response.get("message");
                        return ValidationResult.invalid(message != null ? message : "权限检查失败");
                    }
                })
                .onErrorReturn(ValidationResult.invalid("权限服务不可用"));
    }

    /**
     * 清除用户订阅缓存
     */
    public Mono<Void> clearUserSubscriptionCache(String userId) {
        String pattern = SUBSCRIPTION_CACHE_PREFIX + userId + ":*";
        return redisTemplate.keys(pattern)
                .flatMap(redisTemplate::delete)
                .then();
    }

    /**
     * 验证结果类
     */
    @lombok.Data
    @lombok.Builder
    @lombok.AllArgsConstructor
    @lombok.NoArgsConstructor
    public static class ValidationResult {
        private boolean valid;
        private String subscriptionId;
        private String reason;

        public static ValidationResult valid(String subscriptionId) {
            return ValidationResult.builder()
                    .valid(true)
                    .subscriptionId(subscriptionId)
                    .build();
        }

        public static ValidationResult invalid(String reason) {
            return ValidationResult.builder()
                    .valid(false)
                    .reason(reason)
                    .build();
        }
    }
}
