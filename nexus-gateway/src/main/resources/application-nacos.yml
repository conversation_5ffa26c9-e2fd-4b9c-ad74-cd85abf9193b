server:
  port: 8080

spring:
  application:
    name: nexus-gateway

  # 禁用Nacos配置导入，只使用服务发现
  # config:
  #   import: "optional:nacos:"

  # Spring Cloud Nacos配置
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        namespace: nexus-microservices
        group: DEFAULT_GROUP
        service: ${spring.application.name}
        metadata:
          version: 1.0.0
          zone: default
      config:
        import-check:
          enabled: false

# 本地开发时的fallback配置
---
spring:
  profiles: local
  datasource:
    url: **************************************
    username: nexus
    password: nexus123
    driver-class-name: org.postgresql.Driver
  redis:
    host: localhost
    port: 6379
    database: 5
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
