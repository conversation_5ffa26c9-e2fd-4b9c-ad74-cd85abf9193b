server:
  port: 8080

spring:
  application:
    name: nexus-gateway

  profiles:
    active: nacos

  config:
    import: "optional:nacos:nexus-gateway.yml"

  # 禁用gRPC相关的自动配置
  autoconfigure:
    exclude:
      - org.springframework.cloud.gateway.config.GatewayAutoConfiguration$GrpcConfiguration
  
  # Redis配置（用于限流和缓存）
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 3000ms

  # Spring Cloud Gateway配置
  cloud:
    gateway:
      # 服务发现配置 - 让gateway根据注册中心找到其他服务
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
          # 启用基于服务发现的路由
          # 移除 StripPrefix=1 过滤器
          filters: []

      # 全局CORS配置
      globalcors:
        cors-configurations:
          '[/**]':
            allowed-origin-patterns: "http://localhost:3000"
            allowed-methods: "*"
            allowed-headers: "*"
            allow-credentials: true
            max-age: 3600

      # 全局过滤器配置
      default-filters:
        - name: Retry
          args:
            retries: 3
            methods: GET,POST
            backoff:
              firstBackoff: 50ms
              maxBackoff: 500ms
        - AddRequestHeader=X-Gateway-Request-Id, static-gateway-id
        - AddRequestHeader=X-Gateway-Timestamp, static-timestamp
        - DedupeResponseHeader=Access-Control-Allow-Origin Access-Control-Allow-Credentials

      # 路由配置
      routes:
        # 认证服务路由
        - id: nexus-auth-service
          uri: lb://nexus-auth-service
          predicates:
            - Path=/auth/**
          filters:
            - StripPrefix=0

        # 实时服务路由 (WebSocket)
        - id: nexus-realtime-service
          uri: lb://nexus-realtime-service
          predicates:
            - Path=/realtime/**
          filters:
            - StripPrefix=0

        # 本地MCP服务路由
        - id: nexus-mcp-local-service
          uri: http://localhost:8082
          predicates:
            - Path=/api/v1/mcp/local/**
          filters:
            - RewritePath=/api/v1/mcp/local/(?<segment>.*), /mcp-local/api/v1/local-mcp/$\{segment}

        # 远程MCP服务路由
        - id: nexus-mcp-remote-service
          uri: http://localhost:8083
          predicates:
            - Path=/api/v1/mcp/remote/**
          filters:
            - RewritePath=/api/v1/mcp/remote/(?<segment>.*), /mcp-remote/api/v1/remote-mcp/$\{segment}

        # MCP命令服务路由
        - id: nexus-mcp-command-service
          uri: http://localhost:8083
          predicates:
            - Path=/api/mcp/command/**
          filters:
            - RewritePath=/api/mcp/command/(?<segment>.*), /mcp-remote/api/mcp/command/$\{segment}

    
    # Nacos配置
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        namespace: nexus-microservices
        group: DEFAULT_GROUP
        service: ${spring.application.name}
        metadata:
          version: 1.0.0
          zone: default
        # 启用服务发现
        enabled: true
        # 心跳间隔
        heart-beat-interval: 5000
        # 心跳超时
        heart-beat-timeout: 15000
      config:
        server-addr: 127.0.0.1:8848
        namespace: nexus-microservices
        group: DEFAULT_GROUP
        file-extension: yml
        shared-configs:
          - data-id: nexus-common-config.yml
            group: DEFAULT_GROUP
            refresh: true

    # 负载均衡配置
    loadbalancer:
      ribbon:
        enabled: false
      # 启用Nacos负载均衡
      nacos:
        enabled: true

# 网关配置
nexus:
  gateway:
    # 认证配置
    auth:
      # 跳过认证的路径
      skip-auth-paths:
        - /auth/**
        - /actuator/**
        - /fallback
        - /favicon.ico
    
    # 限流配置
    rate-limit:
      # 启用限流
      enabled: true
      # 默认限流策略
      default-replenish-rate: 10
      default-burst-capacity: 20
      # 限流键解析器
      key-resolver: user # user, ip, path
    
    # 熔断配置
    circuit-breaker:
      # 启用熔断
      enabled: true
      # 失败率阈值
      failure-rate-threshold: 50
      # 慢调用阈值
      slow-call-duration-threshold: 2000ms
      # 最小调用数
      minimum-number-of-calls: 10
    
    # 日志配置
    logging:
      # 启用请求日志
      enabled: true
      # 记录请求体
      include-request-body: false
      # 记录响应体
      include-response-body: false
      # 最大日志长度



# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,gateway,discovery
  endpoint:
    health:
      show-details: when-authorized
    gateway:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true

# 日志配置
logging:
  level:
    com.nexus.gateway: DEBUG
    com.nexus.common: DEBUG
    org.springframework.cloud.gateway: INFO
    org.springframework.web.reactive: INFO
    reactor.netty: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{50}] - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{50}] - %msg%n"
