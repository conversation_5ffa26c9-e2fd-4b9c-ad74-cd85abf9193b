package com.nexus.market;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 服务市场微服务启动类
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@SpringBootApplication(scanBasePackages = {"com.nexus.market", "com.nexus.common"})
@EnableDiscoveryClient
@EnableCaching
@EnableAsync
@EnableScheduling
public class MarketServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(MarketServiceApplication.class, args);
    }
}
