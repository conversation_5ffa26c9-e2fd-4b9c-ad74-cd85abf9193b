package com.nexus.market.client;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MCP服务客户端
 * 用于调用本地和远程MCP服务
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class MCPServiceClient {

    private final RestTemplate restTemplate;

    @Value("${service.mcp-local.url}")
    private String mcpLocalServiceUrl;

    @Value("${service.mcp-remote.url}")
    private String mcpRemoteServiceUrl;

    /**
     * 获取本地MCP服务列表
     */
    public List<Map<String, Object>> getLocalMCPServices() {
        try {
            String url = UriComponentsBuilder.fromHttpUrl(mcpLocalServiceUrl)
                    .path("/api/mcp/services")
                    .toUriString();

            log.debug("调用本地MCP服务: {}", url);

            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    null,
                    new ParameterizedTypeReference<Map<String, Object>>() {}
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                if (Boolean.TRUE.equals(responseBody.get("success"))) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> services = (List<Map<String, Object>>) responseBody.get("data");
                    log.debug("获取到 {} 个本地MCP服务", services != null ? services.size() : 0);
                    return services;
                }
            }

            log.warn("本地MCP服务响应异常: {}", response.getStatusCode());
            return Collections.emptyList();

        } catch (Exception e) {
            log.error("调用本地MCP服务失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取远程MCP服务列表
     */
    public List<Map<String, Object>> getRemoteMCPServices() {
        try {
            String url = UriComponentsBuilder.fromHttpUrl(mcpRemoteServiceUrl)
                    .path("/api/mcp/remote/services")
                    .toUriString();

            log.debug("调用远程MCP服务: {}", url);

            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    null,
                    new ParameterizedTypeReference<Map<String, Object>>() {}
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                if (Boolean.TRUE.equals(responseBody.get("success"))) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> services = (List<Map<String, Object>>) responseBody.get("data");
                    log.debug("获取到 {} 个远程MCP服务", services != null ? services.size() : 0);
                    return services;
                }
            }

            log.warn("远程MCP服务响应异常: {}", response.getStatusCode());
            return Collections.emptyList();

        } catch (Exception e) {
            log.error("调用远程MCP服务失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取MCP服务详情
     */
    public Map<String, Object> getMCPServiceDetail(String serviceName, boolean isRemote) {
        try {
            String baseUrl = isRemote ? mcpRemoteServiceUrl : mcpLocalServiceUrl;
            String path = isRemote ? "/api/mcp/remote/services/" : "/api/mcp/services/";
            
            String url = UriComponentsBuilder.fromHttpUrl(baseUrl)
                    .path(path + serviceName)
                    .toUriString();

            log.debug("获取MCP服务详情: {}", url);

            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    null,
                    new ParameterizedTypeReference<Map<String, Object>>() {}
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                if (Boolean.TRUE.equals(responseBody.get("success"))) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> serviceDetail = (Map<String, Object>) responseBody.get("data");
                    log.debug("获取到MCP服务详情: {}", serviceName);
                    return serviceDetail;
                }
            }

            log.warn("获取MCP服务详情失败: {} - {}", serviceName, response.getStatusCode());
            return Collections.emptyMap();

        } catch (Exception e) {
            log.error("获取MCP服务详情异常: {} - {}", serviceName, e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    /**
     * 检查MCP服务状态
     */
    public Map<String, Object> checkMCPServiceStatus(String serviceName, boolean isRemote) {
        try {
            String baseUrl = isRemote ? mcpRemoteServiceUrl : mcpLocalServiceUrl;
            String path = isRemote ? "/api/mcp/remote/services/" : "/api/mcp/services/";
            
            String url = UriComponentsBuilder.fromHttpUrl(baseUrl)
                    .path(path + serviceName + "/status")
                    .toUriString();

            log.debug("检查MCP服务状态: {}", url);

            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    null,
                    new ParameterizedTypeReference<Map<String, Object>>() {}
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                if (Boolean.TRUE.equals(responseBody.get("success"))) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> status = (Map<String, Object>) responseBody.get("data");
                    log.debug("获取到MCP服务状态: {} - {}", serviceName, status.get("status"));
                    return status;
                }
            }

            log.warn("检查MCP服务状态失败: {} - {}", serviceName, response.getStatusCode());
            Map<String, Object> unknownStatus = new HashMap<>();
            unknownStatus.put("status", "UNKNOWN");
            unknownStatus.put("connectionStatus", "DISCONNECTED");
            return unknownStatus;

        } catch (Exception e) {
            log.error("检查MCP服务状态异常: {} - {}", serviceName, e.getMessage(), e);
            Map<String, Object> errorStatus = new HashMap<>();
            errorStatus.put("status", "ERROR");
            errorStatus.put("connectionStatus", "ERROR");
            errorStatus.put("error", e.getMessage());
            return errorStatus;
        }
    }
}
