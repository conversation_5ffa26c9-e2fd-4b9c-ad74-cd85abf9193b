package com.nexus.market.client;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;
import java.util.Map;

/**
 * 订阅服务客户端
 * 用于获取用户订阅信息
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SubscriptionServiceClient {

    private final RestTemplate restTemplate;

    @Value("${service.subscription.url}")
    private String subscriptionServiceUrl;

    /**
     * 获取用户的所有订阅
     */
    public List<Map<String, Object>> getUserSubscriptions(Long userId) {
        try {
            String url = UriComponentsBuilder.fromHttpUrl(subscriptionServiceUrl)
                    .path("/api/subscriptions/user/" + userId)
                    .toUriString();

            log.debug("获取用户订阅信息: userId={}", userId);

            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    null,
                    new ParameterizedTypeReference<Map<String, Object>>() {}
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                if (Boolean.TRUE.equals(responseBody.get("success"))) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> subscriptions = (List<Map<String, Object>>) responseBody.get("data");
                    log.debug("获取到用户 {} 的 {} 个订阅", userId, subscriptions != null ? subscriptions.size() : 0);
                    return subscriptions;
                }
            }

            log.warn("获取用户订阅信息失败: userId={}, status={}", userId, response.getStatusCode());
            return List.of();

        } catch (Exception e) {
            log.error("获取用户订阅信息异常: userId={}, error={}", userId, e.getMessage(), e);
            return List.of();
        }
    }

    /**
     * 获取用户对特定服务的订阅信息
     */
    public Map<String, Object> getUserServiceSubscription(Long userId, String serviceName) {
        try {
            String url = UriComponentsBuilder.fromHttpUrl(subscriptionServiceUrl)
                    .path("/api/subscriptions/user/" + userId + "/service/" + serviceName)
                    .toUriString();

            log.debug("获取用户服务订阅信息: userId={}, serviceName={}", userId, serviceName);

            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    null,
                    new ParameterizedTypeReference<Map<String, Object>>() {}
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                if (Boolean.TRUE.equals(responseBody.get("success"))) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> subscription = (Map<String, Object>) responseBody.get("data");
                    log.debug("获取到用户服务订阅信息: userId={}, serviceName={}", userId, serviceName);
                    return subscription;
                }
            }

            log.debug("用户未订阅该服务: userId={}, serviceName={}", userId, serviceName);
            return Map.of();

        } catch (Exception e) {
            log.error("获取用户服务订阅信息异常: userId={}, serviceName={}, error={}", 
                     userId, serviceName, e.getMessage(), e);
            return Map.of();
        }
    }

    /**
     * 获取服务的订阅统计信息
     */
    public Map<String, Object> getServiceSubscriptionStats(String serviceName) {
        try {
            String url = UriComponentsBuilder.fromHttpUrl(subscriptionServiceUrl)
                    .path("/api/subscriptions/service/" + serviceName + "/stats")
                    .toUriString();

            log.debug("获取服务订阅统计: serviceName={}", serviceName);

            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    null,
                    new ParameterizedTypeReference<Map<String, Object>>() {}
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                if (Boolean.TRUE.equals(responseBody.get("success"))) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> stats = (Map<String, Object>) responseBody.get("data");
                    log.debug("获取到服务订阅统计: serviceName={}", serviceName);
                    return stats;
                }
            }

            log.warn("获取服务订阅统计失败: serviceName={}, status={}", serviceName, response.getStatusCode());
            return Map.of();

        } catch (Exception e) {
            log.error("获取服务订阅统计异常: serviceName={}, error={}", serviceName, e.getMessage(), e);
            return Map.of();
        }
    }

    /**
     * 获取用户的使用统计信息
     */
    public Map<String, Object> getUserUsageStats(Long userId, String serviceName) {
        try {
            String url = UriComponentsBuilder.fromHttpUrl(subscriptionServiceUrl)
                    .path("/api/subscriptions/user/" + userId + "/service/" + serviceName + "/usage")
                    .toUriString();

            log.debug("获取用户使用统计: userId={}, serviceName={}", userId, serviceName);

            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    null,
                    new ParameterizedTypeReference<Map<String, Object>>() {}
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                if (Boolean.TRUE.equals(responseBody.get("success"))) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> usage = (Map<String, Object>) responseBody.get("data");
                    log.debug("获取到用户使用统计: userId={}, serviceName={}", userId, serviceName);
                    return usage;
                }
            }

            log.debug("用户无使用统计: userId={}, serviceName={}", userId, serviceName);
            return Map.of();

        } catch (Exception e) {
            log.error("获取用户使用统计异常: userId={}, serviceName={}, error={}", 
                     userId, serviceName, e.getMessage(), e);
            return Map.of();
        }
    }
}
