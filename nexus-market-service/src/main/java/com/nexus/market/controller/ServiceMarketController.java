package com.nexus.market.controller;

import com.nexus.common.dto.UserDTO;
import com.nexus.common.response.ApiResponse;
import com.nexus.common.util.JwtUtil;
import com.nexus.market.dto.ServiceMarketDTO;
import com.nexus.market.service.ServiceMarketService;
import com.nexus.market.service.ServiceStatusService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 服务市场控制器
 * 提供服务市场相关的REST API
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/services")
@RequiredArgsConstructor
@Slf4j
@Validated
@Tag(name = "服务市场", description = "服务市场相关API")
public class ServiceMarketController {

    private final ServiceMarketService serviceMarketService;
    private final ServiceStatusService serviceStatusService;
    private final JwtUtil jwtUtil;

    /**
     * 获取服务市场列表
     */
    @GetMapping("/market")
    @Operation(summary = "获取服务市场列表", description = "获取带用户数据增强的服务市场列表，支持分页和过滤")
    public ResponseEntity<ApiResponse<ServiceMarketDTO.ServiceMarketResponseDTO>> getServiceMarket(
            @Parameter(description = "页码，从1开始") @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @Parameter(description = "每页数量") @RequestParam(defaultValue = "10") @Min(1) Integer size,
            @Parameter(description = "服务分类") @RequestParam(required = false) String category,
            @Parameter(description = "服务状态") @RequestParam(required = false) String status,
            @Parameter(description = "服务提供商") @RequestParam(required = false) String provider,
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "priority") String sortBy,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "desc") String sortOrder,
            HttpServletRequest request) {

        try {
            // 验证JWT并获取用户信息
            UserDTO currentUser = jwtUtil.getCurrentUserFromRequest(request);
            if (currentUser == null) {
                return ResponseEntity.status(401)
                        .body(ApiResponse.error(401, "未授权访问"));
            }

            // 构建查询参数
            ServiceMarketDTO.ServiceMarketQueryDTO queryDTO = ServiceMarketDTO.ServiceMarketQueryDTO.builder()
                    .page(page)
                    .size(Math.min(size, 100)) // 限制最大每页数量
                    .category(category)
                    .status(status)
                    .provider(provider)
                    .keyword(keyword)
                    .sortBy(sortBy)
                    .sortOrder(sortOrder)
                    .build();

            log.info("用户 {} 访问服务市场，查询参数: {}", currentUser.getUsername(), queryDTO);

            // 获取服务市场数据
            ServiceMarketDTO.ServiceMarketResponseDTO response = 
                    serviceMarketService.getServiceMarket(queryDTO, currentUser);

            log.info("用户 {} 获取服务市场成功，返回 {} 个服务", 
                    currentUser.getUsername(), response.getServices().size());

            return ResponseEntity.ok(ApiResponse.success("获取服务市场成功", response));

        } catch (Exception e) {
            log.error("获取服务市场失败: {}", e.getMessage(), e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("获取服务市场失败: " + e.getMessage()));
        }
    }

    /**
     * 获取服务详情
     */
    @GetMapping("/market/{serviceName}")
    @Operation(summary = "获取服务详情", description = "获取指定服务的详细信息")
    public ResponseEntity<ApiResponse<ServiceMarketDTO.ServiceDetailDTO>> getServiceDetail(
            @Parameter(description = "服务名称") @PathVariable @NotBlank String serviceName,
            HttpServletRequest request) {

        try {
            // 验证JWT并获取用户信息
            UserDTO currentUser = jwtUtil.getCurrentUserFromRequest(request);
            if (currentUser == null) {
                return ResponseEntity.status(401)
                        .body(ApiResponse.error(401, "未授权访问"));
            }

            log.info("用户 {} 获取服务详情: {}", currentUser.getUsername(), serviceName);

            // 获取服务详情
            ServiceMarketDTO.ServiceDetailDTO detail =
                    serviceMarketService.getServiceDetail(serviceName, currentUser);

            if (detail == null || detail.getServiceName() == null) {
                return ResponseEntity.status(404)
                        .body(ApiResponse.error(404, "服务不存在"));
            }

            log.info("用户 {} 获取服务详情成功: {}", currentUser.getUsername(), serviceName);

            return ResponseEntity.ok(ApiResponse.success("获取服务详情成功", detail));

        } catch (Exception e) {
            log.error("获取服务详情失败: {} - {}", serviceName, e.getMessage(), e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("获取服务详情失败: " + e.getMessage()));
        }
    }

    /**
     * 检查服务状态
     */
    @PostMapping("/market/{serviceName}/status/check")
    @Operation(summary = "检查服务状态", description = "手动检查指定服务的状态")
    public ResponseEntity<ApiResponse<Map<String, Object>>> checkServiceStatus(
            @Parameter(description = "服务名称") @PathVariable @NotBlank String serviceName,
            HttpServletRequest request) {

        try {
            // 验证JWT并获取用户信息
            UserDTO currentUser = jwtUtil.getCurrentUserFromRequest(request);
            if (currentUser == null) {
                return ResponseEntity.status(401)
                        .body(ApiResponse.error(401, "未授权访问"));
            }

            log.info("用户 {} 手动检查服务状态: {}", currentUser.getUsername(), serviceName);

            // 检查服务状态
            Map<String, Object> status = serviceStatusService.checkServiceStatusManually(serviceName);

            log.info("用户 {} 检查服务状态完成: {} - {}",
                    currentUser.getUsername(), serviceName, status.get("status"));

            return ResponseEntity.ok(ApiResponse.success("检查服务状态完成", status));

        } catch (Exception e) {
            log.error("检查服务状态失败: {} - {}", serviceName, e.getMessage(), e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("检查服务状态失败: " + e.getMessage()));
        }
    }

    /**
     * 获取服务状态统计
     */
    @GetMapping("/market/statistics/status")
    @Operation(summary = "获取服务状态统计", description = "获取所有服务的状态统计信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getServiceStatusStatistics(
            HttpServletRequest request) {

        try {
            // 验证JWT并获取用户信息
            UserDTO currentUser = jwtUtil.getCurrentUserFromRequest(request);
            if (currentUser == null) {
                return ResponseEntity.status(401)
                        .body(ApiResponse.error(401, "未授权访问"));
            }

            log.debug("用户 {} 获取服务状态统计", currentUser.getUsername());

            // 获取状态统计
            Map<String, Object> statistics = serviceStatusService.getServiceStatusStatistics();

            return ResponseEntity.ok(ApiResponse.success("获取服务状态统计成功", statistics));

        } catch (Exception e) {
            log.error("获取服务状态统计失败: {}", e.getMessage(), e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("获取服务状态统计失败: " + e.getMessage()));
        }
    }

    /**
     * 重置服务状态检查
     */
    @PostMapping("/market/status/reset")
    @Operation(summary = "重置服务状态检查", description = "重置所有服务的状态检查时间，强制重新检查")
    public ResponseEntity<ApiResponse<String>> resetServiceStatusCheck(
            HttpServletRequest request) {

        try {
            // 验证JWT并获取用户信息
            UserDTO currentUser = jwtUtil.getCurrentUserFromRequest(request);
            if (currentUser == null) {
                return ResponseEntity.status(401)
                        .body(ApiResponse.error(401, "未授权访问"));
            }

            log.info("用户 {} 重置服务状态检查", currentUser.getUsername());

            // 重置状态检查时间
            serviceStatusService.resetAllServiceStatusCheckTime();

            log.info("用户 {} 重置服务状态检查完成", currentUser.getUsername());

            return ResponseEntity.ok(ApiResponse.success("重置完成", "重置服务状态检查完成"));

        } catch (Exception e) {
            log.error("重置服务状态检查失败: {}", e.getMessage(), e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("重置服务状态检查失败: " + e.getMessage()));
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "服务健康检查接口")
    public ResponseEntity<ApiResponse<Map<String, Object>>> health() {
        Map<String, Object> healthInfo = Map.of(
                "status", "UP",
                "service", "nexus-market-service",
                "timestamp", System.currentTimeMillis()
        );
        return ResponseEntity.ok(ApiResponse.success("服务正常", healthInfo));
    }
}
