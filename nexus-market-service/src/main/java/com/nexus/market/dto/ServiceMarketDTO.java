package com.nexus.market.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 服务市场相关DTO
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public class ServiceMarketDTO {

    /**
     * 服务市场查询请求DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ServiceMarketQueryDTO {
        
        @Min(value = 1, message = "页码必须大于0")
        @Builder.Default
        private Integer page = 1;

        @Min(value = 1, message = "每页数量必须大于0")
        @Builder.Default
        private Integer size = 10;
        
        private String category;
        private String status;
        private String provider;
        private String keyword;
        private List<String> tags;
        @Builder.Default
        private String sortBy = "priority";
        @Builder.Default
        private String sortOrder = "desc";
    }

    /**
     * 服务市场响应DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ServiceMarketResponseDTO {
        
        private List<ServiceItemDTO> services;
        private Long totalCount;
        private Integer currentPage;
        private Integer totalPages;
        private Integer pageSize;
        private Boolean hasNext;
        private Boolean hasPrevious;
    }

    /**
     * 服务项DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ServiceItemDTO {
        
        private Long id;
        private String serviceName;
        private String displayName;
        private String description;
        private String serviceType;
        private String version;
        private Boolean isPublic;
        private String iconUrl;
        private String endpoint;
        private List<String> tags;
        private String category;
        private String provider;
        private String status;
        private String connectionStatus;
        private Long responseTimeMs;
        private LocalDateTime lastChecked;
        private String uptime;
        private Integer errorCount;
        
        // 功能信息
        private List<Map<String, Object>> tools;
        private Integer toolCount;
        private List<Map<String, Object>> resources;
        private Integer resourceCount;
        private List<String> capabilities;
        private Integer capabilityCount;
        
        // 定价信息
        private PricingDTO pricing;
        
        // 统计信息
        private StatisticsDTO statistics;
        
        // 用户相关信息
        private UserServiceInfoDTO userInfo;
        
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
        private String documentationUrl;
        private Integer priority;
    }

    /**
     * 定价信息DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PricingDTO {
        private String model;  // free, pay-per-use, subscription
        private Double price;
        private String currency;
        private String unit;
    }

    /**
     * 统计信息DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StatisticsDTO {
        private Long totalExecutions;
        private Double successRate;
        private Double avgResponseTime;
    }

    /**
     * 用户服务信息DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserServiceInfoDTO {
        private Boolean isSubscribed;
        private Boolean isRecommended;
        private Long usedCalls;
        private Long callLimit;
        private Double usagePercentage;
        private LocalDateTime subscriptionDate;
        private String subscriptionStatus;
        private LocalDateTime lastUsed;
    }

    /**
     * 服务详情DTO
     */
    @Data
    @EqualsAndHashCode(callSuper = false)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ServiceDetailDTO extends ServiceItemDTO {
        // 扩展详情信息
        private String fullDescription;
        private List<Map<String, Object>> examples;
        private Map<String, Object> configuration;
        private List<String> supportedFormats;
        private Map<String, Object> limits;
        private String changelog;
    }
}
