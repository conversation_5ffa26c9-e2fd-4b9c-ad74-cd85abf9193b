package com.nexus.market.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 服务配置实体类
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Entity
@Table(name = "service_configs")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceConfig {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "service_name", nullable = false, unique = true)
    private String serviceName;

    @Column(name = "display_name")
    private String displayName;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "service_type")
    private String serviceType;

    @Column(name = "version")
    private String version;

    @Column(name = "is_public")
    private Boolean isPublic;

    @Column(name = "icon_url")
    private String iconUrl;

    @Column(name = "endpoint")
    private String endpoint;

    @Column(name = "tags")
    private String tags;

    @Column(name = "documentation_url")
    private String documentationUrl;

    @Column(name = "priority")
    private Integer priority;

    @Column(name = "provider")
    private String provider;

    @Column(name = "category")
    private String category;

    @Column(name = "pricing_model")
    private String pricingModel;

    @Column(name = "price")
    private Double price;

    @Column(name = "currency")
    private String currency;

    @Column(name = "price_unit")
    private String priceUnit;

    @Column(name = "status")
    private String status;

    @Column(name = "connection_status")
    private String connectionStatus;

    @Column(name = "response_time_ms")
    private Long responseTimeMs;

    @Column(name = "last_checked")
    private LocalDateTime lastChecked;

    @Column(name = "uptime")
    private String uptime;

    @Column(name = "error_count")
    private Integer errorCount;

    @Column(name = "tool_count")
    private Integer toolCount;

    @Column(name = "resource_count")
    private Integer resourceCount;

    @Column(name = "capability_count")
    private Integer capabilityCount;

    @Column(name = "total_executions")
    private Long totalExecutions;

    @Column(name = "success_rate")
    private Double successRate;

    @Column(name = "avg_response_time")
    private Double avgResponseTime;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "updated_by")
    private String updatedBy;
}
