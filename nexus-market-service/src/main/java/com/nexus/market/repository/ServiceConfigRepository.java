package com.nexus.market.repository;

import com.nexus.market.entity.ServiceConfig;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 服务配置Repository
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Repository
public interface ServiceConfigRepository extends JpaRepository<ServiceConfig, Long> {

    /**
     * 根据服务名查找服务配置
     */
    Optional<ServiceConfig> findByServiceName(String serviceName);

    /**
     * 根据服务名和端点查找服务配置
     */
    Optional<ServiceConfig> findByServiceNameAndEndpoint(String serviceName, String endpoint);

    /**
     * 查找所有公开的服务
     */
    List<ServiceConfig> findByIsPublicTrue();

    /**
     * 根据状态查找服务
     */
    List<ServiceConfig> findByStatus(String status);

    /**
     * 根据分类查找服务
     */
    List<ServiceConfig> findByCategory(String category);

    /**
     * 根据提供商查找服务
     */
    List<ServiceConfig> findByProvider(String provider);

    /**
     * 分页查询公开服务
     */
    Page<ServiceConfig> findByIsPublicTrue(Pageable pageable);

    /**
     * 根据分类分页查询公开服务
     */
    Page<ServiceConfig> findByIsPublicTrueAndCategory(String category, Pageable pageable);

    /**
     * 根据状态分页查询公开服务
     */
    Page<ServiceConfig> findByIsPublicTrueAndStatus(String status, Pageable pageable);

    /**
     * 根据提供商分页查询公开服务
     */
    Page<ServiceConfig> findByIsPublicTrueAndProvider(String provider, Pageable pageable);

    /**
     * 复合条件分页查询
     */
    @Query("SELECT sc FROM ServiceConfig sc WHERE sc.isPublic = true " +
           "AND (:category IS NULL OR sc.category = :category) " +
           "AND (:status IS NULL OR sc.status = :status) " +
           "AND (:provider IS NULL OR sc.provider = :provider) " +
           "AND (:keyword IS NULL OR LOWER(sc.serviceName) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
           "     OR LOWER(sc.displayName) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
           "     OR LOWER(sc.description) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<ServiceConfig> findServicesWithFilters(
            @Param("category") String category,
            @Param("status") String status,
            @Param("provider") String provider,
            @Param("keyword") String keyword,
            Pageable pageable);

    /**
     * 根据标签查询服务
     */
    @Query("SELECT sc FROM ServiceConfig sc WHERE sc.isPublic = true " +
           "AND (:tag IS NULL OR sc.tags LIKE CONCAT('%', :tag, '%'))")
    List<ServiceConfig> findByTag(@Param("tag") String tag);

    /**
     * 查询高优先级服务
     */
    @Query("SELECT sc FROM ServiceConfig sc WHERE sc.isPublic = true " +
           "AND sc.priority >= :minPriority ORDER BY sc.priority DESC")
    List<ServiceConfig> findHighPriorityServices(@Param("minPriority") Integer minPriority);

    /**
     * 查询最近更新的服务
     */
    @Query("SELECT sc FROM ServiceConfig sc WHERE sc.isPublic = true " +
           "AND sc.updatedAt >= :since ORDER BY sc.updatedAt DESC")
    List<ServiceConfig> findRecentlyUpdatedServices(@Param("since") LocalDateTime since);

    /**
     * 统计各分类的服务数量
     */
    @Query("SELECT sc.category, COUNT(sc) FROM ServiceConfig sc " +
           "WHERE sc.isPublic = true GROUP BY sc.category")
    List<Object[]> countServicesByCategory();

    /**
     * 统计各状态的服务数量
     */
    @Query("SELECT sc.status, COUNT(sc) FROM ServiceConfig sc " +
           "WHERE sc.isPublic = true GROUP BY sc.status")
    List<Object[]> countServicesByStatus();

    /**
     * 统计各提供商的服务数量
     */
    @Query("SELECT sc.provider, COUNT(sc) FROM ServiceConfig sc " +
           "WHERE sc.isPublic = true GROUP BY sc.provider")
    List<Object[]> countServicesByProvider();

    /**
     * 查询需要状态检查的服务
     */
    @Query("SELECT sc FROM ServiceConfig sc WHERE sc.isPublic = true " +
           "AND (sc.lastChecked IS NULL OR sc.lastChecked < :threshold)")
    List<ServiceConfig> findServicesNeedingStatusCheck(@Param("threshold") LocalDateTime threshold);

    /**
     * 批量更新服务状态
     */
    @Query("UPDATE ServiceConfig sc SET sc.status = :status, sc.lastChecked = :lastChecked " +
           "WHERE sc.serviceName IN :serviceNames")
    int updateServiceStatus(@Param("serviceNames") List<String> serviceNames, 
                           @Param("status") String status, 
                           @Param("lastChecked") LocalDateTime lastChecked);
}
