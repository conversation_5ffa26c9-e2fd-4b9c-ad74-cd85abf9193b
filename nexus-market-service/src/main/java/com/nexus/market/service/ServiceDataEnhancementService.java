package com.nexus.market.service;

import com.nexus.common.dto.UserDTO;
import com.nexus.market.client.SubscriptionServiceClient;
import com.nexus.market.dto.ServiceMarketDTO;
import com.nexus.market.entity.ServiceConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 服务数据增强服务
 * 负责将MCP服务数据与用户订阅信息进行整合增强
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ServiceDataEnhancementService {

    private final SubscriptionServiceClient subscriptionServiceClient;

    /**
     * 增强服务数据：添加用户订阅和使用信息
     */
    public List<ServiceMarketDTO.ServiceItemDTO> enhanceServicesWithUserData(
            List<Map<String, Object>> services, UserDTO user) {
        
        if (services == null || services.isEmpty() || user == null) {
            return Collections.emptyList();
        }

        log.debug("为用户 {} 增强 {} 个服务的数据", user.getUsername(), services.size());

        // 获取用户的所有订阅信息
        List<Map<String, Object>> userSubscriptions = subscriptionServiceClient.getUserSubscriptions(user.getId());
        Map<String, Map<String, Object>> subscriptionMap = userSubscriptions.stream()
                .collect(Collectors.toMap(
                    sub -> (String) sub.get("serviceName"),
                    sub -> sub,
                    (existing, replacement) -> existing // 如果有重复，保留第一个
                ));

        log.debug("用户 {} 共有 {} 个订阅", user.getUsername(), subscriptionMap.size());

        // 增强每个服务的数据
        return services.stream()
                .map(service -> enhanceServiceWithUserData(service, user, subscriptionMap))
                .collect(Collectors.toList());
    }

    /**
     * 增强单个服务的用户数据
     */
    private ServiceMarketDTO.ServiceItemDTO enhanceServiceWithUserData(
            Map<String, Object> service, UserDTO user, Map<String, Map<String, Object>> subscriptionMap) {
        
        String serviceName = (String) service.get("serviceName");
        Map<String, Object> subscription = subscriptionMap.get(serviceName);

        // 构建基础服务信息
        ServiceMarketDTO.ServiceItemDTO serviceItem = buildServiceItem(service);

        // 添加用户相关信息
        ServiceMarketDTO.UserServiceInfoDTO userInfo = buildUserServiceInfo(subscription, user, serviceName);
        serviceItem.setUserInfo(userInfo);

        // 设置推荐状态
        boolean isRecommended = isServiceRecommendedForUser(service, user, subscription);
        if (userInfo != null) {
            userInfo.setIsRecommended(isRecommended);
        }

        log.debug("增强服务数据完成: {} - 订阅状态: {}, 推荐状态: {}", 
                 serviceName, userInfo != null ? userInfo.getIsSubscribed() : false, isRecommended);

        return serviceItem;
    }

    /**
     * 构建服务项DTO
     */
    private ServiceMarketDTO.ServiceItemDTO buildServiceItem(Map<String, Object> service) {
        return ServiceMarketDTO.ServiceItemDTO.builder()
                .id(getLongValue(service, "id"))
                .serviceName((String) service.get("serviceName"))
                .displayName((String) service.get("displayName"))
                .description((String) service.get("description"))
                .serviceType((String) service.get("serviceType"))
                .version((String) service.get("version"))
                .isPublic(getBooleanValue(service, "isPublic", true))
                .iconUrl((String) service.get("iconUrl"))
                .endpoint((String) service.get("endpoint"))
                .tags(getListValue(service, "tags"))
                .category((String) service.get("category"))
                .provider((String) service.get("provider"))
                .status((String) service.get("status"))
                .connectionStatus((String) service.get("connectionStatus"))
                .responseTimeMs(getLongValue(service, "responseTimeMs"))
                .lastChecked(getLocalDateTimeValue(service, "lastChecked"))
                .uptime((String) service.get("uptime"))
                .errorCount(getIntegerValue(service, "errorCount"))
                .tools(getListMapValue(service, "tools"))
                .toolCount(getIntegerValue(service, "toolCount"))
                .resources(getListMapValue(service, "resources"))
                .resourceCount(getIntegerValue(service, "resourceCount"))
                .capabilities(getListValue(service, "capabilities"))
                .capabilityCount(getIntegerValue(service, "capabilityCount"))
                .pricing(buildPricingDTO(service))
                .statistics(buildStatisticsDTO(service))
                .createdAt(getLocalDateTimeValue(service, "createdAt"))
                .updatedAt(getLocalDateTimeValue(service, "updatedAt"))
                .documentationUrl((String) service.get("documentationUrl"))
                .priority(getIntegerValue(service, "priority"))
                .build();
    }

    /**
     * 构建用户服务信息DTO
     */
    private ServiceMarketDTO.UserServiceInfoDTO buildUserServiceInfo(
            Map<String, Object> subscription, UserDTO user, String serviceName) {
        
        if (subscription == null || subscription.isEmpty()) {
            return ServiceMarketDTO.UserServiceInfoDTO.builder()
                    .isSubscribed(false)
                    .isRecommended(false)
                    .usedCalls(0L)
                    .callLimit(0L)
                    .usagePercentage(0.0)
                    .build();
        }

        Long usedCalls = getLongValue(subscription, "usedCalls");
        Long callLimit = getLongValue(subscription, "callLimit");
        Double usagePercentage = calculateUsagePercentage(usedCalls, callLimit);

        return ServiceMarketDTO.UserServiceInfoDTO.builder()
                .isSubscribed(true)
                .usedCalls(usedCalls)
                .callLimit(callLimit)
                .usagePercentage(usagePercentage)
                .subscriptionDate(getLocalDateTimeValue(subscription, "createdAt"))
                .subscriptionStatus((String) subscription.get("status"))
                .lastUsed(getLocalDateTimeValue(subscription, "lastUsed"))
                .build();
    }

    /**
     * 构建定价信息DTO
     */
    private ServiceMarketDTO.PricingDTO buildPricingDTO(Map<String, Object> service) {
        @SuppressWarnings("unchecked")
        Map<String, Object> pricing = (Map<String, Object>) service.get("pricing");
        
        if (pricing == null) {
            return ServiceMarketDTO.PricingDTO.builder()
                    .model("free")
                    .price(0.0)
                    .currency("USD")
                    .unit("")
                    .build();
        }

        return ServiceMarketDTO.PricingDTO.builder()
                .model((String) pricing.get("model"))
                .price(getDoubleValue(pricing, "price"))
                .currency((String) pricing.get("currency"))
                .unit((String) pricing.get("unit"))
                .build();
    }

    /**
     * 构建统计信息DTO
     */
    private ServiceMarketDTO.StatisticsDTO buildStatisticsDTO(Map<String, Object> service) {
        @SuppressWarnings("unchecked")
        Map<String, Object> statistics = (Map<String, Object>) service.get("statistics");
        
        if (statistics == null) {
            return ServiceMarketDTO.StatisticsDTO.builder()
                    .totalExecutions(0L)
                    .successRate(100.0)
                    .avgResponseTime(0.0)
                    .build();
        }

        return ServiceMarketDTO.StatisticsDTO.builder()
                .totalExecutions(getLongValue(statistics, "totalExecutions"))
                .successRate(getDoubleValue(statistics, "successRate"))
                .avgResponseTime(getDoubleValue(statistics, "avgResponseTime"))
                .build();
    }

    /**
     * 判断服务是否推荐给用户
     */
    private boolean isServiceRecommendedForUser(Map<String, Object> service, UserDTO user, Map<String, Object> subscription) {
        // 如果用户已订阅且使用率较高，推荐
        if (subscription != null) {
            Long usedCalls = getLongValue(subscription, "usedCalls");
            Long callLimit = getLongValue(subscription, "callLimit");
            if (calculateUsagePercentage(usedCalls, callLimit) > 50.0) {
                return true;
            }
        }

        // 如果是高优先级服务且用户未订阅，推荐
        Integer priority = getIntegerValue(service, "priority");
        if (subscription == null && priority != null && priority >= 8) {
            return true;
        }

        return false;
    }

    /**
     * 计算使用百分比
     */
    private Double calculateUsagePercentage(Long usedCalls, Long callLimit) {
        if (callLimit == null || callLimit <= 0) {
            return 0.0;
        }
        if (usedCalls == null) {
            return 0.0;
        }
        return Math.min(100.0, (usedCalls.doubleValue() / callLimit.doubleValue()) * 100.0);
    }

    /**
     * 将ServiceConfig转换为Map
     */
    public Map<String, Object> convertServiceConfigToMap(ServiceConfig config) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", config.getId());
        map.put("serviceName", config.getServiceName());
        map.put("displayName", config.getDisplayName());
        map.put("description", config.getDescription());
        map.put("serviceType", config.getServiceType());
        map.put("version", config.getVersion());
        map.put("isPublic", config.getIsPublic());
        map.put("iconUrl", config.getIconUrl());
        map.put("endpoint", config.getEndpoint());
        map.put("category", config.getCategory());
        map.put("provider", config.getProvider());
        map.put("status", config.getStatus());
        map.put("connectionStatus", config.getConnectionStatus());
        map.put("responseTimeMs", config.getResponseTimeMs());
        map.put("lastChecked", config.getLastChecked());
        map.put("uptime", config.getUptime());
        map.put("errorCount", config.getErrorCount());
        map.put("toolCount", config.getToolCount());
        map.put("resourceCount", config.getResourceCount());
        map.put("capabilityCount", config.getCapabilityCount());
        map.put("createdAt", config.getCreatedAt());
        map.put("updatedAt", config.getUpdatedAt());
        map.put("documentationUrl", config.getDocumentationUrl());
        map.put("priority", config.getPriority());

        // 解析标签
        if (config.getTags() != null && !config.getTags().trim().isEmpty()) {
            String[] tagArray = config.getTags().split(",");
            map.put("tags", Arrays.asList(tagArray));
        } else {
            map.put("tags", Collections.emptyList());
        }

        // 构建定价信息
        Map<String, Object> pricing = new HashMap<>();
        pricing.put("model", config.getPricingModel() != null ? config.getPricingModel() : "free");
        pricing.put("price", config.getPrice() != null ? config.getPrice() : 0.0);
        pricing.put("currency", config.getCurrency() != null ? config.getCurrency() : "USD");
        pricing.put("unit", config.getPriceUnit() != null ? config.getPriceUnit() : "");
        map.put("pricing", pricing);

        // 构建统计信息
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalExecutions", config.getTotalExecutions() != null ? config.getTotalExecutions() : 0L);
        statistics.put("successRate", config.getSuccessRate() != null ? config.getSuccessRate() : 100.0);
        statistics.put("avgResponseTime", config.getAvgResponseTime() != null ? config.getAvgResponseTime() : 0.0);
        map.put("statistics", statistics);

        return map;
    }

    /**
     * 构建服务详情DTO
     */
    public ServiceMarketDTO.ServiceDetailDTO buildServiceDetail(
            ServiceConfig config, Map<String, Object> mcpData, UserDTO user) {
        
        // 合并配置和MCP数据
        Map<String, Object> mergedData = new HashMap<>();
        if (config != null) {
            mergedData.putAll(convertServiceConfigToMap(config));
        }
        if (mcpData != null && !mcpData.isEmpty()) {
            mergedData.putAll(mcpData);
        }

        // 构建基础服务项
        ServiceMarketDTO.ServiceItemDTO baseItem = buildServiceItem(mergedData);

        // 获取用户订阅信息
        String serviceName = (String) mergedData.get("serviceName");
        Map<String, Object> subscription = subscriptionServiceClient
                .getUserServiceSubscription(user.getId(), serviceName);
        
        ServiceMarketDTO.UserServiceInfoDTO userInfo = buildUserServiceInfo(subscription, user, serviceName);
        baseItem.setUserInfo(userInfo);

        // 转换为详情DTO并添加扩展信息
        ServiceMarketDTO.ServiceDetailDTO detailDTO = new ServiceMarketDTO.ServiceDetailDTO();

        // 复制基础信息
        detailDTO.setId(baseItem.getId());
        detailDTO.setServiceName(baseItem.getServiceName());
        detailDTO.setDisplayName(baseItem.getDisplayName());
        detailDTO.setDescription(baseItem.getDescription());
        detailDTO.setServiceType(baseItem.getServiceType());
        detailDTO.setVersion(baseItem.getVersion());
        detailDTO.setIsPublic(baseItem.getIsPublic());
        detailDTO.setIconUrl(baseItem.getIconUrl());
        detailDTO.setEndpoint(baseItem.getEndpoint());
        detailDTO.setTags(baseItem.getTags());
        detailDTO.setCategory(baseItem.getCategory());
        detailDTO.setProvider(baseItem.getProvider());
        detailDTO.setStatus(baseItem.getStatus());
        detailDTO.setConnectionStatus(baseItem.getConnectionStatus());
        detailDTO.setResponseTimeMs(baseItem.getResponseTimeMs());
        detailDTO.setLastChecked(baseItem.getLastChecked());
        detailDTO.setUptime(baseItem.getUptime());
        detailDTO.setErrorCount(baseItem.getErrorCount());
        detailDTO.setTools(baseItem.getTools());
        detailDTO.setToolCount(baseItem.getToolCount());
        detailDTO.setResources(baseItem.getResources());
        detailDTO.setResourceCount(baseItem.getResourceCount());
        detailDTO.setCapabilities(baseItem.getCapabilities());
        detailDTO.setCapabilityCount(baseItem.getCapabilityCount());
        detailDTO.setPricing(baseItem.getPricing());
        detailDTO.setStatistics(baseItem.getStatistics());
        detailDTO.setUserInfo(baseItem.getUserInfo());
        detailDTO.setCreatedAt(baseItem.getCreatedAt());
        detailDTO.setUpdatedAt(baseItem.getUpdatedAt());
        detailDTO.setDocumentationUrl(baseItem.getDocumentationUrl());
        detailDTO.setPriority(baseItem.getPriority());

        // 设置扩展详情信息
        detailDTO.setFullDescription((String) mergedData.get("fullDescription"));
        detailDTO.setExamples(getListMapValue(mergedData, "examples"));
        detailDTO.setConfiguration(getMapValue(mergedData, "configuration"));
        detailDTO.setSupportedFormats(getListValue(mergedData, "supportedFormats"));
        detailDTO.setLimits(getMapValue(mergedData, "limits"));
        detailDTO.setChangelog((String) mergedData.get("changelog"));

        return detailDTO;
    }

    // 辅助方法
    private Long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        return null;
    }

    private Integer getIntegerValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return null;
    }

    private Double getDoubleValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return null;
    }

    private Boolean getBooleanValue(Map<String, Object> map, String key, Boolean defaultValue) {
        Object value = map.get(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        return defaultValue;
    }

    @SuppressWarnings("unchecked")
    private List<String> getListValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof List) {
            return (List<String>) value;
        }
        return Collections.emptyList();
    }

    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> getListMapValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof List) {
            return (List<Map<String, Object>>) value;
        }
        return Collections.emptyList();
    }

    @SuppressWarnings("unchecked")
    private Map<String, Object> getMapValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Map) {
            return (Map<String, Object>) value;
        }
        return Collections.emptyMap();
    }

    private LocalDateTime getLocalDateTimeValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof LocalDateTime) {
            return (LocalDateTime) value;
        }
        if (value instanceof String) {
            try {
                return LocalDateTime.parse((String) value);
            } catch (Exception e) {
                log.warn("无法解析日期时间: {}", value);
            }
        }
        return null;
    }
}
