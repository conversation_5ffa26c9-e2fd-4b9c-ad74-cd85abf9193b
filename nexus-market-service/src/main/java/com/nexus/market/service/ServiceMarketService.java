package com.nexus.market.service;

import com.nexus.common.dto.UserDTO;
import com.nexus.market.client.MCPServiceClient;
import com.nexus.market.client.SubscriptionServiceClient;
import com.nexus.market.dto.ServiceMarketDTO;
import com.nexus.market.entity.ServiceConfig;
import com.nexus.market.repository.ServiceConfigRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 服务市场核心业务服务
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(readOnly = true)
public class ServiceMarketService {

    private final ServiceConfigRepository serviceConfigRepository;
    private final MCPServiceClient mcpServiceClient;
    private final SubscriptionServiceClient subscriptionServiceClient;
    private final ServiceDataEnhancementService dataEnhancementService;
    private final ServiceStatusService statusService;

    /**
     * 获取服务市场列表（带用户数据增强）
     */
    @Cacheable(value = "serviceMarket", key = "#queryDTO.toString() + '_' + #user.id", unless = "#result.services.isEmpty()")
    public ServiceMarketDTO.ServiceMarketResponseDTO getServiceMarket(
            ServiceMarketDTO.ServiceMarketQueryDTO queryDTO, UserDTO user) {
        
        log.info("用户 {} 访问服务市场，查询参数: {}", user.getUsername(), queryDTO);

        // 1. 获取所有MCP服务数据
        List<Map<String, Object>> allServices = getAllMCPServices();
        log.debug("获取到 {} 个MCP服务", allServices.size());

        // 2. 增强服务数据：添加用户订阅和使用信息
        List<ServiceMarketDTO.ServiceItemDTO> enhancedServices = 
                dataEnhancementService.enhanceServicesWithUserData(allServices, user);
        log.debug("完成用户数据增强，共 {} 个服务", enhancedServices.size());

        // 3. 应用过滤条件
        List<ServiceMarketDTO.ServiceItemDTO> filteredServices = 
                applyFilters(enhancedServices, queryDTO);
        log.debug("应用过滤条件后，剩余 {} 个服务", filteredServices.size());

        // 4. 应用排序
        List<ServiceMarketDTO.ServiceItemDTO> sortedServices = 
                applySorting(filteredServices, queryDTO);

        // 5. 应用分页
        ServiceMarketDTO.ServiceMarketResponseDTO response = 
                applyPagination(sortedServices, queryDTO);

        log.info("用户 {} 获取服务市场成功，返回 {} 个服务，总计 {} 个", 
                user.getUsername(), response.getServices().size(), response.getTotalCount());

        return response;
    }

    /**
     * 获取服务详情
     */
    @Cacheable(value = "serviceDetail", key = "#serviceName + '_' + #user.id")
    public ServiceMarketDTO.ServiceDetailDTO getServiceDetail(String serviceName, UserDTO user) {
        log.info("用户 {} 获取服务详情: {}", user.getUsername(), serviceName);

        // 1. 从数据库查找服务配置
        Optional<ServiceConfig> configOpt = serviceConfigRepository.findByServiceName(serviceName);
        
        // 2. 从MCP服务获取动态信息
        Map<String, Object> mcpServiceData = getMCPServiceData(serviceName);
        
        // 3. 合并数据并转换为详情DTO
        ServiceMarketDTO.ServiceDetailDTO detail = dataEnhancementService
                .buildServiceDetail(configOpt.orElse(null), mcpServiceData, user);

        log.info("用户 {} 获取服务详情成功: {}", user.getUsername(), serviceName);
        return detail;
    }

    /**
     * 获取所有MCP服务（本地 + 远程 + 数据库配置）
     */
    private List<Map<String, Object>> getAllMCPServices() {
        List<Map<String, Object>> allServices = new ArrayList<>();

        try {
            // 1. 获取本地MCP服务
            List<Map<String, Object>> localServices = mcpServiceClient.getLocalMCPServices();
            allServices.addAll(localServices);
            log.debug("获取到 {} 个本地MCP服务", localServices.size());

            // 2. 获取远程MCP服务
            List<Map<String, Object>> remoteServices = mcpServiceClient.getRemoteMCPServices();
            // 去重：避免本地和远程服务重复
            List<Map<String, Object>> uniqueRemoteServices = remoteServices.stream()
                    .filter(remote -> allServices.stream()
                            .noneMatch(local -> Objects.equals(
                                    remote.get("serviceName"), local.get("serviceName"))))
                    .collect(Collectors.toList());
            allServices.addAll(uniqueRemoteServices);
            log.debug("获取到 {} 个远程MCP服务（去重后 {} 个）", 
                     remoteServices.size(), uniqueRemoteServices.size());

            // 3. 获取数据库中的服务配置
            List<ServiceConfig> dbServices = serviceConfigRepository.findByIsPublicTrue();
            for (ServiceConfig config : dbServices) {
                // 检查是否已存在于MCP服务中
                boolean exists = allServices.stream()
                        .anyMatch(service -> Objects.equals(
                                config.getServiceName(), service.get("serviceName")));
                
                if (!exists) {
                    // 转换为Map格式
                    Map<String, Object> serviceMap = dataEnhancementService
                            .convertServiceConfigToMap(config);
                    allServices.add(serviceMap);
                }
            }
            log.debug("从数据库获取到 {} 个服务配置", dbServices.size());

        } catch (Exception e) {
            log.error("获取MCP服务列表失败: {}", e.getMessage(), e);
        }

        log.info("总共获取到 {} 个MCP服务", allServices.size());
        return allServices;
    }

    /**
     * 获取单个MCP服务的数据
     */
    private Map<String, Object> getMCPServiceData(String serviceName) {
        // 先尝试本地MCP服务
        Map<String, Object> serviceData = mcpServiceClient.getMCPServiceDetail(serviceName, false);
        
        if (serviceData.isEmpty()) {
            // 再尝试远程MCP服务
            serviceData = mcpServiceClient.getMCPServiceDetail(serviceName, true);
        }
        
        return serviceData;
    }

    /**
     * 应用过滤条件
     */
    private List<ServiceMarketDTO.ServiceItemDTO> applyFilters(
            List<ServiceMarketDTO.ServiceItemDTO> services, 
            ServiceMarketDTO.ServiceMarketQueryDTO queryDTO) {
        
        return services.stream()
                .filter(service -> {
                    // 分类过滤
                    if (queryDTO.getCategory() != null && !queryDTO.getCategory().isEmpty()) {
                        if (!Objects.equals(queryDTO.getCategory(), service.getCategory())) {
                            return false;
                        }
                    }
                    
                    // 状态过滤
                    if (queryDTO.getStatus() != null && !queryDTO.getStatus().isEmpty()) {
                        if (!Objects.equals(queryDTO.getStatus(), service.getStatus())) {
                            return false;
                        }
                    }
                    
                    // 提供商过滤
                    if (queryDTO.getProvider() != null && !queryDTO.getProvider().isEmpty()) {
                        if (!Objects.equals(queryDTO.getProvider(), service.getProvider())) {
                            return false;
                        }
                    }
                    
                    // 关键词过滤
                    if (queryDTO.getKeyword() != null && !queryDTO.getKeyword().isEmpty()) {
                        String keyword = queryDTO.getKeyword().toLowerCase();
                        return service.getServiceName().toLowerCase().contains(keyword) ||
                               (service.getDisplayName() != null && 
                                service.getDisplayName().toLowerCase().contains(keyword)) ||
                               (service.getDescription() != null && 
                                service.getDescription().toLowerCase().contains(keyword));
                    }
                    
                    // 标签过滤
                    if (queryDTO.getTags() != null && !queryDTO.getTags().isEmpty()) {
                        if (service.getTags() == null || service.getTags().isEmpty()) {
                            return false;
                        }
                        return queryDTO.getTags().stream()
                                .anyMatch(tag -> service.getTags().contains(tag));
                    }
                    
                    return true;
                })
                .collect(Collectors.toList());
    }

    /**
     * 应用排序
     */
    private List<ServiceMarketDTO.ServiceItemDTO> applySorting(
            List<ServiceMarketDTO.ServiceItemDTO> services, 
            ServiceMarketDTO.ServiceMarketQueryDTO queryDTO) {
        
        String sortBy = queryDTO.getSortBy() != null ? queryDTO.getSortBy() : "priority";
        String sortOrder = queryDTO.getSortOrder() != null ? queryDTO.getSortOrder() : "desc";
        boolean ascending = "asc".equalsIgnoreCase(sortOrder);
        
        Comparator<ServiceMarketDTO.ServiceItemDTO> comparator = null;
        
        switch (sortBy.toLowerCase()) {
            case "priority":
                comparator = Comparator.comparing(
                        service -> service.getPriority() != null ? service.getPriority() : 0);
                break;
            case "name":
                comparator = Comparator.comparing(ServiceMarketDTO.ServiceItemDTO::getServiceName);
                break;
            case "createdat":
                comparator = Comparator.comparing(ServiceMarketDTO.ServiceItemDTO::getCreatedAt);
                break;
            case "updatedat":
                comparator = Comparator.comparing(ServiceMarketDTO.ServiceItemDTO::getUpdatedAt);
                break;
            case "toolcount":
                comparator = Comparator.comparing(
                        service -> service.getToolCount() != null ? service.getToolCount() : 0);
                break;
            default:
                comparator = Comparator.comparing(
                        service -> service.getPriority() != null ? service.getPriority() : 0);
        }
        
        if (!ascending) {
            comparator = comparator.reversed();
        }
        
        return services.stream()
                .sorted(comparator)
                .collect(Collectors.toList());
    }

    /**
     * 应用分页
     */
    private ServiceMarketDTO.ServiceMarketResponseDTO applyPagination(
            List<ServiceMarketDTO.ServiceItemDTO> services, 
            ServiceMarketDTO.ServiceMarketQueryDTO queryDTO) {
        
        int page = queryDTO.getPage() != null ? queryDTO.getPage() : 1;
        int size = queryDTO.getSize() != null ? queryDTO.getSize() : 10;
        
        // 限制每页最大数量
        size = Math.min(size, 100);
        
        long totalCount = services.size();
        int totalPages = (int) Math.ceil((double) totalCount / size);
        
        // 转换为0基索引
        int zeroBasedPage = Math.max(0, page - 1);
        int startIndex = zeroBasedPage * size;
        int endIndex = Math.min(startIndex + size, services.size());
        
        List<ServiceMarketDTO.ServiceItemDTO> pagedServices;
        if (startIndex >= services.size() || services.isEmpty()) {
            pagedServices = new ArrayList<>();
        } else {
            pagedServices = services.subList(startIndex, endIndex);
        }
        
        return ServiceMarketDTO.ServiceMarketResponseDTO.builder()
                .services(pagedServices)
                .totalCount(totalCount)
                .currentPage(page)
                .totalPages(totalPages)
                .pageSize(size)
                .hasNext(page < totalPages)
                .hasPrevious(page > 1)
                .build();
    }
}
