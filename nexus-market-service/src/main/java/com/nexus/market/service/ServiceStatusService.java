package com.nexus.market.service;

import com.nexus.market.client.MCPServiceClient;
import com.nexus.market.entity.ServiceConfig;
import com.nexus.market.repository.ServiceConfigRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * 服务状态管理服务
 * 负责定期检查和更新服务状态
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ServiceStatusService {

    private final ServiceConfigRepository serviceConfigRepository;
    private final MCPServiceClient mcpServiceClient;
    
    @Value("${market.service.status-check-interval:60}")
    private int statusCheckInterval;

    private final Executor statusCheckExecutor = Executors.newFixedThreadPool(5);

    /**
     * 定期检查服务状态
     * 每分钟执行一次
     */
    @Scheduled(fixedRateString = "${market.service.status-check-interval:60}000")
    @Transactional
    public void checkServiceStatus() {
        log.debug("开始定期服务状态检查");

        try {
            // 获取需要检查状态的服务
            LocalDateTime threshold = LocalDateTime.now().minusMinutes(statusCheckInterval);
            List<ServiceConfig> servicesToCheck = serviceConfigRepository
                    .findServicesNeedingStatusCheck(threshold);

            if (servicesToCheck.isEmpty()) {
                log.debug("没有需要检查状态的服务");
                return;
            }

            log.info("需要检查状态的服务数量: {}", servicesToCheck.size());

            // 异步检查每个服务的状态
            List<CompletableFuture<Void>> futures = servicesToCheck.stream()
                    .map(this::checkSingleServiceStatusAsync)
                    .toList();

            // 等待所有检查完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .join();

            log.info("服务状态检查完成，共检查 {} 个服务", servicesToCheck.size());

        } catch (Exception e) {
            log.error("定期服务状态检查失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 异步检查单个服务状态
     */
    private CompletableFuture<Void> checkSingleServiceStatusAsync(ServiceConfig serviceConfig) {
        return CompletableFuture.runAsync(() -> {
            try {
                checkSingleServiceStatus(serviceConfig);
            } catch (Exception e) {
                log.error("检查服务状态失败: {} - {}", serviceConfig.getServiceName(), e.getMessage());
            }
        }, statusCheckExecutor);
    }

    /**
     * 检查单个服务状态
     */
    @Transactional
    public void checkSingleServiceStatus(ServiceConfig serviceConfig) {
        String serviceName = serviceConfig.getServiceName();
        log.debug("检查服务状态: {}", serviceName);

        try {
            // 先尝试本地MCP服务
            Map<String, Object> status = mcpServiceClient.checkMCPServiceStatus(serviceName, false);
            
            // 如果本地服务无响应，尝试远程服务
            if (status.isEmpty() || "UNKNOWN".equals(status.get("status"))) {
                status = mcpServiceClient.checkMCPServiceStatus(serviceName, true);
            }

            // 更新服务状态
            updateServiceStatus(serviceConfig, status);

            log.debug("服务状态检查完成: {} - {}", serviceName, status.get("status"));

        } catch (Exception e) {
            log.error("检查服务状态异常: {} - {}", serviceName, e.getMessage());
            
            // 标记为错误状态
            Map<String, Object> errorStatus = Map.of(
                    "status", "ERROR",
                    "connectionStatus", "ERROR",
                    "error", e.getMessage()
            );
            updateServiceStatus(serviceConfig, errorStatus);
        }
    }

    /**
     * 更新服务状态
     */
    @Transactional
    public void updateServiceStatus(ServiceConfig serviceConfig, Map<String, Object> statusData) {
        boolean updated = false;

        // 更新状态
        String newStatus = (String) statusData.get("status");
        if (newStatus != null && !newStatus.equals(serviceConfig.getStatus())) {
            serviceConfig.setStatus(newStatus);
            updated = true;
        }

        // 更新连接状态
        String newConnectionStatus = (String) statusData.get("connectionStatus");
        if (newConnectionStatus != null && !newConnectionStatus.equals(serviceConfig.getConnectionStatus())) {
            serviceConfig.setConnectionStatus(newConnectionStatus);
            updated = true;
        }

        // 更新响应时间
        Object responseTimeObj = statusData.get("responseTime");
        if (responseTimeObj instanceof Number) {
            Long newResponseTime = ((Number) responseTimeObj).longValue();
            if (!newResponseTime.equals(serviceConfig.getResponseTimeMs())) {
                serviceConfig.setResponseTimeMs(newResponseTime);
                updated = true;
            }
        }

        // 更新错误计数
        Object errorCountObj = statusData.get("errorCount");
        if (errorCountObj instanceof Number) {
            Integer newErrorCount = ((Number) errorCountObj).intValue();
            if (!newErrorCount.equals(serviceConfig.getErrorCount())) {
                serviceConfig.setErrorCount(newErrorCount);
                updated = true;
            }
        }

        // 更新运行时间
        String newUptime = (String) statusData.get("uptime");
        if (newUptime != null && !newUptime.equals(serviceConfig.getUptime())) {
            serviceConfig.setUptime(newUptime);
            updated = true;
        }

        // 更新最后检查时间
        serviceConfig.setLastChecked(LocalDateTime.now());
        updated = true;

        if (updated) {
            serviceConfigRepository.save(serviceConfig);
            log.debug("服务状态已更新: {} - status: {}, connectionStatus: {}", 
                     serviceConfig.getServiceName(), newStatus, newConnectionStatus);
        }
    }

    /**
     * 手动检查指定服务的状态
     */
    @Transactional
    public Map<String, Object> checkServiceStatusManually(String serviceName) {
        log.info("手动检查服务状态: {}", serviceName);

        try {
            // 查找服务配置
            ServiceConfig serviceConfig = serviceConfigRepository.findByServiceName(serviceName)
                    .orElse(null);

            // 检查MCP服务状态
            Map<String, Object> status = mcpServiceClient.checkMCPServiceStatus(serviceName, false);
            
            if (status.isEmpty() || "UNKNOWN".equals(status.get("status"))) {
                status = mcpServiceClient.checkMCPServiceStatus(serviceName, true);
            }

            // 如果找到服务配置，更新状态
            if (serviceConfig != null) {
                updateServiceStatus(serviceConfig, status);
            }

            log.info("手动检查服务状态完成: {} - {}", serviceName, status.get("status"));
            return status;

        } catch (Exception e) {
            log.error("手动检查服务状态失败: {} - {}", serviceName, e.getMessage(), e);
            return Map.of(
                    "status", "ERROR",
                    "connectionStatus", "ERROR",
                    "error", e.getMessage()
            );
        }
    }

    /**
     * 获取服务状态统计
     */
    public Map<String, Object> getServiceStatusStatistics() {
        try {
            List<Object[]> statusCounts = serviceConfigRepository.countServicesByStatus();
            Map<String, Long> statusMap = statusCounts.stream()
                    .collect(java.util.stream.Collectors.toMap(
                            row -> (String) row[0],
                            row -> (Long) row[1]
                    ));

            long totalServices = statusMap.values().stream().mapToLong(Long::longValue).sum();
            long activeServices = statusMap.getOrDefault("ACTIVE", 0L);
            long inactiveServices = statusMap.getOrDefault("INACTIVE", 0L);
            long errorServices = statusMap.getOrDefault("ERROR", 0L);

            return Map.of(
                    "totalServices", totalServices,
                    "activeServices", activeServices,
                    "inactiveServices", inactiveServices,
                    "errorServices", errorServices,
                    "statusBreakdown", statusMap,
                    "healthPercentage", totalServices > 0 ? (double) activeServices / totalServices * 100 : 0.0
            );

        } catch (Exception e) {
            log.error("获取服务状态统计失败: {}", e.getMessage(), e);
            return Map.of("error", e.getMessage());
        }
    }

    /**
     * 批量更新服务状态
     */
    @Transactional
    public void batchUpdateServiceStatus(List<String> serviceNames, String status) {
        try {
            LocalDateTime now = LocalDateTime.now();
            int updatedCount = serviceConfigRepository.updateServiceStatus(serviceNames, status, now);
            log.info("批量更新服务状态完成: {} 个服务更新为 {}", updatedCount, status);
        } catch (Exception e) {
            log.error("批量更新服务状态失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 重置所有服务状态检查时间
     */
    @Transactional
    public void resetAllServiceStatusCheckTime() {
        try {
            List<ServiceConfig> allServices = serviceConfigRepository.findByIsPublicTrue();
            LocalDateTime resetTime = LocalDateTime.now().minusHours(1); // 设置为1小时前，强制重新检查
            
            for (ServiceConfig service : allServices) {
                service.setLastChecked(resetTime);
            }
            
            serviceConfigRepository.saveAll(allServices);
            log.info("重置所有服务状态检查时间完成，共 {} 个服务", allServices.size());
            
        } catch (Exception e) {
            log.error("重置服务状态检查时间失败: {}", e.getMessage(), e);
        }
    }
}
