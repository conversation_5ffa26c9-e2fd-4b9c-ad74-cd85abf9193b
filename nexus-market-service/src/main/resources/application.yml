server:
  port: 8085

spring:
  application:
    name: nexus-market-service
  
  profiles:
    active: dev
  
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: nexus-microservices
        group: DEFAULT_GROUP
        metadata:
          version: 1.0.0
          description: "服务市场微服务"
      config:
        server-addr: localhost:8848
        namespace: nexus-microservices
        group: DEFAULT_GROUP
        file-extension: yml
        shared-configs:
          - data-id: nexus-common.yml
            group: DEFAULT_GROUP
            refresh: true

  datasource:
    # 使用内存数据库H2作为fallback，避免外部数据库连接问题
    url: jdbc:h2:mem:nexus_market;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password:
    driver-class-name: org.h2.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
        use_sql_comments: true

  redis:
    host: localhost
    port: 6379
    password: 
    database: 2
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 5000ms

  cache:
    type: redis
    redis:
      time-to-live: 300000  # 5分钟缓存

# 服务间调用配置
service:
  auth:
    url: http://nexus-auth-service:8081
  subscription:
    url: http://nexus-subscription-service:8084
  mcp-local:
    url: http://nexus-mcp-local-service:8082
  mcp-remote:
    url: http://nexus-mcp-remote-service:8083

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# 日志配置
logging:
  level:
    com.nexus.market: DEBUG
    org.springframework.amqp: INFO
    org.springframework.cache: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{50}] - %msg%n"

# 业务配置
market:
  cache:
    service-list-ttl: 300  # 服务列表缓存5分钟
    service-detail-ttl: 600  # 服务详情缓存10分钟
  pagination:
    default-size: 10
    max-size: 100
  service:
    status-check-interval: 60  # 服务状态检查间隔(秒)
    connection-timeout: 5000   # 连接超时(毫秒)
