package com.nexus.mcp.local;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Nexus本地MCP服务启动类
 * 
 * 功能：
 * - 管理需要在用户本地部署的MCP服务
 * - 提供本地MCP服务的注册和发现
 * - 处理本地MCP服务的调用和代理
 * - 管理本地服务的生命周期
 * - 提供本地服务的监控和健康检查
 */
@SpringBootApplication(scanBasePackages = {
    "com.nexus.mcp.local",  // 当前服务包
    "com.nexus.common"      // 公共组件包
})
@EnableDiscoveryClient
@EnableFeignClients
@EnableCaching
@EnableAsync
@EnableScheduling
@EnableTransactionManagement
@EntityScan(basePackages = {
    "com.nexus.common.entity",      // 公共实体类
    "com.nexus.mcp.local.entity"    // 本地MCP服务实体类
})
@EnableJpaRepositories(basePackages = {
    "com.nexus.mcp.local.repository" // 本地MCP服务Repository
})
public class NexusMcpLocalServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(NexusMcpLocalServiceApplication.class, args);
    }
}
