package com.nexus.mcp.local.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * 认证服务客户端
 * 用于调用认证服务的API
 */
@FeignClient(
    name = "nexus-auth-service",
    path = "/auth/api/v1/auth",
    fallback = AuthServiceClientFallback.class
)
public interface AuthServiceClient {

    /**
     * 验证API密钥
     */
    @PostMapping("/validate/api-key")
    Map<String, Object> validateApiKey(@RequestParam("apiKey") String apiKey);

    /**
     * 验证JWT令牌
     */
    @PostMapping("/validate/token")
    Map<String, Object> validateToken(@RequestParam("token") String token);
}
