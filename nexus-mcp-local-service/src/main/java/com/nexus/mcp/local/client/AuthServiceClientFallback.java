package com.nexus.mcp.local.client;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 认证服务客户端降级处理
 * 当认证服务不可用时的降级逻辑
 */
@Slf4j
@Component
public class AuthServiceClientFallback implements AuthServiceClient {

    @Override
    public Map<String, Object> validateApiKey(String apiKey) {
        log.warn("认证服务不可用，API密钥验证降级处理: {}", apiKey);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", "认证服务暂时不可用");
        response.put("code", 503);
        
        return response;
    }

    @Override
    public Map<String, Object> validateToken(String token) {
        log.warn("认证服务不可用，JWT令牌验证降级处理");
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", "认证服务暂时不可用");
        response.put("code", 503);
        
        return response;
    }
}
