package com.nexus.mcp.local.consumer;

import com.nexus.common.constants.RocketMQConstants;
import com.nexus.common.event.MCPTaskCreatedEvent;
import com.nexus.common.event.MCPTaskExecutingEvent;
import com.nexus.common.event.MCPTaskCompletedEvent;
import com.nexus.common.event.MCPTaskFailedEvent;
import com.nexus.common.service.RocketMQProducerService;
import com.nexus.mcp.local.service.LocalServiceExecutor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * MCP任务消费者
 * 处理异步的MCP工具调用任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
    topic = RocketMQConstants.MCP_ASYNC_TOPIC,
    selectorExpression = RocketMQConstants.MCP_ASYNC_TAG,
    consumerGroup = RocketMQConstants.MCP_ASYNC_CONSUMER_GROUP,
    nameServer = "${rocketmq.name-server:localhost:9876}"
)
public class MCPTaskConsumer implements RocketMQListener<MCPTaskCreatedEvent> {

    private final LocalServiceExecutor localServiceExecutor;
    private final RocketMQProducerService messageProducerService;

    /**
     * RocketMQ消息监听器实现
     */
    @Override
    public void onMessage(MCPTaskCreatedEvent event) {
        log.info("接收到MCP任务创建事件: taskId={}, toolName={}, serviceName={}",
                event.getTaskId(), event.getToolName(), event.getServiceName());

        try {
            // 验证事件数据
            if (!event.isValid()) {
                log.warn("MCP任务事件数据无效: taskId={}", event.getTaskId());
                return;
            }

            // 处理MCP任务
            processMCPTask(event);

            log.info("MCP任务事件处理成功: taskId={}", event.getTaskId());

        } catch (Exception e) {
            log.error("MCP任务事件处理失败: taskId={} - {}",
                    event.getTaskId(), e.getMessage(), e);
            // RocketMQ会自动重试
        }
    }

    /**
     * 处理MCP任务创建事件
     */
    private void processMCPTask(MCPTaskCreatedEvent event) {
        // 1. 发送任务执行中事件
        boolean executingEventSent = sendTaskExecutingEvent(event);
        if (!executingEventSent) {
            log.warn("任务执行中事件发送失败: taskId={}", event.getTaskId());
        }

        // 2. 异步执行MCP工具调用
        executeTaskAsync(event);
    }
    
    /**
     * 异步执行MCP任务
     */
    private void executeTaskAsync(MCPTaskCreatedEvent event) {
        CompletableFuture.supplyAsync(() -> {
            LocalDateTime startTime = LocalDateTime.now();
            String executorId = generateExecutorId();
            
            try {
                log.debug("开始执行MCP工具调用: taskId={}, toolName={}", 
                        event.getTaskId(), event.getToolName());
                
                // 检查任务是否已超时
                if (event.isTimeout()) {
                    throw new RuntimeException("任务已超时");
                }
                
                // 执行MCP工具调用（简化实现）
                Map<String, Object> result = executeLocalTool(
                        event.getServiceId(),
                        event.getToolName(),
                        event.getParameters()
                );
                
                // 发送任务完成事件
                sendTaskCompletedEvent(event, result, startTime, executorId);
                
                log.info("MCP工具调用执行成功: taskId={}, toolName={}, duration={}ms", 
                        event.getTaskId(), event.getToolName(), 
                        java.time.Duration.between(startTime, LocalDateTime.now()).toMillis());
                
                return result;
                
            } catch (Exception e) {
                log.error("MCP工具调用执行失败: taskId={}, toolName={} - {}", 
                        event.getTaskId(), event.getToolName(), e.getMessage(), e);
                
                // 发送任务失败事件
                sendTaskFailedEvent(event, e, startTime, executorId);
                
                throw new RuntimeException("工具调用失败", e);
            }
            
        }).orTimeout(getTaskTimeout(event), TimeUnit.MILLISECONDS)
          .exceptionally(throwable -> {
              log.error("MCP任务执行异常: taskId={} - {}", 
                      event.getTaskId(), throwable.getMessage(), throwable);
              
              return null;
          });
    }
    
    /**
     * 发送任务执行中事件
     */
    private boolean sendTaskExecutingEvent(MCPTaskCreatedEvent event) {
        try {
            MCPTaskExecutingEvent executingEvent = new MCPTaskExecutingEvent(
                    event.getTaskId(),
                    event.getUserId(),
                    event.getToolName(),
                    event.getServiceId(),
                    event.getServiceName(),
                    event.getServiceType(),
                    event.getCorrelationId(),
                    event.getParameters()
            );
            
            executingEvent.setSourceService("nexus-mcp-local-service");
            
            boolean sent = messageProducerService.sendEvent(executingEvent);
            if (sent) {
                log.debug("任务执行中事件发送成功: taskId={}", event.getTaskId());
            } else {
                log.warn("任务执行中事件发送失败: taskId={}", event.getTaskId());
            }
            
            return sent;
            
        } catch (Exception e) {
            log.error("发送任务执行中事件异常: taskId={} - {}", 
                    event.getTaskId(), e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 发送任务完成事件
     */
    private boolean sendTaskCompletedEvent(MCPTaskCreatedEvent event, Map<String, Object> result,
                                          LocalDateTime startTime, String executorId) {
        try {
            MCPTaskCompletedEvent completedEvent = new MCPTaskCompletedEvent(
                    event.getTaskId(),
                    event.getUserId(),
                    event.getToolName(),
                    event.getServiceId(),
                    event.getServiceName(),
                    event.getServiceType(),
                    event.getCorrelationId(),
                    result,
                    startTime,
                    executorId
            );
            
            completedEvent.setSourceService("nexus-mcp-local-service");
            
            boolean sent = messageProducerService.sendEvent(completedEvent);
            if (sent) {
                log.debug("任务完成事件发送成功: taskId={}", event.getTaskId());
            } else {
                log.warn("任务完成事件发送失败: taskId={}", event.getTaskId());
            }
            
            return sent;
            
        } catch (Exception e) {
            log.error("发送任务完成事件异常: taskId={} - {}", 
                    event.getTaskId(), e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 发送任务失败事件
     */
    private boolean sendTaskFailedEvent(MCPTaskCreatedEvent event, Exception exception) {
        return sendTaskFailedEvent(event, exception, null, null);
    }
    
    /**
     * 发送任务失败事件
     */
    private boolean sendTaskFailedEvent(MCPTaskCreatedEvent event, Exception exception,
                                       LocalDateTime startTime, String executorId) {
        try {
            MCPTaskFailedEvent failedEvent = new MCPTaskFailedEvent(
                    event.getTaskId(),
                    event.getUserId(),
                    event.getToolName(),
                    event.getServiceId(),
                    event.getServiceName(),
                    event.getServiceType(),
                    event.getCorrelationId(),
                    exception
            );
            
            if (startTime != null) {
                failedEvent.setStartTime(startTime);
            }
            
            if (executorId != null) {
                failedEvent.setExecutorId(executorId);
            }
            
            failedEvent.setSourceService("nexus-mcp-local-service");
            
            boolean sent = messageProducerService.sendEvent(failedEvent);
            if (sent) {
                log.debug("任务失败事件发送成功: taskId={}", event.getTaskId());
            } else {
                log.warn("任务失败事件发送失败: taskId={}", event.getTaskId());
            }
            
            return sent;
            
        } catch (Exception e) {
            log.error("发送任务失败事件异常: taskId={} - {}", 
                    event.getTaskId(), e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 获取任务超时时间
     */
    private long getTaskTimeout(MCPTaskCreatedEvent event) {
        if (event.getTimeoutMs() != null && event.getTimeoutMs() > 0) {
            return event.getTimeoutMs();
        }
        
        // 默认超时时间：根据任务优先级设置
        switch (event.getTaskPriority()) {
            case URGENT:
                return 30000; // 30秒
            case HIGH:
                return 60000; // 1分钟
            case NORMAL:
                return 120000; // 2分钟
            case LOW:
            default:
                return 300000; // 5分钟
        }
    }
    
    /**
     * 生成执行器ID
     */
    private String generateExecutorId() {
        return "mcp-local-executor-" + System.currentTimeMillis() + "-" + 
               Thread.currentThread().getId();
    }

    /**
     * 执行本地工具调用（简化实现）
     */
    private Map<String, Object> executeLocalTool(Long serviceId, String toolName,
                                                Map<String, Object> parameters) {
        log.debug("执行本地工具调用: serviceId={}, toolName={}, parameters={}",
                serviceId, toolName, parameters);

        // 模拟本地工具执行
        try {
            //TODO 真实的和数据平面交互的本地隐私性MCP服务调用
            Thread.sleep(500 + (long)(Math.random() * 1500)); // 模拟0.5-2秒的执行时间
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("本地工具调用被中断", e);
        }

        // 模拟返回结果
        Map<String, Object> result = new java.util.HashMap<>();
        result.put("success", true);
        result.put("toolName", toolName);
        result.put("serviceId", serviceId);
        result.put("executionTime", System.currentTimeMillis());
        result.put("data", "本地工具调用结果数据");
        result.put("parameters", parameters);

        return result;
    }
}
