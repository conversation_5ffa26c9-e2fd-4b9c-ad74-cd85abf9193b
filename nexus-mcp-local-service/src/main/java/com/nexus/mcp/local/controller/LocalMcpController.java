package com.nexus.mcp.local.controller;

import com.nexus.common.model.MCPServiceMetadata;
import com.nexus.mcp.local.entity.LocalMcpService;
import com.nexus.mcp.local.service.LocalMcpServiceManager;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 本地MCP服务控制器
 * 提供本地MCP服务管理的REST API
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/local-mcp")
@RequiredArgsConstructor
@Validated
@Tag(name = "本地MCP服务管理", description = "本地MCP服务注册、启动、停止等管理接口")
public class LocalMcpController {

    private final LocalMcpServiceManager serviceManager;

    /**
     * 注册本地MCP服务
     */
    @PostMapping("/register")
    @Operation(summary = "注册本地MCP服务", description = "注册一个新的本地MCP服务")
    public ResponseEntity<Map<String, Object>> registerService(
            @Valid @RequestBody MCPServiceMetadata metadata,
            @Parameter(description = "代理ID") @RequestParam String agentId) {
        
        log.info("注册本地MCP服务请求: {} (代理: {})", metadata.getServiceName(), agentId);
        
        try {
            LocalMcpService service = serviceManager.registerService(metadata, agentId);
            return ResponseEntity.ok(buildSuccessResponse("服务注册成功", service));
        } catch (Exception e) {
            log.error("服务注册失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 启动本地服务
     */
    @PostMapping("/{serviceId}/start")
    @Operation(summary = "启动本地服务", description = "启动指定的本地MCP服务")
    public ResponseEntity<Map<String, Object>> startService(
            @Parameter(description = "服务ID") @PathVariable Long serviceId) {
        
        log.info("启动本地服务请求: {}", serviceId);
        
        try {
            serviceManager.startService(serviceId);
            return ResponseEntity.ok(buildSuccessResponse("服务启动成功", null));
        } catch (Exception e) {
            log.error("服务启动失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 停止本地服务
     */
    @PostMapping("/{serviceId}/stop")
    @Operation(summary = "停止本地服务", description = "停止指定的本地MCP服务")
    public ResponseEntity<Map<String, Object>> stopService(
            @Parameter(description = "服务ID") @PathVariable Long serviceId) {
        
        log.info("停止本地服务请求: {}", serviceId);
        
        try {
            serviceManager.stopService(serviceId);
            return ResponseEntity.ok(buildSuccessResponse("服务停止成功", null));
        } catch (Exception e) {
            log.error("服务停止失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 重启本地服务
     */
    @PostMapping("/{serviceId}/restart")
    @Operation(summary = "重启本地服务", description = "重启指定的本地MCP服务")
    public ResponseEntity<Map<String, Object>> restartService(
            @Parameter(description = "服务ID") @PathVariable Long serviceId) {
        
        log.info("重启本地服务请求: {}", serviceId);
        
        try {
            serviceManager.restartService(serviceId);
            return ResponseEntity.ok(buildSuccessResponse("服务重启成功", null));
        } catch (Exception e) {
            log.error("服务重启失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 注销本地服务
     */
    @DeleteMapping("/{serviceId}")
    @Operation(summary = "注销本地服务", description = "注销指定的本地MCP服务")
    public ResponseEntity<Map<String, Object>> unregisterService(
            @Parameter(description = "服务ID") @PathVariable Long serviceId) {
        
        log.info("注销本地服务请求: {}", serviceId);
        
        try {
            serviceManager.unregisterService(serviceId);
            return ResponseEntity.ok(buildSuccessResponse("服务注销成功", null));
        } catch (Exception e) {
            log.error("服务注销失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 获取所有本地服务
     */
    @GetMapping("/services")
    @Operation(summary = "获取所有本地服务", description = "获取所有已注册的本地MCP服务列表")
    public ResponseEntity<Map<String, Object>> getAllServices(
            @Parameter(description = "代理ID（可选）") @RequestParam(required = false) String agentId) {

        log.debug("获取所有本地服务请求: agentId={}", agentId);

        try {
            List<LocalMcpService> services;
            if (agentId != null) {
                services = serviceManager.getServicesByAgent(agentId);
            } else {
                services = serviceManager.getAllServices();
            }
            return ResponseEntity.ok(buildSuccessResponse("获取服务列表成功", services));
        } catch (Exception e) {
            log.error("获取服务列表失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 获取服务详情
     */
    @GetMapping("/{serviceId}")
    @Operation(summary = "获取服务详情", description = "获取指定本地MCP服务的详细信息")
    public ResponseEntity<Map<String, Object>> getService(
            @Parameter(description = "服务ID") @PathVariable Long serviceId) {

        log.debug("获取服务详情请求: {}", serviceId);

        try {
            LocalMcpService service = serviceManager.getServiceById(serviceId);
            return ResponseEntity.ok(buildSuccessResponse("获取服务详情成功", service));
        } catch (Exception e) {
            log.error("获取服务详情失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 根据服务名称获取服务
     */
    @GetMapping("/by-name/{serviceName}")
    @Operation(summary = "根据名称获取服务", description = "根据服务名称获取本地MCP服务信息")
    public ResponseEntity<Map<String, Object>> getServiceByName(
            @Parameter(description = "服务名称") @PathVariable String serviceName) {
        
        log.debug("根据名称获取服务请求: {}", serviceName);
        
        try {
            Optional<LocalMcpService> service = serviceManager.getServiceByName(serviceName);
            if (service.isPresent()) {
                return ResponseEntity.ok(buildSuccessResponse("获取服务成功", service.get()));
            } else {
                return ResponseEntity.ok(buildSuccessResponse("服务不存在", null));
            }
        } catch (Exception e) {
            log.error("获取服务失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 获取代理的所有服务
     */
    @GetMapping("/agent/{agentId}")
    @Operation(summary = "获取代理服务列表", description = "获取指定代理的所有本地MCP服务")
    public ResponseEntity<Map<String, Object>> getServicesByAgent(
            @Parameter(description = "代理ID") @PathVariable String agentId) {
        
        log.debug("获取代理服务列表请求: {}", agentId);
        
        try {
            List<LocalMcpService> services = serviceManager.getServicesByAgent(agentId);
            return ResponseEntity.ok(buildSuccessResponse("获取代理服务列表成功", services));
        } catch (Exception e) {
            log.error("获取代理服务列表失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 获取运行中的服务列表
     */
    @GetMapping("/running")
    @Operation(summary = "获取运行中服务", description = "获取所有运行中的本地MCP服务")
    public ResponseEntity<Map<String, Object>> getRunningServices(
            @Parameter(description = "代理ID（可选）") @RequestParam(required = false) String agentId) {
        
        log.debug("获取运行中服务请求: agentId={}", agentId);
        
        try {
            List<LocalMcpService> services;
            if (agentId != null) {
                services = serviceManager.getRunningServicesByAgent(agentId);
            } else {
                services = serviceManager.getRunningServices();
            }
            return ResponseEntity.ok(buildSuccessResponse("获取运行中服务成功", services));
        } catch (Exception e) {
            log.error("获取运行中服务失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 执行健康检查
     */
    @PostMapping("/{serviceId}/health-check")
    @Operation(summary = "执行健康检查", description = "对指定服务执行健康检查")
    public ResponseEntity<Map<String, Object>> performHealthCheck(
            @Parameter(description = "服务ID") @PathVariable Long serviceId) {
        
        log.debug("执行健康检查请求: {}", serviceId);
        
        try {
            serviceManager.performHealthCheck(serviceId);
            return ResponseEntity.ok(buildSuccessResponse("健康检查完成", null));
        } catch (Exception e) {
            log.error("健康检查失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 批量健康检查
     */
    @PostMapping("/health-check/batch")
    @Operation(summary = "批量健康检查", description = "对所有运行中的服务执行健康检查")
    public ResponseEntity<Map<String, Object>> performBatchHealthCheck() {
        
        log.debug("批量健康检查请求");
        
        try {
            serviceManager.performBatchHealthCheck();
            return ResponseEntity.ok(buildSuccessResponse("批量健康检查完成", null));
        } catch (Exception e) {
            log.error("批量健康检查失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 自动启动服务
     */
    @PostMapping("/auto-start")
    @Operation(summary = "自动启动服务", description = "启动指定代理的所有自动启动服务")
    public ResponseEntity<Map<String, Object>> autoStartServices(
            @Parameter(description = "代理ID") @RequestParam String agentId) {
        
        log.info("自动启动服务请求: {}", agentId);
        
        try {
            serviceManager.autoStartServices(agentId);
            return ResponseEntity.ok(buildSuccessResponse("自动启动服务完成", null));
        } catch (Exception e) {
            log.error("自动启动服务失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 获取服务统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取服务统计", description = "获取本地MCP服务的统计信息")
    public ResponseEntity<Map<String, Object>> getServiceStatistics(
            @Parameter(description = "代理ID（可选）") @RequestParam(required = false) String agentId) {
        
        log.debug("获取服务统计请求: agentId={}", agentId);
        
        try {
            LocalMcpServiceManager.ServiceStatistics statistics = serviceManager.getServiceStatistics(agentId);
            return ResponseEntity.ok(buildSuccessResponse("获取服务统计成功", statistics));
        } catch (Exception e) {
            log.error("获取服务统计失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 健康检查端点
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "检查本地MCP服务的健康状态")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "nexus-mcp-local-service");
        health.put("timestamp", System.currentTimeMillis());
        
        // 添加服务统计信息
        try {
            LocalMcpServiceManager.ServiceStatistics stats = serviceManager.getServiceStatistics(null);
            health.put("totalServices", stats.getTotalServices());
            health.put("runningServices", stats.getRunningServices());
        } catch (Exception e) {
            log.warn("获取健康检查统计信息失败", e);
        }
        
        return ResponseEntity.ok(health);
    }

    /**
     * 构建成功响应
     */
    private Map<String, Object> buildSuccessResponse(String message, Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", message);
        response.put("timestamp", System.currentTimeMillis());
        if (data != null) {
            response.put("data", data);
        }
        return response;
    }
}
