package com.nexus.mcp.local.entity;

import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 本地MCP服务实体类
 * 存储需要在用户本地部署的MCP服务信息
 */
@Entity
@Table(name = "local_mcp_services", indexes = {
        @Index(name = "idx_local_mcp_service_name", columnList = "serviceName"),
        @Index(name = "idx_local_mcp_service_type", columnList = "serviceType"),
        @Index(name = "idx_local_mcp_service_status", columnList = "status"),
        @Index(name = "idx_local_mcp_agent_id", columnList = "agentId")
})
@TypeDef(name = "jsonb", typeClass = JsonBinaryType.class)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LocalMcpService {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 服务名称，唯一标识
     */
    @Column(unique = true, nullable = false, length = 100)
    @NotBlank(message = "服务名称不能为空")
    @Size(max = 100, message = "服务名称长度不能超过100个字符")
    private String serviceName;

    /**
     * 服务显示名称
     */
    @Column(nullable = false, length = 200)
    @NotBlank(message = "服务显示名称不能为空")
    @Size(max = 200, message = "服务显示名称长度不能超过200个字符")
    private String displayName;

    /**
     * 服务描述
     */
    @Column(length = 1000)
    @Size(max = 1000, message = "服务描述长度不能超过1000个字符")
    private String description;

    /**
     * 服务类型（npx, uv, python, node, docker, custom等）
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private LocalServiceType serviceType;

    /**
     * 服务状态
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Builder.Default
    private ServiceStatus status = ServiceStatus.INACTIVE;

    /**
     * 服务版本
     */
    @Column(length = 50)
    private String version;

    /**
     * 代理ID（标识哪个用户代理注册的服务）
     */
    @Column(nullable = false, length = 100)
    @NotBlank(message = "代理ID不能为空")
    private String agentId;

    /**
     * 执行命令或脚本
     */
    @Column(length = 1000)
    private String command;

    /**
     * 工作目录
     */
    @Column(length = 500)
    private String workingDirectory;

    /**
     * 环境变量（JSON格式）
     */
    @Type(type = "jsonb")
    @Column(columnDefinition = "jsonb")
    @Builder.Default
    private Map<String, String> environmentVariables = new HashMap<>();

    /**
     * 服务配置参数（JSON格式）
     */
    @Type(type = "jsonb")
    @Column(columnDefinition = "jsonb")
    @Builder.Default
    private Map<String, Object> configParams = new HashMap<>();

    /**
     * 服务元数据（JSON格式）
     */
    @Type(type = "jsonb")
    @Column(columnDefinition = "jsonb")
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();

    /**
     * 健康检查配置（JSON格式）
     */
    @Type(type = "jsonb")
    @Column(columnDefinition = "jsonb")
    @Builder.Default
    private Map<String, Object> healthCheck = new HashMap<>();

    /**
     * 最后健康检查时间
     */
    private LocalDateTime lastHealthCheckAt;

    /**
     * 健康检查状态
     */
    @Enumerated(EnumType.STRING)
    @Builder.Default
    private HealthStatus healthStatus = HealthStatus.UNKNOWN;

    /**
     * 健康检查错误信息
     */
    @Column(length = 1000)
    private String healthError;

    /**
     * 服务启动时间
     */
    private LocalDateTime startedAt;

    /**
     * 服务停止时间
     */
    private LocalDateTime stoppedAt;

    /**
     * 进程ID（如果适用）
     */
    private Long processId;

    /**
     * 服务端口（如果适用）
     */
    private Integer port;

    /**
     * 是否自动启动
     */
    @Builder.Default
    private Boolean autoStart = false;

    /**
     * 最大重启次数
     */
    @Builder.Default
    private Integer maxRestarts = 3;

    /**
     * 当前重启次数
     */
    @Builder.Default
    private Integer currentRestarts = 0;

    /**
     * 服务创建时间
     */
    @CreationTimestamp
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 服务更新时间
     */
    @UpdateTimestamp
    @Column(nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 本地服务类型枚举
     */
    public enum LocalServiceType {
        NPX("NPX服务", "使用npx运行的Node.js服务"),
        UV("UV服务", "使用uv运行的Python服务"),
        PYTHON("Python服务", "直接运行的Python服务"),
        NODE("Node.js服务", "直接运行的Node.js服务"),
        DOCKER("Docker服务", "运行在Docker容器中的服务"),
        CUSTOM("自定义服务", "用户自定义的服务类型");

        private final String displayName;
        private final String description;

        LocalServiceType(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 服务状态枚举
     */
    public enum ServiceStatus {
        ACTIVE("运行中"),
        INACTIVE("未运行"),
        STARTING("启动中"),
        STOPPING("停止中"),
        ERROR("错误"),
        MAINTENANCE("维护中");

        private final String description;

        ServiceStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 健康状态枚举
     */
    public enum HealthStatus {
        HEALTHY("健康"),
        UNHEALTHY("不健康"),
        UNKNOWN("未知");

        private final String description;

        HealthStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 检查服务是否运行中
     */
    public boolean isRunning() {
        return ServiceStatus.ACTIVE.equals(this.status);
    }

    /**
     * 检查服务是否健康
     */
    public boolean isHealthy() {
        return HealthStatus.HEALTHY.equals(this.healthStatus);
    }

    /**
     * 检查是否可以重启
     */
    public boolean canRestart() {
        return currentRestarts < maxRestarts;
    }

    /**
     * 增加重启次数
     */
    public void incrementRestarts() {
        this.currentRestarts++;
    }

    /**
     * 重置重启次数
     */
    public void resetRestarts() {
        this.currentRestarts = 0;
    }

    /**
     * 更新健康状态
     */
    public void updateHealthStatus(HealthStatus status, String error) {
        this.healthStatus = status;
        this.healthError = error;
        this.lastHealthCheckAt = LocalDateTime.now();
    }

    /**
     * 标记服务启动
     */
    public void markAsStarted(Long pid, Integer servicePort) {
        this.status = ServiceStatus.ACTIVE;
        this.startedAt = LocalDateTime.now();
        this.stoppedAt = null;
        this.processId = pid;
        this.port = servicePort;
    }

    /**
     * 标记服务停止
     */
    public void markAsStopped() {
        this.status = ServiceStatus.INACTIVE;
        this.stoppedAt = LocalDateTime.now();
        this.processId = null;
    }

    /**
     * 添加配置参数
     */
    public void addConfigParam(String key, Object value) {
        if (this.configParams == null) {
            this.configParams = new HashMap<>();
        }
        this.configParams.put(key, value);
    }

    /**
     * 添加环境变量
     */
    public void addEnvironmentVariable(String key, String value) {
        if (this.environmentVariables == null) {
            this.environmentVariables = new HashMap<>();
        }
        this.environmentVariables.put(key, value);
    }

    /**
     * 添加元数据
     */
    public void addMetadata(String key, Object value) {
        if (this.metadata == null) {
            this.metadata = new HashMap<>();
        }
        this.metadata.put(key, value);
    }
}
