package com.nexus.mcp.local.repository;

import com.nexus.mcp.local.entity.LocalMcpService;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 本地MCP服务数据访问层
 * 提供本地MCP服务相关的数据库操作
 */
@Repository
public interface LocalMcpServiceRepository extends JpaRepository<LocalMcpService, Long> {

    /**
     * 根据服务名称查找服务
     */
    Optional<LocalMcpService> findByServiceName(String serviceName);

    /**
     * 根据代理ID查找服务列表
     */
    List<LocalMcpService> findByAgentId(String agentId);

    /**
     * 根据服务类型查找服务列表
     */
    List<LocalMcpService> findByServiceType(LocalMcpService.LocalServiceType serviceType);

    /**
     * 根据状态查找服务列表
     */
    List<LocalMcpService> findByStatus(LocalMcpService.ServiceStatus status);

    /**
     * 根据健康状态查找服务列表
     */
    List<LocalMcpService> findByHealthStatus(LocalMcpService.HealthStatus healthStatus);

    /**
     * 根据代理ID和状态查找服务列表
     */
    List<LocalMcpService> findByAgentIdAndStatus(String agentId, LocalMcpService.ServiceStatus status);

    /**
     * 查找运行中的服务
     */
    @Query("SELECT s FROM LocalMcpService s WHERE s.status = 'ACTIVE'")
    List<LocalMcpService> findRunningServices();

    /**
     * 查找指定代理的运行中服务
     */
    @Query("SELECT s FROM LocalMcpService s WHERE s.agentId = :agentId AND s.status = 'ACTIVE'")
    List<LocalMcpService> findRunningServicesByAgent(@Param("agentId") String agentId);

    /**
     * 查找需要健康检查的服务
     */
    @Query("SELECT s FROM LocalMcpService s WHERE s.status = 'ACTIVE' AND " +
           "(s.lastHealthCheckAt IS NULL OR s.lastHealthCheckAt < :threshold)")
    List<LocalMcpService> findServicesNeedingHealthCheck(@Param("threshold") LocalDateTime threshold);

    /**
     * 查找自动启动的服务
     */
    @Query("SELECT s FROM LocalMcpService s WHERE s.autoStart = true AND s.status != 'ACTIVE'")
    List<LocalMcpService> findAutoStartServices();

    /**
     * 查找指定代理的自动启动服务
     */
    @Query("SELECT s FROM LocalMcpService s WHERE s.agentId = :agentId AND s.autoStart = true AND s.status != 'ACTIVE'")
    List<LocalMcpService> findAutoStartServicesByAgent(@Param("agentId") String agentId);

    /**
     * 查找长时间未更新的服务
     */
    @Query("SELECT s FROM LocalMcpService s WHERE s.updatedAt < :threshold")
    List<LocalMcpService> findStaleServices(@Param("threshold") LocalDateTime threshold);

    /**
     * 查找错误状态的服务
     */
    @Query("SELECT s FROM LocalMcpService s WHERE s.status = 'ERROR' OR s.healthStatus = 'UNHEALTHY'")
    List<LocalMcpService> findErrorServices();

    /**
     * 根据进程ID查找服务
     */
    Optional<LocalMcpService> findByProcessId(Long processId);

    /**
     * 根据端口查找服务
     */
    Optional<LocalMcpService> findByPort(Integer port);

    /**
     * 检查服务名称是否存在
     */
    boolean existsByServiceName(String serviceName);

    /**
     * 检查端口是否被占用
     */
    boolean existsByPort(Integer port);

    /**
     * 更新服务状态
     */
    @Modifying
    @Query("UPDATE LocalMcpService s SET s.status = :status WHERE s.id = :serviceId")
    void updateServiceStatus(@Param("serviceId") Long serviceId, 
                           @Param("status") LocalMcpService.ServiceStatus status);

    /**
     * 更新健康状态
     */
    @Modifying
    @Query("UPDATE LocalMcpService s SET s.healthStatus = :healthStatus, s.healthError = :error, " +
           "s.lastHealthCheckAt = :checkTime WHERE s.id = :serviceId")
    void updateHealthStatus(@Param("serviceId") Long serviceId,
                          @Param("healthStatus") LocalMcpService.HealthStatus healthStatus,
                          @Param("error") String error,
                          @Param("checkTime") LocalDateTime checkTime);

    /**
     * 更新进程信息
     */
    @Modifying
    @Query("UPDATE LocalMcpService s SET s.processId = :processId, s.port = :port, " +
           "s.startedAt = :startTime WHERE s.id = :serviceId")
    void updateProcessInfo(@Param("serviceId") Long serviceId,
                         @Param("processId") Long processId,
                         @Param("port") Integer port,
                         @Param("startTime") LocalDateTime startTime);

    /**
     * 清除进程信息
     */
    @Modifying
    @Query("UPDATE LocalMcpService s SET s.processId = NULL, s.port = NULL, " +
           "s.stoppedAt = :stopTime WHERE s.id = :serviceId")
    void clearProcessInfo(@Param("serviceId") Long serviceId, @Param("stopTime") LocalDateTime stopTime);

    /**
     * 增加重启次数
     */
    @Modifying
    @Query("UPDATE LocalMcpService s SET s.currentRestarts = s.currentRestarts + 1 WHERE s.id = :serviceId")
    void incrementRestartCount(@Param("serviceId") Long serviceId);

    /**
     * 重置重启次数
     */
    @Modifying
    @Query("UPDATE LocalMcpService s SET s.currentRestarts = 0 WHERE s.id = :serviceId")
    void resetRestartCount(@Param("serviceId") Long serviceId);

    /**
     * 统计服务总数
     */
    @Query("SELECT COUNT(s) FROM LocalMcpService s")
    long countTotalServices();

    /**
     * 统计运行中的服务数
     */
    @Query("SELECT COUNT(s) FROM LocalMcpService s WHERE s.status = 'ACTIVE'")
    long countRunningServices();

    /**
     * 统计指定代理的服务数
     */
    @Query("SELECT COUNT(s) FROM LocalMcpService s WHERE s.agentId = :agentId")
    long countServicesByAgent(@Param("agentId") String agentId);

    /**
     * 统计指定代理运行中的服务数
     */
    @Query("SELECT COUNT(s) FROM LocalMcpService s WHERE s.agentId = :agentId AND s.status = 'ACTIVE'")
    long countRunningServicesByAgent(@Param("agentId") String agentId);

    /**
     * 统计各种状态的服务数量
     */
    @Query("SELECT s.status, COUNT(s) FROM LocalMcpService s GROUP BY s.status")
    List<Object[]> countServicesByStatus();

    /**
     * 统计各种类型的服务数量
     */
    @Query("SELECT s.serviceType, COUNT(s) FROM LocalMcpService s GROUP BY s.serviceType")
    List<Object[]> countServicesByType();

    /**
     * 根据服务名称模糊查询
     */
    @Query("SELECT s FROM LocalMcpService s WHERE s.serviceName LIKE %:keyword% OR s.displayName LIKE %:keyword%")
    List<LocalMcpService> searchServices(@Param("keyword") String keyword);

    /**
     * 查找需要清理的服务（长时间处于错误状态）
     */
    @Query("SELECT s FROM LocalMcpService s WHERE s.status = 'ERROR' AND s.updatedAt < :threshold")
    List<LocalMcpService> findServicesToCleanup(@Param("threshold") LocalDateTime threshold);

    /**
     * 删除指定代理的所有服务
     */
    @Modifying
    @Query("DELETE FROM LocalMcpService s WHERE s.agentId = :agentId")
    void deleteByAgentId(@Param("agentId") String agentId);
}
