package com.nexus.mcp.local.scheduler;

import com.nexus.mcp.local.service.LocalMcpServiceManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 本地服务调度器
 * 负责定时执行健康检查、自动启动等任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "nexus.mcp.local.registry.enabled", havingValue = "true", matchIfMissing = true)
public class LocalServiceScheduler {

    private final LocalMcpServiceManager serviceManager;

    /**
     * 定时健康检查
     * 每分钟执行一次
     */
    @Scheduled(fixedRateString = "${nexus.mcp.local.registry.health-check-interval:60000}")
    public void performScheduledHealthCheck() {
        log.debug("开始执行定时健康检查");
        
        try {
            serviceManager.performBatchHealthCheck();
            log.debug("定时健康检查完成");
        } catch (Exception e) {
            log.error("定时健康检查失败", e);
        }
    }

    /**
     * 定时服务扫描
     * 每30秒执行一次，检查是否有需要自动启动的服务
     */
    @Scheduled(fixedRateString = "${nexus.mcp.local.registry.scan-interval:30000}")
    public void performServiceScan() {
        log.debug("开始执行服务扫描");
        
        try {
            // 这里可以添加服务扫描逻辑
            // 比如检查新的服务配置、清理过期服务等
            
            log.debug("服务扫描完成");
        } catch (Exception e) {
            log.error("服务扫描失败", e);
        }
    }

    /**
     * 定时统计信息收集
     * 每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void collectStatistics() {
        log.debug("开始收集服务统计信息");
        
        try {
            LocalMcpServiceManager.ServiceStatistics stats = serviceManager.getServiceStatistics(null);
            log.info("服务统计 - 总数: {}, 运行中: {}, 停止: {}, 运行率: {:.2f}%", 
                    stats.getTotalServices(), 
                    stats.getRunningServices(), 
                    stats.getStoppedServices(),
                    stats.getRunningPercentage());
            
        } catch (Exception e) {
            log.error("收集服务统计信息失败", e);
        }
    }

    /**
     * 定时清理任务
     * 每小时执行一次
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    public void performCleanup() {
        log.debug("开始执行清理任务");
        
        try {
            // 这里可以添加清理逻辑
            // 比如清理长时间停止的服务、清理日志文件等
            
            log.debug("清理任务完成");
        } catch (Exception e) {
            log.error("清理任务失败", e);
        }
    }
}
