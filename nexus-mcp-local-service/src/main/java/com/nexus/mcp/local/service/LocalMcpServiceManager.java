package com.nexus.mcp.local.service;

import com.nexus.common.constants.CacheNames;
import com.nexus.common.event.MCPTaskCreatedEvent;
import com.nexus.common.exception.NexusException;
import com.nexus.common.model.MCPServiceMetadata;
import com.nexus.common.service.RocketMQProducerService;
import com.nexus.mcp.local.entity.LocalMcpService;
import com.nexus.mcp.local.repository.LocalMcpServiceRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 本地MCP服务管理器
 * 负责本地MCP服务的注册、管理和生命周期控制
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LocalMcpServiceManager {

    private final LocalMcpServiceRepository serviceRepository;
    private final LocalServiceExecutor serviceExecutor;
    private final LocalServiceHealthChecker healthChecker;
    private final RocketMQProducerService messageProducerService;

    /**
     * 注册本地MCP服务
     */
    @Transactional
    @CacheEvict(value = CacheNames.MCP_SERVICES, allEntries = true)
    public LocalMcpService registerService(MCPServiceMetadata metadata, String agentId) {
        log.info("注册本地MCP服务: {} (代理: {})", metadata.getServiceName(), agentId);

        // 检查服务是否已存在
        if (serviceRepository.existsByServiceName(metadata.getServiceName())) {
            throw new NexusException.BusinessException("服务已存在: " + metadata.getServiceName());
        }

        // 创建本地服务实体
        LocalMcpService service = LocalMcpService.builder()
                .serviceName(metadata.getServiceName())
                .displayName(metadata.getDisplayName())
                .description(metadata.getDescription())
                .serviceType(determineServiceType(metadata))
                .status(LocalMcpService.ServiceStatus.INACTIVE)
                .version(metadata.getVersion())
                .agentId(agentId)
                .autoStart(false)
                .maxRestarts(3)
                .currentRestarts(0)
                .build();

        // 设置元数据
        if (metadata.getMetadata() != null) {
            metadata.getMetadata().forEach(service::addMetadata);
        }

        // 设置配置参数
        service.addConfigParam("protocol", metadata.getProtocolType());
        service.addConfigParam("endpoint", metadata.getEndpoint());
        service.addConfigParam("toolCount", metadata.getToolCount());

        service = serviceRepository.save(service);
        log.info("本地MCP服务注册成功: {} (ID: {})", service.getServiceName(), service.getId());

        return service;
    }

    /**
     * 启动本地服务
     */
    @Transactional
    @CacheEvict(value = {CacheNames.MCP_SERVICES, CacheNames.MCP_SERVICE_STATUS}, allEntries = true)
    public void startService(Long serviceId) {
        log.info("启动本地MCP服务: {}", serviceId);

        LocalMcpService service = getServiceById(serviceId);
        
        if (service.isRunning()) {
            log.warn("服务已在运行中: {}", service.getServiceName());
            return;
        }

        try {
            // 更新状态为启动中
            service.setStatus(LocalMcpService.ServiceStatus.STARTING);
            serviceRepository.save(service);

            // 执行启动命令
            LocalServiceExecutor.ExecutionResult result = serviceExecutor.startService(service);
            
            if (result.isSuccess()) {
                // 启动成功，更新服务信息
                service.markAsStarted(result.getProcessId(), result.getPort());
                service.resetRestarts();
                serviceRepository.save(service);
                
                log.info("本地MCP服务启动成功: {} (PID: {}, Port: {})", 
                        service.getServiceName(), result.getProcessId(), result.getPort());
            } else {
                // 启动失败
                service.setStatus(LocalMcpService.ServiceStatus.ERROR);
                service.incrementRestarts();
                serviceRepository.save(service);
                
                log.error("本地MCP服务启动失败: {} - {}", service.getServiceName(), result.getErrorMessage());
                throw new NexusException.BusinessException("服务启动失败: " + result.getErrorMessage());
            }
        } catch (Exception e) {
            service.setStatus(LocalMcpService.ServiceStatus.ERROR);
            serviceRepository.save(service);
            throw e;
        }
    }

    /**
     * 停止本地服务
     */
    @Transactional
    @CacheEvict(value = {CacheNames.MCP_SERVICES, CacheNames.MCP_SERVICE_STATUS}, allEntries = true)
    public void stopService(Long serviceId) {
        log.info("停止本地MCP服务: {}", serviceId);

        LocalMcpService service = getServiceById(serviceId);
        
        if (!service.isRunning()) {
            log.warn("服务未在运行: {}", service.getServiceName());
            return;
        }

        try {
            // 更新状态为停止中
            service.setStatus(LocalMcpService.ServiceStatus.STOPPING);
            serviceRepository.save(service);

            // 执行停止命令
            boolean stopped = serviceExecutor.stopService(service);
            
            if (stopped) {
                service.markAsStopped();
                serviceRepository.save(service);
                log.info("本地MCP服务停止成功: {}", service.getServiceName());
            } else {
                service.setStatus(LocalMcpService.ServiceStatus.ERROR);
                serviceRepository.save(service);
                log.error("本地MCP服务停止失败: {}", service.getServiceName());
                throw new NexusException.BusinessException("服务停止失败");
            }
        } catch (Exception e) {
            service.setStatus(LocalMcpService.ServiceStatus.ERROR);
            serviceRepository.save(service);
            throw e;
        }
    }

    /**
     * 重启本地服务
     */
    @Transactional
    public void restartService(Long serviceId) {
        log.info("重启本地MCP服务: {}", serviceId);

        LocalMcpService service = getServiceById(serviceId);
        
        if (!service.canRestart()) {
            throw new NexusException.BusinessException("服务重启次数已达上限: " + service.getMaxRestarts());
        }

        // 先停止服务
        if (service.isRunning()) {
            stopService(serviceId);
        }

        // 等待一段时间后启动
        try {
            Thread.sleep(2000); // 等待2秒
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 启动服务
        startService(serviceId);
    }

    /**
     * 注销本地服务
     */
    @Transactional
    @CacheEvict(value = CacheNames.MCP_SERVICES, allEntries = true)
    public void unregisterService(Long serviceId) {
        log.info("注销本地MCP服务: {}", serviceId);

        LocalMcpService service = getServiceById(serviceId);
        
        // 如果服务正在运行，先停止
        if (service.isRunning()) {
            stopService(serviceId);
        }

        // 删除服务记录
        serviceRepository.delete(service);
        log.info("本地MCP服务注销成功: {}", service.getServiceName());
    }

    /**
     * 获取服务详情
     */
    @Cacheable(value = CacheNames.MCP_SERVICES, key = "#serviceId")
    public LocalMcpService getServiceById(Long serviceId) {
        return serviceRepository.findById(serviceId)
                .orElseThrow(() -> new NexusException.ResourceNotFoundException("本地MCP服务不存在: " + serviceId));
    }

    /**
     * 根据服务名称获取服务
     */
    @Cacheable(value = CacheNames.MCP_SERVICES, key = "#serviceName")
    public Optional<LocalMcpService> getServiceByName(String serviceName) {
        return serviceRepository.findByServiceName(serviceName);
    }

    /**
     * 获取所有服务
     */
    // @Cacheable(value = CacheNames.MCP_SERVICES, key = "'all'")
    public List<LocalMcpService> getAllServices() {
        log.debug("获取所有本地MCP服务");
        return serviceRepository.findAll();
    }

    /**
     * 获取代理的所有服务
     */
    @Cacheable(value = CacheNames.MCP_SERVICES, key = "'agent:' + #agentId")
    public List<LocalMcpService> getServicesByAgent(String agentId) {
        return serviceRepository.findByAgentId(agentId);
    }

    /**
     * 获取运行中的服务列表
     */
    @Cacheable(value = CacheNames.MCP_SERVICE_STATUS, key = "'running'")
    public List<LocalMcpService> getRunningServices() {
        return serviceRepository.findRunningServices();
    }

    /**
     * 获取代理的运行中服务
     */
    @Cacheable(value = CacheNames.MCP_SERVICE_STATUS, key = "'running:' + #agentId")
    public List<LocalMcpService> getRunningServicesByAgent(String agentId) {
        return serviceRepository.findRunningServicesByAgent(agentId);
    }

    /**
     * 执行健康检查
     */
    @Transactional
    public void performHealthCheck(Long serviceId) {
        LocalMcpService service = getServiceById(serviceId);
        
        if (!service.isRunning()) {
            return;
        }

        log.debug("执行健康检查: {}", service.getServiceName());
        
        LocalServiceHealthChecker.HealthCheckResult result = healthChecker.checkHealth(service);
        
        service.updateHealthStatus(
                result.isHealthy() ? LocalMcpService.HealthStatus.HEALTHY : LocalMcpService.HealthStatus.UNHEALTHY,
                result.getErrorMessage()
        );
        
        serviceRepository.save(service);
        
        // 如果健康检查失败且服务应该运行，尝试重启
        if (!result.isHealthy() && service.canRestart()) {
            log.warn("服务健康检查失败，尝试重启: {}", service.getServiceName());
            try {
                restartService(serviceId);
            } catch (Exception e) {
                log.error("服务重启失败: {}", service.getServiceName(), e);
            }
        }
    }

    /**
     * 批量健康检查
     */
    public void performBatchHealthCheck() {
        LocalDateTime threshold = LocalDateTime.now().minusMinutes(5); // 5分钟前
        List<LocalMcpService> services = serviceRepository.findServicesNeedingHealthCheck(threshold);
        
        log.debug("执行批量健康检查，服务数量: {}", services.size());
        
        services.forEach(service -> {
            try {
                performHealthCheck(service.getId());
            } catch (Exception e) {
                log.error("健康检查失败: {}", service.getServiceName(), e);
            }
        });
    }

    /**
     * 自动启动服务
     */
    public void autoStartServices(String agentId) {
        List<LocalMcpService> services = serviceRepository.findAutoStartServicesByAgent(agentId);
        
        log.info("自动启动服务，代理: {}, 服务数量: {}", agentId, services.size());
        
        services.forEach(service -> {
            try {
                startService(service.getId());
            } catch (Exception e) {
                log.error("自动启动服务失败: {}", service.getServiceName(), e);
            }
        });
    }

    /**
     * 确定服务类型
     */
    private LocalMcpService.LocalServiceType determineServiceType(MCPServiceMetadata metadata) {
        String serviceName = metadata.getServiceName().toLowerCase();
        
        if (serviceName.contains("npx") || serviceName.contains("npm")) {
            return LocalMcpService.LocalServiceType.NPX;
        } else if (serviceName.contains("uv") || serviceName.contains("python")) {
            return LocalMcpService.LocalServiceType.UV;
        } else if (serviceName.contains("node")) {
            return LocalMcpService.LocalServiceType.NODE;
        } else if (serviceName.contains("docker")) {
            return LocalMcpService.LocalServiceType.DOCKER;
        } else {
            return LocalMcpService.LocalServiceType.CUSTOM;
        }
    }

    /**
     * 获取服务统计信息
     */
    public ServiceStatistics getServiceStatistics(String agentId) {
        long totalServices = agentId != null ? 
                serviceRepository.countServicesByAgent(agentId) : 
                serviceRepository.countTotalServices();
        
        long runningServices = agentId != null ? 
                serviceRepository.countRunningServicesByAgent(agentId) : 
                serviceRepository.countRunningServices();
        
        return ServiceStatistics.builder()
                .totalServices(totalServices)
                .runningServices(runningServices)
                .stoppedServices(totalServices - runningServices)
                .build();
    }

    /**
     * 服务统计信息
     */
    @lombok.Data
    @lombok.Builder
    public static class ServiceStatistics {
        private long totalServices;
        private long runningServices;
        private long stoppedServices;
        
        public double getRunningPercentage() {
            return totalServices > 0 ? (double) runningServices / totalServices * 100.0 : 0.0;
        }
    }

    /**
     * 基于事件的异步工具调用
     * 返回任务ID，客户端可以通过任务ID查询结果
     */
    public String callToolAsyncWithEvent(String toolName, java.util.Map<String, Object> arguments,
                                        String userId, String clientIp, String userAgent) {
        log.info("基于事件的异步调用本地MCP工具: {} (用户: {})", toolName, userId);

        try {
            // 查找提供该工具的服务
            LocalMcpService service = findServiceByTool(toolName);
            if (service == null) {
                throw new NexusException.ResourceNotFoundException("工具不存在: " + toolName);
            }

            // 检查服务是否运行中
            if (service.getStatus() != LocalMcpService.ServiceStatus.ACTIVE) {
                throw new NexusException.BusinessException("服务未运行: " + service.getServiceName());
            }

            // 生成任务ID
            String taskId = generateTaskId(toolName, userId);

            // 创建MCP任务事件
            MCPTaskCreatedEvent taskEvent = new MCPTaskCreatedEvent(
                    taskId,
                    userId,
                    toolName,
                    service.getId(),
                    service.getServiceName(),
                    "LOCAL", // 服务类型
                    arguments
            );

            // 设置任务优先级
            taskEvent.setTaskPriority(determineTaskPriority(toolName, userId));

            // 设置超时时间
            taskEvent.setTimeoutMs(getToolTimeout(toolName));

            // 添加任务标签
            taskEvent.addTaskTag("toolType", "local");
            taskEvent.addTaskTag("serviceName", service.getServiceName());
            taskEvent.addTaskTag("agentId", service.getAgentId());

            // 设置客户端信息
            taskEvent.setClientIp(clientIp);
            taskEvent.setUserAgent(userAgent);

            taskEvent.setSourceService("nexus-mcp-local-service");

            // 发送任务创建事件
            boolean sent = messageProducerService.sendEventAsync(taskEvent);

            if (sent) {
                log.info("本地MCP任务事件发送成功: taskId={}, toolName={}", taskId, toolName);
                return taskId;
            } else {
                throw new NexusException.BusinessException("任务事件发送失败");
            }

        } catch (Exception e) {
            log.error("基于事件的异步本地工具调用失败: {} - {}", toolName, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 根据工具名称查找提供该工具的服务
     */
    private LocalMcpService findServiceByTool(String toolName) {
        try {
            List<LocalMcpService> runningServices = serviceRepository.findByStatus(LocalMcpService.ServiceStatus.ACTIVE);

            for (LocalMcpService service : runningServices) {
                try {
                    // 简化实现：假设服务名称包含工具名称或者直接返回第一个运行中的服务
                    // 在实际实现中，应该通过服务接口查询工具列表
                    if (service.getServiceName().toLowerCase().contains(toolName.toLowerCase()) ||
                        service.getDisplayName().toLowerCase().contains(toolName.toLowerCase())) {
                        return service;
                    }

                } catch (Exception e) {
                    log.warn("检查本地服务工具失败: serviceId={} - {}", service.getId(), e.getMessage());
                }
            }

            // 如果没有找到匹配的服务，返回第一个运行中的服务（简化处理）
            if (!runningServices.isEmpty()) {
                log.debug("未找到匹配工具的服务，使用第一个运行中的服务: {}", runningServices.get(0).getServiceName());
                return runningServices.get(0);
            }

            return null;

        } catch (Exception e) {
            log.error("查找本地工具服务失败: toolName={} - {}", toolName, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 生成任务ID
     */
    private String generateTaskId(String toolName, String userId) {
        return String.format("mcp-local-task-%s-%s-%d",
                toolName.replaceAll("[^a-zA-Z0-9]", ""),
                userId,
                System.currentTimeMillis());
    }

    /**
     * 确定任务优先级
     */
    private MCPTaskCreatedEvent.TaskPriority determineTaskPriority(String toolName, String userId) {
        // 本地工具通常响应更快，可以设置较高优先级
        if (toolName.contains("urgent") || toolName.contains("critical")) {
            return MCPTaskCreatedEvent.TaskPriority.URGENT;
        } else if (toolName.contains("high") || toolName.contains("important")) {
            return MCPTaskCreatedEvent.TaskPriority.HIGH;
        } else if (toolName.contains("low") || toolName.contains("batch")) {
            return MCPTaskCreatedEvent.TaskPriority.LOW;
        } else {
            return MCPTaskCreatedEvent.TaskPriority.NORMAL;
        }
    }

    /**
     * 获取工具超时时间
     */
    private Long getToolTimeout(String toolName) {
        // 本地工具通常执行更快，设置较短的超时时间
        if (toolName.contains("quick") || toolName.contains("fast")) {
            return 15000L; // 15秒
        } else if (toolName.contains("slow") || toolName.contains("heavy")) {
            return 300000L; // 5分钟
        } else {
            return 60000L; // 1分钟
        }
    }
}
