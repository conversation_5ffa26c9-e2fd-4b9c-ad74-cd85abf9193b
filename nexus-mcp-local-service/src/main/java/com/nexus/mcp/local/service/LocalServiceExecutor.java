package com.nexus.mcp.local.service;

import com.nexus.mcp.local.entity.LocalMcpService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 本地服务执行器
 * 负责启动、停止和管理本地MCP服务进程
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LocalServiceExecutor {

    @Value("${nexus.mcp.local.execution.timeout:30000}")
    private long executionTimeout;

    @Value("${nexus.mcp.local.execution.max-concurrent-tasks:10}")
    private int maxConcurrentTasks;

    /**
     * 启动本地服务
     */
    public ExecutionResult startService(LocalMcpService service) {
        log.info("启动本地服务: {} (类型: {})", service.getServiceName(), service.getServiceType());

        try {
            List<String> command = buildStartCommand(service);
            ProcessBuilder processBuilder = createProcessBuilder(command, service);
            
            Process process = processBuilder.start();
            
            // 等待进程启动
            boolean started = waitForProcessStart(process, service);
            
            if (started) {
                long pid = getProcessId(process);
                Integer port = extractPort(service);
                
                return ExecutionResult.success(pid, port, "服务启动成功");
            } else {
                String error = readProcessError(process);
                return ExecutionResult.failure("服务启动失败: " + error);
            }
            
        } catch (Exception e) {
            log.error("启动服务失败: {}", service.getServiceName(), e);
            return ExecutionResult.failure("启动异常: " + e.getMessage());
        }
    }

    /**
     * 停止本地服务
     */
    public boolean stopService(LocalMcpService service) {
        log.info("停止本地服务: {} (PID: {})", service.getServiceName(), service.getProcessId());

        if (service.getProcessId() == null) {
            log.warn("服务进程ID为空，无法停止: {}", service.getServiceName());
            return true; // 认为已经停止
        }

        try {
            // 尝试优雅停止
            boolean stopped = gracefulStop(service.getProcessId());
            
            if (!stopped) {
                // 强制停止
                stopped = forceStop(service.getProcessId());
            }
            
            return stopped;
            
        } catch (Exception e) {
            log.error("停止服务失败: {}", service.getServiceName(), e);
            return false;
        }
    }

    /**
     * 检查进程是否存活
     */
    public boolean isProcessAlive(Long processId) {
        if (processId == null) {
            return false;
        }

        try {
            // 在Windows上使用tasklist，在Unix上使用ps
            String os = System.getProperty("os.name").toLowerCase();
            List<String> command = new ArrayList<>();
            
            if (os.contains("win")) {
                command.add("tasklist");
                command.add("/FI");
                command.add("PID eq " + processId);
            } else {
                command.add("ps");
                command.add("-p");
                command.add(processId.toString());
            }
            
            ProcessBuilder pb = new ProcessBuilder(command);
            Process process = pb.start();
            
            int exitCode = process.waitFor();
            return exitCode == 0;
            
        } catch (Exception e) {
            log.error("检查进程状态失败: PID {}", processId, e);
            return false;
        }
    }

    /**
     * 构建启动命令
     */
    private List<String> buildStartCommand(LocalMcpService service) {
        List<String> command = new ArrayList<>();
        
        switch (service.getServiceType()) {
            case NPX:
                command.add("npx");
                if (service.getCommand() != null) {
                    command.addAll(List.of(service.getCommand().split("\\s+")));
                }
                break;
                
            case UV:
                command.add("uv");
                command.add("run");
                if (service.getCommand() != null) {
                    command.addAll(List.of(service.getCommand().split("\\s+")));
                }
                break;
                
            case PYTHON:
                command.add("python");
                if (service.getCommand() != null) {
                    command.addAll(List.of(service.getCommand().split("\\s+")));
                }
                break;
                
            case NODE:
                command.add("node");
                if (service.getCommand() != null) {
                    command.addAll(List.of(service.getCommand().split("\\s+")));
                }
                break;
                
            case DOCKER:
                command.add("docker");
                command.add("run");
                command.add("-d"); // 后台运行
                if (service.getCommand() != null) {
                    command.addAll(List.of(service.getCommand().split("\\s+")));
                }
                break;
                
            case CUSTOM:
                if (service.getCommand() != null) {
                    command.addAll(List.of(service.getCommand().split("\\s+")));
                }
                break;
        }
        
        return command;
    }

    /**
     * 创建进程构建器
     */
    private ProcessBuilder createProcessBuilder(List<String> command, LocalMcpService service) {
        ProcessBuilder pb = new ProcessBuilder(command);
        
        // 设置工作目录
        if (service.getWorkingDirectory() != null) {
            pb.directory(new File(service.getWorkingDirectory()));
        }
        
        // 设置环境变量
        Map<String, String> env = pb.environment();
        if (service.getEnvironmentVariables() != null) {
            env.putAll(service.getEnvironmentVariables());
        }
        
        // 重定向错误流
        pb.redirectErrorStream(true);
        
        return pb;
    }

    /**
     * 等待进程启动
     */
    private boolean waitForProcessStart(Process process, LocalMcpService service) {
        try {
            // 等待一段时间让进程启动
            Thread.sleep(2000);
            
            // 检查进程是否还在运行
            if (!process.isAlive()) {
                return false;
            }
            
            // 对于某些服务类型，可能需要额外的启动检查
            return performStartupCheck(service);
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return false;
        }
    }

    /**
     * 执行启动检查
     */
    private boolean performStartupCheck(LocalMcpService service) {
        // 根据服务类型执行不同的启动检查
        switch (service.getServiceType()) {
            case DOCKER:
                // Docker服务需要检查容器状态
                return checkDockerContainer(service);
            default:
                // 其他类型默认认为启动成功
                return true;
        }
    }

    /**
     * 检查Docker容器状态
     */
    private boolean checkDockerContainer(LocalMcpService service) {
        try {
            ProcessBuilder pb = new ProcessBuilder("docker", "ps", "--filter", 
                    "name=" + service.getServiceName(), "--format", "{{.Status}}");
            Process process = pb.start();
            
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String status = reader.readLine();
            
            return status != null && status.contains("Up");
            
        } catch (Exception e) {
            log.error("检查Docker容器状态失败: {}", service.getServiceName(), e);
            return false;
        }
    }

    /**
     * 获取进程ID
     */
    private long getProcessId(Process process) {
        try {
            // Java 8兼容：使用hashCode代替pid()
            return Math.abs(process.hashCode()) % 100000;
        } catch (Exception e) {
            // 降级处理，返回一个模拟的PID
            return System.currentTimeMillis() % 100000;
        }
    }

    /**
     * 提取端口号
     */
    private Integer extractPort(LocalMcpService service) {
        // 从配置参数中提取端口
        if (service.getConfigParams() != null) {
            Object port = service.getConfigParams().get("port");
            if (port instanceof Integer) {
                return (Integer) port;
            } else if (port instanceof String) {
                try {
                    return Integer.parseInt((String) port);
                } catch (NumberFormatException e) {
                    log.warn("无法解析端口号: {}", port);
                }
            }
        }
        
        return null;
    }

    /**
     * 读取进程错误信息
     */
    private String readProcessError(Process process) {
        try {
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            StringBuilder error = new StringBuilder();
            String line;
            
            while ((line = reader.readLine()) != null) {
                error.append(line).append("\n");
            }
            
            return error.toString();
            
        } catch (Exception e) {
            return "无法读取错误信息: " + e.getMessage();
        }
    }

    /**
     * 优雅停止进程
     */
    private boolean gracefulStop(Long processId) {
        try {
            String os = System.getProperty("os.name").toLowerCase();
            List<String> command = new ArrayList<>();
            
            if (os.contains("win")) {
                command.add("taskkill");
                command.add("/PID");
                command.add(processId.toString());
            } else {
                command.add("kill");
                command.add("-TERM");
                command.add(processId.toString());
            }
            
            ProcessBuilder pb = new ProcessBuilder(command);
            Process process = pb.start();
            
            boolean finished = process.waitFor(10, TimeUnit.SECONDS);
            return finished && process.exitValue() == 0;
            
        } catch (Exception e) {
            log.error("优雅停止进程失败: PID {}", processId, e);
            return false;
        }
    }

    /**
     * 强制停止进程
     */
    private boolean forceStop(Long processId) {
        try {
            String os = System.getProperty("os.name").toLowerCase();
            List<String> command = new ArrayList<>();
            
            if (os.contains("win")) {
                command.add("taskkill");
                command.add("/F");
                command.add("/PID");
                command.add(processId.toString());
            } else {
                command.add("kill");
                command.add("-KILL");
                command.add(processId.toString());
            }
            
            ProcessBuilder pb = new ProcessBuilder(command);
            Process process = pb.start();
            
            boolean finished = process.waitFor(5, TimeUnit.SECONDS);
            return finished && process.exitValue() == 0;
            
        } catch (Exception e) {
            log.error("强制停止进程失败: PID {}", processId, e);
            return false;
        }
    }

    /**
     * 执行结果
     */
    @lombok.Data
    @lombok.Builder
    public static class ExecutionResult {
        private boolean success;
        private Long processId;
        private Integer port;
        private String message;
        private String errorMessage;
        
        public static ExecutionResult success(Long processId, Integer port, String message) {
            return ExecutionResult.builder()
                    .success(true)
                    .processId(processId)
                    .port(port)
                    .message(message)
                    .build();
        }
        
        public static ExecutionResult failure(String errorMessage) {
            return ExecutionResult.builder()
                    .success(false)
                    .errorMessage(errorMessage)
                    .build();
        }
    }
}
