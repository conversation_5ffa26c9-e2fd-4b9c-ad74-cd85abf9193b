package com.nexus.mcp.local.service;

import com.nexus.mcp.local.entity.LocalMcpService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.Socket;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 本地服务健康检查器
 * 负责检查本地MCP服务的健康状态
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LocalServiceHealthChecker {

    private final LocalServiceExecutor serviceExecutor;
    private final RestTemplate restTemplate = new RestTemplate();

    @Value("${nexus.mcp.local.registry.health-check-interval:60000}")
    private long healthCheckInterval;

    @Value("${nexus.mcp.local.execution.timeout:30000}")
    private long healthCheckTimeout;

    /**
     * 执行健康检查
     */
    public HealthCheckResult checkHealth(LocalMcpService service) {
        log.debug("执行健康检查: {}", service.getServiceName());

        try {
            // 首先检查进程是否存活
            if (!isProcessAlive(service)) {
                return HealthCheckResult.unhealthy("进程不存在或已停止");
            }

            // 根据服务类型执行特定的健康检查
            return performServiceSpecificHealthCheck(service);

        } catch (Exception e) {
            log.error("健康检查异常: {}", service.getServiceName(), e);
            return HealthCheckResult.unhealthy("健康检查异常: " + e.getMessage());
        }
    }

    /**
     * 检查进程是否存活
     */
    private boolean isProcessAlive(LocalMcpService service) {
        if (service.getProcessId() == null) {
            return false;
        }

        return serviceExecutor.isProcessAlive(service.getProcessId());
    }

    /**
     * 执行服务特定的健康检查
     */
    private HealthCheckResult performServiceSpecificHealthCheck(LocalMcpService service) {
        switch (service.getServiceType()) {
            case DOCKER:
                return checkDockerServiceHealth(service);
            case NPX:
            case UV:
            case PYTHON:
            case NODE:
                return checkScriptServiceHealth(service);
            case CUSTOM:
                return checkCustomServiceHealth(service);
            default:
                return checkBasicServiceHealth(service);
        }
    }

    /**
     * 检查Docker服务健康状态
     */
    private HealthCheckResult checkDockerServiceHealth(LocalMcpService service) {
        try {
            // 检查Docker容器状态
            ProcessBuilder pb = new ProcessBuilder("docker", "inspect", 
                    "--format={{.State.Health.Status}}", service.getServiceName());
            Process process = pb.start();
            
            boolean finished = process.waitFor(5, TimeUnit.SECONDS);
            if (!finished) {
                process.destroyForcibly();
                return HealthCheckResult.unhealthy("Docker健康检查超时");
            }

            if (process.exitValue() == 0) {
                // 读取健康状态
                String healthStatus = new String(process.getInputStream().readAllBytes()).trim();
                if ("healthy".equals(healthStatus)) {
                    return HealthCheckResult.healthy("Docker容器健康");
                } else {
                    return HealthCheckResult.unhealthy("Docker容器状态: " + healthStatus);
                }
            } else {
                return HealthCheckResult.unhealthy("无法获取Docker容器状态");
            }

        } catch (Exception e) {
            log.error("Docker健康检查失败: {}", service.getServiceName(), e);
            return HealthCheckResult.unhealthy("Docker健康检查异常: " + e.getMessage());
        }
    }

    /**
     * 检查脚本服务健康状态
     */
    private HealthCheckResult checkScriptServiceHealth(LocalMcpService service) {
        // 对于脚本服务，主要检查端口连通性
        if (service.getPort() != null) {
            return checkPortConnectivity(service.getPort());
        }

        // 如果没有端口信息，检查进程是否响应
        return checkProcessResponsiveness(service);
    }

    /**
     * 检查自定义服务健康状态
     */
    private HealthCheckResult checkCustomServiceHealth(LocalMcpService service) {
        // 检查是否有自定义健康检查配置
        Map<String, Object> healthCheck = service.getHealthCheck();
        if (healthCheck != null && !healthCheck.isEmpty()) {
            return performCustomHealthCheck(service, healthCheck);
        }

        // 默认检查
        return checkBasicServiceHealth(service);
    }

    /**
     * 执行自定义健康检查
     */
    private HealthCheckResult performCustomHealthCheck(LocalMcpService service, Map<String, Object> healthCheck) {
        String type = (String) healthCheck.get("type");
        
        if ("http".equals(type)) {
            String url = (String) healthCheck.get("url");
            return checkHttpEndpoint(url);
        } else if ("tcp".equals(type)) {
            String host = (String) healthCheck.getOrDefault("host", "localhost");
            Integer port = (Integer) healthCheck.get("port");
            return checkTcpConnectivity(host, port);
        } else if ("command".equals(type)) {
            String command = (String) healthCheck.get("command");
            return checkCommandExecution(command);
        }

        return HealthCheckResult.unhealthy("不支持的健康检查类型: " + type);
    }

    /**
     * 基本健康检查
     */
    private HealthCheckResult checkBasicServiceHealth(LocalMcpService service) {
        // 检查进程存活时间
        if (service.getStartedAt() != null) {
            LocalDateTime now = LocalDateTime.now();
            long runningMinutes = java.time.Duration.between(service.getStartedAt(), now).toMinutes();
            
            if (runningMinutes < 1) {
                return HealthCheckResult.unhealthy("服务刚启动，等待稳定");
            }
        }

        return HealthCheckResult.healthy("基本健康检查通过");
    }

    /**
     * 检查端口连通性
     */
    private HealthCheckResult checkPortConnectivity(Integer port) {
        try (Socket socket = new Socket()) {
            socket.connect(new java.net.InetSocketAddress("localhost", port), 5000);
            return HealthCheckResult.healthy("端口连通性正常");
        } catch (IOException e) {
            return HealthCheckResult.unhealthy("端口不可达: " + port);
        }
    }

    /**
     * 检查TCP连通性
     */
    private HealthCheckResult checkTcpConnectivity(String host, Integer port) {
        if (port == null) {
            return HealthCheckResult.unhealthy("端口配置为空");
        }

        try (Socket socket = new Socket()) {
            socket.connect(new java.net.InetSocketAddress(host, port), 5000);
            return HealthCheckResult.healthy("TCP连接正常");
        } catch (IOException e) {
            return HealthCheckResult.unhealthy("TCP连接失败: " + host + ":" + port);
        }
    }

    /**
     * 检查HTTP端点
     */
    private HealthCheckResult checkHttpEndpoint(String url) {
        try {
            HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);
            
            int responseCode = connection.getResponseCode();
            if (responseCode >= 200 && responseCode < 300) {
                return HealthCheckResult.healthy("HTTP端点响应正常");
            } else {
                return HealthCheckResult.unhealthy("HTTP端点响应异常: " + responseCode);
            }
        } catch (Exception e) {
            return HealthCheckResult.unhealthy("HTTP端点检查失败: " + e.getMessage());
        }
    }

    /**
     * 检查命令执行
     */
    private HealthCheckResult checkCommandExecution(String command) {
        try {
            ProcessBuilder pb = new ProcessBuilder(command.split("\\s+"));
            Process process = pb.start();
            
            boolean finished = process.waitFor(10, TimeUnit.SECONDS);
            if (!finished) {
                process.destroyForcibly();
                return HealthCheckResult.unhealthy("健康检查命令超时");
            }

            if (process.exitValue() == 0) {
                return HealthCheckResult.healthy("健康检查命令执行成功");
            } else {
                return HealthCheckResult.unhealthy("健康检查命令执行失败");
            }

        } catch (Exception e) {
            return HealthCheckResult.unhealthy("健康检查命令异常: " + e.getMessage());
        }
    }

    /**
     * 检查进程响应性
     */
    private HealthCheckResult checkProcessResponsiveness(LocalMcpService service) {
        // 这里可以实现更复杂的进程响应性检查
        // 比如发送信号、检查CPU使用率等
        
        // 简单实现：检查进程是否还在运行
        if (isProcessAlive(service)) {
            return HealthCheckResult.healthy("进程响应正常");
        } else {
            return HealthCheckResult.unhealthy("进程无响应");
        }
    }

    /**
     * 健康检查结果
     */
    @lombok.Data
    @lombok.Builder
    public static class HealthCheckResult {
        private boolean healthy;
        private String message;
        private String errorMessage;
        private LocalDateTime checkTime;
        
        public static HealthCheckResult healthy(String message) {
            return HealthCheckResult.builder()
                    .healthy(true)
                    .message(message)
                    .checkTime(LocalDateTime.now())
                    .build();
        }
        
        public static HealthCheckResult unhealthy(String errorMessage) {
            return HealthCheckResult.builder()
                    .healthy(false)
                    .errorMessage(errorMessage)
                    .checkTime(LocalDateTime.now())
                    .build();
        }
    }
}
