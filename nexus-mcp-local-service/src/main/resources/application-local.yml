# 本地MCP服务配置
spring:
  # 数据库配置 - 使用PostgreSQL
  datasource:
    url: *****************************************************************************************************************************************************************************
    username: neondb_owner
    password: npg_kc2S7QGCPbEh
    driver-class-name: org.postgresql.Driver
    hikari:
      pool-name: NexusMcpLocalPool
      maximum-pool-size: 5
      minimum-idle: 2
      connection-timeout: 30000
      idle-timeout: 120000
      max-lifetime: 300000
      leak-detection-threshold: 15000
      connection-test-query: SELECT 1
      connection-init-sql: SET TIME ZONE 'UTC'
      validation-timeout: 5000
      keepalive-time: 60000
      auto-commit: true
      data-source-properties:
        socketTimeout: 30
        connectTimeout: 15
        loginTimeout: 20
        tcpKeepAlive: true
        ssl: true
        sslmode: require
        sslfactory: org.postgresql.ssl.NonValidatingFactory
        reWriteBatchedInserts: true
        prepareThreshold: 5
        preferQueryMode: extended
        autoReconnect: true

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    open-in-view: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQL10Dialect
        format_sql: true
        jdbc:
          batch_size: 25
          batch_versioned_data: true
        order_inserts: true
        order_updates: true
        connection:
          release_mode: after_transaction
        time_zone: UTC
        physical_naming_strategy: org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy
        implicit_naming_strategy: org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
        cache:
          use_second_level_cache: false
          use_query_cache: false
        generate_statistics: false

  # Redis配置
  redis:
    host: localhost
    port: 6379
    database: 1
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 10
        max-idle: 5
        min-idle: 2
        max-wait: 3000ms

# 日志配置
logging:
  level:
    com.nexus.mcp.local: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

# MCP服务配置
mcp:
  local:
    # 本地服务存储路径
    storage-path: ./mcp-local-storage
    # 服务发现间隔（秒）
    discovery-interval: 30
    # 健康检查间隔（秒）
    health-check-interval: 60
    # 最大并发连接数
    max-connections: 100

# 管理端点
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
