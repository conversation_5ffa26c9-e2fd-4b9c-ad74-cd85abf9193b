server:
  port: 8082
  servlet:
    context-path: /mcp-local

spring:
  application:
    name: nexus-mcp-local-service

  profiles:
    active: local

  # config:
  #   import: "nacos:nexus-mcp-local-service.yml"

  # Spring Cloud Nacos配置 - 本地环境禁用
  cloud:
    nacos:
      config:
        import-check:
          enabled: false
      discovery:
        enabled: false

# 本地开发时的fallback配置
---
spring:
  config:
    activate:
      on-profile: local
  datasource:
    url: **************************************
    username: nexus
    password: nexus123
    driver-class-name: org.postgresql.Driver
  redis:
    host: localhost
    port: 6379
    database: 3
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest

  # WebSocket配置
  websocket:
    enabled: true
    path: /ws/mcp-local
    allowed-origins: "*"
    heartbeat-interval: 30000 # 30秒心跳

  # gRPC配置
  grpc:
    enabled: true
    port: 9082
    max-message-size: 4194304 # 4MB
    keep-alive-time: 30
    keep-alive-timeout: 5
    permit-keep-alive-without-calls: true

  # 本地服务类型配置
  service-types:
    # 支持的本地服务类型
    supported:
      - npx
      - uv
      - python
      - node
      - docker
      - custom

    # 默认配置
    defaults:
      npx:
        timeout: 60000
        max-memory: "512m"
      uv:
        timeout: 120000
        max-memory: "1g"
      python:
        timeout: 60000
        max-memory: "512m"
      node:
        timeout: 60000
        max-memory: "512m"
      docker:
        timeout: 300000
        max-memory: "2g"

# Feign配置
feign:
  client:
    config:
      default:
        connect-timeout: 5000
        read-timeout: 10000
        logger-level: basic
      nexus-auth-service:
        connect-timeout: 3000
        read-timeout: 5000

# 日志配置
logging:
  level:
    com.nexus.mcp.local: DEBUG
    com.nexus.common: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    io.grpc: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{50}] - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{50}] - %msg%n"

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

# 异步任务配置
async:
  core-pool-size: 5
  max-pool-size: 20
  queue-capacity: 100
  thread-name-prefix: "mcp-local-async-"
  keep-alive-seconds: 60
