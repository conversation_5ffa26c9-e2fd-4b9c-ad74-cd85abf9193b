package com.nexus.mcp.remote;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Nexus远程MCP服务启动类
 * 
 * 功能：
 * - 管理可以在服务端部署的MCP服务
 * - 提供UnifiedMCP统一接口
 * - 处理MCP工具和资源的调用
 * - 管理MCP服务的缓存和优化
 * - 提供异步任务处理能力
 */
@SpringBootApplication(scanBasePackages = {
    "com.nexus.mcp.remote",  // 当前服务包
    "com.nexus.common"       // 公共组件包
})
@EnableDiscoveryClient
@EnableFeignClients
@EnableCaching
@EnableAsync
@EnableScheduling
@EnableTransactionManagement
@EntityScan(basePackages = {
    "com.nexus.common.entity",       // 公共实体类
    "com.nexus.mcp.remote.entity"    // 远程MCP服务实体类
})
@EnableJpaRepositories(basePackages = {
    "com.nexus.mcp.remote.repository" // 远程MCP服务Repository
})
public class NexusMcpRemoteServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(NexusMcpRemoteServiceApplication.class, args);
    }
}
