package com.nexus.mcp.remote.consumer;

import com.nexus.common.constants.RocketMQConstants;
import com.nexus.common.event.MCPTaskCreatedEvent;
import com.nexus.common.service.RocketMQProducerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

/**
 * 远程MCP任务消费者
 * 处理异步的远程MCP工具调用任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
    topic = RocketMQConstants.MCP_ASYNC_TOPIC,
    selectorExpression = RocketMQConstants.MCP_ASYNC_TAG,
    consumerGroup = "remote-mcp-task-consumer-group",
    nameServer = "${rocketmq.name-server:localhost:9876}"
)
public class MCPTaskConsumer implements RocketMQListener<MCPTaskCreatedEvent> {

    private final RocketMQProducerService messageProducerService;

    /**
     * RocketMQ消息监听器实现
     */
    @Override
    public void onMessage(MCPTaskCreatedEvent event) {
        log.info("接收到远程MCP任务创建事件: eventId={}, taskId={}, toolName={}",
                event.getEventId(), event.getTaskId(), event.getToolName());

        try {
            // 验证事件数据
            if (!event.isValid()) {
                log.warn("远程MCP任务创建事件数据无效: eventId={}", event.getEventId());
                return;
            }

            // 处理任务
            processRemoteMCPTask(event);

            log.info("远程MCP任务创建事件处理成功: eventId={}", event.getEventId());

        } catch (Exception e) {
            log.error("远程MCP任务创建事件处理失败: eventId={} - {}",
                    event.getEventId(), e.getMessage(), e);
            // RocketMQ会自动重试
        }
    }

    /**
     * 处理远程MCP任务
     */
    private void processRemoteMCPTask(MCPTaskCreatedEvent event) {
        // 这里可以添加具体的远程MCP任务处理逻辑
        log.info("处理远程MCP任务: 任务[{}] 工具[{}]",
                event.getTaskId(), event.getToolName());
    }
}