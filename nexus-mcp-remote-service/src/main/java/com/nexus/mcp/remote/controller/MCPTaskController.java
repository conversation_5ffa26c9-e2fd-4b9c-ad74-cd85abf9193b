package com.nexus.mcp.remote.controller;

import com.nexus.common.service.MCPTaskStateManager;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * MCP任务状态查询控制器
 * 提供MCP任务状态查询和管理接口
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/mcp/tasks")
@RequiredArgsConstructor
@Validated
@Tag(name = "MCP任务管理", description = "MCP任务状态查询和管理接口")
public class MCPTaskController {
    
    private final MCPTaskStateManager taskStateManager;
    
    /**
     * 查询任务状态
     */
    @GetMapping("/{taskId}/status")
    @Operation(summary = "查询任务状态", description = "根据任务ID查询MCP任务的执行状态")
    public ResponseEntity<Map<String, Object>> getTaskStatus(
            @Parameter(description = "任务ID") @PathVariable String taskId,
            HttpServletRequest request) {
        
        log.debug("查询任务状态请求: taskId={}", taskId);
        
        try {
            Map<String, Object> taskState = taskStateManager.getTaskState(taskId);
            
            if (taskState == null) {
                return ResponseEntity.ok(buildErrorResponse("任务不存在", "TASK_NOT_FOUND"));
            }
            
            return ResponseEntity.ok(buildSuccessResponse("查询成功", taskState));
            
        } catch (Exception e) {
            log.error("查询任务状态失败: taskId={} - {}", taskId, e.getMessage(), e);
            return ResponseEntity.ok(buildErrorResponse("查询失败: " + e.getMessage(), "QUERY_ERROR"));
        }
    }
    
    /**
     * 获取任务结果
     */
    @GetMapping("/{taskId}/result")
    @Operation(summary = "获取任务结果", description = "根据任务ID获取MCP任务的执行结果")
    public ResponseEntity<Map<String, Object>> getTaskResult(
            @Parameter(description = "任务ID") @PathVariable String taskId,
            HttpServletRequest request) {
        
        log.debug("获取任务结果请求: taskId={}", taskId);
        
        try {
            // 先检查任务是否存在
            if (!taskStateManager.taskExists(taskId)) {
                return ResponseEntity.ok(buildErrorResponse("任务不存在", "TASK_NOT_FOUND"));
            }
            
            // 获取任务状态
            Map<String, Object> taskState = taskStateManager.getTaskState(taskId);
            String state = (String) taskState.get("state");
            
            // 检查任务是否已完成
            if (!"COMPLETED".equals(state) && !"FAILED".equals(state)) {
                return ResponseEntity.ok(buildErrorResponse("任务尚未完成", "TASK_NOT_COMPLETED"));
            }
            
            // 获取任务结果
            Map<String, Object> result = taskStateManager.getTaskResult(taskId);
            
            if (result == null) {
                return ResponseEntity.ok(buildErrorResponse("任务结果不存在", "RESULT_NOT_FOUND"));
            }
            
            return ResponseEntity.ok(buildSuccessResponse("获取成功", result));
            
        } catch (Exception e) {
            log.error("获取任务结果失败: taskId={} - {}", taskId, e.getMessage(), e);
            return ResponseEntity.ok(buildErrorResponse("获取失败: " + e.getMessage(), "QUERY_ERROR"));
        }
    }
    
    /**
     * 获取任务完整信息
     */
    @GetMapping("/{taskId}")
    @Operation(summary = "获取任务完整信息", description = "根据任务ID获取MCP任务的完整信息，包括状态、结果和进度历史")
    public ResponseEntity<Map<String, Object>> getTaskFullInfo(
            @Parameter(description = "任务ID") @PathVariable String taskId,
            HttpServletRequest request) {
        
        log.debug("获取任务完整信息请求: taskId={}", taskId);
        
        try {
            Map<String, Object> fullInfo = taskStateManager.getTaskFullInfo(taskId);
            
            if (fullInfo == null || !Boolean.TRUE.equals(fullInfo.get("hasFullInfo"))) {
                return ResponseEntity.ok(buildErrorResponse("任务不存在", "TASK_NOT_FOUND"));
            }
            
            return ResponseEntity.ok(buildSuccessResponse("获取成功", fullInfo));
            
        } catch (Exception e) {
            log.error("获取任务完整信息失败: taskId={} - {}", taskId, e.getMessage(), e);
            return ResponseEntity.ok(buildErrorResponse("获取失败: " + e.getMessage(), "QUERY_ERROR"));
        }
    }
    
    /**
     * 获取任务进度历史
     */
    @GetMapping("/{taskId}/progress")
    @Operation(summary = "获取任务进度历史", description = "根据任务ID获取MCP任务的进度变化历史")
    public ResponseEntity<Map<String, Object>> getTaskProgress(
            @Parameter(description = "任务ID") @PathVariable String taskId,
            HttpServletRequest request) {
        
        log.debug("获取任务进度历史请求: taskId={}", taskId);
        
        try {
            if (!taskStateManager.taskExists(taskId)) {
                return ResponseEntity.ok(buildErrorResponse("任务不存在", "TASK_NOT_FOUND"));
            }
            
            java.util.List<Object> progressHistory = taskStateManager.getTaskProgressHistory(taskId);
            
            Map<String, Object> progressInfo = new HashMap<>();
            progressInfo.put("taskId", taskId);
            progressInfo.put("progressHistory", progressHistory);
            progressInfo.put("historyCount", progressHistory.size());
            
            return ResponseEntity.ok(buildSuccessResponse("获取成功", progressInfo));
            
        } catch (Exception e) {
            log.error("获取任务进度历史失败: taskId={} - {}", taskId, e.getMessage(), e);
            return ResponseEntity.ok(buildErrorResponse("获取失败: " + e.getMessage(), "QUERY_ERROR"));
        }
    }
    
    /**
     * 获取用户的所有任务
     */
    @GetMapping("/user/{userId}")
    @Operation(summary = "获取用户任务列表", description = "根据用户ID获取该用户的所有MCP任务")
    public ResponseEntity<Map<String, Object>> getUserTasks(
            @Parameter(description = "用户ID") @PathVariable String userId,
            HttpServletRequest request) {
        
        log.debug("获取用户任务列表请求: userId={}", userId);
        
        try {
            Set<Object> taskIds = taskStateManager.getUserTasks(userId);
            
            // 获取每个任务的基本状态信息
            java.util.List<Map<String, Object>> tasks = new java.util.ArrayList<>();
            
            for (Object taskIdObj : taskIds) {
                String taskId = taskIdObj.toString();
                Map<String, Object> taskState = taskStateManager.getTaskState(taskId);
                
                if (taskState != null) {
                    // 只返回基本信息，不包含详细结果
                    Map<String, Object> taskSummary = new HashMap<>();
                    taskSummary.put("taskId", taskId);
                    taskSummary.put("toolName", taskState.get("toolName"));
                    taskSummary.put("serviceName", taskState.get("serviceName"));
                    taskSummary.put("state", taskState.get("state"));
                    taskSummary.put("stateDescription", taskState.get("stateDescription"));
                    taskSummary.put("progress", taskState.get("progress"));
                    taskSummary.put("createdAt", taskState.get("createdAt"));
                    taskSummary.put("updatedAt", taskState.get("updatedAt"));
                    
                    tasks.add(taskSummary);
                }
            }
            
            Map<String, Object> userTasksInfo = new HashMap<>();
            userTasksInfo.put("userId", userId);
            userTasksInfo.put("tasks", tasks);
            userTasksInfo.put("totalCount", tasks.size());
            
            return ResponseEntity.ok(buildSuccessResponse("获取成功", userTasksInfo));
            
        } catch (Exception e) {
            log.error("获取用户任务列表失败: userId={} - {}", userId, e.getMessage(), e);
            return ResponseEntity.ok(buildErrorResponse("获取失败: " + e.getMessage(), "QUERY_ERROR"));
        }
    }
    
    /**
     * 取消任务
     */
    @PostMapping("/{taskId}/cancel")
    @Operation(summary = "取消任务", description = "取消指定的MCP任务")
    public ResponseEntity<Map<String, Object>> cancelTask(
            @Parameter(description = "任务ID") @PathVariable String taskId,
            HttpServletRequest request) {
        
        log.info("取消任务请求: taskId={}", taskId);
        
        try {
            if (!taskStateManager.taskExists(taskId)) {
                return ResponseEntity.ok(buildErrorResponse("任务不存在", "TASK_NOT_FOUND"));
            }
            
            // 获取当前任务状态
            Map<String, Object> taskState = taskStateManager.getTaskState(taskId);
            String currentState = (String) taskState.get("state");
            
            // 检查任务是否可以取消
            if ("COMPLETED".equals(currentState) || "FAILED".equals(currentState) || 
                "CANCELLED".equals(currentState)) {
                return ResponseEntity.ok(buildErrorResponse("任务已完成或已取消，无法取消", "TASK_NOT_CANCELLABLE"));
            }
            
            // 更新任务状态为已取消
            taskStateManager.updateTaskState(taskId, "CANCELLED", "用户取消任务", null);
            
            log.info("任务取消成功: taskId={}", taskId);
            return ResponseEntity.ok(buildSuccessResponse("任务取消成功", Map.of("taskId", taskId, "state", "CANCELLED")));
            
        } catch (Exception e) {
            log.error("取消任务失败: taskId={} - {}", taskId, e.getMessage(), e);
            return ResponseEntity.ok(buildErrorResponse("取消失败: " + e.getMessage(), "CANCEL_ERROR"));
        }
    }
    
    /**
     * 删除任务
     */
    @DeleteMapping("/{taskId}")
    @Operation(summary = "删除任务", description = "删除指定的MCP任务及其相关数据")
    public ResponseEntity<Map<String, Object>> deleteTask(
            @Parameter(description = "任务ID") @PathVariable String taskId,
            @Parameter(description = "用户ID") @RequestParam(required = false) String userId,
            HttpServletRequest request) {
        
        log.info("删除任务请求: taskId={}, userId={}", taskId, userId);
        
        try {
            if (!taskStateManager.taskExists(taskId)) {
                return ResponseEntity.ok(buildErrorResponse("任务不存在", "TASK_NOT_FOUND"));
            }
            
            // 删除任务状态和相关数据
            taskStateManager.deleteTaskState(taskId, userId);
            
            log.info("任务删除成功: taskId={}", taskId);
            return ResponseEntity.ok(buildSuccessResponse("任务删除成功", Map.of("taskId", taskId, "deleted", true)));
            
        } catch (Exception e) {
            log.error("删除任务失败: taskId={} - {}", taskId, e.getMessage(), e);
            return ResponseEntity.ok(buildErrorResponse("删除失败: " + e.getMessage(), "DELETE_ERROR"));
        }
    }
    
    /**
     * 构建成功响应
     */
    private Map<String, Object> buildSuccessResponse(String message, Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", message);
        response.put("data", data);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }
    
    /**
     * 构建错误响应
     */
    private Map<String, Object> buildErrorResponse(String message, String errorCode) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", message);
        response.put("errorCode", errorCode);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }
}
