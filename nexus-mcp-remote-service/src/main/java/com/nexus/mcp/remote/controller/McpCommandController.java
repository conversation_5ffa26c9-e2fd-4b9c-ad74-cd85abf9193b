package com.nexus.mcp.remote.controller;

import com.nexus.common.mcp.process.McpServiceConfig;
import com.nexus.mcp.remote.service.McpCommandService;
import com.nexus.mcp.remote.service.McpCommandService.McpServiceStartResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MCP命令控制器
 * 
 * 提供通过命令启动MCP服务的REST API接口
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/mcp/command")
@RequiredArgsConstructor
public class McpCommandController {
    
    private final McpCommandService mcpCommandService;

    /**
     * 创建响应Map的辅助方法（Java 8兼容）
     */
    private Map<String, Object> createResponseMap(Object... keyValuePairs) {
        Map<String, Object> map = new HashMap<>();
        for (int i = 0; i < keyValuePairs.length; i += 2) {
            map.put((String) keyValuePairs[i], keyValuePairs[i + 1]);
        }
        return map;
    }
    
    /**
     * 通过命令启动MCP服务
     * 
     * POST /api/mcp/command/start
     * {
     *   "command": "npx -y @modelcontextprotocol/server-filesystem /path/to/files",
     *   "serviceName": "my-filesystem" // 可选
     * }
     */
    @PostMapping("/start")
    public ResponseEntity<Map<String, Object>> startServiceByCommand(@RequestBody StartServiceRequest request) {
        log.info("接收到启动MCP服务命令: {}", request.getCommand());
        
        try {
            McpServiceStartResult result = mcpCommandService.startServiceByCommand(
                    request.getCommand(), 
                    request.getServiceName()
            );
            
            Map<String, Object> response = createResponseMap(
                    "success", result.isSuccess(),
                    "serviceName", result.getServiceName(),
                    "message", result.getMessage(),
                    "command", request.getCommand()
            );
            
            if (result.getConfig() != null) {
                Map<String, Object> configMap = createResponseMap(
                        "serviceType", result.getConfig().getServiceType(),
                        "servicePath", result.getConfig().getServicePath(),
                        "args", result.getConfig().getArgs()
                );
                response = createResponseMap(
                        "success", result.isSuccess(),
                        "serviceName", result.getServiceName(),
                        "message", result.getMessage(),
                        "command", request.getCommand(),
                        "config", configMap
                );
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("启动MCP服务失败: {} - {}", request.getCommand(), e.getMessage());
            
            Map<String, Object> errorResponse = createResponseMap(
                    "success", false,
                    "message", e.getMessage(),
                    "command", request.getCommand()
            );
            
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }
    
    /**
     * 批量启动MCP服务
     * 
     * POST /api/mcp/command/start-batch
     * {
     *   "commands": [
     *     "npx -y @modelcontextprotocol/server-filesystem /path/to/files",
     *     "uvx mcp-server-git --repository /path/to/repo"
     *   ]
     * }
     */
    @PostMapping("/start-batch")
    public ResponseEntity<Map<String, Object>> startServicesByCommands(@RequestBody BatchStartRequest request) {
        log.info("接收到批量启动MCP服务命令: {}", request.getCommands());
        
        try {
            Map<String, McpServiceStartResult> results = mcpCommandService.startServicesByCommands(request.getCommands());
            
            Map<String, Object> response = createResponseMap(
                    "success", true,
                    "results", results,
                    "totalCommands", request.getCommands().size(),
                    "successCount", results.values().stream().mapToLong(r -> r.isSuccess() ? 1 : 0).sum()
            );
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("批量启动MCP服务失败: {}", e.getMessage());
            
            Map<String, Object> errorResponse = createResponseMap(
                    "success", false,
                    "message", e.getMessage()
            );
            
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }
    
    /**
     * 通过命令停止MCP服务
     * 
     * POST /api/mcp/command/stop
     * {
     *   "command": "npx -y @modelcontextprotocol/server-filesystem /path/to/files"
     * }
     */
    @PostMapping("/stop")
    public ResponseEntity<Map<String, Object>> stopServiceByCommand(@RequestBody StopServiceRequest request) {
        log.info("接收到停止MCP服务命令: {}", request.getCommand());
        
        try {
            boolean success = mcpCommandService.stopServiceByCommand(request.getCommand());
            
            Map<String, Object> response = createResponseMap(
                    "success", success,
                    "message", success ? "服务停止成功" : "服务停止失败或未找到对应服务",
                    "command", request.getCommand()
            );
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("停止MCP服务失败: {} - {}", request.getCommand(), e.getMessage());
            
            Map<String, Object> errorResponse = createResponseMap(
                    "success", false,
                    "message", e.getMessage(),
                    "command", request.getCommand()
            );
            
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }
    
    /**
     * 验证命令是否可以解析
     * 
     * POST /api/mcp/command/validate
     * {
     *   "command": "npx -y @modelcontextprotocol/server-filesystem /path/to/files"
     * }
     */
    @PostMapping("/validate")
    public ResponseEntity<Map<String, Object>> validateCommand(@RequestBody ValidateCommandRequest request) {
        try {
            boolean valid = mcpCommandService.validateCommand(request.getCommand());
            
            Map<String, Object> response = createResponseMap(
                    "valid", valid,
                    "command", request.getCommand()
            );
            
            if (valid) {
                // 如果命令有效，返回解析预览
                McpServiceConfig config = mcpCommandService.previewCommand(request.getCommand());
                Map<String, Object> previewMap = createResponseMap(
                        "serviceName", config.getServiceName(),
                        "serviceType", config.getServiceType(),
                        "servicePath", config.getServicePath(),
                        "args", config.getArgs()
                );
                response = createResponseMap(
                        "valid", true,
                        "command", request.getCommand(),
                        "preview", previewMap
                );
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, Object> response = createResponseMap(
                    "valid", false,
                    "command", request.getCommand(),
                    "error", e.getMessage()
            );
            
            return ResponseEntity.ok(response);
        }
    }
    
    /**
     * 获取命令示例
     * 
     * GET /api/mcp/command/examples
     */
    @GetMapping("/examples")
    public ResponseEntity<Map<String, Object>> getCommandExamples() {
        List<String> examples = mcpCommandService.getCommonCommandExamples();
        
        Map<String, Object> response = createResponseMap(
                "examples", examples,
                "count", examples.size()
        );
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取所有通过命令启动的服务
     * 
     * GET /api/mcp/command/mappings
     */
    @GetMapping("/mappings")
    public ResponseEntity<Map<String, Object>> getCommandServiceMappings() {
        Map<String, String> mappings = mcpCommandService.getCommandServiceMappings();
        
        Map<String, Object> response = createResponseMap(
                "mappings", mappings,
                "count", mappings.size()
        );
        
        return ResponseEntity.ok(response);
    }

    /**
     * 获取运行状态
     *
     * GET /api/mcp/command/status
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getRunningStatus() {
        Map<String, Object> runningServices = mcpCommandService.getRunningServices();

        Map<String, Object> response = createResponseMap(
                "running", runningServices,
                "count", runningServices.size()
        );

        return ResponseEntity.ok(response);
    }

    // 请求数据类
    public static class StartServiceRequest {
        private String command;
        private String serviceName; // 可选
        
        // Getters and setters
        public String getCommand() { return command; }
        public void setCommand(String command) { this.command = command; }
        public String getServiceName() { return serviceName; }
        public void setServiceName(String serviceName) { this.serviceName = serviceName; }
    }
    
    public static class BatchStartRequest {
        private List<String> commands;
        
        // Getters and setters
        public List<String> getCommands() { return commands; }
        public void setCommands(List<String> commands) { this.commands = commands; }
    }
    
    public static class StopServiceRequest {
        private String command;
        
        // Getters and setters
        public String getCommand() { return command; }
        public void setCommand(String command) { this.command = command; }
    }
    
    public static class ValidateCommandRequest {
        private String command;
        
        // Getters and setters
        public String getCommand() { return command; }
        public void setCommand(String command) { this.command = command; }
    }
}
