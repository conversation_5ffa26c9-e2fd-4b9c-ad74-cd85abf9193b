package com.nexus.mcp.remote.controller;

import com.nexus.common.model.MCPServiceMetadata;
import com.nexus.mcp.remote.entity.RemoteMcpService;
import com.nexus.mcp.remote.service.RemoteMcpServiceManager;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 远程MCP服务控制器
 * 提供远程MCP服务管理的REST API
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/remote-mcp")
@RequiredArgsConstructor
@Validated
@Tag(name = "远程MCP服务管理", description = "远程MCP服务注册、启动、停止等管理接口")
public class RemoteMcpController {

    private final RemoteMcpServiceManager serviceManager;

    /**
     * 获取所有远程服务
     */
    @GetMapping("/services")
    @Operation(summary = "获取所有远程服务", description = "获取所有已注册的远程MCP服务列表")
    public ResponseEntity<Map<String, Object>> getAllServices() {
        log.debug("获取所有远程服务请求");

        try {
            List<RemoteMcpService> services = serviceManager.getAllServices();
            return ResponseEntity.ok(buildSuccessResponse("获取服务列表成功", services));
        } catch (Exception e) {
            log.error("获取服务列表失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 手动同步服务工具和资源信息
     */
    @PostMapping("/services/{serviceId}/sync-tools")
    @Operation(summary = "同步服务工具信息", description = "手动同步指定服务的工具和资源信息")
    public ResponseEntity<Map<String, Object>> syncServiceTools(@PathVariable Long serviceId) {
        log.info("手动同步服务工具信息请求: serviceId={}", serviceId);

        try {
            serviceManager.syncServiceToolsAndResources(serviceId);
            return ResponseEntity.ok(buildSuccessResponse("服务工具信息同步成功", null));
        } catch (Exception e) {
            log.error("同步服务工具信息失败: serviceId={}, error={}", serviceId, e.getMessage());
            return ResponseEntity.ok(buildErrorResponse("同步服务工具信息失败", e.getMessage()));
        }
    }

    /**
     * 根据ID获取服务
     */
    @GetMapping("/services/{serviceId}")
    @Operation(summary = "获取服务详情", description = "根据服务ID获取远程MCP服务详情")
    public ResponseEntity<Map<String, Object>> getService(
            @Parameter(description = "服务ID") @PathVariable Long serviceId) {
        
        log.debug("获取服务详情请求: {}", serviceId);
        
        try {
            RemoteMcpService service = serviceManager.getServiceById(serviceId);
            return ResponseEntity.ok(buildSuccessResponse("获取服务详情成功", service));
        } catch (Exception e) {
            log.error("获取服务详情失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 注册远程MCP服务
     */
    @PostMapping("/services")
    @Operation(summary = "注册远程MCP服务", description = "注册一个新的远程MCP服务")
    public ResponseEntity<Map<String, Object>> registerService(
            @Valid @RequestBody MCPServiceMetadata metadata) {
        
        log.info("注册远程MCP服务请求: {}", metadata.getServiceName());
        
        try {
            RemoteMcpService service = serviceManager.registerService(metadata);
            return ResponseEntity.ok(buildSuccessResponse("服务注册成功", service));
        } catch (Exception e) {
            log.error("服务注册失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 启动远程服务
     */
    @PostMapping("/services/{serviceId}/start")
    @Operation(summary = "启动远程服务", description = "启动指定的远程MCP服务")
    public ResponseEntity<Map<String, Object>> startService(
            @Parameter(description = "服务ID") @PathVariable Long serviceId) {
        
        log.info("启动远程服务请求: {}", serviceId);
        
        try {
            serviceManager.updateServiceStatus(serviceId, RemoteMcpService.ServiceStatus.ACTIVE);
            return ResponseEntity.ok(buildSuccessResponse("服务启动成功", null));
        } catch (Exception e) {
            log.error("服务启动失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 停止远程服务
     */
    @PostMapping("/services/{serviceId}/stop")
    @Operation(summary = "停止远程服务", description = "停止指定的远程MCP服务")
    public ResponseEntity<Map<String, Object>> stopService(
            @Parameter(description = "服务ID") @PathVariable Long serviceId) {
        
        log.info("停止远程服务请求: {}", serviceId);
        
        try {
            serviceManager.updateServiceStatus(serviceId, RemoteMcpService.ServiceStatus.INACTIVE);
            return ResponseEntity.ok(buildSuccessResponse("服务停止成功", null));
        } catch (Exception e) {
            log.error("服务停止失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 重启远程服务
     */
    @PostMapping("/services/{serviceId}/restart")
    @Operation(summary = "重启远程服务", description = "重启指定的远程MCP服务")
    public ResponseEntity<Map<String, Object>> restartService(
            @Parameter(description = "服务ID") @PathVariable Long serviceId) {

        log.info("重启远程服务请求: {}", serviceId);

        try {
            // 先停止再启动
            serviceManager.updateServiceStatus(serviceId, RemoteMcpService.ServiceStatus.INACTIVE);
            Thread.sleep(1000); // 等待1秒
            serviceManager.updateServiceStatus(serviceId, RemoteMcpService.ServiceStatus.ACTIVE);
            return ResponseEntity.ok(buildSuccessResponse("服务重启成功", null));
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("服务重启被中断: {}", e.getMessage());
            throw new RuntimeException("服务重启被中断", e);
        } catch (Exception e) {
            log.error("服务重启失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 删除远程服务
     */
    @PostMapping("/services/{serviceId}/delete")
    @Operation(summary = "删除远程服务", description = "删除指定的远程MCP服务")
    public ResponseEntity<Map<String, Object>> deleteService(
            @Parameter(description = "服务ID") @PathVariable Long serviceId) {
        
        log.info("删除远程服务请求: {}", serviceId);
        
        try {
            serviceManager.deleteService(serviceId);
            return ResponseEntity.ok(buildSuccessResponse("服务删除成功", null));
        } catch (Exception e) {
            log.error("服务删除失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 健康检查
     */
    @PostMapping("/services/{serviceId}/health")
    @Operation(summary = "健康检查", description = "检查指定远程MCP服务的健康状态")
    public ResponseEntity<Map<String, Object>> healthCheck(
            @Parameter(description = "服务ID") @PathVariable Long serviceId) {
        
        log.info("健康检查请求: {}", serviceId);
        
        try {
            RemoteMcpService service = serviceManager.getServiceById(serviceId);
            Map<String, Object> healthInfo = new HashMap<>();
            healthInfo.put("serviceId", serviceId);
            healthInfo.put("serviceName", service.getServiceName());
            healthInfo.put("status", service.getStatus());
            healthInfo.put("healthy", service.getStatus() == RemoteMcpService.ServiceStatus.ACTIVE);
            healthInfo.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(buildSuccessResponse("健康检查完成", healthInfo));
        } catch (Exception e) {
            log.error("健康检查失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 构建成功响应
     */
    private Map<String, Object> buildSuccessResponse(String message, Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", message);
        response.put("timestamp", System.currentTimeMillis());
        if (data != null) {
            response.put("data", data);
        }
        return response;
    }

    /**
     * 构建错误响应
     */
    private Map<String, Object> buildErrorResponse(String message, String error) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", message);
        response.put("error", error);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }
}
