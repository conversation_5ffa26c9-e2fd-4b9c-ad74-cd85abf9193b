package com.nexus.mcp.remote.controller;

import com.nexus.mcp.remote.service.UnifiedMcpService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 统一MCP控制器
 * 提供统一的MCP接口，兼容标准MCP协议
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/unified-mcp")
@RequiredArgsConstructor
@Validated
@Tag(name = "统一MCP接口", description = "提供统一的MCP服务接口，整合所有MCP服务提供商")
public class UnifiedMcpController {

    private final UnifiedMcpService unifiedMcpService;

    /**
     * 获取服务器信息
     */
    @GetMapping("/server/info")
    @Operation(summary = "获取服务器信息", description = "获取UnifiedMCP服务器的基本信息和统计数据")
    public ResponseEntity<Map<String, Object>> getServerInfo() {
        log.debug("获取服务器信息请求");
        
        try {
            Map<String, Object> serverInfo = unifiedMcpService.getServerInfo();
            return ResponseEntity.ok(buildMcpResponse("server/info", serverInfo));
        } catch (Exception e) {
            log.error("获取服务器信息失败: {}", e.getMessage());
            return ResponseEntity.ok(buildMcpErrorResponse("server/info", e.getMessage()));
        }
    }

    /**
     * 列出所有可用工具
     */
    @GetMapping("/tools/list")
    @Operation(summary = "列出所有工具", description = "获取所有可用的MCP工具列表")
    public ResponseEntity<Map<String, Object>> listTools() {
        log.debug("列出工具请求");
        
        try {
            List<Map<String, Object>> tools = unifiedMcpService.listTools();
            Map<String, Object> result = createResponseMap("tools", tools);
            return ResponseEntity.ok(buildMcpResponse("tools/list", result));
        } catch (Exception e) {
            log.error("列出工具失败: {}", e.getMessage());
            return ResponseEntity.ok(buildMcpErrorResponse("tools/list", e.getMessage()));
        }
    }

    /**
     * 调用MCP工具
     */
    @PostMapping("/tools/call")
    @Operation(summary = "调用MCP工具", description = "调用指定的MCP工具")
    public ResponseEntity<Map<String, Object>> callTool(
            @RequestBody Map<String, Object> request,
            HttpServletRequest httpRequest) {
        
        // 提取参数
        Map<String, Object> params = (Map<String, Object>) request.get("params");
        String toolName = (String) params.get("name");
        Map<String, Object> arguments = (Map<String, Object>) params.get("arguments");
        String userId = extractUserId(httpRequest);
        
        log.info("调用工具请求: {} (用户: {})", toolName, userId);
        
        try {
            Map<String, Object> result = unifiedMcpService.callTool(toolName, arguments, userId);
            return ResponseEntity.ok(buildMcpResponse("tools/call", result, request.get("id")));
        } catch (Exception e) {
            log.error("调用工具失败: {} - {}", toolName, e.getMessage());
            return ResponseEntity.ok(buildMcpErrorResponse("tools/call", e.getMessage(), request.get("id")));
        }
    }

    /**
     * 异步调用MCP工具
     */
    @PostMapping("/tools/call-async")
    @Operation(summary = "异步调用MCP工具", description = "异步调用指定的MCP工具")
    public ResponseEntity<Map<String, Object>> callToolAsync(
            @RequestBody Map<String, Object> request,
            HttpServletRequest httpRequest) {
        
        // 提取参数
        Map<String, Object> params = (Map<String, Object>) request.get("params");
        String toolName = (String) params.get("name");
        Map<String, Object> arguments = (Map<String, Object>) params.get("arguments");
        String userId = extractUserId(httpRequest);
        
        log.info("异步调用工具请求: {} (用户: {})", toolName, userId);
        
        try {
            CompletableFuture<Map<String, Object>> future = unifiedMcpService.callToolAsync(toolName, arguments, userId);
            
            // 返回任务ID，客户端可以通过任务ID查询结果
            String taskId = java.util.UUID.randomUUID().toString();
            Map<String, Object> result = createResponseMap(
                    "taskId", taskId,
                    "status", "PENDING",
                    "message", "任务已提交，正在处理中"
            );
            
            return ResponseEntity.ok(buildMcpResponse("tools/call-async", result, request.get("id")));
        } catch (Exception e) {
            log.error("异步调用工具失败: {} - {}", toolName, e.getMessage());
            return ResponseEntity.ok(buildMcpErrorResponse("tools/call-async", e.getMessage(), request.get("id")));
        }
    }

    /**
     * 列出所有可用资源
     */
    @GetMapping("/resources/list")
    @Operation(summary = "列出所有资源", description = "获取所有可用的MCP资源列表")
    public ResponseEntity<Map<String, Object>> listResources() {
        log.debug("列出资源请求");
        
        try {
            List<Map<String, Object>> resources = unifiedMcpService.listResources();
            Map<String, Object> result = createResponseMap("resources", resources);
            return ResponseEntity.ok(buildMcpResponse("resources/list", result));
        } catch (Exception e) {
            log.error("列出资源失败: {}", e.getMessage());
            return ResponseEntity.ok(buildMcpErrorResponse("resources/list", e.getMessage()));
        }
    }

    /**
     * 读取MCP资源
     */
    @PostMapping("/resources/read")
    @Operation(summary = "读取MCP资源", description = "读取指定的MCP资源")
    public ResponseEntity<Map<String, Object>> readResource(
            @RequestBody Map<String, Object> request,
            HttpServletRequest httpRequest) {
        
        // 提取参数
        Map<String, Object> params = (Map<String, Object>) request.get("params");
        String resourceUri = (String) params.get("uri");
        String userId = extractUserId(httpRequest);
        
        log.info("读取资源请求: {} (用户: {})", resourceUri, userId);
        
        try {
            Map<String, Object> result = unifiedMcpService.readResource(resourceUri, userId);
            return ResponseEntity.ok(buildMcpResponse("resources/read", result, request.get("id")));
        } catch (Exception e) {
            log.error("读取资源失败: {} - {}", resourceUri, e.getMessage());
            return ResponseEntity.ok(buildMcpErrorResponse("resources/read", e.getMessage(), request.get("id")));
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "检查UnifiedMCP服务的健康状态")
    public ResponseEntity<Map<String, Object>> health() {
        try {
            Map<String, Object> health = unifiedMcpService.getHealthStatus();
            return ResponseEntity.ok(health);
        } catch (Exception e) {
            log.error("健康检查失败: {}", e.getMessage());
            Map<String, Object> health = new HashMap<>();
            health.put("status", "DOWN");
            health.put("error", e.getMessage());
            health.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.ok(health);
        }
    }

    /**
     * 刷新缓存
     */
    @PostMapping("/cache/refresh")
    @Operation(summary = "刷新缓存", description = "刷新UnifiedMCP服务的缓存")
    public ResponseEntity<Map<String, Object>> refreshCache() {
        log.info("刷新缓存请求");
        
        try {
            unifiedMcpService.refreshCache();
            return ResponseEntity.ok(buildSuccessResponse("缓存刷新成功", null));
        } catch (Exception e) {
            log.error("刷新缓存失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 调试接口 - 获取连接状态
     */
    @GetMapping("/debug/connections")
    @Operation(summary = "调试连接状态", description = "获取所有MCP服务的连接状态信息")
    public ResponseEntity<Map<String, Object>> debugConnections() {
        log.debug("调试连接状态请求");

        try {
            Map<String, Object> debugInfo = unifiedMcpService.getDebugConnectionInfo();
            return ResponseEntity.ok(buildMcpResponse("debug/connections", debugInfo));
        } catch (Exception e) {
            log.error("获取调试信息失败: {}", e.getMessage());
            return ResponseEntity.ok(buildMcpErrorResponse("debug/connections", e.getMessage()));
        }
    }

    /**
     * 提取用户ID
     */
    private String extractUserId(HttpServletRequest request) {
        // 从请求头中提取用户ID
        String userId = request.getHeader("X-User-ID");
        if (userId == null) {
            userId = request.getHeader("Authorization");
            // 这里可以解析JWT token获取用户ID
        }
        return userId != null ? userId : "anonymous";
    }

    /**
     * 构建MCP标准响应
     */
    private Map<String, Object> buildMcpResponse(String method, Object result) {
        return buildMcpResponse(method, result, null);
    }

    /**
     * 构建MCP标准响应（带ID）
     */
    private Map<String, Object> buildMcpResponse(String method, Object result, Object id) {
        Map<String, Object> response = new HashMap<>();
        response.put("jsonrpc", "2.0");
        response.put("result", result);
        if (id != null) {
            response.put("id", id);
        }
        return response;
    }

    /**
     * 构建MCP错误响应
     */
    private Map<String, Object> buildMcpErrorResponse(String method, String errorMessage) {
        return buildMcpErrorResponse(method, errorMessage, null);
    }

    /**
     * 构建MCP错误响应（带ID）
     */
    private Map<String, Object> buildMcpErrorResponse(String method, String errorMessage, Object id) {
        Map<String, Object> error = new HashMap<>();
        error.put("code", -32603); // Internal error
        error.put("message", errorMessage);
        
        Map<String, Object> response = new HashMap<>();
        response.put("jsonrpc", "2.0");
        response.put("error", error);
        if (id != null) {
            response.put("id", id);
        }
        return response;
    }

    /**
     * 构建成功响应
     */
    private Map<String, Object> buildSuccessResponse(String message, Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", message);
        response.put("timestamp", System.currentTimeMillis());
        if (data != null) {
            response.put("data", data);
        }
        return response;
    }

    /**
     * 创建响应Map（Java 8兼容版本）
     */
    private Map<String, Object> createResponseMap(Object... keyValues) {
        Map<String, Object> map = new HashMap<>();
        for (int i = 0; i < keyValues.length; i += 2) {
            if (i + 1 < keyValues.length) {
                map.put((String) keyValues[i], keyValues[i + 1]);
            }
        }
        return map;
    }
}
