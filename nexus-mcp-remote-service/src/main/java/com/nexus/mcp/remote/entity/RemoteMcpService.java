package com.nexus.mcp.remote.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 远程MCP服务实体类
 * 存储可以在服务端部署并集成的MCP服务信息
 */
@Entity
@Table(name = "remote_mcp_services", indexes = {
        @Index(name = "idx_remote_mcp_service_name", columnList = "serviceName"),
        @Index(name = "idx_remote_mcp_service_type", columnList = "serviceType"),
        @Index(name = "idx_remote_mcp_service_status", columnList = "status"),
        @Index(name = "idx_remote_mcp_endpoint", columnList = "endpoint")
})
@TypeDef(name = "jsonb", typeClass = JsonBinaryType.class)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RemoteMcpService {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 服务名称，唯一标识
     */
    @Column(unique = true, nullable = false, length = 100)
    @NotBlank(message = "服务名称不能为空")
    @Size(max = 100, message = "服务名称长度不能超过100个字符")
    private String serviceName;

    /**
     * 服务显示名称
     */
    @Column(nullable = false, length = 200)
    @NotBlank(message = "服务显示名称不能为空")
    @Size(max = 200, message = "服务显示名称长度不能超过200个字符")
    private String displayName;

    /**
     * 服务描述
     */
    @Column(length = 1000)
    @Size(max = 1000, message = "服务描述长度不能超过1000个字符")
    private String description;

    /**
     * 服务类型
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private RemoteServiceType serviceType;

    /**
     * 服务状态
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Builder.Default
    private ServiceStatus status = ServiceStatus.INACTIVE;

    /**
     * 服务版本
     */
    @Column(length = 50)
    private String version;

    /**
     * 服务端点URL
     */
    @Column(nullable = false, length = 500)
    @NotBlank(message = "服务端点不能为空")
    private String endpoint;

    /**
     * 服务协议类型（HTTP, gRPC, WebSocket等）
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Builder.Default
    private ProtocolType protocolType = ProtocolType.HTTP;

    /**
     * 认证配置（JSON格式）
     */
    @Type(type = "jsonb")
    @Column(columnDefinition = "jsonb")
    @Builder.Default
    private Map<String, Object> authConfig = new HashMap<>();

    /**
     * 服务配置参数（JSON格式）
     */
    @Type(type = "jsonb")
    @Column(columnDefinition = "jsonb")
    @Builder.Default
    private Map<String, Object> configParams = new HashMap<>();

    /**
     * 服务元数据（JSON格式）
     */
    @Type(type = "jsonb")
    @Column(columnDefinition = "jsonb")
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();

    /**
     * 可用工具列表（JSON格式）
     */
    @Type(type = "jsonb")
    @Column(columnDefinition = "jsonb")
    @Builder.Default
    private List<Map<String, Object>> tools = new java.util.ArrayList<>();

    /**
     * 可用资源列表（JSON格式）
     */
    @Type(type = "jsonb")
    @Column(columnDefinition = "jsonb")
    @Builder.Default
    private List<Map<String, Object>> resources = new java.util.ArrayList<>();

    /**
     * 健康检查配置（JSON格式）
     */
    @Type(type = "jsonb")
    @Column(columnDefinition = "jsonb")
    @Builder.Default
    private Map<String, Object> healthCheck = new HashMap<>();

    /**
     * 最后健康检查时间
     */
    private LocalDateTime lastHealthCheckAt;

    /**
     * 健康检查状态
     */
    @Enumerated(EnumType.STRING)
    @Builder.Default
    private HealthStatus healthStatus = HealthStatus.UNKNOWN;

    /**
     * 健康检查错误信息
     */
    @Column(length = 1000)
    private String healthError;

    /**
     * 服务优先级（用于负载均衡）
     */
    @Builder.Default
    private Integer priority = 0;

    /**
     * 服务权重（用于负载均衡）
     */
    @Builder.Default
    private Integer weight = 100;

    /**
     * 是否启用缓存
     */
    @Builder.Default
    private Boolean cacheEnabled = true;

    /**
     * 缓存TTL（秒）
     */
    @Builder.Default
    private Integer cacheTtl = 300;

    /**
     * 最大并发请求数
     */
    @Builder.Default
    private Integer maxConcurrentRequests = 10;

    /**
     * 请求超时时间（毫秒）
     */
    @Builder.Default
    private Integer requestTimeout = 30000;

    /**
     * 重试次数
     */
    @Builder.Default
    private Integer maxRetries = 3;

    /**
     * 重试延迟（毫秒）
     */
    @Builder.Default
    private Integer retryDelay = 1000;

    /**
     * 服务创建时间
     */
    @CreationTimestamp
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 服务更新时间
     */
    @UpdateTimestamp
    @Column(nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 远程服务类型枚举
     */
    public enum RemoteServiceType {
        WEB_API("Web API服务", "基于HTTP REST API的服务"),
        GRPC_SERVICE("gRPC服务", "基于gRPC协议的服务"),
        WEBSOCKET_SERVICE("WebSocket服务", "基于WebSocket的实时服务"),
        DATABASE_SERVICE("数据库服务", "数据库查询和操作服务"),
        FILE_SERVICE("文件服务", "文件操作和管理服务"),
        SEARCH_SERVICE("搜索服务", "搜索和检索服务"),
        AI_SERVICE("AI服务", "人工智能和机器学习服务"),
        CUSTOM("自定义服务", "用户自定义的服务类型");

        private final String displayName;
        private final String description;

        RemoteServiceType(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 协议类型枚举
     */
    public enum ProtocolType {
        HTTP("HTTP协议"),
        HTTPS("HTTPS协议"),
        GRPC("gRPC协议"),
        WEBSOCKET("WebSocket协议"),
        TCP("TCP协议"),
        UDP("UDP协议");

        private final String description;

        ProtocolType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 服务状态枚举
     */
    public enum ServiceStatus {
        ACTIVE("激活"),
        INACTIVE("未激活"),
        MAINTENANCE("维护中"),
        ERROR("错误"),
        DEPRECATED("已弃用");

        private final String description;

        ServiceStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 健康状态枚举
     */
    public enum HealthStatus {
        HEALTHY("健康"),
        UNHEALTHY("不健康"),
        UNKNOWN("未知");

        private final String description;

        HealthStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 检查服务是否可用
     */
    public boolean isAvailable() {
        return ServiceStatus.ACTIVE.equals(this.status) && 
               HealthStatus.HEALTHY.equals(this.healthStatus);
    }

    /**
     * 检查服务是否健康
     */
    public boolean isHealthy() {
        return HealthStatus.HEALTHY.equals(this.healthStatus);
    }

    /**
     * 更新健康状态
     */
    public void updateHealthStatus(HealthStatus status, String error) {
        this.healthStatus = status;
        this.healthError = error;
        this.lastHealthCheckAt = LocalDateTime.now();
    }

    /**
     * 添加工具
     */
    public void addTool(Map<String, Object> tool) {
        if (this.tools == null) {
            this.tools = new java.util.ArrayList<>();
        }
        this.tools.add(tool);
    }

    /**
     * 添加资源
     */
    public void addResource(Map<String, Object> resource) {
        if (this.resources == null) {
            this.resources = new java.util.ArrayList<>();
        }
        this.resources.add(resource);
    }

    /**
     * 添加配置参数
     */
    public void addConfigParam(String key, Object value) {
        if (this.configParams == null) {
            this.configParams = new HashMap<>();
        }
        this.configParams.put(key, value);
    }

    /**
     * 添加元数据
     */
    public void addMetadata(String key, Object value) {
        if (this.metadata == null) {
            this.metadata = new HashMap<>();
        }
        this.metadata.put(key, value);
    }

    /**
     * 获取工具数量
     */
    public int getToolCount() {
        return tools != null ? tools.size() : 0;
    }

    /**
     * 获取资源数量
     */
    public int getResourceCount() {
        return resources != null ? resources.size() : 0;
    }
}
