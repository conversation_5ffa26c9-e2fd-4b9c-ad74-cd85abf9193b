package com.nexus.mcp.remote.notification;

import com.nexus.common.mcp.notification.McpNotificationListener;
import com.nexus.common.mcp.notification.McpNotificationType;
import com.nexus.common.mcp.notification.ServiceStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 服务状态通知监听器
 * 
 * 监听MCP服务状态变更通知，并执行相应的处理逻辑
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ServiceStatusNotificationListener implements McpNotificationListener {
    
    @Override
    public void onNotification(String serviceId, McpNotificationType type, Map<String, Object> data) {
        switch (type) {
            case SERVICE_STATUS_CHANGED:
                handleServiceStatusChanged(serviceId, data);
                break;
            case TOOLS_LIST_CHANGED:
                handleToolsListChanged(serviceId);
                break;
            case RESOURCES_LIST_CHANGED:
                handleResourcesListChanged(serviceId);
                break;
            case PROGRESS:
                handleProgress(serviceId, data);
                break;
            case CANCELLED:
                handleCancelled(serviceId, data);
                break;
            default:
                log.debug("收到未处理的通知类型: {} (服务: {})", type, serviceId);
                break;
        }
    }
    
    /**
     * 处理服务状态变更通知
     */
    private void handleServiceStatusChanged(String serviceId, Map<String, Object> data) {
        if (data == null) {
            return;
        }
        
        Object statusObj = data.get("status");
        String message = (String) data.get("message");
        Long timestamp = (Long) data.get("timestamp");
        
        ServiceStatus status = null;
        if (statusObj instanceof ServiceStatus) {
            status = (ServiceStatus) statusObj;
        } else if (statusObj instanceof String) {
            status = ServiceStatus.fromCode((String) statusObj);
        }
        
        if (status != null) {
            log.info("服务状态变更: {} -> {} ({})", serviceId, status.getDescription(), message);
            
            // 根据状态执行不同的处理逻辑
            switch (status) {
                case STARTING:
                    handleServiceStarting(serviceId, message);
                    break;
                case RUNNING:
                    handleServiceRunning(serviceId, message);
                    break;
                case STOPPING:
                    handleServiceStopping(serviceId, message);
                    break;
                case STOPPED:
                    handleServiceStopped(serviceId, message);
                    break;
                case FAILED:
                    handleServiceFailed(serviceId, message);
                    break;
                case RESTARTING:
                    handleServiceRestarting(serviceId, message);
                    break;
                default:
                    log.debug("未处理的服务状态: {} (服务: {})", status, serviceId);
                    break;
            }
        }
    }
    
    /**
     * 处理工具列表变更通知
     */
    private void handleToolsListChanged(String serviceId) {
        log.info("工具列表已变更: {}", serviceId);
        
        // 这里可以实现工具列表变更的处理逻辑
        // 例如：刷新缓存、更新服务注册信息等
        
        // 示例：记录事件
        recordEvent(serviceId, "TOOLS_LIST_CHANGED", "工具列表已更新");
    }
    
    /**
     * 处理资源列表变更通知
     */
    private void handleResourcesListChanged(String serviceId) {
        log.info("资源列表已变更: {}", serviceId);
        
        // 这里可以实现资源列表变更的处理逻辑
        // 例如：刷新资源缓存、更新索引等
        
        // 示例：记录事件
        recordEvent(serviceId, "RESOURCES_LIST_CHANGED", "资源列表已更新");
    }
    
    /**
     * 处理进度通知
     */
    private void handleProgress(String serviceId, Map<String, Object> data) {
        if (data == null) {
            return;
        }
        
        String progressToken = (String) data.get("progressToken");
        Object progress = data.get("progress");
        
        log.debug("收到进度通知: {} -> {} ({})", serviceId, progressToken, progress);
        
        // 这里可以实现进度处理逻辑
        // 例如：更新进度条、通知前端等
    }
    
    /**
     * 处理取消通知
     */
    private void handleCancelled(String serviceId, Map<String, Object> data) {
        if (data == null) {
            return;
        }
        
        String requestId = (String) data.get("requestId");
        String reason = (String) data.get("reason");
        
        log.info("收到取消通知: {} -> {} (原因: {})", serviceId, requestId, reason);
        
        // 这里可以实现取消处理逻辑
        // 例如：清理资源、更新状态等
        
        recordEvent(serviceId, "REQUEST_CANCELLED", "请求已取消: " + requestId + " (" + reason + ")");
    }
    
    /**
     * 处理服务启动中状态
     */
    private void handleServiceStarting(String serviceId, String message) {
        log.info("服务启动中: {} ({})", serviceId, message);
        recordEvent(serviceId, "SERVICE_STARTING", message);
    }
    
    /**
     * 处理服务运行中状态
     */
    private void handleServiceRunning(String serviceId, String message) {
        log.info("服务已启动: {} ({})", serviceId, message);
        recordEvent(serviceId, "SERVICE_RUNNING", message);
        
        // 服务启动成功后的处理逻辑
        // 例如：注册服务、启用健康检查等
    }
    
    /**
     * 处理服务停止中状态
     */
    private void handleServiceStopping(String serviceId, String message) {
        log.info("服务停止中: {} ({})", serviceId, message);
        recordEvent(serviceId, "SERVICE_STOPPING", message);
    }
    
    /**
     * 处理服务已停止状态
     */
    private void handleServiceStopped(String serviceId, String message) {
        log.info("服务已停止: {} ({})", serviceId, message);
        recordEvent(serviceId, "SERVICE_STOPPED", message);
        
        // 服务停止后的清理逻辑
        // 例如：注销服务、清理缓存等
    }
    
    /**
     * 处理服务失败状态
     */
    private void handleServiceFailed(String serviceId, String message) {
        log.error("服务运行失败: {} ({})", serviceId, message);
        recordEvent(serviceId, "SERVICE_FAILED", message);
        
        // 服务失败后的处理逻辑
        // 例如：发送告警、尝试重启等
    }
    
    /**
     * 处理服务重启中状态
     */
    private void handleServiceRestarting(String serviceId, String message) {
        log.info("服务重启中: {} ({})", serviceId, message);
        recordEvent(serviceId, "SERVICE_RESTARTING", message);
    }
    
    /**
     * 记录事件
     */
    private void recordEvent(String serviceId, String eventType, String message) {
        // 这里可以实现事件记录逻辑
        // 例如：写入数据库、发送到消息队列等
        log.debug("记录事件: {} -> {} ({})", serviceId, eventType, message);
    }
    
    @Override
    public boolean supportsNotificationType(McpNotificationType type) {
        // 只处理特定类型的通知
        return type == McpNotificationType.SERVICE_STATUS_CHANGED ||
               type == McpNotificationType.TOOLS_LIST_CHANGED ||
               type == McpNotificationType.RESOURCES_LIST_CHANGED ||
               type == McpNotificationType.PROGRESS ||
               type == McpNotificationType.CANCELLED;
    }
    
    @Override
    public String getListenerName() {
        return "ServiceStatusNotificationListener";
    }
}
