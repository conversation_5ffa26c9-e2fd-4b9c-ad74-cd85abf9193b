package com.nexus.mcp.remote.producer;

import com.nexus.common.model.MCPAsyncTask;
import com.nexus.common.constants.RocketMQConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * MCP任务生产者
 * 负责将MCP异步任务发送到RocketMQ
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class McpTaskProducer {

    private final RocketMQTemplate rocketMQTemplate;

    /**
     * 发送MCP异步任务到队列
     *
     * @param task MCP异步任务
     */
    public void sendTask(MCPAsyncTask task) {
        try {
            // 设置任务创建时间
            if (task.getCreatedAt() == null) {
                task.setCreatedAt(LocalDateTime.now());
            }

            // 设置任务ID（如果未设置）
            if (task.getTaskId() == null) {
                task.setTaskId(UUID.randomUUID().toString());
            }

            // 设置关联ID（如果未设置）
            if (task.getCorrelationId() == null) {
                task.setCorrelationId(UUID.randomUUID().toString());
            }

            // 发送任务到RocketMQ
            String destination = RocketMQConstants.MCP_ASYNC_TOPIC + ":" + RocketMQConstants.MCP_ASYNC_TAG;
            rocketMQTemplate.convertAndSend(destination, task);

            log.info("MCP异步任务已发送到队列: taskId={}, toolName={}, userId={}",
                    task.getTaskId(), task.getToolName(), task.getUserId());

        } catch (Exception e) {
            log.error("发送MCP异步任务失败: taskId={} - {}", task.getTaskId(), e.getMessage());
            throw new RuntimeException("发送MCP异步任务失败", e);
        }
    }

    /**
     * 发送任务结果到结果队列
     *
     * @param taskResult 任务结果
     */
    public void sendTaskResult(Object taskResult) {
        try {
            String destination = RocketMQConstants.MCP_ASYNC_TOPIC + ":" + RocketMQConstants.MCP_RESULT_TAG;
            rocketMQTemplate.convertAndSend(destination, taskResult);

            log.debug("任务结果已发送到结果队列");

        } catch (Exception e) {
            log.error("发送任务结果失败: {}", e.getMessage());
            throw new RuntimeException("发送任务结果失败", e);
        }
    }

    /**
     * 发送重试任务到重试队列
     *
     * @param task 需要重试的任务
     */
    public void sendRetryTask(MCPAsyncTask task) {
        try {
            // 增加重试次数
            task.incrementRetries();
            
            // 重置任务状态以便重试
            task.resetForRetry();

            String destination = RocketMQConstants.MCP_ASYNC_TOPIC + ":" + RocketMQConstants.MCP_RETRY_TAG;
            rocketMQTemplate.convertAndSend(destination, task);

            log.info("重试任务已发送到重试队列: taskId={}, currentRetries={}",
                    task.getTaskId(), task.getCurrentRetries());

        } catch (Exception e) {
            log.error("发送重试任务失败: taskId={} - {}", task.getTaskId(), e.getMessage());
            throw new RuntimeException("发送重试任务失败", e);
        }
    }

    /**
     * 发送任务日志到日志队列
     *
     * @param logMessage 日志消息
     */
    public void sendTaskLog(Object logMessage) {
        try {
            String destination = RocketMQConstants.MCP_ASYNC_TOPIC + ":" + RocketMQConstants.MCP_LOG_TAG;
            rocketMQTemplate.convertAndSend(destination, logMessage);

            log.debug("任务日志已发送到日志队列");

        } catch (Exception e) {
            log.error("发送任务日志失败: {}", e.getMessage());
        }
    }
}