package com.nexus.mcp.remote.repository;

import com.nexus.mcp.remote.entity.RemoteMcpService;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 远程MCP服务数据访问层
 * 提供远程MCP服务相关的数据库操作
 */
@Repository
public interface RemoteMcpServiceRepository extends JpaRepository<RemoteMcpService, Long> {

    /**
     * 根据服务名称查找服务
     */
    Optional<RemoteMcpService> findByServiceName(String serviceName);

    /**
     * 根据服务类型查找服务列表
     */
    List<RemoteMcpService> findByServiceType(RemoteMcpService.RemoteServiceType serviceType);

    /**
     * 根据状态查找服务列表
     */
    List<RemoteMcpService> findByStatus(RemoteMcpService.ServiceStatus status);

    /**
     * 根据健康状态查找服务列表
     */
    List<RemoteMcpService> findByHealthStatus(RemoteMcpService.HealthStatus healthStatus);

    /**
     * 查找可用的服务（状态为ACTIVE且健康）
     */
    @Query("SELECT s FROM RemoteMcpService s WHERE s.status = 'ACTIVE' AND s.healthStatus = 'HEALTHY'")
    List<RemoteMcpService> findAvailableServices();

    /**
     * 查找需要健康检查的服务
     */
    @Query("SELECT s FROM RemoteMcpService s WHERE s.status = 'ACTIVE' AND " +
           "(s.lastHealthCheckAt IS NULL OR s.lastHealthCheckAt < :threshold)")
    List<RemoteMcpService> findServicesNeedingHealthCheck(@Param("threshold") LocalDateTime threshold);

    /**
     * 根据端点查找服务
     */
    Optional<RemoteMcpService> findByEndpoint(String endpoint);

    /**
     * 检查服务名称是否存在
     */
    boolean existsByServiceName(String serviceName);

    /**
     * 检查端点是否存在
     */
    boolean existsByEndpoint(String endpoint);

    /**
     * 更新服务状态
     */
    @Modifying
    @Query("UPDATE RemoteMcpService s SET s.status = :status WHERE s.id = :serviceId")
    void updateServiceStatus(@Param("serviceId") Long serviceId, 
                           @Param("status") RemoteMcpService.ServiceStatus status);

    /**
     * 更新健康状态
     */
    @Modifying
    @Query("UPDATE RemoteMcpService s SET s.healthStatus = :healthStatus, s.healthError = :error, " +
           "s.lastHealthCheckAt = :checkTime WHERE s.id = :serviceId")
    void updateHealthStatus(@Param("serviceId") Long serviceId,
                          @Param("healthStatus") RemoteMcpService.HealthStatus healthStatus,
                          @Param("error") String error,
                          @Param("checkTime") LocalDateTime checkTime);

    /**
     * 统计服务总数
     */
    @Query("SELECT COUNT(s) FROM RemoteMcpService s")
    long countTotalServices();

    /**
     * 统计可用服务数
     */
    @Query("SELECT COUNT(s) FROM RemoteMcpService s WHERE s.status = 'ACTIVE' AND s.healthStatus = 'HEALTHY'")
    long countAvailableServices();

    /**
     * 统计各种状态的服务数量
     */
    @Query("SELECT s.status, COUNT(s) FROM RemoteMcpService s GROUP BY s.status")
    List<Object[]> countServicesByStatus();

    /**
     * 统计各种类型的服务数量
     */
    @Query("SELECT s.serviceType, COUNT(s) FROM RemoteMcpService s GROUP BY s.serviceType")
    List<Object[]> countServicesByType();

    /**
     * 根据服务名称模糊查询
     */
    @Query("SELECT s FROM RemoteMcpService s WHERE s.serviceName LIKE %:keyword% OR s.displayName LIKE %:keyword%")
    List<RemoteMcpService> searchServices(@Param("keyword") String keyword);

    /**
     * 查找长时间未更新的服务
     */
    @Query("SELECT s FROM RemoteMcpService s WHERE s.updatedAt < :threshold")
    List<RemoteMcpService> findStaleServices(@Param("threshold") LocalDateTime threshold);

    /**
     * 查找错误状态的服务
     */
    @Query("SELECT s FROM RemoteMcpService s WHERE s.status = 'ERROR' OR s.healthStatus = 'UNHEALTHY'")
    List<RemoteMcpService> findErrorServices();
}
