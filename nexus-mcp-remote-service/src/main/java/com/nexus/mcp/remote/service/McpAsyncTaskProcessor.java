package com.nexus.mcp.remote.service;

import com.nexus.common.model.MCPAsyncTask;
import com.nexus.common.model.MCPTaskResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * MCP异步任务处理器
 * 负责处理MCP相关的异步任务
 */
@Slf4j
@Service
public class McpAsyncTaskProcessor {

    private final McpToolExecutor toolExecutor;
    private final McpResourceAccessor resourceAccessor;
    private final UnifiedMcpService unifiedMcpService;
    private final RedisTemplate<String, Object> redisTemplate;

    public McpAsyncTaskProcessor(McpToolExecutor toolExecutor,
                                McpResourceAccessor resourceAccessor,
                                @Lazy UnifiedMcpService unifiedMcpService,
                                RedisTemplate<String, Object> redisTemplate) {
        this.toolExecutor = toolExecutor;
        this.resourceAccessor = resourceAccessor;
        this.unifiedMcpService = unifiedMcpService;
        this.redisTemplate = redisTemplate;
    }

    @Value("${nexus.mcp.remote.async.task-timeout:60000}")
    private long taskTimeout;

    private static final String TASK_RESULT_PREFIX = "task_result:";
    private static final String TASK_STATUS_PREFIX = "task_status:";

    /**
     * 处理异步任务
     */
    public MCPTaskResult processTask(MCPAsyncTask task) {
        log.info("处理异步任务: {} (类型: {})", task.getTaskId(), task.getMethod());

        try {
            // 更新任务状态为处理中
            updateTaskStatus(task.getTaskId(), "PROCESSING");

            MCPTaskResult result;
            switch (task.getMethod()) {
                case "tools/call":
                    result = processToolCallTask(task);
                    break;
                case "resources/read":
                    result = processResourceReadTask(task);
                    break;
                default:
                    result = MCPTaskResult.failure(task.getTaskId(), "不支持的任务类型: " + task.getMethod());
            }

            // 缓存任务结果
            cacheTaskResult(task.getTaskId(), result);
            
            // 更新任务状态
            updateTaskStatus(task.getTaskId(), result.isSuccess() ? "COMPLETED" : "FAILED");

            log.info("异步任务处理完成: {} (成功: {})", task.getTaskId(), result.isSuccess());
            return result;

        } catch (Exception e) {
            log.error("异步任务处理失败: {} - {}", task.getTaskId(), e.getMessage());
            
            MCPTaskResult errorResult = MCPTaskResult.failure(task.getTaskId(), "任务处理异常: " + e.getMessage());
            cacheTaskResult(task.getTaskId(), errorResult);
            updateTaskStatus(task.getTaskId(), "FAILED");
            
            return errorResult;
        }
    }

    /**
     * 异步处理任务
     */
    @Async("mcpServiceExecutor")
    public CompletableFuture<MCPTaskResult> processTaskAsync(MCPAsyncTask task) {
        return CompletableFuture.completedFuture(processTask(task));
    }

    /**
     * 处理工具调用任务
     */
    private MCPTaskResult processToolCallTask(MCPAsyncTask task) {
        try {
            String toolName = task.getToolName();
            Map<String, Object> parameters = task.getParameters();
            String userId = task.getUserId();

            Map<String, Object> result = unifiedMcpService.callTool(toolName, parameters, userId);
            
            return MCPTaskResult.success(task.getTaskId(), result);

        } catch (Exception e) {
            log.error("工具调用任务失败: {} - {}", task.getTaskId(), e.getMessage());
            return MCPTaskResult.failure(task.getTaskId(), "工具调用失败: " + e.getMessage());
        }
    }

    /**
     * 处理资源读取任务
     */
    private MCPTaskResult processResourceReadTask(MCPAsyncTask task) {
        try {
            Map<String, Object> parameters = task.getParameters();
            String resourceUri = (String) parameters.get("uri");
            String userId = task.getUserId();

            Map<String, Object> result = unifiedMcpService.readResource(resourceUri, userId);
            
            return MCPTaskResult.success(task.getTaskId(), result);

        } catch (Exception e) {
            log.error("资源读取任务失败: {} - {}", task.getTaskId(), e.getMessage());
            return MCPTaskResult.failure(task.getTaskId(), "资源读取失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务结果
     */
    public MCPTaskResult getTaskResult(String taskId) {
        try {
            String key = TASK_RESULT_PREFIX + taskId;
            Object result = redisTemplate.opsForValue().get(key);
            
            if (result instanceof MCPTaskResult) {
                return (MCPTaskResult) result;
            } else if (result != null) {
                // 尝试从Map转换
                return convertToTaskResult(taskId, result);
            } else {
                return MCPTaskResult.failure(taskId, "任务结果不存在或已过期");
            }
        } catch (Exception e) {
            log.error("获取任务结果失败: {} - {}", taskId, e.getMessage());
            return MCPTaskResult.failure(taskId, "获取任务结果失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务状态
     */
    public String getTaskStatus(String taskId) {
        try {
            String key = TASK_STATUS_PREFIX + taskId;
            Object status = redisTemplate.opsForValue().get(key);
            return status != null ? status.toString() : "UNKNOWN";
        } catch (Exception e) {
            log.error("获取任务状态失败: {} - {}", taskId, e.getMessage());
            return "ERROR";
        }
    }

    /**
     * 取消任务
     */
    public boolean cancelTask(String taskId) {
        try {
            updateTaskStatus(taskId, "CANCELLED");
            
            // 清除任务结果
            String resultKey = TASK_RESULT_PREFIX + taskId;
            redisTemplate.delete(resultKey);
            
            log.info("任务已取消: {}", taskId);
            return true;
        } catch (Exception e) {
            log.error("取消任务失败: {} - {}", taskId, e.getMessage());
            return false;
        }
    }

    /**
     * 清理过期任务
     */
    public void cleanupExpiredTasks() {
        log.debug("开始清理过期任务");
        
        try {
            // 清理过期的任务结果
            String resultPattern = TASK_RESULT_PREFIX + "*";
            redisTemplate.keys(resultPattern).forEach(key -> {
                try {
                    Long ttl = redisTemplate.getExpire(key);
                    if (ttl != null && ttl <= 0) {
                        redisTemplate.delete(key);
                    }
                } catch (Exception e) {
                    log.warn("清理任务结果失败: {} - {}", key, e.getMessage());
                }
            });

            // 清理过期的任务状态
            String statusPattern = TASK_STATUS_PREFIX + "*";
            redisTemplate.keys(statusPattern).forEach(key -> {
                try {
                    Long ttl = redisTemplate.getExpire(key);
                    if (ttl != null && ttl <= 0) {
                        redisTemplate.delete(key);
                    }
                } catch (Exception e) {
                    log.warn("清理任务状态失败: {} - {}", key, e.getMessage());
                }
            });

        } catch (Exception e) {
            log.error("清理过期任务失败", e);
        }
    }

    /**
     * 缓存任务结果
     */
    private void cacheTaskResult(String taskId, MCPTaskResult result) {
        try {
            String key = TASK_RESULT_PREFIX + taskId;
            redisTemplate.opsForValue().set(key, result, 1, TimeUnit.HOURS);
        } catch (Exception e) {
            log.error("缓存任务结果失败: {} - {}", taskId, e.getMessage());
        }
    }

    /**
     * 更新任务状态
     */
    private void updateTaskStatus(String taskId, String status) {
        try {
            String key = TASK_STATUS_PREFIX + taskId;
            redisTemplate.opsForValue().set(key, status, 1, TimeUnit.HOURS);
        } catch (Exception e) {
            log.error("更新任务状态失败: {} - {}", taskId, e.getMessage());
        }
    }

    /**
     * 转换为任务结果对象
     */
    private MCPTaskResult convertToTaskResult(String taskId, Object result) {
        try {
            if (result instanceof Map) {
                Map<String, Object> resultMap = (Map<String, Object>) result;
                Boolean success = (Boolean) resultMap.get("success");
                Map<String, Object> data = (Map<String, Object>) resultMap.get("data");
                String errorMessage = (String) resultMap.get("errorMessage");
                
                if (Boolean.TRUE.equals(success)) {
                    return MCPTaskResult.success(taskId, data);
                } else {
                    return MCPTaskResult.failure(taskId, errorMessage);
                }
            } else {
                return MCPTaskResult.success(taskId, Map.of("result", result));
            }
        } catch (Exception e) {
            log.error("转换任务结果失败: {} - {}", taskId, e.getMessage());
            return MCPTaskResult.failure(taskId, "任务结果格式错误");
        }
    }

    /**
     * 创建任务摘要
     */
    public Map<String, Object> getTaskSummary(String taskId) {
        Map<String, Object> summary = new HashMap<>();
        summary.put("taskId", taskId);
        summary.put("status", getTaskStatus(taskId));
        summary.put("timestamp", LocalDateTime.now());
        
        MCPTaskResult result = getTaskResult(taskId);
        if (result != null) {
            summary.put("success", result.isSuccess());
            summary.put("hasResult", true);
            if (!result.isSuccess()) {
                summary.put("errorMessage", result.getErrorMessage());
            }
        } else {
            summary.put("hasResult", false);
        }
        
        return summary;
    }
}
