package com.nexus.mcp.remote.service;

import com.nexus.common.mcp.config.McpCommandParser;
import com.nexus.common.mcp.process.McpServiceConfig;
import com.nexus.common.mcp.process.McpServiceProcessManager;
import com.nexus.common.mcp.process.ManagedMcpProcess;
import com.nexus.common.mcp.protocol.McpDataModels;
import com.nexus.common.model.MCPServiceMetadata;
import com.nexus.mcp.remote.entity.RemoteMcpService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * MCP命令服务
 * 
 * 提供直接通过命令启动MCP服务的功能
 * 支持npx、uvx、python、java、node等各种启动方式
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class McpCommandService {

    private final McpCommandParser commandParser;
    private final McpServiceProcessManager processManager;
    private final StandardMcpGateway standardMcpGateway;
    private final RemoteMcpServiceManager serviceManager;

    // 命令到服务的映射
    private final Map<String, String> commandToServiceMap = new ConcurrentHashMap<>();
    
    /**
     * 直接通过命令启动MCP服务
     * 
     * @param command 启动命令，例如：
     *                - "npx -y @modelcontextprotocol/server-filesystem /path/to/files"
     *                - "uvx mcp-server-git --repository /path/to/repo"
     *                - "python -m mcp_server_sqlite --db-path /path/to/db.sqlite"
     *                - "java -jar mcp-server.jar --port 8080"
     * @return 启动结果
     */
    public McpServiceStartResult startServiceByCommand(String command) {
        return startServiceByCommand(command, null);
    }
    
    /**
     * 直接通过命令启动MCP服务
     * 
     * @param command 启动命令
     * @param serviceName 自定义服务名（可选）
     * @return 启动结果
     */
    public McpServiceStartResult startServiceByCommand(String command, String serviceName) {
        log.info("通过命令启动MCP服务: {}", command);
        
        try {
            // 1. 解析命令生成配置
            McpServiceConfig config = commandParser.parseCommand(command, serviceName);
            
            // 2. 检查服务是否已经运行
            if (standardMcpGateway.isStandardMcpServiceRunning(config.getServiceName())) {
                return McpServiceStartResult.alreadyRunning(config.getServiceName());
            }
            
            // 3. 启动MCP服务
            boolean success = standardMcpGateway.startStandardMcpService(config.getServiceName(), config);

            if (success) {
                // 4. 记录命令映射
                commandToServiceMap.put(command, config.getServiceName());

                // 5. 同步到数据库和缓存
                try {
                    registerServiceToDatabase(config, command);
                    log.info("服务已同步到数据库: {}", config.getServiceName());
                } catch (Exception e) {
                    log.warn("同步服务到数据库失败，但服务已启动: {} - {}", config.getServiceName(), e.getMessage());
                }

                log.info("MCP服务启动成功: {} (命令: {})", config.getServiceName(), command);
                return McpServiceStartResult.success(config.getServiceName(), config);
            } else {
                log.error("MCP服务启动失败: {} (命令: {})", config.getServiceName(), command);
                return McpServiceStartResult.failed(config.getServiceName(), "服务启动失败");
            }
            
        } catch (Exception e) {
            log.error("通过命令启动MCP服务失败: {} - {}", command, e.getMessage(), e);
            return McpServiceStartResult.failed("unknown", e.getMessage());
        }
    }
    
    /**
     * 批量启动MCP服务
     */
    public Map<String, McpServiceStartResult> startServicesByCommands(List<String> commands) {
        Map<String, McpServiceStartResult> results = new HashMap<>();
        
        for (String command : commands) {
            try {
                McpServiceStartResult result = startServiceByCommand(command);
                results.put(command, result);
            } catch (Exception e) {
                log.error("批量启动服务失败: {} - {}", command, e.getMessage());
                results.put(command, McpServiceStartResult.failed("unknown", e.getMessage()));
            }
        }
        
        return results;
    }
    
    /**
     * 通过命令停止MCP服务
     */
    public boolean stopServiceByCommand(String command) {
        String serviceName = commandToServiceMap.get(command);
        if (serviceName == null) {
            log.warn("未找到命令对应的服务: {}", command);
            return false;
        }

        boolean success = standardMcpGateway.stopStandardMcpService(serviceName);
        if (success) {
            commandToServiceMap.remove(command);

            // 更新数据库状态
            try {
                updateServiceStatusInDatabase(serviceName, RemoteMcpService.ServiceStatus.INACTIVE);
                log.info("服务状态已更新到数据库: {} -> INACTIVE", serviceName);
            } catch (Exception e) {
                log.warn("更新服务状态到数据库失败: {} - {}", serviceName, e.getMessage());
            }

            log.info("通过命令停止MCP服务成功: {} (命令: {})", serviceName, command);
        }

        return success;
    }
    
    /**
     * 获取所有通过命令启动的服务
     */
    public Map<String, String> getCommandServiceMappings() {
        return new HashMap<>(commandToServiceMap);
    }
    
    /**
     * 验证命令是否可以解析
     */
    public boolean validateCommand(String command) {
        try {
            commandParser.parseCommand(command);
            return true;
        } catch (Exception e) {
            log.debug("命令验证失败: {} - {}", command, e.getMessage());
            return false;
        }
    }
    
    /**
     * 预览命令解析结果（不启动服务）
     */
    public McpServiceConfig previewCommand(String command) {
        return commandParser.parseCommand(command);
    }
    
    /**
     * 获取常用命令示例
     */
    public List<String> getCommonCommandExamples() {
        return Arrays.asList(
                // Node.js官方服务
                "npx -y @modelcontextprotocol/server-filesystem /path/to/allowed/files",
                "npx -y @modelcontextprotocol/server-memory",
                "npx -y @modelcontextprotocol/server-time",
                "npx -y @modelcontextprotocol/server-fetch",
                
                // Python官方服务
                "uvx mcp-server-git --repository /path/to/git/repo",
                
                // 社区服务
                "uvx mcp-server-sqlite --db-path /path/to/database.db",
                "npx -y @modelcontextprotocol/server-postgres postgresql://user:pass@localhost/db",
                
                // 自定义服务
                "python weather_service.py --api-key YOUR_API_KEY",
                "java -jar custom-mcp-server.jar --port 8080",
                "node custom-server.js --config config.json"
        );
    }

    /**
     * 获取运行中的服务
     */
    public Map<String, Object> getRunningServices() {
        Map<String, Object> runningServices = new HashMap<>();

        // 从StandardMcpGateway获取运行中的服务
        try {
            Map<String, ManagedMcpProcess> standardServices = standardMcpGateway.getAllStandardMcpServices();
            for (Map.Entry<String, ManagedMcpProcess> entry : standardServices.entrySet()) {
                String serviceName = entry.getKey();
                ManagedMcpProcess process = entry.getValue();

                if (standardMcpGateway.isStandardMcpServiceRunning(serviceName)) {
                    Map<String, Object> serviceInfo = new HashMap<>();
                    serviceInfo.put("serviceName", serviceName);
                    serviceInfo.put("processId", process.getProcess().hashCode());
                    serviceInfo.put("status", "RUNNING");
                    serviceInfo.put("startTime", process.getStartTime());
                    serviceInfo.put("config", process.getConfig());

                    runningServices.put(serviceName, serviceInfo);
                }
            }
        } catch (Exception e) {
            log.warn("Failed to get running services: {}", e.getMessage());
        }

        return runningServices;
    }

    /**
     * MCP服务启动结果
     */
    public static class McpServiceStartResult {
        private final boolean success;
        private final String serviceName;
        private final String message;
        private final McpServiceConfig config;
        
        private McpServiceStartResult(boolean success, String serviceName, String message, McpServiceConfig config) {
            this.success = success;
            this.serviceName = serviceName;
            this.message = message;
            this.config = config;
        }
        
        public static McpServiceStartResult success(String serviceName, McpServiceConfig config) {
            return new McpServiceStartResult(true, serviceName, "服务启动成功", config);
        }
        
        public static McpServiceStartResult failed(String serviceName, String message) {
            return new McpServiceStartResult(false, serviceName, message, null);
        }
        
        public static McpServiceStartResult alreadyRunning(String serviceName) {
            return new McpServiceStartResult(false, serviceName, "服务已在运行", null);
        }
        
        // Getters
        public boolean isSuccess() { return success; }
        public String getServiceName() { return serviceName; }
        public String getMessage() { return message; }
        public McpServiceConfig getConfig() { return config; }
        
        @Override
        public String toString() {
            return String.format("McpServiceStartResult{success=%s, serviceName='%s', message='%s'}",
                    success, serviceName, message);
        }
    }

    /**
     * 将通过命令启动的服务注册到数据库
     */
    private void registerServiceToDatabase(McpServiceConfig config, String command) {
        try {
            // 检查服务是否已存在
            if (serviceManager.existsByServiceName(config.getServiceName())) {
                // 如果存在，更新状态为活跃
                serviceManager.updateServiceStatusByName(config.getServiceName(), RemoteMcpService.ServiceStatus.ACTIVE);
                log.info("更新现有服务状态为活跃: {}", config.getServiceName());
                return;
            }

            // 创建服务元数据
            MCPServiceMetadata metadata = new MCPServiceMetadata();
            metadata.setServiceName(config.getServiceName());
            metadata.setRealServiceName(generateDisplayName(config));
            metadata.setDescription(generateDescription(config, command));
            metadata.setVersion("1.0.0");
            metadata.setEndpoint("stdio://localhost"); // 标准输入输出连接

            // 添加元数据
            metadata.addMetadata("startCommand", command);
            metadata.addMetadata("serviceType", config.getServiceType().toString());
            metadata.addMetadata("servicePath", config.getServicePath());
            metadata.addMetadata("startedByCommand", "true");

            // 注册服务
            RemoteMcpService service = serviceManager.registerCommandStartedService(metadata);
            log.info("命令启动的服务已注册到数据库: {} (ID: {})", service.getServiceName(), service.getId());

            // 异步获取并更新工具和资源信息
            try {
                updateServiceToolsAndResources(service);
            } catch (Exception e) {
                log.warn("获取服务工具和资源信息失败: {} - {}", config.getServiceName(), e.getMessage());
            }

        } catch (Exception e) {
            log.error("注册命令启动的服务到数据库失败: {} - {}", config.getServiceName(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 更新服务状态到数据库
     */
    private void updateServiceStatusInDatabase(String serviceName, RemoteMcpService.ServiceStatus status) {
        try {
            serviceManager.updateServiceStatusByName(serviceName, status);
        } catch (Exception e) {
            log.error("更新服务状态失败: {} -> {} - {}", serviceName, status, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 生成服务显示名称
     */
    private String generateDisplayName(McpServiceConfig config) {
        String serviceName = config.getServiceName();

        // 根据服务名生成友好的显示名称
        if (serviceName.contains("filesystem")) {
            return "文件系统服务";
        } else if (serviceName.contains("memory")) {
            return "内存服务";
        } else if (serviceName.contains("git")) {
            return "Git仓库服务";
        } else if (serviceName.contains("sqlite")) {
            return "SQLite数据库服务";
        } else if (serviceName.contains("postgres")) {
            return "PostgreSQL数据库服务";
        } else if (serviceName.contains("time")) {
            return "时间服务";
        } else if (serviceName.contains("fetch")) {
            return "网络请求服务";
        } else {
            return serviceName + "服务";
        }
    }

    /**
     * 生成服务描述
     */
    private String generateDescription(McpServiceConfig config, String command) {
        String serviceName = config.getServiceName();
        String baseDescription = "通过命令启动的MCP服务";

        if (serviceName.contains("filesystem")) {
            baseDescription = "提供文件系统访问功能的MCP服务";
        } else if (serviceName.contains("memory")) {
            baseDescription = "提供内存存储功能的MCP服务";
        } else if (serviceName.contains("git")) {
            baseDescription = "提供Git仓库操作功能的MCP服务";
        } else if (serviceName.contains("sqlite")) {
            baseDescription = "提供SQLite数据库访问功能的MCP服务";
        } else if (serviceName.contains("postgres")) {
            baseDescription = "提供PostgreSQL数据库访问功能的MCP服务";
        } else if (serviceName.contains("time")) {
            baseDescription = "提供时间相关功能的MCP服务";
        } else if (serviceName.contains("fetch")) {
            baseDescription = "提供网络请求功能的MCP服务";
        }

        return baseDescription + "\n启动命令: " + command;
    }

    /**
     * 更新服务的工具和资源信息
     */
    private void updateServiceToolsAndResources(RemoteMcpService service) {
        String serviceName = service.getServiceName();
        log.info("开始获取服务工具和资源信息: {}", serviceName);

        try {
            // 等待服务完全启动
            Thread.sleep(2000);

            // 获取工具信息
            List<McpDataModels.Tool> tools = standardMcpGateway.listStandardMcpTools(serviceName);
            if (tools != null && !tools.isEmpty()) {
                List<Map<String, Object>> toolMaps = tools.stream()
                        .map(this::convertToolToMap)
                        .collect(Collectors.toList());
                service.setTools(toolMaps);
                log.info("获取到 {} 个工具: {}", tools.size(), serviceName);
            }

            // 获取资源信息
            List<McpDataModels.Resource> resources = standardMcpGateway.listStandardMcpResources(serviceName);
            if (resources != null && !resources.isEmpty()) {
                List<Map<String, Object>> resourceMaps = resources.stream()
                        .map(this::convertResourceToMap)
                        .collect(Collectors.toList());
                service.setResources(resourceMaps);
                log.info("获取到 {} 个资源: {}", resources.size(), serviceName);
            }

            // 更新数据库
            serviceManager.updateServiceToolsAndResources(service);
            log.info("服务工具和资源信息已更新: {}", serviceName);

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("获取服务信息时被中断: {}", serviceName);
        } catch (Exception e) {
            log.error("获取服务工具和资源信息失败: {} - {}", serviceName, e.getMessage(), e);
        }
    }

    /**
     * 将工具转换为Map格式
     */
    private Map<String, Object> convertToolToMap(McpDataModels.Tool tool) {
        Map<String, Object> toolMap = new HashMap<>();
        toolMap.put("name", tool.getName());
        toolMap.put("description", tool.getDescription());
        toolMap.put("inputSchema", tool.getInputSchema());
        return toolMap;
    }

    /**
     * 将资源转换为Map格式
     */
    private Map<String, Object> convertResourceToMap(McpDataModels.Resource resource) {
        Map<String, Object> resourceMap = new HashMap<>();
        resourceMap.put("uri", resource.getUri());
        resourceMap.put("name", resource.getName());
        resourceMap.put("description", resource.getDescription());
        resourceMap.put("mimeType", resource.getMimeType());
        return resourceMap;
    }
}
