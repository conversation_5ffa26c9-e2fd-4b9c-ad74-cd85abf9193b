package com.nexus.mcp.remote.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nexus.common.exception.NexusException;
import com.nexus.common.mcp.protocol.*;
import com.nexus.mcp.remote.entity.RemoteMcpService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * MCP资源访问器
 * 负责访问远程MCP服务的资源
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class McpResourceAccessor {

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final StandardMcpGateway standardMcpGateway;

    @Value("${nexus.mcp.remote.resources.default-timeout:30000}")
    private long defaultTimeout;

    @Value("${nexus.mcp.remote.resources.max-resource-size:10485760}")
    private long maxResourceSize;

    /**
     * 读取MCP资源
     */
    public Map<String, Object> readResource(RemoteMcpService service, String resourceUri, String userId) {
        log.info("读取MCP资源: {} (服务: {}, 用户: {})", resourceUri, service.getServiceName(), userId);

        long startTime = System.currentTimeMillis();
        
        try {
            // 1. 首先尝试标准MCP协议
            Map<String, Object> result = tryReadStandardMcpResource(service, resourceUri, userId);

            if (result != null) {
                log.debug("通过标准MCP协议读取资源成功: {}", resourceUri);
            } else {
                // 2. 回退到传统协议
                log.debug("标准MCP协议不可用，回退到传统协议: {}", resourceUri);

                switch (service.getProtocolType()) {
                    case HTTP:
                    case HTTPS:
                        result = readHttpResource(service, resourceUri, userId);
                        break;
                    case GRPC:
                        result = readGrpcResource(service, resourceUri, userId);
                        break;
                    case WEBSOCKET:
                        result = readWebSocketResource(service, resourceUri, userId);
                        break;
                    default:
                        throw new NexusException.BusinessException("不支持的协议类型: " + service.getProtocolType());
                }
            }

            long executionTime = System.currentTimeMillis() - startTime;
            log.info("资源读取完成: {} (耗时: {}ms)", resourceUri, executionTime);

            // 添加访问元数据
            result.put("_metadata", Map.of(
                    "accessTime", executionTime,
                    "serviceId", service.getId(),
                    "serviceName", service.getServiceName(),
                    "resourceUri", resourceUri,
                    "timestamp", LocalDateTime.now()
            ));

            return result;

        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            log.error("资源读取失败: {} (耗时: {}ms) - {}", resourceUri, executionTime, e.getMessage());
            throw new NexusException.BusinessException("资源读取失败: " + e.getMessage());
        }
    }

    /**
     * 读取HTTP协议的资源
     */
    private Map<String, Object> readHttpResource(RemoteMcpService service, String resourceUri, String userId) {
        try {
            // 构建请求URL
            String url = buildResourceUrl(service, resourceUri);
            
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("method", "resources/read");
            requestBody.put("params", Map.of(
                    "uri", resourceUri
            ));
            requestBody.put("id", System.currentTimeMillis());

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            // 添加认证信息
            addAuthHeaders(headers, service, userId);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            // 发送请求
            ResponseEntity<Map> response = restTemplate.exchange(
                    url, HttpMethod.POST, request, Map.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                
                // 检查是否有错误
                if (responseBody.containsKey("error")) {
                    Map<String, Object> error = (Map<String, Object>) responseBody.get("error");
                    throw new NexusException.BusinessException("资源访问错误: " + error.get("message"));
                }
                
                // 返回结果
                Object result = responseBody.get("result");
                if (result instanceof Map) {
                    return (Map<String, Object>) result;
                } else {
                    return Map.of("content", result);
                }
            } else {
                throw new NexusException.BusinessException("HTTP请求失败: " + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("HTTP资源访问失败: {}", e.getMessage());
            throw new NexusException.BusinessException("HTTP资源访问失败: " + e.getMessage());
        }
    }

    /**
     * 读取gRPC协议的资源
     */
    private Map<String, Object> readGrpcResource(RemoteMcpService service, String resourceUri, String userId) {
        // TODO: 实现gRPC资源访问
        log.warn("gRPC资源访问暂未实现: {}", resourceUri);
        throw new NexusException.BusinessException("gRPC协议暂未支持");
    }

    /**
     * 读取WebSocket协议的资源
     */
    private Map<String, Object> readWebSocketResource(RemoteMcpService service, String resourceUri, String userId) {
        // TODO: 实现WebSocket资源访问
        log.warn("WebSocket资源访问暂未实现: {}", resourceUri);
        throw new NexusException.BusinessException("WebSocket协议暂未支持");
    }

    /**
     * 构建资源访问URL
     */
    private String buildResourceUrl(RemoteMcpService service, String resourceUri) {
        String baseUrl = service.getEndpoint();
        if (!baseUrl.endsWith("/")) {
            baseUrl += "/";
        }
        
        // 根据服务类型构建不同的URL路径
        switch (service.getServiceType()) {
            case WEB_API:
                return baseUrl + "mcp/resources/read";
            case GRPC_SERVICE:
                return baseUrl + "mcp.ResourceService/ReadResource";
            case WEBSOCKET_SERVICE:
                return baseUrl + "ws/mcp";
            default:
                return baseUrl + "resources/" + resourceUri;
        }
    }

    /**
     * 添加认证头
     */
    private void addAuthHeaders(HttpHeaders headers, RemoteMcpService service, String userId) {
        Map<String, Object> authConfig = service.getAuthConfig();
        if (authConfig == null || authConfig.isEmpty()) {
            return;
        }

        String authType = (String) authConfig.get("type");
        if (authType == null) {
            return;
        }

        switch (authType.toLowerCase()) {
            case "bearer":
                String token = (String) authConfig.get("token");
                if (token != null) {
                    headers.setBearerAuth(token);
                }
                break;
                
            case "apikey":
                String apiKey = (String) authConfig.get("apiKey");
                String headerName = (String) authConfig.getOrDefault("headerName", "X-API-Key");
                if (apiKey != null) {
                    headers.set(headerName, apiKey);
                }
                break;
                
            case "basic":
                String username = (String) authConfig.get("username");
                String password = (String) authConfig.get("password");
                if (username != null && password != null) {
                    headers.setBasicAuth(username, password);
                }
                break;
        }

        // 添加用户标识
        if (userId != null) {
            headers.set("X-User-ID", userId);
        }
    }

    /**
     * 验证资源大小
     */
    private void validateResourceSize(Map<String, Object> resource) {
        try {
            String jsonString = objectMapper.writeValueAsString(resource);
            long size = jsonString.getBytes().length;

            if (size > maxResourceSize) {
                throw new NexusException.BusinessException(
                        String.format("资源大小超过限制: %d bytes (最大: %d bytes)", size, maxResourceSize));
            }
        } catch (Exception e) {
            log.warn("无法验证资源大小: {}", e.getMessage());
        }
    }

    /**
     * 尝试通过标准MCP协议读取资源
     */
    private Map<String, Object> tryReadStandardMcpResource(RemoteMcpService service, String resourceUri, String userId) {
        try {
            // 检查是否有对应的标准MCP服务
            String standardServiceName = findStandardMcpServiceName(service);
            if (standardServiceName == null) {
                return null; // 没有对应的标准MCP服务
            }

            // 检查标准MCP服务是否运行
            if (!standardMcpGateway.isStandardMcpServiceRunning(standardServiceName)) {
                log.debug("标准MCP服务未运行: {}", standardServiceName);
                return null;
            }

            // 调用标准MCP资源读取
            return standardMcpGateway.readStandardMcpResource(standardServiceName, resourceUri);

        } catch (Exception e) {
            log.warn("标准MCP协议读取资源失败，将回退到传统协议: {} - {}", resourceUri, e.getMessage());
            return null;
        }
    }

    /**
     * 查找对应的标准MCP服务名
     */
    private String findStandardMcpServiceName(RemoteMcpService service) {
        // 这里可以通过配置映射、服务标签或命名约定来查找对应的标准MCP服务
        // 简化实现：基于服务名称进行映射
        String serviceName = service.getServiceName().toLowerCase();

        // 常见的映射规则
        if (serviceName.contains("filesystem") || serviceName.contains("file")) {
            return "filesystem";
        } else if (serviceName.contains("git")) {
            return "git";
        } else if (serviceName.contains("memory")) {
            return "memory";
        } else if (serviceName.contains("time")) {
            return "time";
        } else if (serviceName.contains("fetch") || serviceName.contains("web")) {
            return "fetch";
        } else if (serviceName.contains("github")) {
            return "github";
        }

        // 如果没有匹配的映射，返回null
        return null;
    }

    /**
     * 资源访问结果
     */
    @lombok.Data
    @lombok.Builder
    public static class ResourceAccessResult {
        private boolean success;
        private Map<String, Object> content;
        private String errorMessage;
        private long accessTime;
        private LocalDateTime timestamp;
        
        public static ResourceAccessResult success(Map<String, Object> content, long accessTime) {
            return ResourceAccessResult.builder()
                    .success(true)
                    .content(content)
                    .accessTime(accessTime)
                    .timestamp(LocalDateTime.now())
                    .build();
        }
        
        public static ResourceAccessResult failure(String errorMessage, long accessTime) {
            return ResourceAccessResult.builder()
                    .success(false)
                    .errorMessage(errorMessage)
                    .accessTime(accessTime)
                    .timestamp(LocalDateTime.now())
                    .build();
        }
    }
}
