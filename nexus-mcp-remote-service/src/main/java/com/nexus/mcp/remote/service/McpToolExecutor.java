package com.nexus.mcp.remote.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nexus.common.exception.NexusException;
import com.nexus.common.mcp.protocol.*;
import com.nexus.mcp.remote.entity.RemoteMcpService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * MCP工具执行器
 * 负责执行远程MCP服务的工具调用
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class McpToolExecutor {

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final StandardMcpGateway standardMcpGateway;
    private final JsonRpcProtocolHandler protocolHandler;

    @Value("${nexus.mcp.remote.tools.default-timeout:30000}")
    private long defaultTimeout;

    @Value("${nexus.mcp.remote.tools.max-concurrent-calls:20}")
    private int maxConcurrentCalls;

    /**
     * 执行MCP工具
     */
    public Map<String, Object> executeTool(RemoteMcpService service, String toolName, 
                                         Map<String, Object> arguments, String userId) {
        log.info("执行MCP工具: {} (服务: {}, 用户: {})", toolName, service.getServiceName(), userId);

        long startTime = System.currentTimeMillis();
        
        try {
            // 1. 首先尝试标准MCP协议
            Map<String, Object> result = tryExecuteStandardMcpTool(service, toolName, arguments, userId);

            if (result != null) {
                log.debug("通过标准MCP协议执行工具成功: {}", toolName);
            } else {
                // 2. 回退到传统协议
                log.debug("标准MCP协议不可用，回退到传统协议: {}", toolName);

                switch (service.getProtocolType()) {
                    case HTTP:
                    case HTTPS:
                        result = executeHttpTool(service, toolName, arguments, userId);
                        break;
                    case GRPC:
                        result = executeGrpcTool(service, toolName, arguments, userId);
                        break;
                    case WEBSOCKET:
                        result = executeWebSocketTool(service, toolName, arguments, userId);
                        break;
                    default:
                        throw new NexusException.BusinessException("不支持的协议类型: " + service.getProtocolType());
                }
            }

            long executionTime = System.currentTimeMillis() - startTime;
            log.info("工具执行完成: {} (耗时: {}ms)", toolName, executionTime);

            // 添加执行元数据
            result.put("_metadata", Map.of(
                    "executionTime", executionTime,
                    "serviceId", service.getId(),
                    "serviceName", service.getServiceName(),
                    "timestamp", LocalDateTime.now()
            ));

            return result;

        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            log.error("工具执行失败: {} (耗时: {}ms) - {}", toolName, executionTime, e.getMessage());
            throw new NexusException.BusinessException("工具执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行HTTP协议的工具调用
     */
    private Map<String, Object> executeHttpTool(RemoteMcpService service, String toolName, 
                                               Map<String, Object> arguments, String userId) {
        try {
            // 构建请求URL
            String url = buildToolUrl(service, toolName);
            
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("method", "tools/call");
            requestBody.put("params", Map.of(
                    "name", toolName,
                    "arguments", arguments != null ? arguments : new HashMap<>()
            ));
            requestBody.put("id", System.currentTimeMillis());

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            // 添加认证信息
            addAuthHeaders(headers, service, userId);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            // 发送请求
            ResponseEntity<Map> response = restTemplate.exchange(
                    url, HttpMethod.POST, request, Map.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                
                // 检查是否有错误
                if (responseBody.containsKey("error")) {
                    Map<String, Object> error = (Map<String, Object>) responseBody.get("error");
                    throw new NexusException.BusinessException("工具执行错误: " + error.get("message"));
                }
                
                // 返回结果
                Object result = responseBody.get("result");
                if (result instanceof Map) {
                    return (Map<String, Object>) result;
                } else {
                    return Map.of("result", result);
                }
            } else {
                throw new NexusException.BusinessException("HTTP请求失败: " + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("HTTP工具调用失败: {}", e.getMessage());
            throw new NexusException.BusinessException("HTTP工具调用失败: " + e.getMessage());
        }
    }

    /**
     * 执行gRPC协议的工具调用
     */
    private Map<String, Object> executeGrpcTool(RemoteMcpService service, String toolName, 
                                              Map<String, Object> arguments, String userId) {
        // TODO: 实现gRPC工具调用
        log.warn("gRPC工具调用暂未实现: {}", toolName);
        throw new NexusException.BusinessException("gRPC协议暂未支持");
    }

    /**
     * 执行WebSocket协议的工具调用
     */
    private Map<String, Object> executeWebSocketTool(RemoteMcpService service, String toolName, 
                                                    Map<String, Object> arguments, String userId) {
        // TODO: 实现WebSocket工具调用
        log.warn("WebSocket工具调用暂未实现: {}", toolName);
        throw new NexusException.BusinessException("WebSocket协议暂未支持");
    }

    /**
     * 构建工具调用URL
     */
    private String buildToolUrl(RemoteMcpService service, String toolName) {
        String baseUrl = service.getEndpoint();
        if (!baseUrl.endsWith("/")) {
            baseUrl += "/";
        }
        
        // 根据服务类型构建不同的URL路径
        switch (service.getServiceType()) {
            case WEB_API:
                return baseUrl + "mcp/tools/call";
            case GRPC_SERVICE:
                return baseUrl + "mcp.ToolService/CallTool";
            case WEBSOCKET_SERVICE:
                return baseUrl + "ws/mcp";
            default:
                return baseUrl + "tools/" + toolName;
        }
    }

    /**
     * 添加认证头
     */
    private void addAuthHeaders(HttpHeaders headers, RemoteMcpService service, String userId) {
        Map<String, Object> authConfig = service.getAuthConfig();
        if (authConfig == null || authConfig.isEmpty()) {
            return;
        }

        String authType = (String) authConfig.get("type");
        if (authType == null) {
            return;
        }

        switch (authType.toLowerCase()) {
            case "bearer":
                String token = (String) authConfig.get("token");
                if (token != null) {
                    headers.setBearerAuth(token);
                }
                break;
                
            case "apikey":
                String apiKey = (String) authConfig.get("apiKey");
                String headerName = (String) authConfig.getOrDefault("headerName", "X-API-Key");
                if (apiKey != null) {
                    headers.set(headerName, apiKey);
                }
                break;
                
            case "basic":
                String username = (String) authConfig.get("username");
                String password = (String) authConfig.get("password");
                if (username != null && password != null) {
                    headers.setBasicAuth(username, password);
                }
                break;
        }

        // 添加用户标识
        if (userId != null) {
            headers.set("X-User-ID", userId);
        }
    }

    /**
     * 验证工具参数
     */
    private void validateToolArguments(RemoteMcpService service, String toolName, Map<String, Object> arguments) {
        // 查找工具定义
        Map<String, Object> toolDef = findToolDefinition(service, toolName);
        if (toolDef == null) {
            throw new NexusException.ValidationException("工具不存在: " + toolName);
        }

        // 验证必需参数
        Map<String, Object> inputSchema = (Map<String, Object>) toolDef.get("inputSchema");
        if (inputSchema != null) {
            Map<String, Object> properties = (Map<String, Object>) inputSchema.get("properties");
            Object required = inputSchema.get("required");
            
            if (required instanceof java.util.List && properties != null) {
                java.util.List<String> requiredFields = (java.util.List<String>) required;
                for (String field : requiredFields) {
                    if (arguments == null || !arguments.containsKey(field)) {
                        throw new NexusException.ValidationException("缺少必需参数: " + field);
                    }
                }
            }
        }
    }

    /**
     * 查找工具定义
     */
    private Map<String, Object> findToolDefinition(RemoteMcpService service, String toolName) {
        if (service.getTools() != null) {
            for (Map<String, Object> tool : service.getTools()) {
                if (toolName.equals(tool.get("name"))) {
                    return tool;
                }
            }
        }
        return null;
    }

    /**
     * 尝试通过标准MCP协议执行工具
     */
    private Map<String, Object> tryExecuteStandardMcpTool(RemoteMcpService service, String toolName,
                                                          Map<String, Object> arguments, String userId) {
        try {
            // 检查是否有对应的标准MCP服务
            String standardServiceName = findStandardMcpServiceName(service);
            if (standardServiceName == null) {
                return null; // 没有对应的标准MCP服务
            }

            // 检查标准MCP服务是否运行
            if (!standardMcpGateway.isStandardMcpServiceRunning(standardServiceName)) {
                log.debug("标准MCP服务未运行: {}", standardServiceName);
                return null;
            }

            // 调用标准MCP工具
            return standardMcpGateway.callStandardMcpTool(standardServiceName, toolName, arguments);

        } catch (Exception e) {
            log.warn("标准MCP协议执行失败，将回退到传统协议: {} - {}", toolName, e.getMessage());
            return null;
        }
    }

    /**
     * 查找对应的标准MCP服务名
     */
    private String findStandardMcpServiceName(RemoteMcpService service) {
        // 这里可以通过配置映射、服务标签或命名约定来查找对应的标准MCP服务
        // 简化实现：基于服务名称进行映射
        String serviceName = service.getServiceName().toLowerCase();

        // 常见的映射规则
        if (serviceName.contains("filesystem") || serviceName.contains("file")) {
            return "filesystem";
        } else if (serviceName.contains("git")) {
            return "git";
        } else if (serviceName.contains("memory")) {
            return "memory";
        } else if (serviceName.contains("time")) {
            return "time";
        } else if (serviceName.contains("fetch") || serviceName.contains("web")) {
            return "fetch";
        } else if (serviceName.contains("github")) {
            return "github";
        }

        // 如果没有匹配的映射，返回null
        return null;
    }

    /**
     * 工具执行结果
     */
    @lombok.Data
    @lombok.Builder
    public static class ToolExecutionResult {
        private boolean success;
        private Map<String, Object> result;
        private String errorMessage;
        private long executionTime;
        private LocalDateTime timestamp;
        
        public static ToolExecutionResult success(Map<String, Object> result, long executionTime) {
            return ToolExecutionResult.builder()
                    .success(true)
                    .result(result)
                    .executionTime(executionTime)
                    .timestamp(LocalDateTime.now())
                    .build();
        }
        
        public static ToolExecutionResult failure(String errorMessage, long executionTime) {
            return ToolExecutionResult.builder()
                    .success(false)
                    .errorMessage(errorMessage)
                    .executionTime(executionTime)
                    .timestamp(LocalDateTime.now())
                    .build();
        }
    }
}
