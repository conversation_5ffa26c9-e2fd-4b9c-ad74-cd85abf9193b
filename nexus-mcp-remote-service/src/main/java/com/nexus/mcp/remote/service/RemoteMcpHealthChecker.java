package com.nexus.mcp.remote.service;

import com.nexus.mcp.remote.entity.RemoteMcpService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.net.Socket;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 远程MCP服务健康检查器
 * 负责检查远程MCP服务的健康状态
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RemoteMcpHealthChecker {

    private final RestTemplate restTemplate = new RestTemplate();

    @Value("${nexus.mcp.remote.management.health-check-interval:60000}")
    private long healthCheckInterval;

    @Value("${nexus.mcp.remote.tools.default-timeout:30000}")
    private long healthCheckTimeout;

    /**
     * 执行健康检查
     */
    public HealthCheckResult checkHealth(RemoteMcpService service) {
        log.debug("执行健康检查: {}", service.getServiceName());

        try {
            // 根据协议类型执行不同的健康检查
            switch (service.getProtocolType()) {
                case HTTP:
                case HTTPS:
                    return checkHttpHealth(service);
                case GRPC:
                    return checkGrpcHealth(service);
                case WEBSOCKET:
                    return checkWebSocketHealth(service);
                case TCP:
                    return checkTcpHealth(service);
                default:
                    return checkBasicConnectivity(service);
            }

        } catch (Exception e) {
            log.error("健康检查异常: {}", service.getServiceName(), e);
            return HealthCheckResult.unhealthy("健康检查异常: " + e.getMessage());
        }
    }

    /**
     * 检查HTTP服务健康状态
     */
    private HealthCheckResult checkHttpHealth(RemoteMcpService service) {
        try {
            String healthUrl = buildHealthCheckUrl(service);
            
            ResponseEntity<Map> response = restTemplate.exchange(
                    healthUrl, HttpMethod.GET, null, Map.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                Map<String, Object> body = response.getBody();
                if (body != null) {
                    // 检查响应内容
                    Object status = body.get("status");
                    if ("UP".equals(status) || "HEALTHY".equals(status)) {
                        return HealthCheckResult.healthy("HTTP健康检查通过");
                    } else {
                        return HealthCheckResult.unhealthy("服务状态异常: " + status);
                    }
                } else {
                    return HealthCheckResult.healthy("HTTP连接正常");
                }
            } else {
                return HealthCheckResult.unhealthy("HTTP响应异常: " + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("HTTP健康检查失败: {}", service.getServiceName(), e);
            return HealthCheckResult.unhealthy("HTTP连接失败: " + e.getMessage());
        }
    }

    /**
     * 检查gRPC服务健康状态
     */
    private HealthCheckResult checkGrpcHealth(RemoteMcpService service) {
        try {
            // 简单的TCP连接检查
            String host = extractHost(service.getEndpoint());
            int port = extractPort(service.getEndpoint(), 9090);
            
            try (Socket socket = new Socket()) {
                socket.connect(new java.net.InetSocketAddress(host, port), 5000);
                return HealthCheckResult.healthy("gRPC连接正常");
            }

        } catch (Exception e) {
            log.error("gRPC健康检查失败: {}", service.getServiceName(), e);
            return HealthCheckResult.unhealthy("gRPC连接失败: " + e.getMessage());
        }
    }

    /**
     * 检查WebSocket服务健康状态
     */
    private HealthCheckResult checkWebSocketHealth(RemoteMcpService service) {
        try {
            // 将WebSocket URL转换为HTTP URL进行基本连接检查
            String httpUrl = service.getEndpoint()
                    .replace("ws://", "http://")
                    .replace("wss://", "https://");
            
            ResponseEntity<String> response = restTemplate.exchange(
                    httpUrl, HttpMethod.GET, null, String.class);

            if (response.getStatusCode().is2xxSuccessful() || 
                response.getStatusCode() == HttpStatus.SWITCHING_PROTOCOLS) {
                return HealthCheckResult.healthy("WebSocket服务可达");
            } else {
                return HealthCheckResult.unhealthy("WebSocket服务响应异常: " + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("WebSocket健康检查失败: {}", service.getServiceName(), e);
            return HealthCheckResult.unhealthy("WebSocket连接失败: " + e.getMessage());
        }
    }

    /**
     * 检查TCP连接健康状态
     */
    private HealthCheckResult checkTcpHealth(RemoteMcpService service) {
        try {
            String host = extractHost(service.getEndpoint());
            int port = extractPort(service.getEndpoint(), 80);
            
            try (Socket socket = new Socket()) {
                socket.connect(new java.net.InetSocketAddress(host, port), 5000);
                return HealthCheckResult.healthy("TCP连接正常");
            }

        } catch (Exception e) {
            log.error("TCP健康检查失败: {}", service.getServiceName(), e);
            return HealthCheckResult.unhealthy("TCP连接失败: " + e.getMessage());
        }
    }

    /**
     * 基本连通性检查
     */
    private HealthCheckResult checkBasicConnectivity(RemoteMcpService service) {
        try {
            String host = extractHost(service.getEndpoint());
            
            // 尝试解析主机名
            java.net.InetAddress.getByName(host);
            return HealthCheckResult.healthy("基本连通性正常");

        } catch (Exception e) {
            log.error("基本连通性检查失败: {}", service.getServiceName(), e);
            return HealthCheckResult.unhealthy("主机不可达: " + e.getMessage());
        }
    }

    /**
     * 构建健康检查URL
     */
    private String buildHealthCheckUrl(RemoteMcpService service) {
        String baseUrl = service.getEndpoint();
        if (!baseUrl.endsWith("/")) {
            baseUrl += "/";
        }

        // 检查是否有自定义健康检查配置
        Map<String, Object> healthCheck = service.getHealthCheck();
        if (healthCheck != null && healthCheck.containsKey("path")) {
            return baseUrl + healthCheck.get("path");
        }

        // 默认健康检查路径
        return baseUrl + "health";
    }

    /**
     * 从URL中提取主机名
     */
    private String extractHost(String url) {
        try {
            java.net.URL parsedUrl = new java.net.URL(url);
            return parsedUrl.getHost();
        } catch (Exception e) {
            // 如果不是完整URL，假设是主机名
            if (url.contains(":")) {
                return url.split(":")[0];
            }
            return url;
        }
    }

    /**
     * 从URL中提取端口
     */
    private int extractPort(String url, int defaultPort) {
        try {
            java.net.URL parsedUrl = new java.net.URL(url);
            int port = parsedUrl.getPort();
            return port != -1 ? port : defaultPort;
        } catch (Exception e) {
            // 如果不是完整URL，尝试从字符串中提取端口
            if (url.contains(":")) {
                String[] parts = url.split(":");
                if (parts.length > 1) {
                    try {
                        return Integer.parseInt(parts[parts.length - 1]);
                    } catch (NumberFormatException ex) {
                        return defaultPort;
                    }
                }
            }
            return defaultPort;
        }
    }

    /**
     * 健康检查结果
     */
    @lombok.Data
    @lombok.Builder
    public static class HealthCheckResult {
        private boolean healthy;
        private String message;
        private String errorMessage;
        private LocalDateTime checkTime;
        
        public static HealthCheckResult healthy(String message) {
            return HealthCheckResult.builder()
                    .healthy(true)
                    .message(message)
                    .checkTime(LocalDateTime.now())
                    .build();
        }
        
        public static HealthCheckResult unhealthy(String errorMessage) {
            return HealthCheckResult.builder()
                    .healthy(false)
                    .errorMessage(errorMessage)
                    .checkTime(LocalDateTime.now())
                    .build();
        }
    }
}
