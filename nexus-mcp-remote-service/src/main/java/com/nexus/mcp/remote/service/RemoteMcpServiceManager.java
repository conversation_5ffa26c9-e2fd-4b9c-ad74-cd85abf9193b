package com.nexus.mcp.remote.service;

import com.nexus.common.constants.CacheNames;
import com.nexus.common.exception.NexusException;
import com.nexus.common.mcp.protocol.McpDataModels;
import com.nexus.common.model.MCPServiceMetadata;
import com.nexus.mcp.remote.entity.RemoteMcpService;
import com.nexus.mcp.remote.repository.RemoteMcpServiceRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 远程MCP服务管理器
 * 负责远程MCP服务的注册、管理和监控
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RemoteMcpServiceManager {

    private final RemoteMcpServiceRepository serviceRepository;

    @Autowired
    private ApplicationContext applicationContext;
    private final RemoteMcpHealthChecker healthChecker;

    /**
     * 注册远程MCP服务
     */
    @Transactional
    @CacheEvict(value = CacheNames.MCP_SERVICES, allEntries = true)
    public RemoteMcpService registerService(MCPServiceMetadata metadata) {
        log.info("注册远程MCP服务: {}", metadata.getServiceName());

        // 检查服务是否已存在
        if (serviceRepository.existsByServiceName(metadata.getServiceName())) {
            throw new NexusException.BusinessException("服务已存在: " + metadata.getServiceName());
        }

        // 检查端点是否已存在
        if (serviceRepository.existsByEndpoint(metadata.getEndpoint())) {
            throw new NexusException.BusinessException("端点已存在: " + metadata.getEndpoint());
        }

        // 创建远程服务实体
        RemoteMcpService service = RemoteMcpService.builder()
                .serviceName(metadata.getServiceName())
                .displayName(metadata.getDisplayName())
                .description(metadata.getDescription())
                .serviceType(determineServiceType(metadata))
                .status(RemoteMcpService.ServiceStatus.INACTIVE)
                .version(metadata.getVersion())
                .endpoint(metadata.getEndpoint())
                .protocolType(determineProtocolType(metadata.getEndpoint()))
                .priority(0)
                .weight(100)
                .cacheEnabled(true)
                .cacheTtl(300)
                .maxConcurrentRequests(10)
                .requestTimeout(30000)
                .maxRetries(3)
                .retryDelay(1000)
                .build();

        // 设置元数据
        if (metadata.getMetadata() != null) {
            metadata.getMetadata().forEach(service::addMetadata);
        }

        // 设置配置参数
        service.addConfigParam("protocol", metadata.getProtocolType());
        service.addConfigParam("toolCount", metadata.getToolCount());

        service = serviceRepository.save(service);
        log.info("远程MCP服务注册成功: {} (ID: {})", service.getServiceName(), service.getId());

        return service;
    }

    /**
     * 更新服务状态
     */
    @Transactional
    @CacheEvict(value = {CacheNames.MCP_SERVICES, CacheNames.MCP_SERVICE_STATUS}, allEntries = true)
    public void updateServiceStatus(Long serviceId, RemoteMcpService.ServiceStatus status) {
        log.info("更新服务状态: ID {}, 状态 {}", serviceId, status);

        RemoteMcpService service = getServiceById(serviceId);
        service.setStatus(status);
        serviceRepository.save(service);
    }

    /**
     * 激活服务
     */
    @Transactional
    public void activateService(Long serviceId) {
        log.info("激活远程MCP服务: {}", serviceId);

        RemoteMcpService service = getServiceById(serviceId);
        
        // 执行健康检查
        RemoteMcpHealthChecker.HealthCheckResult healthResult = healthChecker.checkHealth(service);
        
        if (healthResult.isHealthy()) {
            service.setStatus(RemoteMcpService.ServiceStatus.ACTIVE);
            service.updateHealthStatus(RemoteMcpService.HealthStatus.HEALTHY, null);
            serviceRepository.save(service);
            log.info("远程MCP服务激活成功: {}", service.getServiceName());
        } else {
            service.setStatus(RemoteMcpService.ServiceStatus.ERROR);
            service.updateHealthStatus(RemoteMcpService.HealthStatus.UNHEALTHY, healthResult.getErrorMessage());
            serviceRepository.save(service);
            throw new NexusException.BusinessException("服务激活失败: " + healthResult.getErrorMessage());
        }
    }

    /**
     * 停用服务
     */
    @Transactional
    @CacheEvict(value = {CacheNames.MCP_SERVICES, CacheNames.MCP_SERVICE_STATUS}, allEntries = true)
    public void deactivateService(Long serviceId) {
        log.info("停用远程MCP服务: {}", serviceId);

        RemoteMcpService service = getServiceById(serviceId);
        service.setStatus(RemoteMcpService.ServiceStatus.INACTIVE);
        serviceRepository.save(service);
    }

    /**
     * 注销服务
     */
    @Transactional
    @CacheEvict(value = CacheNames.MCP_SERVICES, allEntries = true)
    public void unregisterService(Long serviceId) {
        log.info("注销远程MCP服务: {}", serviceId);

        RemoteMcpService service = getServiceById(serviceId);
        serviceRepository.delete(service);
        log.info("远程MCP服务注销成功: {}", service.getServiceName());
    }

    /**
     * 获取服务详情
     */
    @Cacheable(value = CacheNames.MCP_SERVICES, key = "#serviceId")
    public RemoteMcpService getServiceById(Long serviceId) {
        return serviceRepository.findById(serviceId)
                .orElseThrow(() -> new NexusException.ResourceNotFoundException("远程MCP服务不存在: " + serviceId));
    }

    /**
     * 根据服务名称获取服务
     */
    @Cacheable(value = CacheNames.MCP_SERVICES, key = "#serviceName")
    public Optional<RemoteMcpService> getServiceByName(String serviceName) {
        return serviceRepository.findByServiceName(serviceName);
    }

    /**
     * 获取可用服务列表
     */
    @Cacheable(value = CacheNames.MCP_SERVICE_STATUS, key = "'available'")
    public List<RemoteMcpService> getAvailableServices() {
        return serviceRepository.findAvailableServices();
    }

    /**
     * 执行健康检查
     */
    @Transactional
    public void performHealthCheck(Long serviceId) {
        RemoteMcpService service = getServiceById(serviceId);
        
        if (!service.getStatus().equals(RemoteMcpService.ServiceStatus.ACTIVE)) {
            return;
        }

        log.debug("执行健康检查: {}", service.getServiceName());
        
        RemoteMcpHealthChecker.HealthCheckResult result = healthChecker.checkHealth(service);
        
        service.updateHealthStatus(
                result.isHealthy() ? RemoteMcpService.HealthStatus.HEALTHY : RemoteMcpService.HealthStatus.UNHEALTHY,
                result.getErrorMessage()
        );
        
        serviceRepository.save(service);
        
        // 如果健康检查失败，将服务标记为错误状态
        if (!result.isHealthy()) {
            log.warn("服务健康检查失败，标记为错误状态: {}", service.getServiceName());
            service.setStatus(RemoteMcpService.ServiceStatus.ERROR);
            serviceRepository.save(service);
        }
    }

    /**
     * 批量健康检查
     */
    public void performBatchHealthCheck() {
        LocalDateTime threshold = LocalDateTime.now().minusMinutes(5); // 5分钟前
        List<RemoteMcpService> services = serviceRepository.findServicesNeedingHealthCheck(threshold);
        
        log.debug("执行批量健康检查，服务数量: {}", services.size());
        
        services.forEach(service -> {
            try {
                performHealthCheck(service.getId());
            } catch (Exception e) {
                log.error("健康检查失败: {}", service.getServiceName(), e);
            }
        });
    }

    /**
     * 确定服务类型
     */
    private RemoteMcpService.RemoteServiceType determineServiceType(MCPServiceMetadata metadata) {
        String serviceName = metadata.getServiceName().toLowerCase();
        String endpoint = metadata.getEndpoint().toLowerCase();
        
        if (endpoint.contains("grpc") || metadata.getProtocolType().contains("grpc")) {
            return RemoteMcpService.RemoteServiceType.GRPC_SERVICE;
        } else if (endpoint.contains("ws://") || endpoint.contains("wss://")) {
            return RemoteMcpService.RemoteServiceType.WEBSOCKET_SERVICE;
        } else if (serviceName.contains("database") || serviceName.contains("db")) {
            return RemoteMcpService.RemoteServiceType.DATABASE_SERVICE;
        } else if (serviceName.contains("file") || serviceName.contains("storage")) {
            return RemoteMcpService.RemoteServiceType.FILE_SERVICE;
        } else if (serviceName.contains("search") || serviceName.contains("index")) {
            return RemoteMcpService.RemoteServiceType.SEARCH_SERVICE;
        } else if (serviceName.contains("ai") || serviceName.contains("ml")) {
            return RemoteMcpService.RemoteServiceType.AI_SERVICE;
        } else {
            return RemoteMcpService.RemoteServiceType.WEB_API;
        }
    }

    /**
     * 获取所有服务
     */
    @Cacheable(value = CacheNames.MCP_SERVICES, key = "'all'")
    public List<RemoteMcpService> getAllServices() {
        log.debug("获取所有远程MCP服务");
        return serviceRepository.findAll();
    }

    /**
     * 删除服务
     */
    @Transactional
    @CacheEvict(value = {CacheNames.MCP_SERVICES, CacheNames.MCP_SERVICE_STATUS}, allEntries = true)
    public void deleteService(Long serviceId) {
        log.info("删除远程MCP服务: {}", serviceId);

        RemoteMcpService service = getServiceById(serviceId);
        serviceRepository.delete(service);

        log.info("远程MCP服务删除成功: {} (ID: {})", service.getServiceName(), serviceId);
    }

    /**
     * 确定协议类型
     */
    private RemoteMcpService.ProtocolType determineProtocolType(String endpoint) {
        if (endpoint.startsWith("https://")) {
            return RemoteMcpService.ProtocolType.HTTPS;
        } else if (endpoint.startsWith("http://")) {
            return RemoteMcpService.ProtocolType.HTTP;
        } else if (endpoint.startsWith("grpc://") || endpoint.contains(":9")) {
            return RemoteMcpService.ProtocolType.GRPC;
        } else if (endpoint.startsWith("ws://") || endpoint.startsWith("wss://")) {
            return RemoteMcpService.ProtocolType.WEBSOCKET;
        } else {
            return RemoteMcpService.ProtocolType.HTTP;
        }
    }

    /**
     * 获取服务统计信息
     */
    public ServiceStatistics getServiceStatistics() {
        long totalServices = serviceRepository.countTotalServices();
        long availableServices = serviceRepository.countAvailableServices();
        
        return ServiceStatistics.builder()
                .totalServices(totalServices)
                .availableServices(availableServices)
                .unavailableServices(totalServices - availableServices)
                .build();
    }

    /**
     * 检查服务名是否已存在
     */
    public boolean existsByServiceName(String serviceName) {
        return serviceRepository.existsByServiceName(serviceName);
    }

    /**
     * 根据服务名更新服务状态
     */
    @Transactional
    @CacheEvict(value = CacheNames.MCP_SERVICES, allEntries = true)
    public void updateServiceStatusByName(String serviceName, RemoteMcpService.ServiceStatus status) {
        log.info("更新服务状态: {} -> {}", serviceName, status);

        Optional<RemoteMcpService> serviceOpt = serviceRepository.findByServiceName(serviceName);
        if (serviceOpt.isPresent()) {
            RemoteMcpService service = serviceOpt.get();
            service.setStatus(status);
            service.setLastHealthCheckAt(LocalDateTime.now());
            serviceRepository.save(service);
            log.info("服务状态更新成功: {} -> {}", serviceName, status);
        } else {
            log.warn("未找到服务，无法更新状态: {}", serviceName);
            throw new NexusException.ResourceNotFoundException("服务不存在: " + serviceName);
        }
    }

    /**
     * 注册通过命令启动的服务
     */
    @Transactional
    @CacheEvict(value = CacheNames.MCP_SERVICES, allEntries = true)
    public RemoteMcpService registerCommandStartedService(MCPServiceMetadata metadata) {
        log.info("注册命令启动的MCP服务: {}", metadata.getServiceName());

        // 检查服务是否已存在，如果存在则更新
        Optional<RemoteMcpService> existingService = serviceRepository.findByServiceName(metadata.getServiceName());
        if (existingService.isPresent()) {
            RemoteMcpService service = existingService.get();
            service.setStatus(RemoteMcpService.ServiceStatus.ACTIVE);
            service.setLastHealthCheckAt(LocalDateTime.now());

            // 更新元数据
            if (metadata.getMetadata() != null) {
                metadata.getMetadata().forEach(service::addMetadata);
            }

            RemoteMcpService savedService = serviceRepository.save(service);
            log.info("更新现有命令启动服务: {} (ID: {})", savedService.getServiceName(), savedService.getId());
            return savedService;
        }

        // 创建新的远程服务实体
        RemoteMcpService service = RemoteMcpService.builder()
                .serviceName(metadata.getServiceName())
                .displayName(metadata.getDisplayName())
                .description(metadata.getDescription())
                .serviceType(RemoteMcpService.RemoteServiceType.CUSTOM)
                .status(RemoteMcpService.ServiceStatus.ACTIVE)
                .version(metadata.getVersion())
                .endpoint(metadata.getEndpoint())
                .protocolType(RemoteMcpService.ProtocolType.TCP)
                .priority(0)
                .weight(100)
                .cacheEnabled(true)
                .cacheTtl(300)
                .maxConcurrentRequests(10)
                .requestTimeout(30000)
                .maxRetries(3)
                .retryDelay(1000)
                .lastHealthCheckAt(LocalDateTime.now())
                .build();

        // 设置元数据
        if (metadata.getMetadata() != null) {
            metadata.getMetadata().forEach(service::addMetadata);
        }

        RemoteMcpService savedService = serviceRepository.save(service);
        log.info("命令启动服务注册成功: {} (ID: {})", savedService.getServiceName(), savedService.getId());

        return savedService;
    }

    /**
     * 更新服务的工具和资源信息
     */
    @Transactional
    @CacheEvict(value = CacheNames.MCP_SERVICES, allEntries = true)
    public void updateServiceToolsAndResources(RemoteMcpService service) {
        log.info("更新服务工具和资源信息: {}", service.getServiceName());

        try {
            serviceRepository.save(service);
            log.info("服务工具和资源信息更新成功: {} (工具数: {}, 资源数: {})",
                    service.getServiceName(),
                    service.getTools() != null ? service.getTools().size() : 0,
                    service.getResources() != null ? service.getResources().size() : 0);
        } catch (Exception e) {
            log.error("更新服务工具和资源信息失败: {} - {}", service.getServiceName(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 手动同步服务的工具和资源信息
     */
    @Transactional
    @CacheEvict(value = CacheNames.MCP_SERVICES, allEntries = true)
    public void syncServiceToolsAndResources(Long serviceId) {
        log.info("开始同步服务工具和资源信息: serviceId={}", serviceId);

        RemoteMcpService service = getServiceById(serviceId);
        String serviceName = service.getServiceName();

        // 检查服务是否是通过命令启动的
        String startedByCommand = (String) service.getMetadata().get("startedByCommand");
        if (!"true".equals(startedByCommand)) {
            throw new RuntimeException("只能同步通过命令启动的服务工具信息");
        }

        try {
            // 这里需要注入StandardMcpGateway来获取工具信息
            // 由于循环依赖问题，我们通过ApplicationContext获取
            StandardMcpGateway gateway = applicationContext.getBean(StandardMcpGateway.class);

            // 获取工具信息
            List<McpDataModels.Tool> tools = gateway.listStandardMcpTools(serviceName);
            if (tools != null && !tools.isEmpty()) {
                List<Map<String, Object>> toolMaps = tools.stream()
                        .map(this::convertToolToMap)
                        .collect(Collectors.toList());
                service.setTools(toolMaps);
                log.info("获取到 {} 个工具: {}", tools.size(), serviceName);
            }

            // 获取资源信息（可能失败，不影响工具信息保存）
            try {
                List<McpDataModels.Resource> resources = gateway.listStandardMcpResources(serviceName);
                if (resources != null && !resources.isEmpty()) {
                    List<Map<String, Object>> resourceMaps = resources.stream()
                            .map(this::convertResourceToMap)
                            .collect(Collectors.toList());
                    service.setResources(resourceMaps);
                    log.info("获取到 {} 个资源: {}", resources.size(), serviceName);
                } else {
                    log.info("服务没有资源或资源列表为空: {}", serviceName);
                }
            } catch (Exception e) {
                log.warn("获取服务资源信息失败，但不影响工具信息保存: {} - {}", serviceName, e.getMessage());
                // 设置空的资源列表
                service.setResources(new ArrayList<>());
            }

            // 保存更新
            serviceRepository.save(service);
            log.info("服务工具和资源信息同步成功: {} (工具数: {}, 资源数: {})",
                    serviceName,
                    service.getTools() != null ? service.getTools().size() : 0,
                    service.getResources() != null ? service.getResources().size() : 0);

        } catch (Exception e) {
            log.error("同步服务工具和资源信息失败: {} - {}", serviceName, e.getMessage(), e);
            throw new RuntimeException("同步服务工具信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将工具转换为Map格式
     */
    private Map<String, Object> convertToolToMap(McpDataModels.Tool tool) {
        Map<String, Object> toolMap = new HashMap<>();
        toolMap.put("name", tool.getName());
        toolMap.put("description", tool.getDescription());
        toolMap.put("inputSchema", tool.getInputSchema());
        return toolMap;
    }

    /**
     * 将资源转换为Map格式
     */
    private Map<String, Object> convertResourceToMap(McpDataModels.Resource resource) {
        Map<String, Object> resourceMap = new HashMap<>();
        resourceMap.put("uri", resource.getUri());
        resourceMap.put("name", resource.getName());
        resourceMap.put("description", resource.getDescription());
        resourceMap.put("mimeType", resource.getMimeType());
        return resourceMap;
    }

    /**
     * 服务统计信息
     */
    @lombok.Data
    @lombok.Builder
    public static class ServiceStatistics {
        private long totalServices;
        private long availableServices;
        private long unavailableServices;

        public double getAvailabilityPercentage() {
            return totalServices > 0 ? (double) availableServices / totalServices * 100.0 : 0.0;
        }
    }
}
