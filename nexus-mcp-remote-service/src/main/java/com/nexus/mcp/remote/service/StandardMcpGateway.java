package com.nexus.mcp.remote.service;

import com.nexus.common.mcp.notification.McpNotificationManager;
import com.nexus.common.mcp.notification.ServiceStatus;
import com.nexus.common.mcp.process.McpServiceConfig;
import com.nexus.common.mcp.process.McpServiceProcessManager;
import com.nexus.common.mcp.process.ManagedMcpProcess;
import com.nexus.common.mcp.protocol.*;
import com.nexus.common.mcp.protocol.McpDataModels.*;
import com.nexus.common.mcp.transport.StdioConnection;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 标准MCP协议网关
 * 
 * 在现有远程MCP服务中集成标准MCP协议支持
 * 提供标准MCP服务的启动、管理和通信功能
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StandardMcpGateway {
    
    private final McpServiceProcessManager processManager;
    private final McpInitializationHandler initializationHandler;
    private final JsonRpcProtocolHandler protocolHandler;
    private final McpNotificationManager notificationManager;
    
    // 服务名称到进程的映射
    private final Map<String, ManagedMcpProcess> serviceProcessMap = new ConcurrentHashMap<>();
    
    // 服务名称到连接的映射
    private final Map<String, McpConnectionWrapper> serviceConnectionMap = new ConcurrentHashMap<>();
    
    /**
     * 启动标准MCP服务
     */
    public boolean startStandardMcpService(String serviceName, McpServiceConfig config) {
        log.info("Starting standard MCP service: {}", serviceName);
        
        try {
            // 检查服务是否已经运行
            if (serviceProcessMap.containsKey(serviceName)) {
                log.warn("Standard MCP service already running: {}", serviceName);
                return false;
            }

            // 发送启动通知
            notificationManager.notifyServiceStatusChanged(serviceName, ServiceStatus.STARTING, "正在启动标准MCP服务");

            // 启动MCP服务进程
            ManagedMcpProcess process = processManager.startMcpService(config);
            serviceProcessMap.put(serviceName, process);
            
            // 建立连接并初始化
            StdioConnection connection = process.getConnection();
            McpConnectionWrapper wrapper = new McpConnectionWrapper(connection);
            
            // 执行客户端初始化
            ClientCapabilities clientCapabilities = ClientCapabilities.builder()
                    .roots(RootsCapability.builder().listChanged(true).build())
                    .sampling(SamplingCapability.builder().build())
                    .build();
            
            ClientInfo clientInfo = ClientInfo.builder()
                    .name("Nexus MCP Remote Service")
                    .version("1.0.0")
                    .build();
            
            log.debug("Starting MCP client initialization for service: {}", serviceName);
            InitializationResult result = initializationHandler.performClientInitialization(
                    wrapper, clientCapabilities, clientInfo);

            log.debug("MCP initialization result for service {}: success={}, error={}",
                    serviceName, result.isSuccess(), result.getErrorMessage());

            if (!result.isSuccess()) {
                log.error("Failed to initialize standard MCP service: {}", result.getErrorMessage());
                processManager.stopMcpService(process.getServiceId());
                serviceProcessMap.remove(serviceName);

                // 发送失败通知
                notificationManager.notifyServiceStatusChanged(serviceName, ServiceStatus.FAILED,
                        "服务初始化失败: " + result.getErrorMessage());
                return false;
            }

            serviceConnectionMap.put(serviceName, wrapper);
            log.debug("Successfully stored connection for service: {}. Total connections: {}",
                    serviceName, serviceConnectionMap.size());

            // 注册连接到通知管理器
            notificationManager.registerConnection(serviceName, connection);

            // 等待服务器完全准备好处理请求
            try {
                Thread.sleep(1000); // 额外等待1秒确保服务器准备就绪
                log.debug("Completed post-connection wait period for service: {}", serviceName);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("Interrupted while waiting for service readiness: {}", serviceName);
            }

            // 发送运行通知
            notificationManager.notifyServiceStatusChanged(serviceName, ServiceStatus.RUNNING, "标准MCP服务启动成功");

            log.info("Standard MCP service started successfully: {}", serviceName);
            return true;
            
        } catch (Exception e) {
            log.error("Failed to start standard MCP service: {}", serviceName, e);
            serviceProcessMap.remove(serviceName);

            // 发送失败通知
            notificationManager.notifyServiceStatusChanged(serviceName, ServiceStatus.FAILED,
                    "服务启动失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 停止标准MCP服务
     */
    public boolean stopStandardMcpService(String serviceName) {
        log.info("Stopping standard MCP service: {}", serviceName);
        
        try {
            // 发送停止通知
            notificationManager.notifyServiceStatusChanged(serviceName, ServiceStatus.STOPPING, "正在停止标准MCP服务");

            // 关闭连接
            McpConnectionWrapper connection = serviceConnectionMap.remove(serviceName);
            if (connection != null) {
                connection.close();
            }

            // 移除通知管理器中的连接
            notificationManager.unregisterConnection(serviceName);

            // 停止进程
            ManagedMcpProcess process = serviceProcessMap.remove(serviceName);
            if (process != null) {
                processManager.stopMcpService(process.getServiceId());

                // 发送停止成功通知
                notificationManager.notifyServiceStatusChanged(serviceName, ServiceStatus.STOPPED, "标准MCP服务已停止");
                return true;
            }

            log.warn("Standard MCP service not found: {}", serviceName);
            return false;
            
        } catch (Exception e) {
            log.error("Failed to stop standard MCP service: {}", serviceName, e);
            return false;
        }
    }
    
    /**
     * 调用标准MCP工具
     */
    public Map<String, Object> callStandardMcpTool(String serviceName, String toolName, Map<String, Object> arguments) {
        log.debug("Calling standard MCP tool: {} on service: {}", toolName, serviceName);
        
        McpConnectionWrapper connection = serviceConnectionMap.get(serviceName);
        if (connection == null) {
            throw new RuntimeException("Standard MCP service not found or not connected: " + serviceName);
        }
        
        try {
            // 构建工具调用请求
            JsonRpcRequest request = protocolHandler.createToolCallRequest(toolName, arguments);
            
            // 发送请求并等待响应
            JsonRpcMessage response = connection.sendRequest(request, 30000);
            
            if (response instanceof JsonRpcErrorResponse) {
                JsonRpcErrorResponse errorResponse = (JsonRpcErrorResponse) response;
                throw new RuntimeException("Tool call failed: " + errorResponse.getError().getMessage());
            }
            
            if (response instanceof JsonRpcResponse) {
                JsonRpcResponse successResponse = (JsonRpcResponse) response;
                return convertToolResult(successResponse.getResult());
            }
            
            throw new RuntimeException("Unexpected response type: " + response.getClass().getSimpleName());
            
        } catch (Exception e) {
            log.error("Failed to call standard MCP tool: {} on service: {}", toolName, serviceName, e);
            throw new RuntimeException("Failed to call standard MCP tool", e);
        }
    }
    
    /**
     * 读取标准MCP资源
     */
    public Map<String, Object> readStandardMcpResource(String serviceName, String resourceUri) {
        log.debug("Reading standard MCP resource: {} from service: {}", resourceUri, serviceName);
        
        McpConnectionWrapper connection = serviceConnectionMap.get(serviceName);
        if (connection == null) {
            throw new RuntimeException("Standard MCP service not found or not connected: " + serviceName);
        }
        
        try {
            // 构建资源读取请求
            JsonRpcRequest request = protocolHandler.createResourceReadRequest(resourceUri);
            
            // 发送请求并等待响应
            JsonRpcMessage response = connection.sendRequest(request, 30000);
            
            if (response instanceof JsonRpcErrorResponse) {
                JsonRpcErrorResponse errorResponse = (JsonRpcErrorResponse) response;
                throw new RuntimeException("Resource read failed: " + errorResponse.getError().getMessage());
            }
            
            if (response instanceof JsonRpcResponse) {
                JsonRpcResponse successResponse = (JsonRpcResponse) response;
                return convertResourceResult(successResponse.getResult());
            }
            
            throw new RuntimeException("Unexpected response type: " + response.getClass().getSimpleName());
            
        } catch (Exception e) {
            log.error("Failed to read standard MCP resource: {} from service: {}", resourceUri, serviceName, e);
            throw new RuntimeException("Failed to read standard MCP resource", e);
        }
    }
    
    /**
     * 检查标准MCP服务状态
     */
    public boolean isStandardMcpServiceRunning(String serviceName) {
        ManagedMcpProcess process = serviceProcessMap.get(serviceName);
        McpConnectionWrapper connection = serviceConnectionMap.get(serviceName);
        
        return process != null && process.isAlive() && 
               connection != null && connection.isAlive();
    }
    
    /**
     * 获取所有运行中的标准MCP服务
     */
    public Map<String, ManagedMcpProcess> getAllStandardMcpServices() {
        return new HashMap<>(serviceProcessMap);
    }

    /**
     * 获取标准MCP服务器信息
     */
    public ServerInfo getStandardMcpServerInfo(String serviceName) {
        log.debug("Getting standard MCP server info for service: {}", serviceName);

        McpConnectionWrapper connection = serviceConnectionMap.get(serviceName);
        if (connection == null) {
            throw new RuntimeException("Standard MCP service not found or not connected: " + serviceName);
        }

        try {
            // 发送initialize请求获取服务器信息
            JsonRpcRequest request = JsonRpcRequest.createInitializeRequest(
                    "init-" + System.currentTimeMillis(),
                    Map.of(
                            "protocolVersion", "2024-11-05",
                            "capabilities", Map.of(),
                            "clientInfo", Map.of("name", "Nexus MCP Client", "version", "1.0.0")
                    )
            );

            JsonRpcMessage response = connection.sendRequest(request, 5000);
            if (response instanceof JsonRpcResponse) {
                JsonRpcResponse jsonResponse = (JsonRpcResponse) response;
                if (jsonResponse.getResult() != null) {
                    Map<String, Object> result = (Map<String, Object>) jsonResponse.getResult();
                    Map<String, Object> serverInfo = (Map<String, Object>) result.get("serverInfo");

                    return ServerInfo.builder()
                            .name((String) serverInfo.get("name"))
                            .version((String) serverInfo.get("version"))
                            .build();
                }
            }

            throw new RuntimeException("Failed to get server info");

        } catch (Exception e) {
            log.error("Error getting server info for service: {}", serviceName, e);
            throw new RuntimeException("Failed to get server info: " + e.getMessage());
        }
    }

    /**
     * 列出标准MCP工具
     */
    public List<McpDataModels.Tool> listStandardMcpTools(String serviceName) {
        log.debug("Listing standard MCP tools for service: {}", serviceName);

        McpConnectionWrapper connection = serviceConnectionMap.get(serviceName);
        if (connection == null) {
            log.error("Standard MCP service not found or not connected: {}. Available connections: {}",
                    serviceName, serviceConnectionMap.keySet());
            throw new RuntimeException("Standard MCP service not found or not connected: " + serviceName);
        }

        log.debug("Found connection for service: {}, connection alive: {}", serviceName, connection.isAlive());

        try {
            // 检查连接是否活跃
            if (!connection.isAlive()) {
                log.warn("Connection is not alive for service: {}", serviceName);
                return new ArrayList<>();
            }

            JsonRpcRequest request = JsonRpcRequest.createRequest("tools/list", null);
            log.debug("Sending tools/list request to service: {}", serviceName);

            // 增加超时时间到15秒
            JsonRpcMessage response = connection.sendRequest(request, 15000);

            if (response instanceof JsonRpcResponse) {
                JsonRpcResponse jsonResponse = (JsonRpcResponse) response;
                if (jsonResponse.getResult() != null) {
                    Map<String, Object> result = (Map<String, Object>) jsonResponse.getResult();
                    List<Map<String, Object>> tools = (List<Map<String, Object>>) result.get("tools");

                    return tools.stream()
                            .map(toolMap -> McpDataModels.Tool.builder()
                                    .name((String) toolMap.get("name"))
                                    .description((String) toolMap.get("description"))
                                    .inputSchema((Map<String, Object>) toolMap.get("inputSchema"))
                                    .build())
                            .collect(Collectors.toList());
                }
            }

            return new ArrayList<>();

        } catch (Exception e) {
            log.warn("Failed to get tools from service {}: {}", serviceName, e.getMessage());
            return new ArrayList<>(); // 返回空列表而不是抛出异常
        }
    }

    /**
     * 列出标准MCP资源
     */
    public List<McpDataModels.Resource> listStandardMcpResources(String serviceName) {
        log.debug("Listing standard MCP resources for service: {}", serviceName);

        McpConnectionWrapper connection = serviceConnectionMap.get(serviceName);
        if (connection == null) {
            throw new RuntimeException("Standard MCP service not found or not connected: " + serviceName);
        }

        try {
            // 检查连接是否活跃
            if (!connection.isAlive()) {
                log.warn("Connection is not alive for service: {}", serviceName);
                return new ArrayList<>();
            }

            JsonRpcRequest request = JsonRpcRequest.createRequest("resources/list", null);
            log.debug("Sending resources/list request to service: {}", serviceName);

            // 增加超时时间到15秒
            JsonRpcMessage response = connection.sendRequest(request, 15000);

            if (response instanceof JsonRpcResponse) {
                JsonRpcResponse jsonResponse = (JsonRpcResponse) response;
                if (jsonResponse.getResult() != null) {
                    Map<String, Object> result = (Map<String, Object>) jsonResponse.getResult();
                    List<Map<String, Object>> resources = (List<Map<String, Object>>) result.get("resources");

                    return resources.stream()
                            .map(resourceMap -> McpDataModels.Resource.builder()
                                    .uri((String) resourceMap.get("uri"))
                                    .name((String) resourceMap.get("name"))
                                    .description((String) resourceMap.get("description"))
                                    .mimeType((String) resourceMap.get("mimeType"))
                                    .build())
                            .collect(Collectors.toList());
                }
            }

            return new ArrayList<>();

        } catch (Exception e) {
            log.error("Error listing resources for service: {}", serviceName, e);
            throw new RuntimeException("Failed to list resources: " + e.getMessage());
        }
    }
    
    /**
     * 转换工具调用结果
     */
    private Map<String, Object> convertToolResult(Object result) {
        // 这里需要根据实际的MCP工具结果格式进行转换
        // 简化实现，返回包装后的结果
        Map<String, Object> toolResult = new HashMap<>();
        toolResult.put("success", true);
        toolResult.put("result", result);
        toolResult.put("timestamp", System.currentTimeMillis());
        return toolResult;
    }
    
    /**
     * 转换资源读取结果
     */
    private Map<String, Object> convertResourceResult(Object result) {
        // 这里需要根据实际的MCP资源结果格式进行转换
        // 简化实现，返回包装后的结果
        Map<String, Object> resourceResult = new HashMap<>();
        resourceResult.put("success", true);
        resourceResult.put("content", result);
        resourceResult.put("timestamp", System.currentTimeMillis());
        return resourceResult;
    }
    
    /**
     * MCP连接包装器
     * 实现McpConnection接口，适配StdioConnection
     */
    private static class McpConnectionWrapper implements McpDataModels.McpConnection {
        private final StdioConnection stdioConnection;
        
        public McpConnectionWrapper(StdioConnection stdioConnection) {
            this.stdioConnection = stdioConnection;
        }
        
        @Override
        public JsonRpcMessage sendRequest(JsonRpcRequest request, long timeoutMs) {
            return stdioConnection.sendRequest(request, timeoutMs);
        }
        
        @Override
        public void sendNotification(JsonRpcNotification notification) {
            stdioConnection.sendMessage(notification);
        }
        
        @Override
        public boolean isAlive() {
            return stdioConnection.isAlive();
        }
        
        @Override
        public void close() {
            stdioConnection.close();
        }
    }
}
