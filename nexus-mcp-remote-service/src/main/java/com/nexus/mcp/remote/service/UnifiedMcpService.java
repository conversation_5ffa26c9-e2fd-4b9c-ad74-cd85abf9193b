package com.nexus.mcp.remote.service;

import com.nexus.common.constants.CacheNames;
import com.nexus.common.event.MCPTaskCreatedEvent;
import com.nexus.common.exception.NexusException;
import com.nexus.common.mcp.process.ManagedMcpProcess;
import com.nexus.common.mcp.protocol.McpDataModels;
import com.nexus.common.model.MCPAsyncTask;
import com.nexus.common.model.MCPTaskResult;
import com.nexus.common.service.RocketMQProducerService;
import com.nexus.mcp.remote.entity.RemoteMcpService;
import com.nexus.mcp.remote.repository.RemoteMcpServiceRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 统一MCP服务
 * 提供统一的MCP接口，整合所有远程MCP服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UnifiedMcpService {

    private final RemoteMcpServiceRepository serviceRepository;
    private final RemoteMcpServiceManager serviceManager;
    private final McpToolExecutor toolExecutor;
    private final McpResourceAccessor resourceAccessor;
    private final McpAsyncTaskProcessor asyncTaskProcessor;
    private final RocketMQProducerService messageProducerService;

    // 新增：标准MCP协议支持
    private final StandardMcpGateway standardMcpGateway;

    /**
     * 获取所有可用的工具列表
     */
    @Cacheable(value = CacheNames.MCP_SERVICE_TOOLS, key = "'all'")
    public List<Map<String, Object>> listTools() {
        log.debug("获取所有可用工具列表");

        List<Map<String, Object>> allTools = new ArrayList<>();

        // 1. 从数据库中的远程MCP服务获取工具
        List<RemoteMcpService> availableServices = serviceRepository.findAvailableServices();
        for (RemoteMcpService service : availableServices) {
            if (service.getTools() != null) {
                for (Map<String, Object> tool : service.getTools()) {
                    // 添加服务信息到工具元数据
                    Map<String, Object> enrichedTool = new HashMap<>(tool);
                    enrichedTool.put("serviceId", service.getId());
                    enrichedTool.put("serviceName", service.getServiceName());
                    enrichedTool.put("serviceType", service.getServiceType());
                    enrichedTool.put("source", "database");
                    allTools.add(enrichedTool);
                }
            }
        }

        // 2. 从运行中的标准MCP服务获取工具
        try {
            Map<String, ManagedMcpProcess> runningServices = standardMcpGateway.getAllStandardMcpServices();
            log.debug("Found {} running standard MCP services", runningServices.size());

            for (Map.Entry<String, ManagedMcpProcess> entry : runningServices.entrySet()) {
                String serviceName = entry.getKey();
                log.debug("Checking service: {}", serviceName);

                if (standardMcpGateway.isStandardMcpServiceRunning(serviceName)) {
                    log.debug("Service {} is running, attempting to get tools", serviceName);
                    try {
                        List<McpDataModels.Tool> tools = standardMcpGateway.listStandardMcpTools(serviceName);
                        log.debug("Service {} returned {} tools", serviceName, tools.size());

                        for (McpDataModels.Tool tool : tools) {
                            Map<String, Object> enrichedTool = new HashMap<>();
                            enrichedTool.put("name", tool.getName());
                            enrichedTool.put("description", tool.getDescription());
                            enrichedTool.put("inputSchema", tool.getInputSchema());
                            enrichedTool.put("serviceName", serviceName);
                            enrichedTool.put("serviceType", "STANDARD_MCP");
                            enrichedTool.put("source", "running");
                            allTools.add(enrichedTool);
                            log.debug("Added tool: {} from service: {}", tool.getName(), serviceName);
                        }
                    } catch (Exception e) {
                        log.warn("Failed to get tools from running service {}: {}", serviceName, e.getMessage(), e);
                    }
                } else {
                    log.debug("Service {} is not running", serviceName);
                }
            }
        } catch (Exception e) {
            log.warn("Failed to get tools from running standard MCP services: {}", e.getMessage(), e);
        }

        log.debug("找到 {} 个可用工具", allTools.size());
        return allTools;
    }

    /**
     * 获取调试连接信息
     */
    public Map<String, Object> getDebugConnectionInfo() {
        Map<String, Object> debugInfo = new HashMap<>();

        // 1. 数据库中的服务信息
        List<RemoteMcpService> availableServices = serviceRepository.findAvailableServices();
        debugInfo.put("databaseServices", availableServices.size());
        debugInfo.put("databaseServiceNames", availableServices.stream()
                .map(RemoteMcpService::getServiceName)
                .collect(Collectors.toList()));

        // 2. 运行中的标准MCP服务信息
        Map<String, ManagedMcpProcess> runningServices = standardMcpGateway.getAllStandardMcpServices();
        debugInfo.put("runningServices", runningServices.size());
        debugInfo.put("runningServiceNames", runningServices.keySet());

        // 3. 检查每个运行中服务的状态
        Map<String, Object> serviceStatuses = new HashMap<>();
        for (String serviceName : runningServices.keySet()) {
            Map<String, Object> status = new HashMap<>();
            status.put("isRunning", standardMcpGateway.isStandardMcpServiceRunning(serviceName));

            try {
                List<McpDataModels.Tool> tools = standardMcpGateway.listStandardMcpTools(serviceName);
                status.put("toolCount", tools.size());
                status.put("toolNames", tools.stream()
                        .map(McpDataModels.Tool::getName)
                        .collect(Collectors.toList()));
            } catch (Exception e) {
                status.put("toolError", e.getMessage());
            }

            serviceStatuses.put(serviceName, status);
        }
        debugInfo.put("serviceStatuses", serviceStatuses);

        return debugInfo;
    }

    /**
     * 获取所有可用的资源列表
     */
    @Cacheable(value = CacheNames.MCP_SERVICE_RESOURCES, key = "'all'")
    public List<Map<String, Object>> listResources() {
        log.debug("获取所有可用资源列表");

        List<Map<String, Object>> allResources = new ArrayList<>();

        // 1. 从数据库中的远程MCP服务获取资源
        List<RemoteMcpService> availableServices = serviceRepository.findAvailableServices();
        for (RemoteMcpService service : availableServices) {
            if (service.getResources() != null) {
                for (Map<String, Object> resource : service.getResources()) {
                    // 添加服务信息到资源元数据
                    Map<String, Object> enrichedResource = new HashMap<>(resource);
                    enrichedResource.put("serviceId", service.getId());
                    enrichedResource.put("serviceName", service.getServiceName());
                    enrichedResource.put("serviceType", service.getServiceType());
                    enrichedResource.put("source", "database");
                    allResources.add(enrichedResource);
                }
            }
        }

        // 2. 从运行中的标准MCP服务获取资源
        try {
            Map<String, ManagedMcpProcess> runningServices = standardMcpGateway.getAllStandardMcpServices();
            for (Map.Entry<String, ManagedMcpProcess> entry : runningServices.entrySet()) {
                String serviceName = entry.getKey();
                if (standardMcpGateway.isStandardMcpServiceRunning(serviceName)) {
                    try {
                        List<McpDataModels.Resource> resources = standardMcpGateway.listStandardMcpResources(serviceName);
                        for (McpDataModels.Resource resource : resources) {
                            Map<String, Object> enrichedResource = new HashMap<>();
                            enrichedResource.put("uri", resource.getUri());
                            enrichedResource.put("name", resource.getName());
                            enrichedResource.put("description", resource.getDescription());
                            enrichedResource.put("mimeType", resource.getMimeType());
                            enrichedResource.put("serviceName", serviceName);
                            enrichedResource.put("serviceType", "STANDARD_MCP");
                            enrichedResource.put("source", "running");
                            allResources.add(enrichedResource);
                        }
                    } catch (Exception e) {
                        log.warn("Failed to get resources from running service {}: {}", serviceName, e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            log.warn("Failed to get resources from running standard MCP services: {}", e.getMessage());
        }

        log.debug("找到 {} 个可用资源", allResources.size());
        return allResources;
    }

    /**
     * 调用MCP工具（支持标准MCP协议和传统REST API）
     */
    public Map<String, Object> callTool(String toolName, Map<String, Object> arguments, String userId) {
        log.info("调用MCP工具: {} (用户: {})", toolName, userId);

        try {
            // 1. 首先尝试标准MCP协议
            Map<String, Object> standardResult = tryCallStandardMcpTool(toolName, arguments, userId);
            if (standardResult != null) {
                log.info("通过标准MCP协议调用工具成功: {}", toolName);
                return standardResult;
            }

            // 2. 回退到传统REST API
            log.debug("标准MCP协议不可用，回退到传统REST API: {}", toolName);

            // 查找提供该工具的服务
            RemoteMcpService service = findServiceByTool(toolName);
            if (service == null) {
                throw new NexusException.ResourceNotFoundException("工具不存在: " + toolName);
            }

            // 检查服务是否可用
            if (!service.isAvailable()) {
                throw new NexusException.BusinessException("服务不可用: " + service.getServiceName());
            }

            // 执行工具调用
            Map<String, Object> result = toolExecutor.executeTool(service, toolName, arguments, userId);

            log.info("工具调用成功: {} (服务: {})", toolName, service.getServiceName());
            return result;

        } catch (Exception e) {
            log.error("工具调用失败: {} - {}", toolName, e.getMessage());
            throw e;
        }
    }

    /**
     * 尝试通过标准MCP协议调用工具
     */
    private Map<String, Object> tryCallStandardMcpTool(String toolName, Map<String, Object> arguments, String userId) {
        try {
            // 查找提供该工具的标准MCP服务
            String serviceName = findStandardMcpServiceByTool(toolName);
            if (serviceName == null) {
                return null; // 没有标准MCP服务提供此工具
            }

            // 检查标准MCP服务是否运行
            if (!standardMcpGateway.isStandardMcpServiceRunning(serviceName)) {
                log.warn("标准MCP服务未运行: {}", serviceName);
                return null;
            }

            // 调用标准MCP工具
            return standardMcpGateway.callStandardMcpTool(serviceName, toolName, arguments);

        } catch (Exception e) {
            log.warn("标准MCP协议调用失败，将回退到传统API: {} - {}", toolName, e.getMessage());
            return null;
        }
    }

    /**
     * 查找提供指定工具的标准MCP服务
     */
    private String findStandardMcpServiceByTool(String toolName) {
        // 这里需要实现标准MCP服务的工具映射逻辑
        // 可以通过配置文件、数据库或服务发现机制来实现
        // 简化实现，返回null表示没有标准MCP服务
        return null;
    }

    /**
     * 异步调用MCP工具
     */
    public CompletableFuture<Map<String, Object>> callToolAsync(String toolName, Map<String, Object> arguments, String userId) {
        log.info("异步调用MCP工具: {} (用户: {})", toolName, userId);

        return CompletableFuture.supplyAsync(() -> {
            try {
                // 创建异步任务
                MCPAsyncTask task = MCPAsyncTask.builder()
                        .taskId(UUID.randomUUID().toString())
                        .correlationId(UUID.randomUUID().toString())
                        .userId(userId)
                        .method("tools/call")
                        .toolName(toolName)
                        .parameters(arguments)
                        .createdAt(LocalDateTime.now())
                        .build();

                // 提交任务处理
                MCPTaskResult result = asyncTaskProcessor.processTask(task);
                
                if (result.isSuccess()) {
                    return result.getData();
                } else {
                    throw new NexusException.TaskExecutionException(result.getErrorMessage());
                }

            } catch (Exception e) {
                log.error("异步工具调用失败: {} - {}", toolName, e.getMessage());
                throw new RuntimeException(e);
            }
        });
    }

    /**
     * 基于事件的异步工具调用
     * 返回任务ID，客户端可以通过任务ID查询结果
     */
    public String callToolAsyncWithEvent(String toolName, Map<String, Object> arguments,
                                        String userId, String clientIp, String userAgent) {
        log.info("基于事件的异步调用MCP工具: {} (用户: {})", toolName, userId);

        try {
            // 查找提供该工具的服务
            RemoteMcpService service = findServiceByTool(toolName);
            if (service == null) {
                throw new NexusException.ResourceNotFoundException("工具不存在: " + toolName);
            }

            // 检查服务是否可用
            if (!service.isAvailable()) {
                throw new NexusException.BusinessException("服务不可用: " + service.getServiceName());
            }

            // 生成任务ID
            String taskId = generateTaskId(toolName, userId);

            // 创建MCP任务事件
            MCPTaskCreatedEvent taskEvent = new MCPTaskCreatedEvent(
                    taskId,
                    userId,
                    toolName,
                    service.getId(),
                    service.getServiceName(),
                    "REMOTE", // 服务类型
                    arguments
            );

            // 设置任务优先级（可以根据用户等级或工具类型设置）
            taskEvent.setTaskPriority(determineTaskPriority(toolName, userId));

            // 设置超时时间
            taskEvent.setTimeoutMs(getToolTimeout(toolName));

            // 添加任务标签
            taskEvent.addTaskTag("toolType", "remote");
            taskEvent.addTaskTag("serviceName", service.getServiceName());

            // 设置客户端信息
            taskEvent.setClientIp(clientIp);
            taskEvent.setUserAgent(userAgent);

            taskEvent.setSourceService("nexus-mcp-remote-service");

            // 发送任务创建事件
            boolean sent = messageProducerService.sendEventAsync(taskEvent);

            if (sent) {
                log.info("MCP任务事件发送成功: taskId={}, toolName={}", taskId, toolName);
                return taskId;
            } else {
                throw new NexusException.BusinessException("任务事件发送失败");
            }

        } catch (Exception e) {
            log.error("基于事件的异步工具调用失败: {} - {}", toolName, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 生成任务ID
     */
    private String generateTaskId(String toolName, String userId) {
        return String.format("mcp-task-%s-%s-%d",
                toolName.replaceAll("[^a-zA-Z0-9]", ""),
                userId,
                System.currentTimeMillis());
    }

    /**
     * 确定任务优先级
     */
    private MCPTaskCreatedEvent.TaskPriority determineTaskPriority(String toolName, String userId) {
        // 可以根据工具类型、用户等级等因素确定优先级
        // 这里简化处理
        if (toolName.contains("urgent") || toolName.contains("critical")) {
            return MCPTaskCreatedEvent.TaskPriority.URGENT;
        } else if (toolName.contains("high") || toolName.contains("important")) {
            return MCPTaskCreatedEvent.TaskPriority.HIGH;
        } else if (toolName.contains("low") || toolName.contains("batch")) {
            return MCPTaskCreatedEvent.TaskPriority.LOW;
        } else {
            return MCPTaskCreatedEvent.TaskPriority.NORMAL;
        }
    }

    /**
     * 获取工具超时时间
     */
    private Long getToolTimeout(String toolName) {
        // 可以根据工具类型设置不同的超时时间
        // 这里简化处理
        if (toolName.contains("quick") || toolName.contains("fast")) {
            return 30000L; // 30秒
        } else if (toolName.contains("slow") || toolName.contains("heavy")) {
            return 600000L; // 10分钟
        } else {
            return 120000L; // 2分钟
        }
    }

    /**
     * 访问MCP资源
     */
    public Map<String, Object> readResource(String resourceUri, String userId) {
        log.info("访问MCP资源: {} (用户: {})", resourceUri, userId);

        try {
            // 查找提供该资源的服务
            RemoteMcpService service = findServiceByResource(resourceUri);
            if (service == null) {
                throw new NexusException.ResourceNotFoundException("资源不存在: " + resourceUri);
            }

            // 检查服务是否可用
            if (!service.isAvailable()) {
                throw new NexusException.BusinessException("服务不可用: " + service.getServiceName());
            }

            // 访问资源
            Map<String, Object> result = resourceAccessor.readResource(service, resourceUri, userId);
            
            log.info("资源访问成功: {} (服务: {})", resourceUri, service.getServiceName());
            return result;

        } catch (Exception e) {
            log.error("资源访问失败: {} - {}", resourceUri, e.getMessage());
            throw e;
        }
    }

    /**
     * 获取服务信息
     */
    @Cacheable(value = CacheNames.MCP_SERVICES, key = "'info'")
    public Map<String, Object> getServerInfo() {
        log.debug("获取服务器信息");

        List<RemoteMcpService> availableServices = serviceRepository.findAvailableServices();
        
        Map<String, Object> serverInfo = new HashMap<>();
        serverInfo.put("name", "Nexus Unified MCP Server");
        serverInfo.put("version", "1.0.0");
        serverInfo.put("description", "统一的MCP服务接口，整合多个MCP服务提供商");
        serverInfo.put("timestamp", System.currentTimeMillis());
        
        // 服务统计
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalServices", availableServices.size());
        statistics.put("totalTools", availableServices.stream().mapToInt(RemoteMcpService::getToolCount).sum());
        statistics.put("totalResources", availableServices.stream().mapToInt(RemoteMcpService::getResourceCount).sum());
        serverInfo.put("statistics", statistics);
        
        // 服务列表
        List<Map<String, Object>> services = availableServices.stream()
                .map(this::buildServiceInfo)
                .collect(Collectors.toList());
        serverInfo.put("services", services);

        return serverInfo;
    }

    /**
     * 根据工具名称查找服务
     */
    private RemoteMcpService findServiceByTool(String toolName) {
        List<RemoteMcpService> availableServices = serviceRepository.findAvailableServices();
        
        for (RemoteMcpService service : availableServices) {
            if (service.getTools() != null) {
                for (Map<String, Object> tool : service.getTools()) {
                    String name = (String) tool.get("name");
                    if (toolName.equals(name)) {
                        return service;
                    }
                }
            }
        }
        
        return null;
    }

    /**
     * 根据资源URI查找服务
     */
    private RemoteMcpService findServiceByResource(String resourceUri) {
        List<RemoteMcpService> availableServices = serviceRepository.findAvailableServices();
        
        for (RemoteMcpService service : availableServices) {
            if (service.getResources() != null) {
                for (Map<String, Object> resource : service.getResources()) {
                    String uri = (String) resource.get("uri");
                    if (resourceUri.equals(uri) || resourceUri.startsWith(uri)) {
                        return service;
                    }
                }
            }
        }
        
        return null;
    }

    /**
     * 构建服务信息
     */
    private Map<String, Object> buildServiceInfo(RemoteMcpService service) {
        Map<String, Object> info = new HashMap<>();
        info.put("id", service.getId());
        info.put("name", service.getServiceName());
        info.put("displayName", service.getDisplayName());
        info.put("description", service.getDescription());
        info.put("type", service.getServiceType());
        info.put("status", service.getStatus());
        info.put("healthStatus", service.getHealthStatus());
        info.put("endpoint", service.getEndpoint());
        info.put("protocol", service.getProtocolType());
        info.put("toolCount", service.getToolCount());
        info.put("resourceCount", service.getResourceCount());
        info.put("version", service.getVersion());
        return info;
    }

    /**
     * 刷新服务缓存
     */
    public void refreshCache() {
        log.info("刷新UnifiedMCP服务缓存");
        // 这里可以添加缓存刷新逻辑
        // 比如清除相关缓存，重新加载服务信息等
    }

    /**
     * 获取服务健康状态
     */
    public Map<String, Object> getHealthStatus() {
        List<RemoteMcpService> allServices = serviceRepository.findAll();
        long healthyCount = allServices.stream()
                .mapToLong(service -> service.isHealthy() ? 1 : 0)
                .sum();
        
        Map<String, Object> health = new HashMap<>();
        health.put("status", healthyCount == allServices.size() ? "UP" : "DEGRADED");
        health.put("totalServices", allServices.size());
        health.put("healthyServices", healthyCount);
        health.put("unhealthyServices", allServices.size() - healthyCount);
        health.put("timestamp", System.currentTimeMillis());
        
        return health;
    }


}
