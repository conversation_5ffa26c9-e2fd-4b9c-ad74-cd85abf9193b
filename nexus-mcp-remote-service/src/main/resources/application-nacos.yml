server:
  port: 8083
  servlet:
    context-path: /mcp-remote

spring:
  application:
    name: nexus-mcp-remote-service

  # 允许循环引用
  main:
    allow-circular-references: true

  config:
    import: "optional:nacos:nexus-mcp-remote-service.yml"

  # Spring Cloud Nacos配置
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        group: DEFAULT_GROUP
        service: ${spring.application.name}
        metadata:
          version: 1.0.0
          zone: default
      config:
        server-addr: 127.0.0.1:8848
        group: DEFAULT_GROUP
        file-extension: yml
        shared-configs:
          - data-id: nexus-common-config.yml
            group: DEFAULT_GROUP
            refresh: true

  # 数据库配置 - 使用Neon云数据库 (覆盖Nacos配置)
  datasource:
    url: "*****************************************************************************************************************************************************************************"
    username: neondb_owner
    password: npg_kc2S7QGCPbEh
    driver-class-name: org.postgresql.Driver
    hikari:
      pool-name: NexusMicroservicesPool
      maximum-pool-size: 10
      minimum-idle: 3
      connection-timeout: 30000
      idle-timeout: 120000

  # Redis配置
  redis:
    host: localhost
    port: 6379
    database: 4

  # RabbitMQ配置
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        jdbc:
          time_zone: UTC

# 本地开发时的fallback配置
---
spring:
  profiles: local
  datasource:
    url: **************************************
    username: nexus
    password: nexus123
    driver-class-name: org.postgresql.Driver
  redis:
    host: localhost
    port: 6379
    database: 2
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
