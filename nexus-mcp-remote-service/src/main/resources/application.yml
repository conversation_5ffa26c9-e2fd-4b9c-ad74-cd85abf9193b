server:
  port: 8083
  servlet:
    context-path: /mcp-remote

spring:
  application:
    name: nexus-mcp-remote-service

  profiles:
    active: local

  # config:
  #   import: "nacos:nexus-mcp-remote-service.yml"

  # Spring Cloud Nacos配置 - 本地环境禁用
  cloud:
    nacos:
      config:
        import-check:
          enabled: false
      discovery:
        enabled: false

# 本地开发时的fallback配置
---
spring:
  profiles: local
  datasource:
    url: **************************************
    username: nexus
    password: nexus123
    driver-class-name: org.postgresql.Driver
  redis:
    host: localhost
    port: 6379
    database: 2
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest

# Nexus配置
nexus:
  # 禁用邮件服务以避免Java版本兼容性问题
  email:
    enabled: false

