# MCP命令启动服务使用指南

现在您可以直接使用npx、uvx、python、java等命令来启动MCP服务，无需手动配置复杂的参数。

## 🚀 支持的命令格式

### 1. Node.js服务 (npx)

```bash
# 官方文件系统服务
npx -y @modelcontextprotocol/server-filesystem /home/<USER>/allowed-files

# 官方内存服务
npx -y @modelcontextprotocol/server-memory

# 官方时间服务
npx -y @modelcontextprotocol/server-time

# 官方网页抓取服务
npx -y @modelcontextprotocol/server-fetch

# GitHub服务（需要环境变量GITHUB_PERSONAL_ACCESS_TOKEN）
npx -y @modelcontextprotocol/server-github
```

### 2. Python服务 (uvx)

```bash
# 官方Git服务
uvx mcp-server-git --repository /path/to/git/repo

# SQLite数据库服务
uvx mcp-server-sqlite --db-path /path/to/database.db

# PostgreSQL服务
uvx mcp-server-postgres postgresql://user:password@localhost:5432/dbname
```

### 3. Python脚本 (python)

```bash
# 使用模块方式
python -m mcp_server_git --repository /path/to/repo

# 直接运行脚本
python weather_service.py --api-key YOUR_API_KEY --cache-ttl 300
```

### 4. Java服务 (java)

```bash
# 标准JAR启动
java -jar mcp-server.jar --port 8080 --config /etc/mcp.conf

# 带JVM参数
java -Xmx512m -Xms256m -jar custom-mcp-server.jar --port 8080
```

### 5. Node.js脚本 (node)

```bash
# 直接运行脚本
node server.js --port 8080 --config config.json
```

### 6. 可执行文件

```bash
# 直接执行
/usr/local/bin/mcp-server --config /etc/mcp.conf --log-level info
```

## 📡 REST API使用

### 启动单个服务

```bash
curl -X POST http://localhost:8080/api/mcp/command/start \
  -H "Content-Type: application/json" \
  -d '{
    "command": "npx -y @modelcontextprotocol/server-filesystem /home/<USER>/files",
    "serviceName": "my-filesystem"
  }'
```

响应：
```json
{
  "success": true,
  "serviceName": "my-filesystem",
  "message": "服务启动成功",
  "command": "npx -y @modelcontextprotocol/server-filesystem /home/<USER>/files",
  "config": {
    "serviceType": "NODEJS",
    "servicePath": "@modelcontextprotocol/server-filesystem",
    "args": ["-y", "/home/<USER>/files"]
  }
}
```

### 批量启动服务

```bash
curl -X POST http://localhost:8080/api/mcp/command/start-batch \
  -H "Content-Type: application/json" \
  -d '{
    "commands": [
      "npx -y @modelcontextprotocol/server-filesystem /home/<USER>/files",
      "uvx mcp-server-git --repository /path/to/repo",
      "npx -y @modelcontextprotocol/server-memory"
    ]
  }'
```

### 验证命令

```bash
curl -X POST http://localhost:8080/api/mcp/command/validate \
  -H "Content-Type: application/json" \
  -d '{
    "command": "npx -y @modelcontextprotocol/server-filesystem /path/to/files"
  }'
```

响应：
```json
{
  "valid": true,
  "command": "npx -y @modelcontextprotocol/server-filesystem /path/to/files",
  "preview": {
    "serviceName": "filesystem",
    "serviceType": "NODEJS",
    "servicePath": "@modelcontextprotocol/server-filesystem",
    "args": ["-y", "/path/to/files"]
  }
}
```

### 停止服务

```bash
curl -X POST http://localhost:8080/api/mcp/command/stop \
  -H "Content-Type: application/json" \
  -d '{
    "command": "npx -y @modelcontextprotocol/server-filesystem /home/<USER>/files"
  }'
```

### 获取命令示例

```bash
curl http://localhost:8080/api/mcp/command/examples
```

### 查看命令映射

```bash
curl http://localhost:8080/api/mcp/command/mappings
```

## 🔧 Java代码使用

```java
@Autowired
private McpCommandService mcpCommandService;

// 启动服务
McpServiceStartResult result = mcpCommandService.startServiceByCommand(
    "npx -y @modelcontextprotocol/server-filesystem /home/<USER>/files"
);

if (result.isSuccess()) {
    System.out.println("服务启动成功: " + result.getServiceName());
} else {
    System.out.println("服务启动失败: " + result.getMessage());
}

// 批量启动
List<String> commands = List.of(
    "npx -y @modelcontextprotocol/server-memory",
    "uvx mcp-server-git --repository /path/to/repo"
);

Map<String, McpServiceStartResult> results = mcpCommandService.startServicesByCommands(commands);

// 验证命令
boolean valid = mcpCommandService.validateCommand("npx -y @modelcontextprotocol/server-time");

// 预览命令解析
McpServiceConfig config = mcpCommandService.previewCommand("uvx mcp-server-git --repository /repo");
```

## 🌟 常用服务快速启动

### 文件操作
```bash
npx -y @modelcontextprotocol/server-filesystem /home/<USER>/workspace
```

### Git仓库管理
```bash
uvx mcp-server-git --repository /path/to/your/repo
```

### 数据库访问
```bash
# SQLite
uvx mcp-server-sqlite --db-path /path/to/database.db

# PostgreSQL
npx -y @modelcontextprotocol/server-postgres postgresql://user:pass@localhost/db
```

### 网络功能
```bash
# 网页抓取
npx -y @modelcontextprotocol/server-fetch

# 搜索功能
uvx mcp-server-brave-search
```

### 实用工具
```bash
# 时间处理
npx -y @modelcontextprotocol/server-time

# 内存存储
npx -y @modelcontextprotocol/server-memory
```

## ⚠️ 注意事项

1. **环境依赖**：确保系统已安装对应的运行环境（Node.js、Python、Java等）
2. **权限控制**：文件系统服务需要指定允许访问的路径
3. **环境变量**：某些服务（如GitHub）需要设置相应的环境变量
4. **网络访问**：确保能够访问npm、PyPI等包管理器
5. **资源限制**：注意服务的资源使用情况，避免系统过载

## 🔍 故障排除

### 常见问题

1. **命令解析失败**
   - 检查命令格式是否正确
   - 使用验证API确认命令可解析

2. **服务启动失败**
   - 检查运行环境是否安装
   - 确认包名和版本是否正确
   - 查看服务日志获取详细错误信息

3. **权限问题**
   - 确保有执行权限
   - 检查文件路径访问权限

4. **网络问题**
   - 检查网络连接
   - 确认包管理器可正常访问
