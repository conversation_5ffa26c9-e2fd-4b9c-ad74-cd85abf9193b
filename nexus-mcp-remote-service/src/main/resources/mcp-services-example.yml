# MCP服务配置示例
# 展示如何配置各种类型的MCP服务

nexus:
  mcp:
    # 标准MCP协议配置
    standard:
      enabled: true
      stdio-timeout: 30000
      max-concurrent-connections: 10
      auto-restart: true
      max-restart-count: 3
      
    # MCP服务配置
    services:
      # 官方Node.js服务 - 文件系统
      filesystem:
        type: NODEJS
        package: "@modelcontextprotocol/server-filesystem"
        executable: "npx"
        args:
          - "-y"
          - "/home/<USER>/allowed-files"  # 允许访问的文件路径
        description: "安全的文件操作服务"
        tags:
          category: "filesystem"
          official: "true"
          
      # 官方Python服务 - Git
      git:
        type: PYTHON
        package: "mcp-server-git"
        executable: "uvx"
        args:
          - "--repository"
          - "/path/to/git/repo"
        description: "Git仓库操作服务"
        tags:
          category: "version-control"
          official: "true"
          
      # 官方Node.js服务 - GitHub (需要环境变量)
      github:
        type: NODEJS
        package: "@modelcontextprotocol/server-github"
        executable: "npx"
        args:
          - "-y"
        environment:
          GITHUB_PERSONAL_ACCESS_TOKEN: "${GITHUB_TOKEN}"
        description: "GitHub API集成服务"
        tags:
          category: "api"
          official: "true"
          
      # 官方Node.js服务 - 内存
      memory:
        type: NODEJS
        package: "@modelcontextprotocol/server-memory"
        executable: "npx"
        args:
          - "-y"
        description: "知识图谱持久化内存"
        tags:
          category: "storage"
          official: "true"
          
      # 官方Node.js服务 - 时间
      time:
        type: NODEJS
        package: "@modelcontextprotocol/server-time"
        executable: "npx"
        args:
          - "-y"
        description: "时间和时区转换"
        tags:
          category: "utility"
          official: "true"
          
      # 官方Node.js服务 - 网页抓取
      fetch:
        type: NODEJS
        package: "@modelcontextprotocol/server-fetch"
        executable: "npx"
        args:
          - "-y"
        description: "网页内容获取和转换"
        tags:
          category: "web"
          official: "true"
          
      # 社区Python服务 - SQLite
      sqlite:
        type: PYTHON
        package: "mcp-server-sqlite"
        executable: "uvx"
        args:
          - "--db-path"
          - "/path/to/database.db"
        description: "SQLite数据库交互"
        tags:
          category: "database"
          community: "true"
          
      # 社区Node.js服务 - PostgreSQL
      postgres:
        type: NODEJS
        package: "@modelcontextprotocol/server-postgres"
        executable: "npx"
        args:
          - "-y"
          - "postgresql://user:password@localhost:5432/dbname"
        description: "PostgreSQL数据库访问"
        tags:
          category: "database"
          community: "true"
          
      # Java服务示例
      custom-java-service:
        type: JAVA
        jar-path: "/opt/mcp-services/custom-service.jar"
        jvm-args:
          - "-Xmx512m"
          - "-Xms256m"
          - "-Dspring.profiles.active=production"
        args:
          - "--port"
          - "8080"
          - "--config"
          - "/etc/mcp/custom-service.conf"
        working-directory: "/opt/mcp-services"
        environment:
          JAVA_HOME: "/usr/lib/jvm/java-11-openjdk"
          SERVICE_ENV: "production"
        description: "自定义Java MCP服务"
        tags:
          category: "custom"
          language: "java"
          
      # 可执行文件服务示例
      custom-executable:
        type: EXECUTABLE
        executable-path: "/usr/local/bin/mcp-custom-service"
        args:
          - "--config"
          - "/etc/mcp/custom.conf"
          - "--log-level"
          - "info"
        working-directory: "/var/lib/mcp"
        environment:
          MCP_CONFIG_DIR: "/etc/mcp"
          MCP_DATA_DIR: "/var/lib/mcp"
        description: "自定义可执行MCP服务"
        tags:
          category: "custom"
          type: "executable"
          
      # Python脚本服务示例
      weather-service:
        type: PYTHON
        script-path: "/opt/mcp-services/weather.py"
        executable: "python"
        args:
          - "--api-key"
          - "${WEATHER_API_KEY}"
          - "--cache-ttl"
          - "300"
        working-directory: "/opt/mcp-services"
        environment:
          PYTHONPATH: "/opt/mcp-services"
          WEATHER_API_KEY: "${WEATHER_API_KEY}"
        description: "天气信息MCP服务"
        tags:
          category: "weather"
          language: "python"
          
    # 服务发现配置
    discovery:
      enabled: true
      auto-register: true
      health-check-interval: 30
      
    # 路由配置
    routing:
      strategy: "round-robin"  # round-robin, weighted, least-connections
      fallback-enabled: true
      timeout: 30000
      
    # 监控配置
    monitoring:
      metrics-enabled: true
      logging-enabled: true
      performance-tracking: true
