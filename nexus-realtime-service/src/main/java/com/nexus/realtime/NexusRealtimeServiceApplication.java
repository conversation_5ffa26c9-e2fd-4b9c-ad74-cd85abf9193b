package com.nexus.realtime;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Nexus实时服务启动类
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@SpringBootApplication(scanBasePackages = {
    "com.nexus.realtime",
    "com.nexus.common"
})
@EnableDiscoveryClient
@EnableAsync
@EnableScheduling
public class NexusRealtimeServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(NexusRealtimeServiceApplication.class, args);
    }
}
