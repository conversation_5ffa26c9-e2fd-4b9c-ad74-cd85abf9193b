package com.nexus.realtime.controller;

import com.nexus.common.response.ApiResponse;
import com.nexus.realtime.dto.RealtimeMessage;
import com.nexus.realtime.dto.SubscriptionRequest;
import com.nexus.realtime.manager.ConnectionManager;
import com.nexus.realtime.router.MessageRouter;
import com.nexus.realtime.service.RealtimeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 实时服务控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/realtime")
@RequiredArgsConstructor
@Tag(name = "实时服务", description = "实时通信和消息推送接口")
public class RealtimeController {

    private final RealtimeService realtimeService;
    private final ConnectionManager connectionManager;
    private final MessageRouter messageRouter;

    /**
     * 发送消息到指定用户
     */
    @PostMapping("/send/{userId}")
    @Operation(summary = "发送消息到用户", description = "向指定用户发送实时消息")
    public ResponseEntity<Map<String, Object>> sendToUser(
            @PathVariable String userId,
            @Valid @RequestBody RealtimeMessage message) {
        
        log.info("Sending message to user: {}", userId);
        
        message.setTimestamp(LocalDateTime.now());
        realtimeService.sendToUser(userId, message);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "Message sent successfully");
        response.put("timestamp", LocalDateTime.now());
        
        return ResponseEntity.ok(response);
    }

    /**
     * 广播消息
     */
    @PostMapping("/broadcast")
    @Operation(summary = "广播消息", description = "向所有连接的客户端广播消息")
    public ResponseEntity<Map<String, Object>> broadcast(
            @Valid @RequestBody RealtimeMessage message) {
        
        log.info("Broadcasting message: {}", message.getType());
        
        message.setTimestamp(LocalDateTime.now());
        realtimeService.broadcast(message);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "Message broadcasted successfully");
        response.put("timestamp", LocalDateTime.now());
        
        return ResponseEntity.ok(response);
    }

    /**
     * 发送服务状态更新
     */
    @PostMapping("/service-status")
    @Operation(summary = "发送服务状态更新", description = "发送服务状态变化通知")
    public ResponseEntity<Map<String, Object>> sendServiceStatus(
            @RequestParam String serviceId,
            @RequestParam String status,
            @RequestBody(required = false) Map<String, Object> details) {
        
        log.info("Sending service status update: {} - {}", serviceId, status);
        
        realtimeService.sendServiceStatusUpdate(serviceId, status, details);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "Service status update sent");
        response.put("timestamp", LocalDateTime.now());
        
        return ResponseEntity.ok(response);
    }

    /**
     * 发送任务进度更新
     */
    @PostMapping("/task-progress")
    @Operation(summary = "发送任务进度更新", description = "发送任务执行进度通知")
    public ResponseEntity<Map<String, Object>> sendTaskProgress(
            @RequestParam String taskId,
            @RequestParam String userId,
            @RequestParam int progress,
            @RequestParam String status) {
        
        log.info("Sending task progress update: {} - {}%", taskId, progress);
        
        realtimeService.sendTaskProgressUpdate(taskId, userId, progress, status);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "Task progress update sent");
        response.put("timestamp", LocalDateTime.now());
        
        return ResponseEntity.ok(response);
    }

    /**
     * 订阅主题
     */
    @PostMapping("/subscribe")
    @Operation(summary = "订阅主题", description = "订阅指定主题的消息")
    public ResponseEntity<Map<String, Object>> subscribe(
            @Valid @RequestBody SubscriptionRequest request) {
        
        log.info("User {} subscribing to topic: {}", request.getUserId(), request.getTopic());
        
        realtimeService.subscribe(request.getUserId(), request);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "Subscribed successfully");
        response.put("topic", request.getTopic());
        response.put("timestamp", LocalDateTime.now());
        
        return ResponseEntity.ok(response);
    }

    /**
     * 取消订阅
     */
    @PostMapping("/unsubscribe")
    @Operation(summary = "取消订阅", description = "取消订阅指定主题")
    public ResponseEntity<Map<String, Object>> unsubscribe(
            @RequestParam String userId,
            @RequestParam String topic) {
        
        log.info("User {} unsubscribing from topic: {}", userId, topic);
        
        realtimeService.unsubscribe(userId, topic);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "Unsubscribed successfully");
        response.put("topic", topic);
        response.put("timestamp", LocalDateTime.now());
        
        return ResponseEntity.ok(response);
    }

    /**
     * 获取连接状态
     */
    @GetMapping("/status")
    @Operation(summary = "获取连接状态", description = "获取实时服务的连接状态信息")
    public ResponseEntity<Map<String, Object>> getStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("activeConnections", realtimeService.getActiveConnectionCount());
        status.put("timestamp", LocalDateTime.now());
        status.put("service", "nexus-realtime-service");
        status.put("status", "UP");
        
        return ResponseEntity.ok(status);
    }

    /**
     * 检查用户在线状态
     */
    @GetMapping("/user/{userId}/online")
    @Operation(summary = "检查用户在线状态", description = "检查指定用户是否在线")
    public ResponseEntity<Map<String, Object>> checkUserOnline(@PathVariable String userId) {
        boolean online = realtimeService.isUserOnline(userId);
        String sessionId = realtimeService.getUserSession(userId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("userId", userId);
        response.put("online", online);
        response.put("sessionId", sessionId);
        response.put("timestamp", LocalDateTime.now());
        
        return ResponseEntity.ok(response);
    }

    /**
     * WebSocket消息处理
     */
    @MessageMapping("/message")
    public void handleMessage(@Payload RealtimeMessage message, SimpMessageHeaderAccessor headerAccessor) {
        String sessionId = headerAccessor.getSessionId();
        log.info("Received WebSocket message from session {}: {}", sessionId, message.getType());
        
        // 处理客户端发送的消息
        if ("PING".equals(message.getType())) {
            // 心跳响应
            RealtimeMessage pongMessage = RealtimeMessage.builder()
                    .type("PONG")
                    .timestamp(LocalDateTime.now())
                    .build();
            
            String userId = (String) headerAccessor.getSessionAttributes().get("userId");
            if (userId != null) {
                realtimeService.sendToUser(userId, pongMessage);
            }
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "检查实时服务的健康状态")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "nexus-realtime-service");
        health.put("activeConnections", realtimeService.getActiveConnectionCount());
        health.put("timestamp", LocalDateTime.now());
        
        return ResponseEntity.ok(health);
    }
}
