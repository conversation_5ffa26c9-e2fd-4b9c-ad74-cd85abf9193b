package com.nexus.realtime.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 实时消息DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RealtimeMessage {
    
    /**
     * 消息类型
     */
    private String type;
    
    /**
     * 消息ID
     */
    private String messageId;
    
    /**
     * 服务ID
     */
    private String serviceId;
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 工具名称
     */
    private String toolName;
    
    /**
     * 消息标题
     */
    private String title;
    
    /**
     * 消息内容
     */
    private String content;
    
    /**
     * 消息级别 (INFO, WARN, ERROR)
     */
    private String level;
    
    /**
     * 状态
     */
    private String status;
    
    /**
     * 进度 (0-100)
     */
    private Integer progress;
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 消息数据
     */
    private Object data;
    
    /**
     * 时间戳
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timestamp;
    
    /**
     * 发送者ID
     */
    private String senderId;
    
    /**
     * 接收者ID
     */
    private String receiverId;
    
    /**
     * 消息优先级
     */
    private Integer priority;
    
    /**
     * 过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expiresAt;
}
