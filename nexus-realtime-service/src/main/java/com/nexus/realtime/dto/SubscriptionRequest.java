package com.nexus.realtime.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 订阅请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SubscriptionRequest {
    
    /**
     * 订阅主题
     */
    @NotBlank(message = "订阅主题不能为空")
    private String topic;
    
    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;
    
    /**
     * 订阅类型
     */
    private String subscriptionType;
    
    /**
     * 过滤条件
     */
    private String filter;
    
    /**
     * 是否持久化订阅
     */
    private Boolean persistent;
}
