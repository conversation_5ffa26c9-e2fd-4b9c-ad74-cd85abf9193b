package com.nexus.realtime.handler;

import com.nexus.realtime.manager.ConnectionManager;
import com.nexus.realtime.service.RealtimeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.WebSocketHandlerDecorator;
import org.springframework.web.socket.handler.WebSocketHandlerDecoratorFactory;
import org.springframework.web.socket.messaging.SessionConnectedEvent;
import org.springframework.web.socket.messaging.SessionDisconnectEvent;
import org.springframework.web.socket.server.HandshakeInterceptor;

import java.util.Map;

/**
 * WebSocket事件处理器
 * 处理WebSocket连接建立、断开等事件
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WebSocketEventHandler implements HandshakeInterceptor {

    @Lazy
    private final RealtimeService realtimeService;
    private final ConnectionManager connectionManager;

    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response,
                                   WebSocketHandler wsHandler, Map<String, Object> attributes) throws Exception {
        // 从请求参数中获取用户ID
        String query = request.getURI().getQuery();
        if (query != null && query.contains("userId=")) {
            String userId = extractUserId(query);
            if (userId != null) {
                attributes.put("userId", userId);
                log.info("WebSocket handshake for user: {}", userId);
                return true;
            }
        }
        
        log.warn("WebSocket handshake failed: missing userId parameter");
        return false;
    }

    @Override
    public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response,
                               WebSocketHandler wsHandler, Exception exception) {
        if (exception != null) {
            log.error("WebSocket handshake error: {}", exception.getMessage());
        }
    }



    @EventListener
    public void handleWebSocketConnectListener(SessionConnectedEvent event) {
        log.info("WebSocket connection established: {}", event.getMessage());

        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
        String sessionId = headerAccessor.getSessionId();

        // 从session attributes中获取userId
        Map<String, Object> sessionAttributes = headerAccessor.getSessionAttributes();
        String userId = sessionAttributes != null ? (String) sessionAttributes.get("userId") : null;

        if (sessionId != null) {
            if (userId == null) {
                userId = "anonymous_" + sessionId.substring(0, 8);
            }

            // 添加连接到管理器
            connectionManager.addConnection(sessionId, userId, sessionAttributes);

            // 通知实时服务
            realtimeService.addConnection(sessionId, userId);
        }
    }

    @EventListener
    public void handleWebSocketDisconnectListener(SessionDisconnectEvent event) {
        log.info("WebSocket connection closed: {}", event.getSessionId());

        // 从连接管理器移除连接
        connectionManager.removeConnection(event.getSessionId());

        // 通知实时服务
        realtimeService.removeConnection(event.getSessionId());
    }

    private String extractUserId(String query) {
        String[] params = query.split("&");
        for (String param : params) {
            if (param.startsWith("userId=")) {
                return param.substring("userId=".length());
            }
        }
        return null;
    }
}
