package com.nexus.realtime.manager;

import com.nexus.realtime.dto.RealtimeMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * WebSocket连接管理器
 * 负责管理WebSocket连接、心跳检测、连接状态等
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ConnectionManager {

    private final SimpMessagingTemplate messagingTemplate;
    private final RedisTemplate<String, Object> redisTemplate;

    // 连接信息存储
    private final Map<String, ConnectionInfo> connections = new ConcurrentHashMap<>();
    private final Map<String, Set<String>> userSessions = new ConcurrentHashMap<>();
    private final Map<String, Set<String>> topicSubscriptions = new ConcurrentHashMap<>();

    // 心跳检测
    private final ScheduledExecutorService heartbeatExecutor = Executors.newScheduledThreadPool(2);
    private static final long HEARTBEAT_INTERVAL = 30; // 30秒
    private static final long CONNECTION_TIMEOUT = 90; // 90秒超时

    /**
     * 连接信息类
     */
    public static class ConnectionInfo {
        private final String sessionId;
        private final String userId;
        private final LocalDateTime connectedAt;
        private volatile LocalDateTime lastHeartbeat;
        private final Set<String> subscribedTopics;
        private final Map<String, Object> attributes;

        public ConnectionInfo(String sessionId, String userId) {
            this.sessionId = sessionId;
            this.userId = userId;
            this.connectedAt = LocalDateTime.now();
            this.lastHeartbeat = LocalDateTime.now();
            this.subscribedTopics = ConcurrentHashMap.newKeySet();
            this.attributes = new ConcurrentHashMap<>();
        }

        // Getters
        public String getSessionId() { return sessionId; }
        public String getUserId() { return userId; }
        public LocalDateTime getConnectedAt() { return connectedAt; }
        public LocalDateTime getLastHeartbeat() { return lastHeartbeat; }
        public Set<String> getSubscribedTopics() { return subscribedTopics; }
        public Map<String, Object> getAttributes() { return attributes; }

        public void updateHeartbeat() {
            this.lastHeartbeat = LocalDateTime.now();
        }

        public boolean isExpired() {
            return LocalDateTime.now().isAfter(lastHeartbeat.plusSeconds(CONNECTION_TIMEOUT));
        }
    }

    /**
     * 初始化连接管理器
     */
    public void init() {
        log.info("初始化连接管理器...");
        
        // 启动心跳检测任务
        heartbeatExecutor.scheduleAtFixedRate(this::sendHeartbeat, 
                HEARTBEAT_INTERVAL, HEARTBEAT_INTERVAL, TimeUnit.SECONDS);
        
        // 启动连接清理任务
        heartbeatExecutor.scheduleAtFixedRate(this::cleanupExpiredConnections, 
                60, 60, TimeUnit.SECONDS);
        
        log.info("连接管理器初始化完成");
    }

    /**
     * 添加连接
     */
    public void addConnection(String sessionId, String userId, Map<String, Object> attributes) {
        log.info("添加连接: sessionId={}, userId={}", sessionId, userId);

        ConnectionInfo connectionInfo = new ConnectionInfo(sessionId, userId);
        if (attributes != null) {
            connectionInfo.getAttributes().putAll(attributes);
        }

        connections.put(sessionId, connectionInfo);
        
        // 更新用户会话映射
        userSessions.computeIfAbsent(userId, k -> ConcurrentHashMap.newKeySet()).add(sessionId);

        // 发送连接成功消息
        sendConnectionMessage(sessionId, "CONNECTED", "连接已建立");

        // 存储到Redis（用于集群环境）
        storeConnectionToRedis(connectionInfo);

        log.info("连接添加成功: sessionId={}, userId={}, 当前连接数: {}", 
                sessionId, userId, connections.size());
    }

    /**
     * 移除连接
     */
    public void removeConnection(String sessionId) {
        ConnectionInfo connectionInfo = connections.remove(sessionId);
        if (connectionInfo != null) {
            String userId = connectionInfo.getUserId();
            
            log.info("移除连接: sessionId={}, userId={}", sessionId, userId);

            // 更新用户会话映射
            Set<String> sessions = userSessions.get(userId);
            if (sessions != null) {
                sessions.remove(sessionId);
                if (sessions.isEmpty()) {
                    userSessions.remove(userId);
                }
            }

            // 清理主题订阅
            for (String topic : connectionInfo.getSubscribedTopics()) {
                unsubscribeFromTopic(sessionId, topic);
            }

            // 从Redis中移除
            removeConnectionFromRedis(sessionId);

            log.info("连接移除成功: sessionId={}, userId={}, 剩余连接数: {}", 
                    sessionId, userId, connections.size());
        }
    }

    /**
     * 更新心跳
     */
    public void updateHeartbeat(String sessionId) {
        ConnectionInfo connectionInfo = connections.get(sessionId);
        if (connectionInfo != null) {
            connectionInfo.updateHeartbeat();
            log.debug("更新心跳: sessionId={}", sessionId);
        }
    }

    /**
     * 订阅主题
     */
    public void subscribeToTopic(String sessionId, String topic) {
        ConnectionInfo connectionInfo = connections.get(sessionId);
        if (connectionInfo != null) {
            connectionInfo.getSubscribedTopics().add(topic);
            
            // 更新主题订阅映射
            topicSubscriptions.computeIfAbsent(topic, k -> ConcurrentHashMap.newKeySet()).add(sessionId);
            
            log.info("订阅主题: sessionId={}, topic={}", sessionId, topic);
        }
    }

    /**
     * 取消订阅主题
     */
    public void unsubscribeFromTopic(String sessionId, String topic) {
        ConnectionInfo connectionInfo = connections.get(sessionId);
        if (connectionInfo != null) {
            connectionInfo.getSubscribedTopics().remove(topic);
            
            // 更新主题订阅映射
            Set<String> subscribers = topicSubscriptions.get(topic);
            if (subscribers != null) {
                subscribers.remove(sessionId);
                if (subscribers.isEmpty()) {
                    topicSubscriptions.remove(topic);
                }
            }
            
            log.info("取消订阅主题: sessionId={}, topic={}", sessionId, topic);
        }
    }

    /**
     * 发送消息到指定会话
     */
    public void sendToSession(String sessionId, RealtimeMessage message) {
        ConnectionInfo connectionInfo = connections.get(sessionId);
        if (connectionInfo != null) {
            try {
                String destination = "/queue/messages";
                messagingTemplate.convertAndSendToUser(sessionId, destination, message);
                log.debug("消息发送到会话: sessionId={}, type={}", sessionId, message.getType());
            } catch (Exception e) {
                log.error("发送消息到会话失败: sessionId={}", sessionId, e);
            }
        }
    }

    /**
     * 发送消息到用户的所有会话
     */
    public void sendToUser(String userId, RealtimeMessage message) {
        Set<String> sessions = userSessions.get(userId);
        if (sessions != null && !sessions.isEmpty()) {
            for (String sessionId : sessions) {
                sendToSession(sessionId, message);
            }
            log.debug("消息发送到用户: userId={}, sessions={}, type={}", 
                     userId, sessions.size(), message.getType());
        }
    }

    /**
     * 广播消息到主题订阅者
     */
    public void broadcastToTopic(String topic, RealtimeMessage message) {
        Set<String> subscribers = topicSubscriptions.get(topic);
        if (subscribers != null && !subscribers.isEmpty()) {
            for (String sessionId : subscribers) {
                sendToSession(sessionId, message);
            }
            log.debug("消息广播到主题: topic={}, subscribers={}, type={}", 
                     topic, subscribers.size(), message.getType());
        }
    }

    /**
     * 广播消息到所有连接
     */
    public void broadcastToAll(RealtimeMessage message) {
        for (String sessionId : connections.keySet()) {
            sendToSession(sessionId, message);
        }
        log.debug("消息广播到所有连接: connections={}, type={}", 
                 connections.size(), message.getType());
    }

    /**
     * 发送心跳消息
     */
    private void sendHeartbeat() {
        if (connections.isEmpty()) {
            return;
        }

        RealtimeMessage heartbeatMessage = RealtimeMessage.builder()
                .type("HEARTBEAT")
                .timestamp(LocalDateTime.now())
                .build();

        for (String sessionId : connections.keySet()) {
            sendToSession(sessionId, heartbeatMessage);
        }

        log.debug("发送心跳消息到 {} 个连接", connections.size());
    }

    /**
     * 清理过期连接
     */
    private void cleanupExpiredConnections() {
        List<String> expiredSessions = new ArrayList<>();
        
        for (Map.Entry<String, ConnectionInfo> entry : connections.entrySet()) {
            if (entry.getValue().isExpired()) {
                expiredSessions.add(entry.getKey());
            }
        }

        for (String sessionId : expiredSessions) {
            log.warn("清理过期连接: sessionId={}", sessionId);
            removeConnection(sessionId);
        }

        if (!expiredSessions.isEmpty()) {
            log.info("清理了 {} 个过期连接", expiredSessions.size());
        }
    }

    /**
     * 发送连接消息
     */
    private void sendConnectionMessage(String sessionId, String type, String content) {
        RealtimeMessage message = RealtimeMessage.builder()
                .type(type)
                .content(content)
                .timestamp(LocalDateTime.now())
                .build();
        
        sendToSession(sessionId, message);
    }

    /**
     * 存储连接信息到Redis
     */
    private void storeConnectionToRedis(ConnectionInfo connectionInfo) {
        try {
            String key = "realtime:connection:" + connectionInfo.getSessionId();
            Map<String, Object> data = new HashMap<>();
            data.put("sessionId", connectionInfo.getSessionId());
            data.put("userId", connectionInfo.getUserId());
            data.put("connectedAt", connectionInfo.getConnectedAt().toString());
            data.put("lastHeartbeat", connectionInfo.getLastHeartbeat().toString());
            redisTemplate.opsForHash().putAll(key, data);
            redisTemplate.expire(key, CONNECTION_TIMEOUT, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("存储连接信息到Redis失败: sessionId={}", connectionInfo.getSessionId(), e);
        }
    }

    /**
     * 从Redis中移除连接信息
     */
    private void removeConnectionFromRedis(String sessionId) {
        try {
            String key = "realtime:connection:" + sessionId;
            redisTemplate.delete(key);
        } catch (Exception e) {
            log.error("从Redis移除连接信息失败: sessionId={}", sessionId, e);
        }
    }

    /**
     * 获取连接统计信息
     */
    public Map<String, Object> getConnectionStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalConnections", connections.size());
        stats.put("totalUsers", userSessions.size());
        stats.put("totalTopics", topicSubscriptions.size());
        stats.put("timestamp", LocalDateTime.now());
        return stats;
    }

    /**
     * 获取用户是否在线
     */
    public boolean isUserOnline(String userId) {
        Set<String> sessions = userSessions.get(userId);
        return sessions != null && !sessions.isEmpty();
    }

    /**
     * 获取用户的连接信息
     */
    public List<ConnectionInfo> getUserConnections(String userId) {
        Set<String> sessions = userSessions.get(userId);
        if (sessions == null || sessions.isEmpty()) {
            return Collections.emptyList();
        }

        List<ConnectionInfo> userConnections = new ArrayList<>();
        for (String sessionId : sessions) {
            ConnectionInfo connectionInfo = connections.get(sessionId);
            if (connectionInfo != null) {
                userConnections.add(connectionInfo);
            }
        }
        return userConnections;
    }

    /**
     * 关闭连接管理器
     */
    public void shutdown() {
        log.info("关闭连接管理器...");
        heartbeatExecutor.shutdown();
        connections.clear();
        userSessions.clear();
        topicSubscriptions.clear();
        log.info("连接管理器已关闭");
    }
}
