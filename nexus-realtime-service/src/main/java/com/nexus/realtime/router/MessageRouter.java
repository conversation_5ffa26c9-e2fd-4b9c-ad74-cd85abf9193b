package com.nexus.realtime.router;

import com.nexus.realtime.dto.RealtimeMessage;
import com.nexus.realtime.manager.ConnectionManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Predicate;

/**
 * 消息路由器
 * 负责智能消息分发、过滤、路由等功能
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class MessageRouter {

    private final ConnectionManager connectionManager;

    // 消息过滤器
    private final Map<String, List<MessageFilter>> topicFilters = new ConcurrentHashMap<>();
    private final Map<String, List<MessageFilter>> userFilters = new ConcurrentHashMap<>();

    /**
     * 消息过滤器接口
     */
    @FunctionalInterface
    public interface MessageFilter {
        boolean test(RealtimeMessage message, Map<String, Object> context);
    }

    /**
     * 路由消息
     */
    public void routeMessage(RealtimeMessage message) {
        try {
            log.debug("路由消息: type={}, messageId={}", message.getType(), message.getMessageId());

            // 根据消息类型进行路由
            switch (message.getType()) {
                case "SERVICE_STATUS_UPDATE":
                    routeServiceStatusUpdate(message);
                    break;
                case "TASK_PROGRESS_UPDATE":
                    routeTaskProgressUpdate(message);
                    break;
                case "CHAIN_EXECUTION_STATUS":
                    routeChainExecutionStatus(message);
                    break;
                case "MCP_TOOL_RESULT":
                    routeMcpToolResult(message);
                    break;
                case "SYSTEM_NOTIFICATION":
                    routeSystemNotification(message);
                    break;
                case "USER_MESSAGE":
                    routeUserMessage(message);
                    break;
                case "BROADCAST":
                    routeBroadcastMessage(message);
                    break;
                default:
                    routeGenericMessage(message);
                    break;
            }

        } catch (Exception e) {
            log.error("路由消息失败: messageId={}", message.getMessageId(), e);
        }
    }

    /**
     * 路由服务状态更新消息
     */
    private void routeServiceStatusUpdate(RealtimeMessage message) {
        String topic = "service.status." + message.getServiceId();
        
        // 应用主题过滤器
        RealtimeMessage filteredMessage = applyTopicFilters(topic, message);
        if (filteredMessage != null) {
            connectionManager.broadcastToTopic(topic, filteredMessage);
            
            // 同时广播到通用服务状态主题
            connectionManager.broadcastToTopic("service.status", filteredMessage);
        }
    }

    /**
     * 路由任务进度更新消息
     */
    private void routeTaskProgressUpdate(RealtimeMessage message) {
        // 发送给特定用户
        if (message.getReceiverId() != null) {
            RealtimeMessage filteredMessage = applyUserFilters(message.getReceiverId(), message);
            if (filteredMessage != null) {
                connectionManager.sendToUser(message.getReceiverId(), filteredMessage);
            }
        }

        // 同时发送到任务主题
        String topic = "task.progress." + message.getTaskId();
        connectionManager.broadcastToTopic(topic, message);
    }

    /**
     * 路由服务链执行状态消息
     */
    private void routeChainExecutionStatus(RealtimeMessage message) {
        // 发送给特定用户
        if (message.getReceiverId() != null) {
            RealtimeMessage filteredMessage = applyUserFilters(message.getReceiverId(), message);
            if (filteredMessage != null) {
                connectionManager.sendToUser(message.getReceiverId(), filteredMessage);
            }
        }

        // 发送到服务链执行主题
        String topic = "chain.execution";
        connectionManager.broadcastToTopic(topic, message);
    }

    /**
     * 路由MCP工具结果消息
     */
    private void routeMcpToolResult(RealtimeMessage message) {
        // 发送给特定用户
        if (message.getReceiverId() != null) {
            RealtimeMessage filteredMessage = applyUserFilters(message.getReceiverId(), message);
            if (filteredMessage != null) {
                connectionManager.sendToUser(message.getReceiverId(), filteredMessage);
            }
        }

        // 发送到工具结果主题
        String topic = "mcp.tool.result." + message.getToolName();
        connectionManager.broadcastToTopic(topic, message);
    }

    /**
     * 路由系统通知消息
     */
    private void routeSystemNotification(RealtimeMessage message) {
        String topic = "system.notification";
        
        // 根据通知级别决定路由策略
        if ("ERROR".equals(message.getLevel()) || "CRITICAL".equals(message.getLevel())) {
            // 高优先级通知广播给所有用户
            connectionManager.broadcastToAll(message);
        } else {
            // 普通通知发送到主题订阅者
            RealtimeMessage filteredMessage = applyTopicFilters(topic, message);
            if (filteredMessage != null) {
                connectionManager.broadcastToTopic(topic, filteredMessage);
            }
        }
    }

    /**
     * 路由用户消息
     */
    private void routeUserMessage(RealtimeMessage message) {
        if (message.getReceiverId() != null) {
            RealtimeMessage filteredMessage = applyUserFilters(message.getReceiverId(), message);
            if (filteredMessage != null) {
                connectionManager.sendToUser(message.getReceiverId(), filteredMessage);
            }
        } else {
            log.warn("用户消息缺少接收者ID: messageId={}", message.getMessageId());
        }
    }

    /**
     * 路由广播消息
     */
    private void routeBroadcastMessage(RealtimeMessage message) {
        connectionManager.broadcastToAll(message);
    }

    /**
     * 路由通用消息
     */
    private void routeGenericMessage(RealtimeMessage message) {
        // 如果有接收者，发送给特定用户
        if (message.getReceiverId() != null) {
            connectionManager.sendToUser(message.getReceiverId(), message);
        } else {
            // 否则广播给所有连接
            connectionManager.broadcastToAll(message);
        }
    }

    /**
     * 应用主题过滤器
     */
    private RealtimeMessage applyTopicFilters(String topic, RealtimeMessage message) {
        List<MessageFilter> filters = topicFilters.get(topic);
        if (filters == null || filters.isEmpty()) {
            return message;
        }

        Map<String, Object> context = createFilterContext(topic, null);
        
        for (MessageFilter filter : filters) {
            try {
                if (!filter.test(message, context)) {
                    log.debug("消息被主题过滤器拒绝: topic={}, messageId={}", topic, message.getMessageId());
                    return null;
                }
            } catch (Exception e) {
                log.error("主题过滤器执行失败: topic={}, messageId={}", topic, message.getMessageId(), e);
            }
        }

        return message;
    }

    /**
     * 应用用户过滤器
     */
    private RealtimeMessage applyUserFilters(String userId, RealtimeMessage message) {
        List<MessageFilter> filters = userFilters.get(userId);
        if (filters == null || filters.isEmpty()) {
            return message;
        }

        Map<String, Object> context = createFilterContext(null, userId);
        
        for (MessageFilter filter : filters) {
            try {
                if (!filter.test(message, context)) {
                    log.debug("消息被用户过滤器拒绝: userId={}, messageId={}", userId, message.getMessageId());
                    return null;
                }
            } catch (Exception e) {
                log.error("用户过滤器执行失败: userId={}, messageId={}", userId, message.getMessageId(), e);
            }
        }

        return message;
    }

    /**
     * 创建过滤器上下文
     */
    private Map<String, Object> createFilterContext(String topic, String userId) {
        Map<String, Object> context = new HashMap<>();
        if (topic != null) {
            context.put("topic", topic);
        }
        if (userId != null) {
            context.put("userId", userId);
            context.put("userOnline", connectionManager.isUserOnline(userId));
        }
        context.put("timestamp", System.currentTimeMillis());
        return context;
    }

    /**
     * 添加主题过滤器
     */
    public void addTopicFilter(String topic, MessageFilter filter) {
        topicFilters.computeIfAbsent(topic, k -> new ArrayList<>()).add(filter);
        log.info("添加主题过滤器: topic={}", topic);
    }

    /**
     * 移除主题过滤器
     */
    public void removeTopicFilter(String topic, MessageFilter filter) {
        List<MessageFilter> filters = topicFilters.get(topic);
        if (filters != null) {
            filters.remove(filter);
            if (filters.isEmpty()) {
                topicFilters.remove(topic);
            }
        }
        log.info("移除主题过滤器: topic={}", topic);
    }

    /**
     * 添加用户过滤器
     */
    public void addUserFilter(String userId, MessageFilter filter) {
        userFilters.computeIfAbsent(userId, k -> new ArrayList<>()).add(filter);
        log.info("添加用户过滤器: userId={}", userId);
    }

    /**
     * 移除用户过滤器
     */
    public void removeUserFilter(String userId, MessageFilter filter) {
        List<MessageFilter> filters = userFilters.get(userId);
        if (filters != null) {
            filters.remove(filter);
            if (filters.isEmpty()) {
                userFilters.remove(userId);
            }
        }
        log.info("移除用户过滤器: userId={}", userId);
    }

    /**
     * 创建常用过滤器
     */
    public static class CommonFilters {
        
        /**
         * 消息级别过滤器
         */
        public static MessageFilter levelFilter(String... allowedLevels) {
            Set<String> allowed = new HashSet<>(Arrays.asList(allowedLevels));
            return (message, context) -> {
                String level = message.getLevel();
                return level == null || allowed.contains(level);
            };
        }

        /**
         * 消息类型过滤器
         */
        public static MessageFilter typeFilter(String... allowedTypes) {
            Set<String> allowed = new HashSet<>(Arrays.asList(allowedTypes));
            return (message, context) -> allowed.contains(message.getType());
        }

        /**
         * 服务ID过滤器
         */
        public static MessageFilter serviceFilter(String... allowedServices) {
            Set<String> allowed = new HashSet<>(Arrays.asList(allowedServices));
            return (message, context) -> {
                String serviceId = message.getServiceId();
                return serviceId == null || allowed.contains(serviceId);
            };
        }

        /**
         * 时间范围过滤器
         */
        public static MessageFilter timeRangeFilter(long startTime, long endTime) {
            return (message, context) -> {
                if (message.getTimestamp() == null) {
                    return true;
                }
                long timestamp = java.time.ZoneOffset.UTC.getRules()
                        .getOffset(message.getTimestamp()).getTotalSeconds() * 1000;
                return timestamp >= startTime && timestamp <= endTime;
            };
        }

        /**
         * 用户在线状态过滤器
         */
        public static MessageFilter onlineUserFilter() {
            return (message, context) -> {
                Boolean userOnline = (Boolean) context.get("userOnline");
                return userOnline == null || userOnline;
            };
        }
    }

    /**
     * 获取路由统计信息
     */
    public Map<String, Object> getRoutingStats() {
        return Map.of(
                "topicFilters", topicFilters.size(),
                "userFilters", userFilters.size(),
                "timestamp", System.currentTimeMillis()
        );
    }
}
