package com.nexus.realtime.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nexus.realtime.dto.RealtimeMessage;
import com.nexus.realtime.dto.SubscriptionRequest;
import com.nexus.realtime.manager.ConnectionManager;
import com.nexus.realtime.router.MessageRouter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 实时服务核心实现
 * 提供WebSocket、Redis Stream、gRPC等多种实时通信方式
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RealtimeService {

    private final SimpMessagingTemplate messagingTemplate;
    private final RedisTemplate<String, Object> redisTemplate;
    private final RedisMessageListenerContainer messageListenerContainer;
    private final ObjectMapper objectMapper;
    private final ConnectionManager connectionManager;
    private final MessageRouter messageRouter;

    // 活跃连接管理
    private final Map<String, String> activeConnections = new ConcurrentHashMap<>();
    
    // 订阅主题管理
    private final Map<String, ChannelTopic> subscriptionTopics = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        log.info("Realtime Service initialized");

        // 初始化连接管理器
        connectionManager.init();
    }

    /**
     * 发送实时消息到指定用户
     */
    public void sendToUser(String userId, RealtimeMessage message) {
        try {
            // 设置消息ID
            if (message.getMessageId() == null) {
                message.setMessageId(UUID.randomUUID().toString());
            }

            // 设置接收者
            message.setReceiverId(userId);

            // 使用消息路由器路由消息
            messageRouter.routeMessage(message);

            log.debug("Message sent to user {}: {}", userId, message.getType());
        } catch (Exception e) {
            log.error("Failed to send message to user {}: {}", userId, e.getMessage());
        }
    }

    /**
     * 广播消息到所有连接的客户端
     */
    public void broadcast(RealtimeMessage message) {
        try {
            // 设置消息ID
            if (message.getMessageId() == null) {
                message.setMessageId(UUID.randomUUID().toString());
            }

            // 设置为广播类型
            message.setType("BROADCAST");

            // 使用消息路由器路由消息
            messageRouter.routeMessage(message);

            log.debug("Broadcast message sent: {}", message.getType());
        } catch (Exception e) {
            log.error("Failed to broadcast message: {}", e.getMessage());
        }
    }

    /**
     * 发送主题消息
     */
    public void sendToTopic(String topic, RealtimeMessage message) {
        try {
            // 设置消息ID
            if (message.getMessageId() == null) {
                message.setMessageId(UUID.randomUUID().toString());
            }

            // 广播到主题订阅者
            connectionManager.broadcastToTopic(topic, message);

            log.debug("Message sent to topic {}: {}", topic, message.getType());
        } catch (Exception e) {
            log.error("Failed to send message to topic {}: {}", topic, e.getMessage());
        }
    }

    /**
     * 发送服务状态更新
     */
    public void sendServiceStatusUpdate(String serviceId, String status, Map<String, Object> details) {
        RealtimeMessage message = RealtimeMessage.builder()
                .type("SERVICE_STATUS_UPDATE")
                .serviceId(serviceId)
                .status(status)
                .data(details)
                .timestamp(LocalDateTime.now())
                .build();
        
        broadcast(message);
    }

    /**
     * 发送任务进度更新
     */
    public void sendTaskProgressUpdate(String taskId, String userId, int progress, String status) {
        RealtimeMessage message = RealtimeMessage.builder()
                .type("TASK_PROGRESS_UPDATE")
                .taskId(taskId)
                .progress(progress)
                .status(status)
                .timestamp(LocalDateTime.now())
                .build();
        
        sendToUser(userId, message);
    }

    /**
     * 发送MCP工具执行结果
     */
    public void sendMcpToolResult(String userId, String toolName, Object result, boolean success) {
        RealtimeMessage message = RealtimeMessage.builder()
                .type("MCP_TOOL_RESULT")
                .toolName(toolName)
                .data(result)
                .success(success)
                .timestamp(LocalDateTime.now())
                .build();
        
        sendToUser(userId, message);
    }

    /**
     * 发送系统通知
     */
    public void sendSystemNotification(String title, String content, String level) {
        RealtimeMessage message = RealtimeMessage.builder()
                .type("SYSTEM_NOTIFICATION")
                .title(title)
                .content(content)
                .level(level)
                .timestamp(LocalDateTime.now())
                .build();
        
        broadcast(message);
    }

    /**
     * 订阅特定主题
     */
    public void subscribe(String userId, SubscriptionRequest request) {
        try {
            String topicKey = request.getTopic() + ":" + userId;
            ChannelTopic topic = new ChannelTopic(request.getTopic());
            
            subscriptionTopics.put(topicKey, topic);
            
            // 添加Redis消息监听器
            messageListenerContainer.addMessageListener((message, pattern) -> {
                try {
                    String content = new String(message.getBody());
                    RealtimeMessage realtimeMessage = objectMapper.readValue(content, RealtimeMessage.class);
                    sendToUser(userId, realtimeMessage);
                } catch (Exception e) {
                    log.error("Failed to process subscription message: {}", e.getMessage());
                }
            }, topic);
            
            log.info("User {} subscribed to topic: {}", userId, request.getTopic());
        } catch (Exception e) {
            log.error("Failed to subscribe user {} to topic {}: {}", userId, request.getTopic(), e.getMessage());
        }
    }

    /**
     * 取消订阅
     */
    public void unsubscribe(String userId, String topic) {
        try {
            String topicKey = topic + ":" + userId;
            ChannelTopic channelTopic = subscriptionTopics.remove(topicKey);
            
            if (channelTopic != null) {
                // 移除Redis消息监听器
                messageListenerContainer.removeMessageListener(null, channelTopic);
                log.info("User {} unsubscribed from topic: {}", userId, topic);
            }
        } catch (Exception e) {
            log.error("Failed to unsubscribe user {} from topic {}: {}", userId, topic, e.getMessage());
        }
    }

    /**
     * 记录用户连接
     */
    public void addConnection(String sessionId, String userId) {
        activeConnections.put(sessionId, userId);
        log.info("User {} connected with session: {}", userId, sessionId);
        
        // 发送连接成功消息
        RealtimeMessage welcomeMessage = RealtimeMessage.builder()
                .type("CONNECTION_ESTABLISHED")
                .content("Welcome to Nexus Realtime Service")
                .timestamp(LocalDateTime.now())
                .build();
        
        sendToUser(userId, welcomeMessage);
    }

    /**
     * 移除用户连接
     */
    public void removeConnection(String sessionId) {
        String userId = activeConnections.remove(sessionId);
        if (userId != null) {
            log.info("User {} disconnected, session: {}", userId, sessionId);
            
            // 清理该用户的所有订阅
            subscriptionTopics.entrySet().removeIf(entry -> entry.getKey().endsWith(":" + userId));
        }
    }

    /**
     * 获取活跃连接数
     */
    public int getActiveConnectionCount() {
        return activeConnections.size();
    }

    /**
     * 获取用户的活跃会话
     */
    public String getUserSession(String userId) {
        return activeConnections.entrySet().stream()
                .filter(entry -> userId.equals(entry.getValue()))
                .map(Map.Entry::getKey)
                .findFirst()
                .orElse(null);
    }

    /**
     * 检查用户是否在线
     */
    public boolean isUserOnline(String userId) {
        return activeConnections.containsValue(userId);
    }
}
