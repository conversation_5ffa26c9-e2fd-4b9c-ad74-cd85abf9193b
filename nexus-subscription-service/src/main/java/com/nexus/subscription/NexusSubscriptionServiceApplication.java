package com.nexus.subscription;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Nexus订阅服务启动类
 * 
 * 功能：
 * - 管理用户对MCP服务的订阅关系
 * - 处理订阅的创建、更新、取消
 * - 管理用户权限和访问控制
 * - 监控订阅使用情况和统计
 * - 处理订阅相关的计费和限制
 */
@SpringBootApplication(scanBasePackages = {
    "com.nexus.subscription",  // 当前服务包
    "com.nexus.common"         // 公共组件包
})
@EnableDiscoveryClient
@EnableFeignClients
@EnableCaching
@EnableAsync
@EnableScheduling
@EnableTransactionManagement
@EntityScan(basePackages = {
    "com.nexus.common.entity",         // 公共实体类
    "com.nexus.subscription.entity"    // 订阅服务实体类
})
@EnableJpaRepositories(basePackages = {
    "com.nexus.subscription.repository" // 订阅服务Repository
})
public class NexusSubscriptionServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(NexusSubscriptionServiceApplication.class, args);
    }
}
