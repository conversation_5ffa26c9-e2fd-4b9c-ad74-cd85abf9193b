package com.nexus.subscription.consumer;

import com.nexus.common.constants.RocketMQConstants;
import com.nexus.common.event.SubscriptionCreatedEvent;
import com.nexus.common.event.SubscriptionStatusChangedEvent;
import com.nexus.common.event.EmailNotificationEvent;
import com.nexus.common.event.StatisticsUpdateEvent;
import com.nexus.common.event.AuditLogEvent;
import com.nexus.common.service.RocketMQProducerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 订阅事件消费者
 * 处理订阅创建和状态变更事件的异步任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
    topic = RocketMQConstants.SUBSCRIPTION_EVENT_TOPIC,
    selectorExpression = "subscription-created",
    consumerGroup = "subscription-event-consumer-group",
    nameServer = "${rocketmq.name-server:localhost:9876}"
)
public class SubscriptionEventConsumer implements RocketMQListener<SubscriptionCreatedEvent> {

    private final RocketMQProducerService messageProducerService;

    /**
     * RocketMQ消息监听器实现
     */
    @Override
    public void onMessage(SubscriptionCreatedEvent event) {
        log.info("接收到订阅创建事件: eventId={}, subscriptionId={}, userId={}",
                event.getEventId(), event.getSubscriptionId(), event.getUserId());

        try {
            // 验证事件数据
            if (!event.isValid()) {
                log.warn("订阅创建事件数据无效: eventId={}", event.getEventId());
                return;
            }

            // 处理订阅创建事件
            processSubscriptionCreated(event);

            log.info("订阅创建事件处理成功: eventId={}", event.getEventId());

        } catch (Exception e) {
            log.error("订阅创建事件处理失败: eventId={} - {}",
                    event.getEventId(), e.getMessage(), e);
            // RocketMQ会自动重试
        }
    }

    /**
     * 处理订阅创建事件
     */
    private void processSubscriptionCreated(SubscriptionCreatedEvent event) {
        log.info("处理订阅创建事件: subscriptionId={}, userId={}, serviceName={}",
                event.getSubscriptionId(), event.getUserId(), event.getServiceName());

        try {
            // 1. 发送订阅确认邮件事件
            sendSubscriptionConfirmEmailEvent(event);

            // 2. 发送统计更新事件
            sendSubscriptionStatisticsEvent(event);

            // 3. 发送审计日志事件
            sendSubscriptionAuditEvent(event);

            // 4. 发送系统通知事件（可选）
            sendSubscriptionNotificationEvent(event);

            log.info("订阅创建事件处理完成: subscriptionId={}", event.getSubscriptionId());

        } catch (Exception e) {
            log.error("处理订阅创建事件异常: subscriptionId={} - {}",
                    event.getSubscriptionId(), e.getMessage(), e);
            throw e; // 重新抛出异常，让RocketMQ处理重试
        }
    }
    
    /**
     * 发送订阅确认邮件事件
     */
    private boolean sendSubscriptionConfirmEmailEvent(SubscriptionCreatedEvent event) {
        try {
            // 准备邮件模板参数
            Map<String, Object> templateParams = new HashMap<>();
            templateParams.put("subscriptionId", event.getSubscriptionId());
            templateParams.put("serviceName", event.getServiceName());
            templateParams.put("startDate", event.getStartDate());
            templateParams.put("endDate", event.getEndDate());
            templateParams.put("callLimit", event.getCallLimit());
            templateParams.put("notes", event.getNotes());
            templateParams.put("subscriptionTime", event.getTimestamp());
            
            // 创建邮件通知事件
            EmailNotificationEvent emailEvent = new EmailNotificationEvent(
                    event.getUserId(),
                    null, // 邮箱地址需要从用户服务获取
                    null, // 用户名需要从用户服务获取
                    "订阅确认 - " + event.getServiceName(),
                    "subscription_confirm", // 模板名称
                    templateParams,
                    EmailNotificationEvent.EmailType.SUBSCRIPTION_CONFIRM
            );
            
            emailEvent.setSourceService("nexus-subscription-service");
            
            boolean sent = messageProducerService.sendEventAsync(emailEvent);
            if (sent) {
                log.debug("订阅确认邮件事件发送成功: subscriptionId={}", event.getSubscriptionId());
            } else {
                log.warn("订阅确认邮件事件发送失败: subscriptionId={}", event.getSubscriptionId());
            }

            return sent;
            
        } catch (Exception e) {
            log.error("发送订阅确认邮件事件异常: subscriptionId={} - {}", 
                    event.getSubscriptionId(), e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 发送订阅统计更新事件
     */
    private boolean sendSubscriptionStatisticsEvent(SubscriptionCreatedEvent event) {
        try {
            // 准备统计指标
            Map<String, Object> metrics = new HashMap<>();
            metrics.put("subscriptionId", event.getSubscriptionId());
            metrics.put("userId", event.getUserId());
            metrics.put("serviceConfigId", event.getServiceConfigId());
            metrics.put("serviceName", event.getServiceName());
            metrics.put("startDate", event.getStartDate());
            metrics.put("endDate", event.getEndDate());
            metrics.put("callLimit", event.getCallLimit());
            metrics.put("subscriptionTime", event.getTimestamp());
            
            // 准备增量数据
            Map<String, Number> increments = new HashMap<>();
            increments.put("totalSubscriptions", 1);
            increments.put("dailySubscriptions", 1);
            increments.put("monthlySubscriptions", 1);
            increments.put("activeSubscriptions", 1);
            
            // 按服务统计
            String serviceKey = "subscriptions_" + event.getServiceName().toLowerCase().replaceAll("[^a-z0-9]", "_");
            increments.put(serviceKey, 1);
            
            // 创建统计更新事件
            StatisticsUpdateEvent statsEvent = new StatisticsUpdateEvent(
                    event.getUserId(),
                    StatisticsUpdateEvent.StatisticsType.SUBSCRIPTION_USAGE,
                    "service:" + event.getServiceConfigId(), // 按服务维度
                    metrics,
                    increments,
                    "daily" // 时间窗口
            );
            
            statsEvent.setSourceService("nexus-subscription-service");
            
            boolean sent = messageProducerService.sendEventAsync(statsEvent);
            if (sent) {
                log.debug("订阅统计更新事件发送成功: subscriptionId={}", event.getSubscriptionId());
            } else {
                log.warn("订阅统计更新事件发送失败: subscriptionId={}", event.getSubscriptionId());
            }

            return sent;
            
        } catch (Exception e) {
            log.error("发送订阅统计更新事件异常: subscriptionId={} - {}", 
                    event.getSubscriptionId(), e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 发送订阅审计日志事件
     */
    private boolean sendSubscriptionAuditEvent(SubscriptionCreatedEvent event) {
        try {
            // 准备审计属性
            Map<String, Object> attributes = new HashMap<>();
            attributes.put("subscriptionId", event.getSubscriptionId());
            attributes.put("serviceConfigId", event.getServiceConfigId());
            attributes.put("serviceName", event.getServiceName());
            attributes.put("startDate", event.getStartDate());
            attributes.put("endDate", event.getEndDate());
            attributes.put("callLimit", event.getCallLimit());
            
            // 创建审计日志事件
            AuditLogEvent auditEvent = new AuditLogEvent(
                    event.getUserId(),
                    "SUBSCRIPTION_CREATED", // 操作类型
                    "SUBSCRIPTION", // 资源类型
                    event.getSubscriptionId().toString(), // 资源ID
                    "SUCCESS", // 操作结果
                    String.format("用户订阅服务成功: %s (ID: %d)", event.getServiceName(), event.getSubscriptionId()),
                    null, // clientIp
                    null, // userAgent
                    event.getEventId() // requestId
            );
            
            auditEvent.setAttributes(attributes);
            auditEvent.setSourceService("nexus-subscription-service");
            
            boolean sent = messageProducerService.sendEventAsync(auditEvent);
            if (sent) {
                log.debug("订阅审计日志事件发送成功: subscriptionId={}", event.getSubscriptionId());
            } else {
                log.warn("订阅审计日志事件发送失败: subscriptionId={}", event.getSubscriptionId());
            }

            return sent;
            
        } catch (Exception e) {
            log.error("发送订阅审计日志事件异常: subscriptionId={} - {}", 
                    event.getSubscriptionId(), e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 发送订阅系统通知事件
     */
    private boolean sendSubscriptionNotificationEvent(SubscriptionCreatedEvent event) {
        try {
            // 这里可以发送系统通知，比如WebSocket推送、短信通知等
            // 暂时返回true，表示成功
            log.debug("订阅系统通知事件处理: subscriptionId={}", event.getSubscriptionId());
            return true;
            
        } catch (Exception e) {
            log.error("发送订阅系统通知事件异常: subscriptionId={} - {}", 
                    event.getSubscriptionId(), e.getMessage(), e);
            return false;
        }
    }
}
