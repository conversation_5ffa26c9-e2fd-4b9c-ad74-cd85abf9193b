package com.nexus.subscription.consumer;

import com.nexus.common.constants.RocketMQConstants;
import com.nexus.common.event.StatisticsUpdateEvent;
import com.nexus.subscription.service.SubscriptionStatisticsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 订阅统计更新消费者
 * 专门处理订阅相关的统计更新事件
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
    topic = RocketMQConstants.STATISTICS_TOPIC,
    selectorExpression = RocketMQConstants.STATISTICS_TAG,
    consumerGroup = "subscription-statistics-consumer-group",
    nameServer = "${rocketmq.name-server:localhost:9876}"
)
public class SubscriptionStatisticsConsumer implements RocketMQListener<StatisticsUpdateEvent> {

    private final SubscriptionStatisticsService subscriptionStatisticsService;

    /**
     * RocketMQ消息监听器实现
     */
    @Override
    public void onMessage(StatisticsUpdateEvent event) {
        log.info("接收到统计更新事件: eventId={}, type={}, dimension={}",
                event.getEventId(), event.getStatisticsType(), event.getDimension());

        try {
            // 只处理订阅相关的统计事件
            if (isSubscriptionRelatedEvent(event)) {
                // 验证事件数据
                if (!event.isValid()) {
                    log.warn("统计更新事件数据无效: eventId={}", event.getEventId());
                    return;
                }

                // 处理订阅统计更新事件
                processSubscriptionStatistics(event);

                log.info("订阅统计更新事件处理成功: eventId={}", event.getEventId());
            } else {
                log.debug("跳过非订阅相关的统计事件: type={}, dimension={}",
                        event.getStatisticsType(), event.getDimension());
            }

        } catch (Exception e) {
            log.error("订阅统计更新事件处理失败: eventId={} - {}",
                    event.getEventId(), e.getMessage(), e);
            // RocketMQ会自动重试
        }
    }

    /**
     * 处理订阅统计更新事件
     */
    private void processSubscriptionStatistics(StatisticsUpdateEvent event) {
        log.info("处理订阅统计更新事件: type={}, dimension={}, userId={}",
                event.getStatisticsType(), event.getDimension(), event.getUserId());

        try {
            // 根据统计类型处理不同的订阅统计
            switch (event.getStatisticsType()) {
                case SUBSCRIPTION_USAGE:
                    processSubscriptionUsageStatistics(event);
                    break;
                case USER_REGISTRATION:
                    processUserRegistrationStatistics(event);
                    break;
                default:
                    log.debug("不支持的统计类型: {}", event.getStatisticsType());
                    break;
            }

        } catch (Exception e) {
            log.error("处理订阅统计更新事件异常: eventId={} - {}",
                    event.getEventId(), e.getMessage(), e);
            throw e; // 重新抛出异常，让RocketMQ处理重试
        }
    }
    
    /**
     * 处理订阅使用统计
     */
    private void processSubscriptionUsageStatistics(StatisticsUpdateEvent event) {
        try {
            log.debug("处理订阅使用统计: dimension={}", event.getDimension());

            // 更新订阅统计数据
            subscriptionStatisticsService.updateSubscriptionStatistics(
                    "subscription_usage",
                    event.getDimension(),
                    event.getIncrements(),
                    event.getTimeWindow()
            );

            // 处理特定的订阅指标
            if (event.getMetrics() != null) {
                processSubscriptionMetrics(event.getMetrics());
            }

            log.debug("订阅使用统计处理完成: dimension={}", event.getDimension());

        } catch (Exception e) {
            log.error("处理订阅使用统计异常: dimension={} - {}",
                    event.getDimension(), e.getMessage(), e);
            throw e; // 重新抛出异常，让RocketMQ处理重试
        }
    }
    
    /**
     * 处理用户注册统计（影响订阅统计）
     */
    private void processUserRegistrationStatistics(StatisticsUpdateEvent event) {
        try {
            log.debug("处理用户注册统计: userId={}", event.getUserId());

            // 用户注册会影响潜在订阅用户数统计
            subscriptionStatisticsService.updateSubscriptionStatistics(
                    "potential_subscribers",
                    "user_registration",
                    event.getIncrements(),
                    event.getTimeWindow()
            );

        } catch (Exception e) {
            log.error("处理用户注册统计异常: userId={} - {}",
                    event.getUserId(), e.getMessage(), e);
            throw e; // 重新抛出异常，让RocketMQ处理重试
        }
    }
    
    /**
     * 处理订阅指标
     */
    private void processSubscriptionMetrics(Map<String, Object> metrics) {
        try {
            log.debug("处理订阅指标: {}", metrics);
            
            // 处理订阅相关的详细指标
            if (metrics.containsKey("subscriptionId")) {
                Long subscriptionId = (Long) metrics.get("subscriptionId");
                log.debug("处理订阅ID {} 的指标", subscriptionId);
                
                // 可以在这里添加更详细的订阅指标处理逻辑
                // 比如：
                // - 订阅生命周期分析
                // - 订阅价值计算
                // - 用户行为分析
            }
            
            if (metrics.containsKey("serviceName")) {
                String serviceName = (String) metrics.get("serviceName");
                log.debug("处理服务 {} 的指标", serviceName);
                
                // 可以在这里添加服务级别的指标处理
                // 比如：
                // - 服务受欢迎程度
                // - 服务收入贡献
                // - 服务使用趋势
            }
            
        } catch (Exception e) {
            log.error("处理订阅指标异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 判断是否为订阅相关的统计事件
     */
    private boolean isSubscriptionRelatedEvent(StatisticsUpdateEvent event) {
        if (event.getStatisticsType() == null) {
            return false;
        }
        
        switch (event.getStatisticsType()) {
            case SUBSCRIPTION_USAGE:
                return true;
            case USER_REGISTRATION:
                // 用户注册间接影响订阅统计
                return true;
            case API_CALL:
                // API调用可能与订阅使用相关
                return event.getDimension() != null && 
                       event.getDimension().startsWith("subscription:");
            default:
                return false;
        }
    }
    
    /**
     * 判断是否为重要的统计数据
     */
    private boolean isImportantStatistics(StatisticsUpdateEvent event) {
        if (event.getStatisticsType() == null) {
            return false;
        }

        switch (event.getStatisticsType()) {
            case SUBSCRIPTION_USAGE:
                return true; // 订阅使用统计很重要
            default:
                return false;
        }
    }
}
