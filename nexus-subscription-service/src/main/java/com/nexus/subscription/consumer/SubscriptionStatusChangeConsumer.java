package com.nexus.subscription.consumer;

import com.nexus.common.constants.RocketMQConstants;
import com.nexus.common.event.SubscriptionStatusChangedEvent;
import com.nexus.common.event.EmailNotificationEvent;
import com.nexus.common.event.StatisticsUpdateEvent;
import com.nexus.common.event.AuditLogEvent;
import com.nexus.common.service.RocketMQProducerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 订阅状态变更事件消费者
 * 处理订阅状态变更的异步任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
    topic = RocketMQConstants.SUBSCRIPTION_EVENT_TOPIC,
    selectorExpression = "subscription-status-changed",
    consumerGroup = "subscription-status-change-consumer-group",
    nameServer = "${rocketmq.name-server:localhost:9876}"
)
public class SubscriptionStatusChangeConsumer implements RocketMQListener<SubscriptionStatusChangedEvent> {

    private final RocketMQProducerService messageProducerService;

    /**
     * RocketMQ消息监听器实现
     */
    @Override
    public void onMessage(SubscriptionStatusChangedEvent event) {
        log.info("接收到订阅状态变更事件: eventId={}, subscriptionId={}, oldStatus={}, newStatus={}",
                event.getEventId(), event.getSubscriptionId(), event.getOldStatus(), event.getNewStatus());

        try {
            // 验证事件数据
            if (!event.isValid()) {
                log.warn("订阅状态变更事件数据无效: eventId={}", event.getEventId());
                return;
            }

            // 处理订阅状态变更事件
            processSubscriptionStatusChanged(event);

            log.info("订阅状态变更事件处理成功: eventId={}", event.getEventId());

        } catch (Exception e) {
            log.error("订阅状态变更事件处理失败: eventId={} - {}",
                    event.getEventId(), e.getMessage(), e);
            // RocketMQ会自动重试
        }
    }

    /**
     * 处理订阅状态变更事件
     */
    private void processSubscriptionStatusChanged(SubscriptionStatusChangedEvent event) {
        log.info("处理订阅状态变更事件: subscriptionId={}, oldStatus={}, newStatus={}, reason={}",
                event.getSubscriptionId(), event.getOldStatus(), event.getNewStatus(), event.getChangeReason());

        try {
            // 1. 发送状态变更通知邮件（如果需要）
            sendStatusChangeEmailEvent(event);

            // 2. 发送统计更新事件
            sendStatusChangeStatisticsEvent(event);

            // 3. 发送审计日志事件
            sendStatusChangeAuditEvent(event);

            // 4. 处理特殊状态变更逻辑
            handleSpecialStatusChange(event);

            log.info("订阅状态变更事件处理完成: subscriptionId={}, newStatus={}",
                    event.getSubscriptionId(), event.getNewStatus());

        } catch (Exception e) {
            log.error("处理订阅状态变更事件异常: subscriptionId={} - {}",
                    event.getSubscriptionId(), e.getMessage(), e);
            throw e; // 重新抛出异常，让RocketMQ处理重试
        }
    }
    
    /**
     * 发送状态变更通知邮件事件
     */
    private boolean sendStatusChangeEmailEvent(SubscriptionStatusChangedEvent event) {
        try {
            // 只有特定状态变更才发送邮件
            if (!shouldSendEmailForStatusChange(event.getOldStatus(), event.getNewStatus())) {
                log.debug("状态变更不需要发送邮件: {} -> {}", event.getOldStatus(), event.getNewStatus());
                return true;
            }
            
            // 准备邮件模板参数
            Map<String, Object> templateParams = new HashMap<>();
            templateParams.put("subscriptionId", event.getSubscriptionId());
            templateParams.put("serviceName", event.getServiceName());
            templateParams.put("oldStatus", event.getOldStatus());
            templateParams.put("newStatus", event.getNewStatus());
            templateParams.put("changeReason", event.getChangeReason());
            templateParams.put("operatorId", event.getOperatorId());
            templateParams.put("changeTime", event.getTimestamp());
            
            // 根据状态变更类型选择邮件模板和类型
            String template = getEmailTemplateForStatusChange(event.getNewStatus());
            String subject = getEmailSubjectForStatusChange(event.getServiceName(), event.getNewStatus());
            EmailNotificationEvent.EmailType emailType = getEmailTypeForStatusChange(event.getNewStatus());
            
            // 创建邮件通知事件
            EmailNotificationEvent emailEvent = new EmailNotificationEvent(
                    event.getUserId(),
                    null, // 邮箱地址需要从用户服务获取
                    null, // 用户名需要从用户服务获取
                    subject,
                    template,
                    templateParams,
                    emailType
            );
            
            emailEvent.setSourceService("nexus-subscription-service");
            
            boolean sent = messageProducerService.sendEventAsync(emailEvent);
            if (sent) {
                log.debug("状态变更邮件事件发送成功: subscriptionId={}, newStatus={}",
                        event.getSubscriptionId(), event.getNewStatus());
            } else {
                log.warn("状态变更邮件事件发送失败: subscriptionId={}, newStatus={}",
                        event.getSubscriptionId(), event.getNewStatus());
            }

            return sent;
            
        } catch (Exception e) {
            log.error("发送状态变更邮件事件异常: subscriptionId={} - {}", 
                    event.getSubscriptionId(), e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 发送状态变更统计更新事件
     */
    private boolean sendStatusChangeStatisticsEvent(SubscriptionStatusChangedEvent event) {
        try {
            // 准备统计指标
            Map<String, Object> metrics = new HashMap<>();
            metrics.put("subscriptionId", event.getSubscriptionId());
            metrics.put("userId", event.getUserId());
            metrics.put("serviceName", event.getServiceName());
            metrics.put("oldStatus", event.getOldStatus());
            metrics.put("newStatus", event.getNewStatus());
            metrics.put("changeReason", event.getChangeReason());
            metrics.put("operatorId", event.getOperatorId());
            metrics.put("changeTime", event.getTimestamp());
            
            // 准备增量数据
            Map<String, Number> increments = new HashMap<>();
            
            // 根据状态变更更新统计
            switch (event.getNewStatus()) {
                case "ACTIVE":
                    increments.put("activeSubscriptions", 1);
                    if ("SUSPENDED".equals(event.getOldStatus()) || "EXPIRED".equals(event.getOldStatus())) {
                        increments.put("reactivatedSubscriptions", 1);
                    }
                    break;
                case "SUSPENDED":
                    increments.put("suspendedSubscriptions", 1);
                    increments.put("activeSubscriptions", -1);
                    break;
                case "CANCELLED":
                    increments.put("cancelledSubscriptions", 1);
                    increments.put("activeSubscriptions", -1);
                    break;
                case "EXPIRED":
                    increments.put("expiredSubscriptions", 1);
                    increments.put("activeSubscriptions", -1);
                    break;
            }
            
            // 状态变更总数
            increments.put("statusChanges", 1);
            
            // 创建统计更新事件
            StatisticsUpdateEvent statsEvent = new StatisticsUpdateEvent(
                    event.getUserId(),
                    StatisticsUpdateEvent.StatisticsType.SUBSCRIPTION_USAGE,
                    "status_change", // 状态变更维度
                    metrics,
                    increments,
                    "daily" // 时间窗口
            );
            
            statsEvent.setSourceService("nexus-subscription-service");
            
            boolean sent = messageProducerService.sendEventAsync(statsEvent);
            if (sent) {
                log.debug("状态变更统计更新事件发送成功: subscriptionId={}, newStatus={}",
                        event.getSubscriptionId(), event.getNewStatus());
            } else {
                log.warn("状态变更统计更新事件发送失败: subscriptionId={}, newStatus={}",
                        event.getSubscriptionId(), event.getNewStatus());
            }

            return sent;
            
        } catch (Exception e) {
            log.error("发送状态变更统计更新事件异常: subscriptionId={} - {}", 
                    event.getSubscriptionId(), e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 发送状态变更审计日志事件
     */
    private boolean sendStatusChangeAuditEvent(SubscriptionStatusChangedEvent event) {
        try {
            // 准备审计属性
            Map<String, Object> attributes = new HashMap<>();
            attributes.put("subscriptionId", event.getSubscriptionId());
            attributes.put("serviceName", event.getServiceName());
            attributes.put("oldStatus", event.getOldStatus());
            attributes.put("newStatus", event.getNewStatus());
            attributes.put("changeReason", event.getChangeReason());
            attributes.put("operatorId", event.getOperatorId());
            
            // 创建审计日志事件
            AuditLogEvent auditEvent = new AuditLogEvent(
                    event.getUserId(),
                    "SUBSCRIPTION_STATUS_CHANGED", // 操作类型
                    "SUBSCRIPTION", // 资源类型
                    event.getSubscriptionId().toString(), // 资源ID
                    "SUCCESS", // 操作结果
                    String.format("订阅状态变更: %s -> %s, 原因: %s", 
                            event.getOldStatus(), event.getNewStatus(), event.getChangeReason()),
                    null, // clientIp
                    null, // userAgent
                    event.getEventId() // requestId
            );
            
            auditEvent.setAttributes(attributes);
            auditEvent.setSourceService("nexus-subscription-service");
            
            boolean sent = messageProducerService.sendEventAsync(auditEvent);
            if (sent) {
                log.debug("状态变更审计日志事件发送成功: subscriptionId={}, newStatus={}",
                        event.getSubscriptionId(), event.getNewStatus());
            } else {
                log.warn("状态变更审计日志事件发送失败: subscriptionId={}, newStatus={}",
                        event.getSubscriptionId(), event.getNewStatus());
            }

            return sent;
            
        } catch (Exception e) {
            log.error("发送状态变更审计日志事件异常: subscriptionId={} - {}", 
                    event.getSubscriptionId(), e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 处理特殊状态变更逻辑
     */
    private boolean handleSpecialStatusChange(SubscriptionStatusChangedEvent event) {
        try {
            switch (event.getNewStatus()) {
                case "EXPIRED":
                    return handleExpiredStatus(event);
                case "CANCELLED":
                    return handleCancelledStatus(event);
                case "SUSPENDED":
                    return handleSuspendedStatus(event);
                case "ACTIVE":
                    return handleActiveStatus(event);
                default:
                    return true;
            }
        } catch (Exception e) {
            log.error("处理特殊状态变更逻辑异常: subscriptionId={}, newStatus={} - {}", 
                    event.getSubscriptionId(), event.getNewStatus(), e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 处理过期状态
     */
    private boolean handleExpiredStatus(SubscriptionStatusChangedEvent event) {
        log.info("处理订阅过期: subscriptionId={}", event.getSubscriptionId());
        // 这里可以添加过期处理逻辑，比如：
        // 1. 清理相关缓存
        // 2. 撤销权限
        // 3. 发送到期提醒邮件
        return true;
    }
    
    /**
     * 处理取消状态
     */
    private boolean handleCancelledStatus(SubscriptionStatusChangedEvent event) {
        log.info("处理订阅取消: subscriptionId={}", event.getSubscriptionId());
        // 这里可以添加取消处理逻辑
        return true;
    }
    
    /**
     * 处理暂停状态
     */
    private boolean handleSuspendedStatus(SubscriptionStatusChangedEvent event) {
        log.info("处理订阅暂停: subscriptionId={}", event.getSubscriptionId());
        // 这里可以添加暂停处理逻辑
        return true;
    }
    
    /**
     * 处理激活状态
     */
    private boolean handleActiveStatus(SubscriptionStatusChangedEvent event) {
        log.info("处理订阅激活: subscriptionId={}", event.getSubscriptionId());
        // 这里可以添加激活处理逻辑
        return true;
    }
    
    /**
     * 判断是否需要为状态变更发送邮件
     */
    private boolean shouldSendEmailForStatusChange(String oldStatus, String newStatus) {
        // 只有重要的状态变更才发送邮件
        return "EXPIRED".equals(newStatus) || 
               "CANCELLED".equals(newStatus) || 
               "SUSPENDED".equals(newStatus) ||
               ("ACTIVE".equals(newStatus) && !"ACTIVE".equals(oldStatus));
    }
    
    /**
     * 获取状态变更的邮件模板
     */
    private String getEmailTemplateForStatusChange(String newStatus) {
        switch (newStatus) {
            case "EXPIRED":
                return "subscription_expired";
            case "CANCELLED":
                return "subscription_cancelled";
            case "SUSPENDED":
                return "subscription_suspended";
            case "ACTIVE":
                return "subscription_reactivated";
            default:
                return "subscription_status_changed";
        }
    }
    
    /**
     * 获取状态变更的邮件主题
     */
    private String getEmailSubjectForStatusChange(String serviceName, String newStatus) {
        switch (newStatus) {
            case "EXPIRED":
                return "订阅已过期 - " + serviceName;
            case "CANCELLED":
                return "订阅已取消 - " + serviceName;
            case "SUSPENDED":
                return "订阅已暂停 - " + serviceName;
            case "ACTIVE":
                return "订阅已激活 - " + serviceName;
            default:
                return "订阅状态变更 - " + serviceName;
        }
    }
    
    /**
     * 获取状态变更的邮件类型
     */
    private EmailNotificationEvent.EmailType getEmailTypeForStatusChange(String newStatus) {
        switch (newStatus) {
            case "EXPIRED":
                return EmailNotificationEvent.EmailType.SUBSCRIPTION_EXPIRE;
            case "CANCELLED":
            case "SUSPENDED":
                return EmailNotificationEvent.EmailType.SYSTEM_NOTIFICATION;
            case "ACTIVE":
                return EmailNotificationEvent.EmailType.SUBSCRIPTION_CONFIRM;
            default:
                return EmailNotificationEvent.EmailType.SYSTEM_NOTIFICATION;
        }
    }
}
