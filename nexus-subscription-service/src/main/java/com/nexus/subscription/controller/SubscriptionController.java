package com.nexus.subscription.controller;

import com.nexus.common.dto.PermissionDTO;
import com.nexus.common.dto.SubscriptionDTO;
import com.nexus.subscription.service.PermissionService;
import com.nexus.subscription.service.SubscriptionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 订阅管理控制器
 * 提供订阅相关的REST API
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/subscriptions")
@RequiredArgsConstructor
@Validated
@Tag(name = "订阅管理", description = "用户订阅和权限管理接口")
public class SubscriptionController {

    private final SubscriptionService subscriptionService;
    private final PermissionService permissionService;

    /**
     * 创建订阅
     */
    @PostMapping
    @Operation(summary = "创建订阅", description = "为用户创建新的服务订阅")
    public ResponseEntity<Map<String, Object>> createSubscription(
            @Valid @RequestBody SubscriptionDTO.CreateSubscriptionDTO createDTO) {
        
        log.info("创建订阅请求: 用户ID {}, 服务ID {}", createDTO.getUserId(), createDTO.getServiceConfigId());
        
        try {
            SubscriptionDTO subscription = subscriptionService.createSubscription(createDTO);
            return ResponseEntity.ok(buildSuccessResponse("订阅创建成功", subscription));
        } catch (Exception e) {
            log.error("创建订阅失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 更新订阅
     */
    @PutMapping("/{subscriptionId}")
    @Operation(summary = "更新订阅", description = "更新指定的订阅信息")
    public ResponseEntity<Map<String, Object>> updateSubscription(
            @Parameter(description = "订阅ID") @PathVariable Long subscriptionId,
            @Valid @RequestBody SubscriptionDTO.UpdateSubscriptionDTO updateDTO) {
        
        log.info("更新订阅请求: ID {}", subscriptionId);
        
        try {
            SubscriptionDTO subscription = subscriptionService.updateSubscription(subscriptionId, updateDTO);
            return ResponseEntity.ok(buildSuccessResponse("订阅更新成功", subscription));
        } catch (Exception e) {
            log.error("更新订阅失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 取消订阅
     */
    @DeleteMapping("/{subscriptionId}")
    @Operation(summary = "取消订阅", description = "取消指定的订阅")
    public ResponseEntity<Map<String, Object>> cancelSubscription(
            @Parameter(description = "订阅ID") @PathVariable Long subscriptionId) {
        
        log.info("取消订阅请求: ID {}", subscriptionId);
        
        try {
            subscriptionService.cancelSubscription(subscriptionId);
            return ResponseEntity.ok(buildSuccessResponse("订阅取消成功", null));
        } catch (Exception e) {
            log.error("取消订阅失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 获取订阅详情
     */
    @GetMapping("/{subscriptionId}")
    @Operation(summary = "获取订阅详情", description = "获取指定订阅的详细信息")
    public ResponseEntity<Map<String, Object>> getSubscription(
            @Parameter(description = "订阅ID") @PathVariable Long subscriptionId) {
        
        log.debug("获取订阅详情请求: ID {}", subscriptionId);
        
        try {
            SubscriptionDTO subscription = subscriptionService.getSubscription(subscriptionId);
            return ResponseEntity.ok(buildSuccessResponse("获取订阅详情成功", subscription));
        } catch (Exception e) {
            log.error("获取订阅详情失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 获取用户订阅列表
     */
    @GetMapping("/user/{userId}")
    @Operation(summary = "获取用户订阅", description = "获取指定用户的所有订阅")
    public ResponseEntity<Map<String, Object>> getUserSubscriptions(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @Parameter(description = "只返回有效订阅") @RequestParam(defaultValue = "false") boolean validOnly) {
        
        log.debug("获取用户订阅请求: 用户ID {}, 仅有效订阅 {}", userId, validOnly);
        
        try {
            List<SubscriptionDTO> subscriptions = validOnly ? 
                    subscriptionService.getUserValidSubscriptions(userId) :
                    subscriptionService.getUserSubscriptions(userId);
            return ResponseEntity.ok(buildSuccessResponse("获取用户订阅成功", subscriptions));
        } catch (Exception e) {
            log.error("获取用户订阅失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 延长订阅
     */
    @PostMapping("/{subscriptionId}/extend")
    @Operation(summary = "延长订阅", description = "延长指定订阅的有效期")
    public ResponseEntity<Map<String, Object>> extendSubscription(
            @Parameter(description = "订阅ID") @PathVariable Long subscriptionId,
            @Parameter(description = "延长天数") @RequestParam int days) {
        
        log.info("延长订阅请求: ID {}, 天数 {}", subscriptionId, days);
        
        try {
            subscriptionService.extendSubscription(subscriptionId, days);
            return ResponseEntity.ok(buildSuccessResponse("订阅延长成功", null));
        } catch (Exception e) {
            log.error("延长订阅失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 重置订阅使用次数
     */
    @PostMapping("/{subscriptionId}/reset-usage")
    @Operation(summary = "重置使用次数", description = "重置订阅的API调用使用次数")
    public ResponseEntity<Map<String, Object>> resetSubscriptionUsage(
            @Parameter(description = "订阅ID") @PathVariable Long subscriptionId) {
        
        log.info("重置订阅使用次数请求: ID {}", subscriptionId);
        
        try {
            subscriptionService.resetSubscriptionUsage(subscriptionId);
            return ResponseEntity.ok(buildSuccessResponse("使用次数重置成功", null));
        } catch (Exception e) {
            log.error("重置使用次数失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 检查订阅有效性
     */
    @GetMapping("/check")
    @Operation(summary = "检查订阅有效性", description = "检查用户是否有指定服务的有效订阅")
    public ResponseEntity<Map<String, Object>> checkSubscription(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @Parameter(description = "服务配置ID") @RequestParam Long serviceConfigId) {
        
        log.debug("检查订阅有效性请求: 用户ID {}, 服务ID {}", userId, serviceConfigId);
        
        try {
            boolean hasValidSubscription = subscriptionService.hasValidSubscription(userId, serviceConfigId);
            Map<String, Object> result = Map.of("hasValidSubscription", hasValidSubscription);
            return ResponseEntity.ok(buildSuccessResponse("订阅检查完成", result));
        } catch (Exception e) {
            log.error("检查订阅有效性失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 记录API调用
     */
    @PostMapping("/record-call")
    @Operation(summary = "记录API调用", description = "记录用户的API调用，更新使用统计")
    public ResponseEntity<Map<String, Object>> recordApiCall(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @Parameter(description = "服务配置ID") @RequestParam Long serviceConfigId) {
        
        log.debug("记录API调用请求: 用户ID {}, 服务ID {}", userId, serviceConfigId);
        
        try {
            subscriptionService.recordApiCall(userId, serviceConfigId);
            return ResponseEntity.ok(buildSuccessResponse("API调用记录成功", null));
        } catch (Exception e) {
            log.error("记录API调用失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 获取即将过期的订阅
     */
    @GetMapping("/expiring-soon")
    @Operation(summary = "获取即将过期订阅", description = "获取即将在指定天数内过期的订阅")
    public ResponseEntity<Map<String, Object>> getExpiringSoonSubscriptions(
            @Parameter(description = "天数") @RequestParam(defaultValue = "7") int days) {
        
        log.debug("获取即将过期订阅请求: 天数 {}", days);
        
        try {
            List<SubscriptionDTO> subscriptions = subscriptionService.getExpiringSoonSubscriptions(days);
            return ResponseEntity.ok(buildSuccessResponse("获取即将过期订阅成功", subscriptions));
        } catch (Exception e) {
            log.error("获取即将过期订阅失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 获取使用统计
     */
    @GetMapping("/usage-statistics")
    @Operation(summary = "获取使用统计", description = "获取订阅使用情况统计")
    public ResponseEntity<Map<String, Object>> getUsageStatistics() {
        
        log.debug("获取使用统计请求");
        
        try {
            List<SubscriptionDTO.SubscriptionUsageDTO> statistics = subscriptionService.getUsageStatistics();
            return ResponseEntity.ok(buildSuccessResponse("获取使用统计成功", statistics));
        } catch (Exception e) {
            log.error("获取使用统计失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 获取订阅统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取订阅统计", description = "获取订阅的总体统计信息")
    public ResponseEntity<Map<String, Object>> getSubscriptionStatistics() {
        
        log.debug("获取订阅统计请求");
        
        try {
            SubscriptionService.SubscriptionStatistics statistics = subscriptionService.getSubscriptionStatistics();
            return ResponseEntity.ok(buildSuccessResponse("获取订阅统计成功", statistics));
        } catch (Exception e) {
            log.error("获取订阅统计失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 检查工具权限
     */
    @GetMapping("/permissions/check-tool")
    @Operation(summary = "检查工具权限", description = "检查用户是否有访问指定工具的权限")
    public ResponseEntity<Map<String, Object>> checkToolPermission(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @Parameter(description = "工具名称") @RequestParam String toolName) {
        
        log.debug("检查工具权限请求: 用户ID {}, 工具 {}", userId, toolName);
        
        try {
            PermissionDTO.PermissionCheckResult result = permissionService.checkToolPermission(userId, toolName);
            return ResponseEntity.ok(buildSuccessResponse("权限检查完成", result));
        } catch (Exception e) {
            log.error("检查工具权限失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 检查资源权限
     */
    @GetMapping("/permissions/check-resource")
    @Operation(summary = "检查资源权限", description = "检查用户是否有访问指定资源的权限")
    public ResponseEntity<Map<String, Object>> checkResourcePermission(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @Parameter(description = "资源名称") @RequestParam String resourceName) {
        
        log.debug("检查资源权限请求: 用户ID {}, 资源 {}", userId, resourceName);
        
        try {
            PermissionDTO.PermissionCheckResult result = permissionService.checkResourcePermission(userId, resourceName);
            return ResponseEntity.ok(buildSuccessResponse("权限检查完成", result));
        } catch (Exception e) {
            log.error("检查资源权限失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 获取用户权限列表
     */
    @GetMapping("/permissions/user/{userId}")
    @Operation(summary = "获取用户权限", description = "获取指定用户的所有权限")
    public ResponseEntity<Map<String, Object>> getUserPermissions(
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        
        log.debug("获取用户权限请求: 用户ID {}", userId);
        
        try {
            List<PermissionDTO> permissions = permissionService.getUserPermissions(userId);
            return ResponseEntity.ok(buildSuccessResponse("获取用户权限成功", permissions));
        } catch (Exception e) {
            log.error("获取用户权限失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "检查订阅服务的健康状态")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "nexus-subscription-service");
        health.put("timestamp", System.currentTimeMillis());
        
        // 添加统计信息
        try {
            SubscriptionService.SubscriptionStatistics stats = subscriptionService.getSubscriptionStatistics();
            health.put("totalSubscriptions", stats.getTotalSubscriptions());
            health.put("activeSubscriptions", stats.getActiveSubscriptions());
        } catch (Exception e) {
            log.warn("获取健康检查统计信息失败", e);
        }
        
        return ResponseEntity.ok(health);
    }

    /**
     * 构建成功响应
     */
    private Map<String, Object> buildSuccessResponse(String message, Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", message);
        response.put("timestamp", System.currentTimeMillis());
        if (data != null) {
            response.put("data", data);
        }
        return response;
    }
}
