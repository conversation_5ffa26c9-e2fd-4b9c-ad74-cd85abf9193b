package com.nexus.subscription.repository;

import com.nexus.common.entity.Permission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 权限数据访问层
 * 提供权限相关的数据库操作
 */
@Repository
public interface PermissionRepository extends JpaRepository<Permission, Long> {

    /**
     * 根据订阅ID查找权限列表
     */
    List<Permission> findBySubscriptionId(Long subscriptionId);

    /**
     * 根据订阅ID和权限类型查找权限列表
     */
    List<Permission> findBySubscriptionIdAndPermissionType(Long subscriptionId, Permission.PermissionType permissionType);

    /**
     * 根据工具名称查找权限
     */
    List<Permission> findByToolName(String toolName);

    /**
     * 根据资源名称查找权限
     */
    List<Permission> findByResourceName(String resourceName);

    /**
     * 根据权限类型查找权限列表
     */
    List<Permission> findByPermissionType(Permission.PermissionType permissionType);

    /**
     * 查找启用的权限
     */
    List<Permission> findByEnabledTrue();

    /**
     * 查找禁用的权限
     */
    List<Permission> findByEnabledFalse();

    /**
     * 根据订阅ID和工具名称查找权限
     */
    Optional<Permission> findBySubscriptionIdAndToolName(Long subscriptionId, String toolName);

    /**
     * 根据订阅ID和资源名称查找权限
     */
    Optional<Permission> findBySubscriptionIdAndResourceName(Long subscriptionId, String resourceName);

    /**
     * 查找用户的所有权限
     */
    @Query("SELECT p FROM Permission p WHERE p.subscription.user.id = :userId AND p.enabled = true")
    List<Permission> findByUserId(@Param("userId") Long userId);

    /**
     * 查找用户的工具权限
     */
    @Query("SELECT p FROM Permission p WHERE p.subscription.user.id = :userId AND " +
           "p.permissionType = 'TOOL' AND p.enabled = true")
    List<Permission> findToolPermissionsByUserId(@Param("userId") Long userId);

    /**
     * 查找用户的资源权限
     */
    @Query("SELECT p FROM Permission p WHERE p.subscription.user.id = :userId AND " +
           "p.permissionType = 'RESOURCE' AND p.enabled = true")
    List<Permission> findResourcePermissionsByUserId(@Param("userId") Long userId);

    /**
     * 检查用户是否有特定工具权限
     */
    @Query("SELECT COUNT(p) > 0 FROM Permission p WHERE p.subscription.user.id = :userId AND " +
           "p.permissionType = 'TOOL' AND p.toolName = :toolName AND p.enabled = true")
    boolean hasToolPermission(@Param("userId") Long userId, @Param("toolName") String toolName);

    /**
     * 检查用户是否有特定资源权限
     */
    @Query("SELECT COUNT(p) > 0 FROM Permission p WHERE p.subscription.user.id = :userId AND " +
           "p.permissionType = 'RESOURCE' AND p.resourceName = :resourceName AND p.enabled = true")
    boolean hasResourcePermission(@Param("userId") Long userId, @Param("resourceName") String resourceName);

    /**
     * 查找过期的权限（关联到过期订阅）
     */
    @Query("SELECT p FROM Permission p WHERE p.subscription.status = 'EXPIRED' OR " +
           "(p.subscription.endDate IS NOT NULL AND p.subscription.endDate < :now)")
    List<Permission> findExpiredPermissions(@Param("now") LocalDateTime now);

    /**
     * 批量启用权限
     */
    @Modifying
    @Query("UPDATE Permission p SET p.enabled = true WHERE p.id IN :permissionIds")
    void enablePermissions(@Param("permissionIds") List<Long> permissionIds);

    /**
     * 批量禁用权限
     */
    @Modifying
    @Query("UPDATE Permission p SET p.enabled = false WHERE p.id IN :permissionIds")
    void disablePermissions(@Param("permissionIds") List<Long> permissionIds);

    /**
     * 根据订阅ID禁用所有权限
     */
    @Modifying
    @Query("UPDATE Permission p SET p.enabled = false WHERE p.subscription.id = :subscriptionId")
    void disablePermissionsBySubscriptionId(@Param("subscriptionId") Long subscriptionId);

    /**
     * 根据订阅ID启用所有权限
     */
    @Modifying
    @Query("UPDATE Permission p SET p.enabled = true WHERE p.subscription.id = :subscriptionId")
    void enablePermissionsBySubscriptionId(@Param("subscriptionId") Long subscriptionId);

    /**
     * 统计权限总数
     */
    @Query("SELECT COUNT(p) FROM Permission p")
    long countTotalPermissions();

    /**
     * 统计启用的权限数
     */
    @Query("SELECT COUNT(p) FROM Permission p WHERE p.enabled = true")
    long countEnabledPermissions();

    /**
     * 统计用户权限数
     */
    @Query("SELECT COUNT(p) FROM Permission p WHERE p.subscription.user.id = :userId")
    long countPermissionsByUser(@Param("userId") Long userId);

    /**
     * 统计各种类型的权限数量
     */
    @Query("SELECT p.permissionType, COUNT(p) FROM Permission p GROUP BY p.permissionType")
    List<Object[]> countPermissionsByType();

    /**
     * 查找需要清理的权限（长时间禁用）
     */
    @Query("SELECT p FROM Permission p WHERE p.enabled = false AND p.updatedAt < :threshold")
    List<Permission> findPermissionsToCleanup(@Param("threshold") LocalDateTime threshold);

    /**
     * 删除指定订阅的所有权限
     */
    @Modifying
    @Query("DELETE FROM Permission p WHERE p.subscription.id = :subscriptionId")
    void deleteBySubscriptionId(@Param("subscriptionId") Long subscriptionId);
}
