package com.nexus.subscription.repository;

import com.nexus.common.entity.Subscription;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 订阅数据访问层
 * 提供订阅相关的数据库操作
 */
@Repository
public interface SubscriptionRepository extends JpaRepository<Subscription, Long> {

    /**
     * 根据用户ID查找订阅列表
     */
    List<Subscription> findByUserId(Long userId);

    /**
     * 根据服务配置ID查找订阅列表
     */
    List<Subscription> findByServiceConfigId(Long serviceConfigId);

    /**
     * 根据用户ID和服务配置ID查找订阅
     */
    Optional<Subscription> findByUserIdAndServiceConfigId(Long userId, Long serviceConfigId);

    /**
     * 根据状态查找订阅列表
     */
    List<Subscription> findByStatus(Subscription.SubscriptionStatus status);

    /**
     * 查找有效的订阅
     */
    @Query("SELECT s FROM Subscription s WHERE s.status = 'ACTIVE' AND " +
           "(s.endDate IS NULL OR s.endDate > :now) AND " +
           "(s.callLimit IS NULL OR s.usedCalls < s.callLimit)")
    List<Subscription> findValidSubscriptions(@Param("now") LocalDateTime now);

    /**
     * 查找用户的有效订阅
     */
    @Query("SELECT s FROM Subscription s WHERE s.user.id = :userId AND s.status = 'ACTIVE' AND " +
           "(s.endDate IS NULL OR s.endDate > :now) AND " +
           "(s.callLimit IS NULL OR s.usedCalls < s.callLimit)")
    List<Subscription> findValidSubscriptionsByUser(@Param("userId") Long userId, @Param("now") LocalDateTime now);

    /**
     * 查找即将过期的订阅
     */
    @Query("SELECT s FROM Subscription s WHERE s.status = 'ACTIVE' AND " +
           "s.endDate IS NOT NULL AND s.endDate BETWEEN :now AND :threshold")
    List<Subscription> findExpiringSoonSubscriptions(@Param("now") LocalDateTime now, 
                                                    @Param("threshold") LocalDateTime threshold);

    /**
     * 查找已过期的订阅
     */
    @Query("SELECT s FROM Subscription s WHERE s.status = 'ACTIVE' AND " +
           "s.endDate IS NOT NULL AND s.endDate < :now")
    List<Subscription> findExpiredSubscriptions(@Param("now") LocalDateTime now);

    /**
     * 查找使用量接近限制的订阅
     */
    @Query("SELECT s FROM Subscription s WHERE s.status = 'ACTIVE' AND " +
           "s.callLimit IS NOT NULL AND s.usedCalls >= (s.callLimit * :threshold / 100)")
    List<Subscription> findHighUsageSubscriptions(@Param("threshold") double threshold);

    /**
     * 查找超出使用限制的订阅
     */
    @Query("SELECT s FROM Subscription s WHERE s.status = 'ACTIVE' AND " +
           "s.callLimit IS NOT NULL AND s.usedCalls >= s.callLimit")
    List<Subscription> findOverLimitSubscriptions();

    /**
     * 更新订阅状态
     */
    @Modifying
    @Query("UPDATE Subscription s SET s.status = :status WHERE s.id = :subscriptionId")
    void updateSubscriptionStatus(@Param("subscriptionId") Long subscriptionId, 
                                @Param("status") Subscription.SubscriptionStatus status);

    /**
     * 增加使用次数
     */
    @Modifying
    @Query("UPDATE Subscription s SET s.usedCalls = s.usedCalls + 1 WHERE s.id = :subscriptionId")
    void incrementUsedCalls(@Param("subscriptionId") Long subscriptionId);

    /**
     * 批量增加使用次数
     */
    @Modifying
    @Query("UPDATE Subscription s SET s.usedCalls = s.usedCalls + :count WHERE s.id = :subscriptionId")
    void incrementUsedCallsByCount(@Param("subscriptionId") Long subscriptionId, @Param("count") Long count);

    /**
     * 重置使用次数
     */
    @Modifying
    @Query("UPDATE Subscription s SET s.usedCalls = 0 WHERE s.id = :subscriptionId")
    void resetUsedCalls(@Param("subscriptionId") Long subscriptionId);

    /**
     * 延长订阅有效期
     */
    @Modifying
    @Query("UPDATE Subscription s SET s.endDate = :newEndDate WHERE s.id = :subscriptionId")
    void extendSubscription(@Param("subscriptionId") Long subscriptionId, 
                          @Param("newEndDate") LocalDateTime newEndDate);

    /**
     * 更新调用限制
     */
    @Modifying
    @Query("UPDATE Subscription s SET s.callLimit = :callLimit WHERE s.id = :subscriptionId")
    void updateCallLimit(@Param("subscriptionId") Long subscriptionId, @Param("callLimit") Long callLimit);

    /**
     * 统计用户订阅数量
     */
    @Query("SELECT COUNT(s) FROM Subscription s WHERE s.user.id = :userId")
    long countSubscriptionsByUser(@Param("userId") Long userId);

    /**
     * 统计用户有效订阅数量
     */
    @Query("SELECT COUNT(s) FROM Subscription s WHERE s.user.id = :userId AND s.status = 'ACTIVE' AND " +
           "(s.endDate IS NULL OR s.endDate > :now)")
    long countValidSubscriptionsByUser(@Param("userId") Long userId, @Param("now") LocalDateTime now);

    /**
     * 统计服务订阅数量
     */
    @Query("SELECT COUNT(s) FROM Subscription s WHERE s.serviceConfig.id = :serviceConfigId")
    long countSubscriptionsByService(@Param("serviceConfigId") Long serviceConfigId);

    /**
     * 统计各种状态的订阅数量
     */
    @Query("SELECT s.status, COUNT(s) FROM Subscription s GROUP BY s.status")
    List<Object[]> countSubscriptionsByStatus();

    /**
     * 查找指定时间段内创建的订阅
     */
    @Query("SELECT s FROM Subscription s WHERE s.createdAt BETWEEN :startTime AND :endTime")
    List<Subscription> findSubscriptionsByCreationPeriod(@Param("startTime") LocalDateTime startTime, 
                                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 查找指定时间段内到期的订阅
     */
    @Query("SELECT s FROM Subscription s WHERE s.endDate BETWEEN :startTime AND :endTime")
    List<Subscription> findSubscriptionsByExpiryPeriod(@Param("startTime") LocalDateTime startTime, 
                                                      @Param("endTime") LocalDateTime endTime);

    /**
     * 查找需要清理的订阅（已取消或过期超过指定天数）
     */
    @Query("SELECT s FROM Subscription s WHERE " +
           "(s.status = 'CANCELLED' AND s.updatedAt < :threshold) OR " +
           "(s.status = 'EXPIRED' AND s.endDate < :threshold)")
    List<Subscription> findSubscriptionsToCleanup(@Param("threshold") LocalDateTime threshold);

    /**
     * 根据用户名模糊查询订阅
     */
    @Query("SELECT s FROM Subscription s WHERE s.user.username LIKE %:keyword% OR " +
           "s.serviceConfig.serviceName LIKE %:keyword%")
    List<Subscription> searchSubscriptions(@Param("keyword") String keyword);

    /**
     * 获取用户订阅的服务列表
     */
    @Query("SELECT DISTINCT s.serviceConfig FROM Subscription s WHERE s.user.id = :userId AND s.status = 'ACTIVE'")
    List<com.nexus.common.entity.ServiceConfig> findSubscribedServicesByUser(@Param("userId") Long userId);

    /**
     * 检查用户是否订阅了指定服务
     */
    @Query("SELECT COUNT(s) > 0 FROM Subscription s WHERE s.user.id = :userId AND " +
           "s.serviceConfig.id = :serviceConfigId AND s.status = 'ACTIVE' AND " +
           "(s.endDate IS NULL OR s.endDate > :now)")
    boolean hasValidSubscription(@Param("userId") Long userId, 
                               @Param("serviceConfigId") Long serviceConfigId, 
                               @Param("now") LocalDateTime now);

    /**
     * 获取订阅使用统计
     */
    @Query("SELECT s.serviceConfig.serviceName, SUM(s.usedCalls), COUNT(s) " +
           "FROM Subscription s WHERE s.status = 'ACTIVE' " +
           "GROUP BY s.serviceConfig.serviceName")
    List<Object[]> getSubscriptionUsageStatistics();

    /**
     * 按状态统计订阅数量
     */
    long countByStatus(Subscription.SubscriptionStatus status);

    /**
     * 统计创建时间范围内的订阅数量
     */
    long countByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计即将过期的订阅数量
     */
    long countByEndDateBetweenAndStatus(LocalDateTime startTime, LocalDateTime endTime,
                                       Subscription.SubscriptionStatus status);

    /**
     * 获取按服务分组的订阅统计
     */
    @Query("SELECT s.serviceConfig.id, s.serviceConfig.serviceName, " +
           "COUNT(s), SUM(CASE WHEN s.status = 'ACTIVE' THEN 1 ELSE 0 END) " +
           "FROM Subscription s " +
           "GROUP BY s.serviceConfig.id, s.serviceConfig.serviceName " +
           "ORDER BY COUNT(s) DESC")
    List<Object[]> getSubscriptionStatsByService();

    /**
     * 获取订阅数量最多的用户
     */
    @Query(value = "SELECT s.user_id, u.username, " +
           "COUNT(s.id), SUM(CASE WHEN s.status = 'ACTIVE' THEN 1 ELSE 0 END) " +
           "FROM subscriptions s " +
           "JOIN users u ON s.user_id = u.id " +
           "GROUP BY s.user_id, u.username " +
           "ORDER BY COUNT(s.id) DESC " +
           "LIMIT :limit", nativeQuery = true)
    List<Object[]> getTopUsersBySubscriptionCount(@Param("limit") int limit);
}
