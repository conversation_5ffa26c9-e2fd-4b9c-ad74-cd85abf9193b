package com.nexus.subscription.service;

import com.nexus.common.constants.CacheNames;
import com.nexus.common.dto.PermissionDTO;
import com.nexus.common.entity.Permission;
import com.nexus.common.entity.Subscription;
import com.nexus.common.exception.NexusException;
import com.nexus.subscription.repository.PermissionRepository;
import com.nexus.subscription.repository.SubscriptionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 权限管理服务
 * 负责用户权限的创建、验证和管理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PermissionService {

    private final PermissionRepository permissionRepository;
    private final SubscriptionRepository subscriptionRepository;

    @Value("${nexus.subscription.permissions.cache-ttl:300}")
    private int permissionCacheTtl;

    @Value("${nexus.subscription.permissions.default-policy:DENY}")
    private String defaultPolicy;

    /**
     * 创建权限
     */
    @Transactional
    @CacheEvict(value = CacheNames.USER_PERMISSIONS, allEntries = true)
    public PermissionDTO createPermission(PermissionDTO.CreatePermissionDTO createDTO) {
        log.info("创建权限: 订阅ID {}, 类型 {}", createDTO.getSubscriptionId(), createDTO.getPermissionType());

        // 验证订阅是否存在且有效
        Subscription subscription = subscriptionRepository.findById(createDTO.getSubscriptionId())
                .orElseThrow(() -> new NexusException.ResourceNotFoundException("订阅不存在"));

        if (!subscription.isValid()) {
            throw new NexusException.BusinessException("订阅无效，无法创建权限");
        }

        // 创建权限实体
        Permission permission = Permission.builder()
                .subscription(subscription)
                .permissionType(createDTO.getPermissionType())
                .toolName(createDTO.getToolName())
                .resourceName(createDTO.getResourceName())
                .description(createDTO.getDescription())
                .enabled(createDTO.getEnabled())
                .build();

        permission = permissionRepository.save(permission);

        log.info("权限创建成功: ID {}", permission.getId());
        return PermissionDTO.fromEntity(permission);
    }

    /**
     * 更新权限
     */
    @Transactional
    @CacheEvict(value = CacheNames.USER_PERMISSIONS, allEntries = true)
    public PermissionDTO updatePermission(Long permissionId, PermissionDTO.UpdatePermissionDTO updateDTO) {
        log.info("更新权限: ID {}", permissionId);

        Permission permission = getPermissionById(permissionId);

        // 更新字段
        if (updateDTO.getToolName() != null) {
            permission.setToolName(updateDTO.getToolName());
        }
        if (updateDTO.getResourceName() != null) {
            permission.setResourceName(updateDTO.getResourceName());
        }
        if (updateDTO.getDescription() != null) {
            permission.setDescription(updateDTO.getDescription());
        }
        if (updateDTO.getEnabled() != null) {
            permission.setEnabled(updateDTO.getEnabled());
        }

        permission = permissionRepository.save(permission);

        log.info("权限更新成功: ID {}", permissionId);
        return PermissionDTO.fromEntity(permission);
    }

    /**
     * 删除权限
     */
    @Transactional
    @CacheEvict(value = CacheNames.USER_PERMISSIONS, allEntries = true)
    public void deletePermission(Long permissionId) {
        log.info("删除权限: ID {}", permissionId);

        Permission permission = getPermissionById(permissionId);
        permissionRepository.delete(permission);

        log.info("权限删除成功: ID {}", permissionId);
    }

    /**
     * 检查用户工具权限
     */
    @Cacheable(value = CacheNames.USER_PERMISSIONS, key = "'tool:' + #userId + ':' + #toolName")
    public PermissionDTO.PermissionCheckResult checkToolPermission(Long userId, String toolName) {
        log.debug("检查工具权限: 用户ID {}, 工具 {}", userId, toolName);

        // 查找用户的有效订阅
        List<Subscription> validSubscriptions = subscriptionRepository.findValidSubscriptionsByUser(userId, LocalDateTime.now());
        
        if (validSubscriptions.isEmpty()) {
            return PermissionDTO.PermissionCheckResult.denied("用户没有有效订阅");
        }

        // 检查工具权限
        for (Subscription subscription : validSubscriptions) {
            List<Permission> permissions = permissionRepository.findBySubscriptionIdAndPermissionType(
                    subscription.getId(), Permission.PermissionType.TOOL);
            
            for (Permission permission : permissions) {
                if (permission.isValid() && toolName.equals(permission.getToolName())) {
                    return PermissionDTO.PermissionCheckResult.allowed(
                            Permission.PermissionType.TOOL, permission.getPermissionIdentifier());
                }
            }
            
            // 检查服务级权限
            List<Permission> servicePermissions = permissionRepository.findBySubscriptionIdAndPermissionType(
                    subscription.getId(), Permission.PermissionType.SERVICE);
            
            if (!servicePermissions.isEmpty() && servicePermissions.stream().anyMatch(Permission::isValid)) {
                return PermissionDTO.PermissionCheckResult.allowed(
                        Permission.PermissionType.SERVICE, "service:" + subscription.getServiceConfig().getServiceName());
            }
        }

        return PermissionDTO.PermissionCheckResult.denied("用户没有访问该工具的权限");
    }

    /**
     * 检查用户资源权限
     */
    @Cacheable(value = CacheNames.USER_PERMISSIONS, key = "'resource:' + #userId + ':' + #resourceName")
    public PermissionDTO.PermissionCheckResult checkResourcePermission(Long userId, String resourceName) {
        log.debug("检查资源权限: 用户ID {}, 资源 {}", userId, resourceName);

        // 查找用户的有效订阅
        List<Subscription> validSubscriptions = subscriptionRepository.findValidSubscriptionsByUser(userId, LocalDateTime.now());
        
        if (validSubscriptions.isEmpty()) {
            return PermissionDTO.PermissionCheckResult.denied("用户没有有效订阅");
        }

        // 检查资源权限
        for (Subscription subscription : validSubscriptions) {
            List<Permission> permissions = permissionRepository.findBySubscriptionIdAndPermissionType(
                    subscription.getId(), Permission.PermissionType.RESOURCE);
            
            for (Permission permission : permissions) {
                if (permission.isValid() && resourceName.equals(permission.getResourceName())) {
                    return PermissionDTO.PermissionCheckResult.allowed(
                            Permission.PermissionType.RESOURCE, permission.getPermissionIdentifier());
                }
            }
            
            // 检查服务级权限
            List<Permission> servicePermissions = permissionRepository.findBySubscriptionIdAndPermissionType(
                    subscription.getId(), Permission.PermissionType.SERVICE);
            
            if (!servicePermissions.isEmpty() && servicePermissions.stream().anyMatch(Permission::isValid)) {
                return PermissionDTO.PermissionCheckResult.allowed(
                        Permission.PermissionType.SERVICE, "service:" + subscription.getServiceConfig().getServiceName());
            }
        }

        return PermissionDTO.PermissionCheckResult.denied("用户没有访问该资源的权限");
    }

    /**
     * 获取用户权限列表
     */
    @Cacheable(value = CacheNames.USER_PERMISSIONS, key = "'list:' + #userId")
    public List<PermissionDTO> getUserPermissions(Long userId) {
        List<Subscription> validSubscriptions = subscriptionRepository.findValidSubscriptionsByUser(userId, LocalDateTime.now());
        
        return validSubscriptions.stream()
                .flatMap(subscription -> subscription.getPermissions().stream())
                .filter(Permission::isValid)
                .map(PermissionDTO::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * 获取订阅权限列表
     */
    public List<PermissionDTO> getSubscriptionPermissions(Long subscriptionId) {
        List<Permission> permissions = permissionRepository.findBySubscriptionId(subscriptionId);
        return permissions.stream()
                .map(PermissionDTO::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * 创建默认权限
     */
    @Transactional
    public void createDefaultPermissions(Long subscriptionId) {
        log.info("为订阅创建默认权限: ID {}", subscriptionId);

        Subscription subscription = subscriptionRepository.findById(subscriptionId)
                .orElseThrow(() -> new NexusException.ResourceNotFoundException("订阅不存在"));

        // 创建服务级权限（允许访问整个服务）
        Permission servicePermission = Permission.builder()
                .subscription(subscription)
                .permissionType(Permission.PermissionType.SERVICE)
                .description("默认服务访问权限")
                .enabled(true)
                .build();

        permissionRepository.save(servicePermission);

        log.info("默认权限创建成功: 订阅ID {}", subscriptionId);
    }

    /**
     * 撤销订阅权限
     */
    @Transactional
    @CacheEvict(value = CacheNames.USER_PERMISSIONS, allEntries = true)
    public void revokeSubscriptionPermissions(Long subscriptionId) {
        log.info("撤销订阅权限: ID {}", subscriptionId);

        List<Permission> permissions = permissionRepository.findBySubscriptionId(subscriptionId);
        for (Permission permission : permissions) {
            permission.setEnabled(false);
        }
        permissionRepository.saveAll(permissions);

        log.info("订阅权限撤销成功: ID {}, 权限数量 {}", subscriptionId, permissions.size());
    }

    /**
     * 批量启用权限
     */
    @Transactional
    @CacheEvict(value = CacheNames.USER_PERMISSIONS, allEntries = true)
    public void enablePermissions(List<Long> permissionIds) {
        log.info("批量启用权限: {}", permissionIds);

        List<Permission> permissions = permissionRepository.findAllById(permissionIds);
        permissions.forEach(permission -> permission.setEnabled(true));
        permissionRepository.saveAll(permissions);

        log.info("权限批量启用成功: 数量 {}", permissions.size());
    }

    /**
     * 批量禁用权限
     */
    @Transactional
    @CacheEvict(value = CacheNames.USER_PERMISSIONS, allEntries = true)
    public void disablePermissions(List<Long> permissionIds) {
        log.info("批量禁用权限: {}", permissionIds);

        List<Permission> permissions = permissionRepository.findAllById(permissionIds);
        permissions.forEach(permission -> permission.setEnabled(false));
        permissionRepository.saveAll(permissions);

        log.info("权限批量禁用成功: 数量 {}", permissions.size());
    }

    /**
     * 获取权限实体
     */
    private Permission getPermissionById(Long permissionId) {
        return permissionRepository.findById(permissionId)
                .orElseThrow(() -> new NexusException.ResourceNotFoundException("权限不存在: " + permissionId));
    }

    /**
     * 清理过期权限
     */
    @Transactional
    public void cleanupExpiredPermissions() {
        log.info("开始清理过期权限");

        // 查找关联到过期订阅的权限
        List<Permission> expiredPermissions = permissionRepository.findExpiredPermissions(LocalDateTime.now());
        
        for (Permission permission : expiredPermissions) {
            permission.setEnabled(false);
        }
        
        permissionRepository.saveAll(expiredPermissions);

        log.info("过期权限清理完成，共处理 {} 个权限", expiredPermissions.size());
    }
}
