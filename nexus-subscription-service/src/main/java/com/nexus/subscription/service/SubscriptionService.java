package com.nexus.subscription.service;

import com.nexus.common.constants.CacheNames;
import com.nexus.common.dto.SubscriptionDTO;
import com.nexus.common.entity.ServiceConfig;
import com.nexus.common.entity.Subscription;
import com.nexus.common.entity.User;
import com.nexus.common.event.SubscriptionCreatedEvent;
import com.nexus.common.event.SubscriptionStatusChangedEvent;
import com.nexus.common.event.SubscriptionRenewedEvent;
import com.nexus.common.exception.NexusException;
import com.nexus.common.service.RocketMQProducerService;
import com.nexus.subscription.repository.SubscriptionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 订阅管理服务
 * 负责用户订阅的创建、管理和监控
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SubscriptionService {

    private final SubscriptionRepository subscriptionRepository;
    private final PermissionService permissionService;
    private final UsageTrackingService usageTrackingService;
    private final RocketMQProducerService messageProducerService;

    @Value("${nexus.subscription.management.default-validity-days:30}")
    private int defaultValidityDays;

    @Value("${nexus.subscription.management.max-subscriptions-per-user:10}")
    private int maxSubscriptionsPerUser;

    /**
     * 创建订阅
     */
    @Transactional
    @CacheEvict(value = {CacheNames.USER_SUBSCRIPTIONS, CacheNames.SUBSCRIPTIONS}, allEntries = true)
    public SubscriptionDTO createSubscription(SubscriptionDTO.CreateSubscriptionDTO createDTO) {
        log.info("创建订阅: 用户ID {}, 服务ID {}", createDTO.getUserId(), createDTO.getServiceConfigId());

        // 检查用户订阅数量限制
        long userSubscriptionCount = subscriptionRepository.countValidSubscriptionsByUser(
                createDTO.getUserId(), LocalDateTime.now());
        if (userSubscriptionCount >= maxSubscriptionsPerUser) {
            throw new NexusException.BusinessException("用户订阅数量已达上限: " + maxSubscriptionsPerUser);
        }

        // 检查是否已存在有效订阅
        if (subscriptionRepository.hasValidSubscription(
                createDTO.getUserId(), createDTO.getServiceConfigId(), LocalDateTime.now())) {
            throw new NexusException.BusinessException("用户已订阅该服务");
        }

        // 创建订阅实体
        Subscription subscription = Subscription.builder()
                .user(User.builder().id(createDTO.getUserId()).build())
                .serviceConfig(ServiceConfig.builder().id(createDTO.getServiceConfigId()).build())
                .status(Subscription.SubscriptionStatus.ACTIVE)
                .startDate(createDTO.getStartDate() != null ? createDTO.getStartDate() : LocalDateTime.now())
                .endDate(createDTO.getEndDate() != null ? createDTO.getEndDate() : 
                        LocalDateTime.now().plusDays(defaultValidityDays))
                .callLimit(createDTO.getCallLimit())
                .usedCalls(0L)
                .notes(createDTO.getNotes())
                .build();

        subscription = subscriptionRepository.save(subscription);

        // 创建默认权限
        permissionService.createDefaultPermissions(subscription.getId());

        log.info("订阅创建成功: ID {}", subscription.getId());

        // 发送订阅创建事件（异步处理）
        sendSubscriptionCreatedEvent(subscription);

        return SubscriptionDTO.fromEntity(subscription);
    }

    /**
     * 更新订阅
     */
    @Transactional
    @CacheEvict(value = {CacheNames.USER_SUBSCRIPTIONS, CacheNames.SUBSCRIPTIONS}, key = "#subscriptionId")
    public SubscriptionDTO updateSubscription(Long subscriptionId, SubscriptionDTO.UpdateSubscriptionDTO updateDTO) {
        log.info("更新订阅: ID {}", subscriptionId);

        Subscription subscription = getSubscriptionById(subscriptionId);

        // 记录原始状态用于事件发送
        Subscription.SubscriptionStatus oldStatus = subscription.getStatus();

        // 更新字段
        boolean statusChanged = false;
        if (updateDTO.getStatus() != null && !updateDTO.getStatus().equals(oldStatus)) {
            subscription.setStatus(updateDTO.getStatus());
            statusChanged = true;
        }
        if (updateDTO.getEndDate() != null) {
            subscription.setEndDate(updateDTO.getEndDate());
        }
        if (updateDTO.getCallLimit() != null) {
            subscription.setCallLimit(updateDTO.getCallLimit());
        }
        if (updateDTO.getNotes() != null) {
            subscription.setNotes(updateDTO.getNotes());
        }

        subscription = subscriptionRepository.save(subscription);

        // 如果状态发生变更，发送状态变更事件
        if (statusChanged) {
            sendSubscriptionStatusChangedEvent(subscription, oldStatus, "手动更新");
        }

        log.info("订阅更新成功: ID {}", subscriptionId);
        return SubscriptionDTO.fromEntity(subscription);
    }

    /**
     * 取消订阅
     */
    @Transactional
    @CacheEvict(value = {CacheNames.USER_SUBSCRIPTIONS, CacheNames.SUBSCRIPTIONS}, key = "#subscriptionId")
    public void cancelSubscription(Long subscriptionId) {
        log.info("取消订阅: ID {}", subscriptionId);

        Subscription subscription = getSubscriptionById(subscriptionId);
        Subscription.SubscriptionStatus oldStatus = subscription.getStatus();

        subscription.setStatus(Subscription.SubscriptionStatus.CANCELLED);
        subscriptionRepository.save(subscription);

        // 撤销相关权限
        permissionService.revokeSubscriptionPermissions(subscriptionId);

        // 发送状态变更事件
        sendSubscriptionStatusChangedEvent(subscription, oldStatus, "用户取消");

        log.info("订阅取消成功: ID {}", subscriptionId);
    }

    /**
     * 延长订阅
     */
    @Transactional
    @CacheEvict(value = {CacheNames.USER_SUBSCRIPTIONS, CacheNames.SUBSCRIPTIONS}, key = "#subscriptionId")
    public void extendSubscription(Long subscriptionId, int days) {
        log.info("延长订阅: ID {}, 天数 {}", subscriptionId, days);

        Subscription subscription = getSubscriptionById(subscriptionId);
        LocalDateTime oldEndDate = subscription.getEndDate();
        LocalDateTime newEndDate = oldEndDate != null ?
                oldEndDate.plusDays(days) :
                LocalDateTime.now().plusDays(days);

        subscription.setEndDate(newEndDate);
        subscriptionRepository.save(subscription);

        // 发送续费事件
        sendSubscriptionRenewedEvent(subscription, oldEndDate, newEndDate,
                SubscriptionRenewedEvent.RenewalType.MANUAL, null, null);

        log.info("订阅延长成功: ID {}, 新到期时间 {}", subscriptionId, newEndDate);
    }

    /**
     * 续费订阅
     */
    @Transactional
    @CacheEvict(value = {CacheNames.USER_SUBSCRIPTIONS, CacheNames.SUBSCRIPTIONS}, key = "#subscriptionId")
    public void renewSubscription(Long subscriptionId, int days, java.math.BigDecimal amount,
                                 String currency, String paymentMethod, String paymentTransactionId) {
        log.info("续费订阅: ID {}, 天数 {}, 金额 {} {}", subscriptionId, days, amount, currency);

        Subscription subscription = getSubscriptionById(subscriptionId);
        LocalDateTime oldEndDate = subscription.getEndDate();
        LocalDateTime newEndDate = oldEndDate != null ?
                oldEndDate.plusDays(days) :
                LocalDateTime.now().plusDays(days);

        // 如果订阅已过期，从当前时间开始计算
        if (oldEndDate != null && oldEndDate.isBefore(LocalDateTime.now())) {
            newEndDate = LocalDateTime.now().plusDays(days);
        }

        subscription.setEndDate(newEndDate);

        // 如果订阅状态不是激活状态，则激活它
        Subscription.SubscriptionStatus oldStatus = subscription.getStatus();
        if (subscription.getStatus() != Subscription.SubscriptionStatus.ACTIVE) {
            subscription.setStatus(Subscription.SubscriptionStatus.ACTIVE);
        }

        subscription = subscriptionRepository.save(subscription);

        // 发送续费事件
        sendSubscriptionRenewedEvent(subscription, oldEndDate, newEndDate,
                SubscriptionRenewedEvent.RenewalType.MANUAL, amount, currency);

        // 如果状态发生变更，发送状态变更事件
        if (!oldStatus.equals(subscription.getStatus())) {
            sendSubscriptionStatusChangedEvent(subscription, oldStatus, "续费激活");
        }

        log.info("订阅续费成功: ID {}, 新到期时间 {}", subscriptionId, newEndDate);
    }

    /**
     * 自动续费订阅
     */
    @Transactional
    public void autoRenewSubscription(Long subscriptionId, int days, java.math.BigDecimal amount,
                                     String currency, String paymentMethod, String paymentTransactionId) {
        log.info("自动续费订阅: ID {}, 天数 {}, 金额 {} {}", subscriptionId, days, amount, currency);

        Subscription subscription = getSubscriptionById(subscriptionId);
        LocalDateTime oldEndDate = subscription.getEndDate();
        LocalDateTime newEndDate = oldEndDate != null ?
                oldEndDate.plusDays(days) :
                LocalDateTime.now().plusDays(days);

        subscription.setEndDate(newEndDate);
        subscription = subscriptionRepository.save(subscription);

        // 发送自动续费事件
        sendSubscriptionRenewedEvent(subscription, oldEndDate, newEndDate,
                SubscriptionRenewedEvent.RenewalType.AUTO, amount, currency);

        log.info("订阅自动续费成功: ID {}, 新到期时间 {}", subscriptionId, newEndDate);
    }

    /**
     * 获取订阅详情
     */
    @Cacheable(value = CacheNames.SUBSCRIPTIONS, key = "#subscriptionId")
    public SubscriptionDTO getSubscription(Long subscriptionId) {
        Subscription subscription = getSubscriptionById(subscriptionId);
        return SubscriptionDTO.fromEntity(subscription);
    }

    /**
     * 获取用户订阅列表
     */
    @Cacheable(value = CacheNames.USER_SUBSCRIPTIONS, key = "#userId")
    public List<SubscriptionDTO> getUserSubscriptions(Long userId) {
        List<Subscription> subscriptions = subscriptionRepository.findByUserId(userId);
        return subscriptions.stream()
                .map(SubscriptionDTO::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * 获取用户有效订阅列表
     */
    @Cacheable(value = CacheNames.USER_SUBSCRIPTIONS, key = "'valid:' + #userId")
    public List<SubscriptionDTO> getUserValidSubscriptions(Long userId) {
        List<Subscription> subscriptions = subscriptionRepository.findValidSubscriptionsByUser(userId, LocalDateTime.now());
        return subscriptions.stream()
                .map(SubscriptionDTO::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * 检查用户是否有有效订阅
     */
    @Cacheable(value = CacheNames.USER_SUBSCRIPTIONS, key = "'check:' + #userId + ':' + #serviceConfigId")
    public boolean hasValidSubscription(Long userId, Long serviceConfigId) {
        return subscriptionRepository.hasValidSubscription(userId, serviceConfigId, LocalDateTime.now());
    }

    /**
     * 记录API调用
     */
    @Transactional
    public void recordApiCall(Long userId, Long serviceConfigId) {
        // 查找有效订阅
        Subscription subscription = subscriptionRepository.findByUserIdAndServiceConfigId(userId, serviceConfigId)
                .orElseThrow(() -> new NexusException.ResourceNotFoundException("订阅不存在"));

        if (!subscription.isValid()) {
            throw new NexusException.BusinessException("订阅无效或已过期");
        }

        // 检查调用限制
        if (!subscription.hasRemainingCalls()) {
            throw new NexusException.BusinessException("API调用次数已达上限");
        }

        // 增加使用次数
        subscription.incrementUsedCalls();
        subscriptionRepository.save(subscription);

        // 记录使用统计
        usageTrackingService.recordUsage(subscription.getId(), 1);

        // 清除相关缓存
        evictUserSubscriptionCache(userId);
    }

    /**
     * 批量处理过期订阅
     */
    @Transactional
    public void processExpiredSubscriptions() {
        log.info("开始处理过期订阅");

        List<Subscription> expiredSubscriptions = subscriptionRepository.findExpiredSubscriptions(LocalDateTime.now());
        
        for (Subscription subscription : expiredSubscriptions) {
            try {
                Subscription.SubscriptionStatus oldStatus = subscription.getStatus();
                subscription.setStatus(Subscription.SubscriptionStatus.EXPIRED);
                subscriptionRepository.save(subscription);

                // 撤销相关权限
                permissionService.revokeSubscriptionPermissions(subscription.getId());

                // 发送状态变更事件
                sendSubscriptionStatusChangedEvent(subscription, oldStatus, "自动过期");

                log.info("订阅已过期: ID {}", subscription.getId());
            } catch (Exception e) {
                log.error("处理过期订阅失败: ID {} - {}", subscription.getId(), e.getMessage());
            }
        }

        log.info("过期订阅处理完成，共处理 {} 个订阅", expiredSubscriptions.size());
    }

    /**
     * 获取即将过期的订阅
     */
    public List<SubscriptionDTO> getExpiringSoonSubscriptions(int days) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime threshold = now.plusDays(days);
        
        List<Subscription> subscriptions = subscriptionRepository.findExpiringSoonSubscriptions(now, threshold);
        return subscriptions.stream()
                .map(SubscriptionDTO::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * 获取使用量统计
     */
    public List<SubscriptionDTO.SubscriptionUsageDTO> getUsageStatistics() {
        List<Object[]> statistics = subscriptionRepository.getSubscriptionUsageStatistics();
        
        return statistics.stream()
                .map(stat -> SubscriptionDTO.SubscriptionUsageDTO.builder()
                        .serviceName((String) stat[0])
                        .usedCalls((Long) stat[1])
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 重置订阅使用次数
     */
    @Transactional
    @CacheEvict(value = {CacheNames.USER_SUBSCRIPTIONS, CacheNames.SUBSCRIPTIONS}, key = "#subscriptionId")
    public void resetSubscriptionUsage(Long subscriptionId) {
        log.info("重置订阅使用次数: ID {}", subscriptionId);

        Subscription subscription = getSubscriptionById(subscriptionId);
        subscription.resetUsedCalls();
        subscriptionRepository.save(subscription);

        log.info("订阅使用次数重置成功: ID {}", subscriptionId);
    }

    /**
     * 获取订阅实体
     */
    private Subscription getSubscriptionById(Long subscriptionId) {
        return subscriptionRepository.findById(subscriptionId)
                .orElseThrow(() -> new NexusException.ResourceNotFoundException("订阅不存在: " + subscriptionId));
    }

    /**
     * 清除用户订阅缓存
     */
    private void evictUserSubscriptionCache(Long userId) {
        // 这里可以使用CacheManager来清除特定的缓存项
        // 简化实现，实际项目中可以更精确地控制缓存清除
    }

    /**
     * 获取订阅统计信息
     */
    public SubscriptionStatistics getSubscriptionStatistics() {
        List<Object[]> statusStats = subscriptionRepository.countSubscriptionsByStatus();
        
        SubscriptionStatistics.SubscriptionStatisticsBuilder builder = SubscriptionStatistics.builder();
        
        long totalSubscriptions = 0;
        for (Object[] stat : statusStats) {
            Subscription.SubscriptionStatus status = (Subscription.SubscriptionStatus) stat[0];
            Long count = (Long) stat[1];
            totalSubscriptions += count;
            
            switch (status) {
                case ACTIVE:
                    builder.activeSubscriptions(count);
                    break;
                case EXPIRED:
                    builder.expiredSubscriptions(count);
                    break;
                case CANCELLED:
                    builder.cancelledSubscriptions(count);
                    break;
                case SUSPENDED:
                    builder.suspendedSubscriptions(count);
                    break;
            }
        }
        
        return builder.totalSubscriptions(totalSubscriptions).build();
    }

    /**
     * 订阅统计信息
     */
    @lombok.Data
    @lombok.Builder
    public static class SubscriptionStatistics {
        private long totalSubscriptions;
        private long activeSubscriptions;
        private long expiredSubscriptions;
        private long cancelledSubscriptions;
        private long suspendedSubscriptions;
        
        public double getActivePercentage() {
            return totalSubscriptions > 0 ? (double) activeSubscriptions / totalSubscriptions * 100.0 : 0.0;
        }
    }

    /**
     * 发送订阅创建事件
     */
    private void sendSubscriptionCreatedEvent(Subscription subscription) {
        try {
            // 获取服务名称（简化处理，实际应该从ServiceConfig获取）
            String serviceName = subscription.getServiceConfig() != null ?
                    "Service-" + subscription.getServiceConfig().getId() : "Unknown Service";

            SubscriptionCreatedEvent event = new SubscriptionCreatedEvent(
                    subscription.getId(),
                    subscription.getUser().getId().toString(),
                    subscription.getServiceConfig().getId(),
                    serviceName,
                    subscription.getStartDate(),
                    subscription.getEndDate(),
                    subscription.getCallLimit(),
                    subscription.getNotes()
            );

            event.setSourceService("nexus-subscription-service");

            boolean sent = messageProducerService.sendEventAsync(event);

            if (sent) {
                log.debug("订阅创建事件发送成功: subscriptionId={}", subscription.getId());
            } else {
                log.warn("订阅创建事件发送失败: subscriptionId={}", subscription.getId());
            }

        } catch (Exception e) {
            log.error("发送订阅创建事件异常: subscriptionId={} - {}",
                    subscription.getId(), e.getMessage(), e);
        }
    }

    /**
     * 发送订阅状态变更事件
     */
    private void sendSubscriptionStatusChangedEvent(Subscription subscription,
                                                   Subscription.SubscriptionStatus oldStatus,
                                                   String changeReason) {
        try {
            // 获取服务名称（简化处理）
            String serviceName = subscription.getServiceConfig() != null ?
                    "Service-" + subscription.getServiceConfig().getId() : "Unknown Service";

            SubscriptionStatusChangedEvent event = new SubscriptionStatusChangedEvent(
                    subscription.getId(),
                    subscription.getUser().getId().toString(),
                    serviceName,
                    oldStatus.name(),
                    subscription.getStatus().name(),
                    changeReason,
                    null // operatorId，如果是系统操作则为null
            );

            event.setSourceService("nexus-subscription-service");

            boolean sent = messageProducerService.sendEventAsync(event);

            if (sent) {
                log.debug("订阅状态变更事件发送成功: subscriptionId={}, {} -> {}",
                        subscription.getId(), oldStatus, subscription.getStatus());
            } else {
                log.warn("订阅状态变更事件发送失败: subscriptionId={}, {} -> {}",
                        subscription.getId(), oldStatus, subscription.getStatus());
            }

        } catch (Exception e) {
            log.error("发送订阅状态变更事件异常: subscriptionId={} - {}",
                    subscription.getId(), e.getMessage(), e);
        }
    }

    /**
     * 发送订阅续费事件
     */
    private void sendSubscriptionRenewedEvent(Subscription subscription,
                                             LocalDateTime oldEndDate,
                                             LocalDateTime newEndDate,
                                             SubscriptionRenewedEvent.RenewalType renewalType,
                                             java.math.BigDecimal amount,
                                             String currency) {
        try {
            // 获取服务名称（简化处理）
            String serviceName = subscription.getServiceConfig() != null ?
                    "Service-" + subscription.getServiceConfig().getId() : "Unknown Service";

            SubscriptionRenewedEvent event = new SubscriptionRenewedEvent(
                    subscription.getId(),
                    subscription.getUser().getId().toString(),
                    subscription.getServiceConfig().getId(),
                    serviceName,
                    renewalType,
                    oldEndDate,
                    newEndDate,
                    amount,
                    currency
            );

            event.setSourceService("nexus-subscription-service");

            boolean sent = messageProducerService.sendEventAsync(event);

            if (sent) {
                log.debug("订阅续费事件发送成功: subscriptionId={}, renewalType={}",
                        subscription.getId(), renewalType);
            } else {
                log.warn("订阅续费事件发送失败: subscriptionId={}, renewalType={}",
                        subscription.getId(), renewalType);
            }

        } catch (Exception e) {
            log.error("发送订阅续费事件异常: subscriptionId={} - {}",
                    subscription.getId(), e.getMessage(), e);
        }
    }
}
