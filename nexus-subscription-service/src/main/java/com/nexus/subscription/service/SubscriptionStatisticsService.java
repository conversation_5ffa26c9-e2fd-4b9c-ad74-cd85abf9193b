package com.nexus.subscription.service;

import com.nexus.common.entity.Subscription;
import com.nexus.subscription.repository.SubscriptionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 订阅统计聚合服务
 * 处理订阅相关的统计数据聚合和分析
 */
@Slf4j
@Service("localSubscriptionStatisticsService")
@RequiredArgsConstructor
public class SubscriptionStatisticsService {
    
    private final SubscriptionRepository subscriptionRepository;
    
    /**
     * 内存中的统计数据缓存
     * 实际生产环境中应该使用Redis或数据库
     */
    private final Map<String, AtomicLong> statisticsCache = new ConcurrentHashMap<>();
    
    /**
     * 收入统计缓存
     */
    private final Map<String, BigDecimal> revenueCache = new ConcurrentHashMap<>();
    
    /**
     * 统计数据的最后更新时间
     */
    private final Map<String, LocalDateTime> lastUpdateTime = new ConcurrentHashMap<>();
    
    /**
     * 更新订阅统计数据
     */
    public void updateSubscriptionStatistics(String statisticsType, String dimension, 
                                           Map<String, Number> increments, String timeWindow) {
        try {
            log.debug("更新订阅统计: type={}, dimension={}, timeWindow={}", 
                    statisticsType, dimension, timeWindow);
            
            String dateStr = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
            
            // 更新各种统计指标
            if (increments != null) {
                increments.forEach((metric, value) -> {
                    String key = buildStatisticsKey(statisticsType, metric, dimension, timeWindow, dateStr);
                    updateCounter(key, value.longValue());
                });
            }
            
            // 更新时间维度统计
            updateTimeDimensionStatistics(statisticsType, dimension, timeWindow);
            
            log.debug("订阅统计更新完成: type={}, dimension={}", statisticsType, dimension);
            
        } catch (Exception e) {
            log.error("更新订阅统计异常: type={}, dimension={} - {}", 
                    statisticsType, dimension, e.getMessage(), e);
        }
    }
    
    /**
     * 获取订阅概览统计
     */
    public SubscriptionOverviewStats getSubscriptionOverview() {
        try {
            LocalDateTime now = LocalDateTime.now();
            
            // 从数据库获取实时数据
            long totalSubscriptions = subscriptionRepository.count();
            long activeSubscriptions = subscriptionRepository.countByStatus(Subscription.SubscriptionStatus.ACTIVE);
            long expiredSubscriptions = subscriptionRepository.countByStatus(Subscription.SubscriptionStatus.EXPIRED);
            long cancelledSubscriptions = subscriptionRepository.countByStatus(Subscription.SubscriptionStatus.CANCELLED);
            long suspendedSubscriptions = subscriptionRepository.countByStatus(Subscription.SubscriptionStatus.SUSPENDED);
            
            // 计算今日新增订阅
            LocalDateTime todayStart = now.toLocalDate().atStartOfDay();
            long todayNewSubscriptions = subscriptionRepository.countByCreatedAtBetween(todayStart, now);
            
            // 计算本月新增订阅
            LocalDateTime monthStart = now.withDayOfMonth(1).toLocalDate().atStartOfDay();
            long monthlyNewSubscriptions = subscriptionRepository.countByCreatedAtBetween(monthStart, now);
            
            // 计算即将过期的订阅（7天内）
            LocalDateTime weekLater = now.plusDays(7);
            long expiringSoonSubscriptions = subscriptionRepository.countByEndDateBetweenAndStatus(
                    now, weekLater, Subscription.SubscriptionStatus.ACTIVE);
            
            return SubscriptionOverviewStats.builder()
                    .totalSubscriptions(totalSubscriptions)
                    .activeSubscriptions(activeSubscriptions)
                    .expiredSubscriptions(expiredSubscriptions)
                    .cancelledSubscriptions(cancelledSubscriptions)
                    .suspendedSubscriptions(suspendedSubscriptions)
                    .todayNewSubscriptions(todayNewSubscriptions)
                    .monthlyNewSubscriptions(monthlyNewSubscriptions)
                    .expiringSoonSubscriptions(expiringSoonSubscriptions)
                    .activeRate(totalSubscriptions > 0 ? (double) activeSubscriptions / totalSubscriptions * 100 : 0)
                    .build();
                    
        } catch (Exception e) {
            log.error("获取订阅概览统计异常: {}", e.getMessage(), e);
            return SubscriptionOverviewStats.builder().build();
        }
    }
    
    /**
     * 获取服务订阅统计
     */
    public Map<String, ServiceSubscriptionStats> getServiceSubscriptionStats() {
        try {
            // 从数据库获取按服务分组的统计数据
            List<Object[]> results = subscriptionRepository.getSubscriptionStatsByService();
            
            Map<String, ServiceSubscriptionStats> serviceStats = new HashMap<>();
            
            for (Object[] result : results) {
                Long serviceConfigId = (Long) result[0];
                String serviceName = (String) result[1];
                Long totalCount = (Long) result[2];
                Long activeCount = (Long) result[3];
                
                ServiceSubscriptionStats stats = ServiceSubscriptionStats.builder()
                        .serviceConfigId(serviceConfigId)
                        .serviceName(serviceName)
                        .totalSubscriptions(totalCount)
                        .activeSubscriptions(activeCount)
                        .inactiveSubscriptions(totalCount - activeCount)
                        .activeRate(totalCount > 0 ? (double) activeCount / totalCount * 100 : 0)
                        .build();
                
                serviceStats.put(serviceName, stats);
            }
            
            return serviceStats;
            
        } catch (Exception e) {
            log.error("获取服务订阅统计异常: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }
    
    /**
     * 获取用户订阅统计
     */
    public Map<String, UserSubscriptionStats> getUserSubscriptionStats(int limit) {
        try {
            // 获取订阅数量最多的用户
            List<Object[]> results = subscriptionRepository.getTopUsersBySubscriptionCount(limit);
            
            Map<String, UserSubscriptionStats> userStats = new HashMap<>();
            
            for (Object[] result : results) {
                Long userId = (Long) result[0];
                String username = (String) result[1];
                Long subscriptionCount = (Long) result[2];
                Long activeCount = (Long) result[3];
                
                UserSubscriptionStats stats = UserSubscriptionStats.builder()
                        .userId(userId)
                        .username(username)
                        .totalSubscriptions(subscriptionCount)
                        .activeSubscriptions(activeCount)
                        .inactiveSubscriptions(subscriptionCount - activeCount)
                        .build();
                
                userStats.put(username, stats);
            }
            
            return userStats;
            
        } catch (Exception e) {
            log.error("获取用户订阅统计异常: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }
    
    /**
     * 获取时间趋势统计
     */
    public Map<String, Long> getSubscriptionTrends(int days) {
        try {
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusDays(days);
            
            Map<String, Long> trends = new HashMap<>();
            
            // 按天统计新增订阅
            for (int i = 0; i < days; i++) {
                LocalDateTime dayStart = startTime.plusDays(i).toLocalDate().atStartOfDay();
                LocalDateTime dayEnd = dayStart.plusDays(1);
                
                long dailyCount = subscriptionRepository.countByCreatedAtBetween(dayStart, dayEnd);
                String dateKey = dayStart.format(DateTimeFormatter.ISO_LOCAL_DATE);
                trends.put(dateKey, dailyCount);
            }
            
            return trends;
            
        } catch (Exception e) {
            log.error("获取订阅趋势统计异常: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }
    
    /**
     * 获取收入统计
     */
    public RevenueStats getRevenueStats() {
        try {
            // 这里简化处理，实际应该从订单或支付表获取收入数据
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime monthStart = now.withDayOfMonth(1).toLocalDate().atStartOfDay();
            LocalDateTime yearStart = now.withDayOfYear(1).toLocalDate().atStartOfDay();
            
            // 模拟收入数据（实际应该从数据库查询）
            BigDecimal todayRevenue = getRevenueFromCache("today", BigDecimal.ZERO);
            BigDecimal monthlyRevenue = getRevenueFromCache("monthly", BigDecimal.ZERO);
            BigDecimal yearlyRevenue = getRevenueFromCache("yearly", BigDecimal.ZERO);
            
            return RevenueStats.builder()
                    .todayRevenue(todayRevenue)
                    .monthlyRevenue(monthlyRevenue)
                    .yearlyRevenue(yearlyRevenue)
                    .averageRevenuePerSubscription(calculateAverageRevenue())
                    .build();
                    
        } catch (Exception e) {
            log.error("获取收入统计异常: {}", e.getMessage(), e);
            return RevenueStats.builder().build();
        }
    }
    
    /**
     * 构建统计键
     */
    private String buildStatisticsKey(String type, String metric, String dimension, 
                                    String timeWindow, String date) {
        return String.format("%s:%s:%s:%s:%s", type, metric, dimension, timeWindow, date);
    }
    
    /**
     * 更新计数器
     */
    private void updateCounter(String key, long increment) {
        AtomicLong counter = statisticsCache.computeIfAbsent(key, k -> new AtomicLong(0));
        long newValue = counter.addAndGet(increment);
        lastUpdateTime.put(key, LocalDateTime.now());
        
        log.debug("统计计数器更新: key={}, increment={}, newValue={}", key, increment, newValue);
    }
    
    /**
     * 更新时间维度统计
     */
    private void updateTimeDimensionStatistics(String type, String dimension, String timeWindow) {
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
        
        // 更新小时、日、月统计
        updateCounter(buildStatisticsKey(type, "hourly_count", dimension, "hourly", dateStr), 1);
        updateCounter(buildStatisticsKey(type, "daily_count", dimension, "daily", dateStr), 1);
        updateCounter(buildStatisticsKey(type, "monthly_count", dimension, "monthly", dateStr), 1);
    }
    
    /**
     * 从缓存获取收入数据
     */
    private BigDecimal getRevenueFromCache(String key, BigDecimal defaultValue) {
        return revenueCache.getOrDefault(key, defaultValue);
    }
    
    /**
     * 计算平均每订阅收入
     */
    private BigDecimal calculateAverageRevenue() {
        try {
            long totalSubscriptions = subscriptionRepository.count();
            BigDecimal totalRevenue = getRevenueFromCache("yearly", BigDecimal.ZERO);
            
            if (totalSubscriptions > 0 && totalRevenue.compareTo(BigDecimal.ZERO) > 0) {
                return totalRevenue.divide(BigDecimal.valueOf(totalSubscriptions), 2, BigDecimal.ROUND_HALF_UP);
            }
            
            return BigDecimal.ZERO;
            
        } catch (Exception e) {
            log.error("计算平均收入异常: {}", e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }
    
    /**
     * 获取所有统计数据
     */
    public Map<String, Long> getAllStatistics() {
        Map<String, Long> result = new HashMap<>();
        statisticsCache.forEach((key, value) -> result.put(key, value.get()));
        return result;
    }
    
    /**
     * 清理过期统计数据
     */
    public void cleanupExpiredStatistics(int daysToKeep) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(daysToKeep);
        
        lastUpdateTime.entrySet().removeIf(entry -> {
            if (entry.getValue().isBefore(cutoffTime)) {
                statisticsCache.remove(entry.getKey());
                revenueCache.remove(entry.getKey());
                log.debug("清理过期统计数据: key={}", entry.getKey());
                return true;
            }
            return false;
        });
        
        log.info("统计数据清理完成，保留{}天内的数据", daysToKeep);
    }
    
    // 统计数据模型类
    
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class SubscriptionOverviewStats {
        private long totalSubscriptions;
        private long activeSubscriptions;
        private long expiredSubscriptions;
        private long cancelledSubscriptions;
        private long suspendedSubscriptions;
        private long todayNewSubscriptions;
        private long monthlyNewSubscriptions;
        private long expiringSoonSubscriptions;
        private double activeRate;
    }
    
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class ServiceSubscriptionStats {
        private Long serviceConfigId;
        private String serviceName;
        private long totalSubscriptions;
        private long activeSubscriptions;
        private long inactiveSubscriptions;
        private double activeRate;
    }
    
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class UserSubscriptionStats {
        private Long userId;
        private String username;
        private long totalSubscriptions;
        private long activeSubscriptions;
        private long inactiveSubscriptions;
    }
    
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class RevenueStats {
        private BigDecimal todayRevenue;
        private BigDecimal monthlyRevenue;
        private BigDecimal yearlyRevenue;
        private BigDecimal averageRevenuePerSubscription;
    }
}
