package com.nexus.subscription.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 使用统计跟踪服务
 * 负责记录和统计API使用情况
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UsageTrackingService {

    private final RedisTemplate<String, Object> redisTemplate;

    private static final String USAGE_KEY_PREFIX = "usage:";
    private static final String DAILY_USAGE_PREFIX = "daily_usage:";
    private static final String MONTHLY_USAGE_PREFIX = "monthly_usage:";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter MONTH_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM");

    /**
     * 记录使用情况
     */
    public void recordUsage(Long subscriptionId, long count) {
        log.debug("记录使用情况: 订阅ID {}, 次数 {}", subscriptionId, count);

        try {
            LocalDateTime now = LocalDateTime.now();
            String today = now.format(DATE_FORMATTER);
            String thisMonth = now.format(MONTH_FORMATTER);

            // 记录总使用量
            String totalKey = USAGE_KEY_PREFIX + "total:" + subscriptionId;
            redisTemplate.opsForValue().increment(totalKey, count);

            // 记录日使用量
            String dailyKey = DAILY_USAGE_PREFIX + subscriptionId + ":" + today;
            redisTemplate.opsForValue().increment(dailyKey, count);
            redisTemplate.expire(dailyKey, 90, TimeUnit.DAYS); // 保留90天

            // 记录月使用量
            String monthlyKey = MONTHLY_USAGE_PREFIX + subscriptionId + ":" + thisMonth;
            redisTemplate.opsForValue().increment(monthlyKey, count);
            redisTemplate.expire(monthlyKey, 365, TimeUnit.DAYS); // 保留1年

            // 记录小时使用量（用于实时监控）
            String hourlyKey = USAGE_KEY_PREFIX + "hourly:" + subscriptionId + ":" + 
                    now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd-HH"));
            redisTemplate.opsForValue().increment(hourlyKey, count);
            redisTemplate.expire(hourlyKey, 7, TimeUnit.DAYS); // 保留7天

        } catch (Exception e) {
            log.error("记录使用情况失败: 订阅ID {} - {}", subscriptionId, e.getMessage());
        }
    }

    /**
     * 获取总使用量
     */
    public long getTotalUsage(Long subscriptionId) {
        try {
            String totalKey = USAGE_KEY_PREFIX + "total:" + subscriptionId;
            Object value = redisTemplate.opsForValue().get(totalKey);
            return value != null ? Long.parseLong(value.toString()) : 0L;
        } catch (Exception e) {
            log.error("获取总使用量失败: 订阅ID {} - {}", subscriptionId, e.getMessage());
            return 0L;
        }
    }

    /**
     * 获取日使用量
     */
    public long getDailyUsage(Long subscriptionId, LocalDate date) {
        try {
            String dailyKey = DAILY_USAGE_PREFIX + subscriptionId + ":" + date.format(DATE_FORMATTER);
            Object value = redisTemplate.opsForValue().get(dailyKey);
            return value != null ? Long.parseLong(value.toString()) : 0L;
        } catch (Exception e) {
            log.error("获取日使用量失败: 订阅ID {} - {}", subscriptionId, e.getMessage());
            return 0L;
        }
    }

    /**
     * 获取今日使用量
     */
    public long getTodayUsage(Long subscriptionId) {
        return getDailyUsage(subscriptionId, LocalDate.now());
    }

    /**
     * 获取月使用量
     */
    public long getMonthlyUsage(Long subscriptionId, String month) {
        try {
            String monthlyKey = MONTHLY_USAGE_PREFIX + subscriptionId + ":" + month;
            Object value = redisTemplate.opsForValue().get(monthlyKey);
            return value != null ? Long.parseLong(value.toString()) : 0L;
        } catch (Exception e) {
            log.error("获取月使用量失败: 订阅ID {} - {}", subscriptionId, e.getMessage());
            return 0L;
        }
    }

    /**
     * 获取本月使用量
     */
    public long getThisMonthUsage(Long subscriptionId) {
        String thisMonth = LocalDate.now().format(MONTH_FORMATTER);
        return getMonthlyUsage(subscriptionId, thisMonth);
    }

    /**
     * 获取使用统计摘要
     */
    public UsageStatistics getUsageStatistics(Long subscriptionId) {
        return UsageStatistics.builder()
                .subscriptionId(subscriptionId)
                .totalUsage(getTotalUsage(subscriptionId))
                .todayUsage(getTodayUsage(subscriptionId))
                .thisMonthUsage(getThisMonthUsage(subscriptionId))
                .build();
    }

    /**
     * 获取最近N天的使用情况
     */
    public Map<String, Long> getRecentDailyUsage(Long subscriptionId, int days) {
        Map<String, Long> usage = new HashMap<>();
        LocalDate today = LocalDate.now();

        for (int i = 0; i < days; i++) {
            LocalDate date = today.minusDays(i);
            String dateStr = date.format(DATE_FORMATTER);
            long count = getDailyUsage(subscriptionId, date);
            usage.put(dateStr, count);
        }

        return usage;
    }

    /**
     * 重置使用统计
     */
    public void resetUsageStatistics(Long subscriptionId) {
        log.info("重置使用统计: 订阅ID {}", subscriptionId);

        try {
            // 重置总使用量
            String totalKey = USAGE_KEY_PREFIX + "total:" + subscriptionId;
            redisTemplate.delete(totalKey);

            // 清除相关的统计数据
            String pattern = "*:" + subscriptionId + ":*";
            redisTemplate.delete(redisTemplate.keys(DAILY_USAGE_PREFIX + pattern));
            redisTemplate.delete(redisTemplate.keys(MONTHLY_USAGE_PREFIX + pattern));
            redisTemplate.delete(redisTemplate.keys(USAGE_KEY_PREFIX + "hourly:" + pattern));

        } catch (Exception e) {
            log.error("重置使用统计失败: 订阅ID {} - {}", subscriptionId, e.getMessage());
        }
    }

    /**
     * 使用统计数据类
     */
    @lombok.Data
    @lombok.Builder
    public static class UsageStatistics {
        private Long subscriptionId;
        private long totalUsage;
        private long todayUsage;
        private long thisMonthUsage;
        private Map<String, Long> recentDailyUsage;
    }
}
