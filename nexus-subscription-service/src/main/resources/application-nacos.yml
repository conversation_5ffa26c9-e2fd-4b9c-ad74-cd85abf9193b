server:
  port: 8084
  servlet:
    context-path: /subscription

spring:
  application:
    name: nexus-subscription-service

  config:
    import: "optional:nacos:nexus-subscription-service.yml"

  # Spring Cloud Nacos配置
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        group: DEFAULT_GROUP
        service: ${spring.application.name}
        metadata:
          version: 1.0.0
          zone: default
      config:
        server-addr: 127.0.0.1:8848
        group: DEFAULT_GROUP
        file-extension: yml
        shared-configs:
          - data-id: nexus-common-config.yml
            group: DEFAULT_GROUP
            refresh: true

  # JPA配置 - 开发环境自动创建表
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        format_sql: true

# 本地开发时的fallback配置
---
spring:
  profiles: local
  datasource:
    url: **************************************
    username: nexus
    password: nexus123
    driver-class-name: org.postgresql.Driver
  redis:
    host: localhost
    port: 6379
    database: 4
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
