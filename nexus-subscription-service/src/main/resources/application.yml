server:
  port: 8084
  servlet:
    context-path: /subscription

spring:
  application:
    name: nexus-subscription-service

  profiles:
    active: nacos

  config:
    import: "nacos:nexus-subscription-service.yml"
  
  # 数据库配置
  datasource:
    url: *****************************************************************************************************************************************************************************
    username: neondb_owner
    password: npg_kc2S7QGCPbEh
    driver-class-name: org.postgresql.Driver
    hikari:
      pool-name: NexusSubscriptionPool
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      max-lifetime: 1200000
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
  
  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 3  # 使用不同的数据库
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 3000ms
  
  # RocketMQ配置
  rocketmq:
    name-server: localhost:9876
    producer:
      group: subscription-producer-group
      send-message-timeout: 3000
      retry-times-when-send-failed: 2
    consumer:
      pull-batch-size: 32
  
  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 1800000 # 30分钟
      cache-null-values: false

  # Spring Cloud Nacos配置
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        namespace: nexus-microservices
        group: DEFAULT_GROUP
        service: ${spring.application.name}
        metadata:
          version: 1.0.0
          zone: default
      config:
        server-addr: 127.0.0.1:8848
        namespace: nexus-microservices
        group: DEFAULT_GROUP
        file-extension: yml
        shared-configs:
          - data-id: nexus-common-config.yml
            group: DEFAULT_GROUP
            refresh: true

# 订阅服务配置
nexus:
  # 邮件服务配置
  email:
    enabled: true
    mock: true  # 开发环境使用模拟模式
    from: <EMAIL>
    from-name: Nexus微服务平台

  subscription:
    # 订阅管理配置
    management:
      # 默认订阅有效期（天）
      default-validity-days: 30
      # 订阅到期前提醒天数
      expiry-reminder-days: 7
      # 自动续费检查间隔（小时）
      auto-renewal-check-interval: 24
      # 最大订阅数量限制
      max-subscriptions-per-user: 10
    
    # 使用统计配置
    usage:
      # 统计数据保留天数
      retention-days: 90
      # 统计聚合间隔（分钟）
      aggregation-interval: 60
      # 实时统计更新间隔（秒）
      realtime-update-interval: 30
    
    # 权限管理配置
    permissions:
      # 权限缓存时间（秒）
      cache-ttl: 300
      # 权限检查超时时间（毫秒）
      check-timeout: 5000
      # 默认权限策略
      default-policy: DENY
    
    # 限流配置
    rate-limit:
      # 启用限流
      enabled: true
      # 默认限流策略
      default-strategy: TOKEN_BUCKET
      # 限流检查间隔（秒）
      check-interval: 60
    
    # 通知配置
    notification:
      # 启用通知
      enabled: true
      # 订阅到期通知
      expiry-notification: true
      # 使用量警告通知
      usage-warning-notification: true
      # 使用量警告阈值（百分比）
      usage-warning-threshold: 80

# Feign配置
feign:
  client:
    config:
      default:
        connect-timeout: 5000
        read-timeout: 10000
        logger-level: basic
      nexus-auth-service:
        connect-timeout: 3000
        read-timeout: 5000

# 日志配置
logging:
  level:
    com.nexus.subscription: DEBUG
    com.nexus.common: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{50}] - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{50}] - %msg%n"

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

# 异步任务配置
async:
  core-pool-size: 5
  max-pool-size: 20
  queue-capacity: 100
  thread-name-prefix: "subscription-async-"
  keep-alive-seconds: 60
