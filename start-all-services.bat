@echo off
echo ========================================
echo    Nexus Microservices Startup
echo ========================================
echo.

:: Check Java
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Java not found! Please install Java 8+
    pause
    exit /b 1
)

:: Create logs directory
if not exist "logs" mkdir logs

echo Compiling all services...
call mvn clean package -DskipTests
if %errorlevel% neq 0 (
    echo ERROR: Compilation failed!
    pause
    exit /b 1
)

echo.
echo Starting services...
echo.

:: Start Auth Service (8081)
if exist "nexus-auth-service\target\nexus-auth-service-0.0.1-SNAPSHOT.jar" (
    echo Starting Auth Service on port 8081...
    start "Auth-Service" java -Xmx256m -Xms128m -jar nexus-auth-service\target\nexus-auth-service-0.0.1-SNAPSHOT.jar
    timeout /t 10 /nobreak >nul
)

:: Start MCP Local Service (8082)
if exist "nexus-mcp-local-service\target\nexus-mcp-local-service-0.0.1-SNAPSHOT.jar" (
    echo Starting MCP Local Service on port 8082...
    start "MCP-Local-Service" java -Xmx256m -Xms128m -jar nexus-mcp-local-service\target\nexus-mcp-local-service-0.0.1-SNAPSHOT.jar
    timeout /t 10 /nobreak >nul
)

:: Start MCP Remote Service (8083)
if exist "nexus-mcp-remote-service\target\nexus-mcp-remote-service-0.0.1-SNAPSHOT.jar" (
    echo Starting MCP Remote Service on port 8083...
    start "MCP-Remote-Service" java -Xmx256m -Xms128m -jar nexus-mcp-remote-service\target\nexus-mcp-remote-service-0.0.1-SNAPSHOT.jar
    timeout /t 10 /nobreak >nul
)

:: Start Subscription Service (8084)
if exist "nexus-subscription-service\target\nexus-subscription-service-0.0.1-SNAPSHOT.jar" (
    echo Starting Subscription Service on port 8084...
    start "Subscription-Service" java -Xmx256m -Xms128m -jar nexus-subscription-service\target\nexus-subscription-service-0.0.1-SNAPSHOT.jar
    timeout /t 10 /nobreak >nul
)

:: Start Market Service (8085)
if exist "nexus-market-service\target\nexus-market-service-0.0.1-SNAPSHOT.jar" (
    echo Starting Market Service on port 8085...
    start "Market-Service" java -Xmx512m -Xms256m -jar nexus-market-service\target\nexus-market-service-0.0.1-SNAPSHOT.jar
    timeout /t 10 /nobreak >nul
)

:: Start Chain Service (8086)
if exist "nexus-chain-service\target\nexus-chain-service-0.0.1-SNAPSHOT.jar" (
    echo Starting Chain Service on port 8086...
    start "Chain-Service" java -Xmx256m -Xms128m -jar nexus-chain-service\target\nexus-chain-service-0.0.1-SNAPSHOT.jar
    timeout /t 10 /nobreak >nul
)

:: Start Realtime Service (8087)
if exist "nexus-realtime-service\target\nexus-realtime-service-0.0.1-SNAPSHOT.jar" (
    echo Starting Realtime Service on port 8087...
    start "Realtime-Service" java -Xmx256m -Xms128m -jar nexus-realtime-service\target\nexus-realtime-service-0.0.1-SNAPSHOT.jar
    timeout /t 10 /nobreak >nul
)

:: Start Gateway (8080)
if exist "nexus-gateway\target\nexus-gateway-0.0.1-SNAPSHOT.jar" (
    echo Starting Gateway on port 8080...
    start "Gateway" java -Xmx512m -Xms256m -jar nexus-gateway\target\nexus-gateway-0.0.1-SNAPSHOT.jar
    timeout /t 10 /nobreak >nul
)

echo.
echo ========================================
echo    All Services Started!
echo ========================================
echo.
echo Service URLs:
echo   - Gateway:      http://localhost:8080
echo   - Auth:         http://localhost:8081
echo   - MCP Local:    http://localhost:8082
echo   - MCP Remote:   http://localhost:8083
echo   - Subscription: http://localhost:8084
echo   - Market:       http://localhost:8085
echo   - Chain:        http://localhost:8086
echo   - Realtime:     http://localhost:8087
echo.
echo Check Task Manager for Java processes
echo.
pause
