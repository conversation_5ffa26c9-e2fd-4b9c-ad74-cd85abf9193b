#!/usr/bin/env node

const { spawn } = require('child_process');
const readline = require('readline');

// 启动memory服务
const mcpProcess = spawn('npx', ['-y', '@modelcontextprotocol/server-memory'], {
    stdio: ['pipe', 'pipe', 'pipe']
});

let requestId = 1;

// 发送JSON-RPC请求
function sendRequest(method, params = null) {
    const request = {
        jsonrpc: "2.0",
        id: requestId++,
        method: method,
        params: params
    };
    
    const message = JSON.stringify(request) + '\n';
    console.log('发送请求:', message.trim());
    mcpProcess.stdin.write(message);
}

// 处理响应
mcpProcess.stdout.on('data', (data) => {
    const lines = data.toString().split('\n').filter(line => line.trim());
    lines.forEach(line => {
        try {
            const response = JSON.parse(line);
            console.log('收到响应:', JSON.stringify(response, null, 2));
        } catch (e) {
            console.log('原始输出:', line);
        }
    });
});

// 处理错误
mcpProcess.stderr.on('data', (data) => {
    console.log('错误输出:', data.toString());
});

// 进程退出
mcpProcess.on('close', (code) => {
    console.log(`MCP进程退出，代码: ${code}`);
    process.exit(code);
});

// 测试序列
setTimeout(() => {
    console.log('\n=== 1. 发送初始化请求 ===');
    sendRequest('initialize', {
        protocolVersion: "2024-11-05",
        capabilities: {
            roots: { listChanged: true },
            sampling: {}
        },
        clientInfo: {
            name: "Test MCP Client",
            version: "1.0.0"
        }
    });
}, 1000);

setTimeout(() => {
    console.log('\n=== 2. 发送初始化完成通知 ===');
    const notification = {
        jsonrpc: "2.0",
        method: "notifications/initialized"
    };
    const message = JSON.stringify(notification) + '\n';
    console.log('发送通知:', message.trim());
    mcpProcess.stdin.write(message);
}, 2000);

setTimeout(() => {
    console.log('\n=== 3. 获取工具列表 ===');
    sendRequest('tools/list');
}, 3000);

setTimeout(() => {
    console.log('\n=== 4. 获取资源列表 ===');
    sendRequest('resources/list');
}, 4000);

setTimeout(() => {
    console.log('\n=== 5. 获取服务器信息 ===');
    sendRequest('server/info');
}, 5000);

// 10秒后退出
setTimeout(() => {
    console.log('\n=== 测试完成，退出 ===');
    mcpProcess.kill();
}, 10000);

// 处理Ctrl+C
process.on('SIGINT', () => {
    console.log('\n收到中断信号，退出...');
    mcpProcess.kill();
    process.exit(0);
});
