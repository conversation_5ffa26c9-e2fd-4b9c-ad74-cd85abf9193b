#!/usr/bin/env python3

import subprocess
import json
import time
import sys
import threading

class MCPClient:
    def __init__(self):
        self.request_id = 1
        self.process = None
        
    def start_memory_server(self):
        """启动memory服务"""
        print("启动memory服务...")
        self.process = subprocess.Popen(
            ['npx', '-y', '@modelcontextprotocol/server-memory'],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=0
        )
        
        # 启动输出读取线程
        self.output_thread = threading.Thread(target=self.read_output)
        self.output_thread.daemon = True
        self.output_thread.start()
        
        # 启动错误读取线程
        self.error_thread = threading.Thread(target=self.read_error)
        self.error_thread.daemon = True
        self.error_thread.start()
        
        time.sleep(2)  # 等待服务启动
        
    def read_output(self):
        """读取标准输出"""
        while self.process and self.process.poll() is None:
            try:
                line = self.process.stdout.readline()
                if line:
                    line = line.strip()
                    if line:
                        print(f"收到响应: {line}")
                        try:
                            response = json.loads(line)
                            print(f"解析响应: {json.dumps(response, indent=2, ensure_ascii=False)}")
                        except json.JSONDecodeError:
                            print(f"原始输出: {line}")
            except Exception as e:
                print(f"读取输出错误: {e}")
                break
                
    def read_error(self):
        """读取错误输出"""
        while self.process and self.process.poll() is None:
            try:
                line = self.process.stderr.readline()
                if line:
                    line = line.strip()
                    if line:
                        print(f"错误输出: {line}")
            except Exception as e:
                print(f"读取错误输出错误: {e}")
                break
    
    def send_request(self, method, params=None):
        """发送JSON-RPC请求"""
        request = {
            "jsonrpc": "2.0",
            "id": self.request_id,
            "method": method
        }
        if params is not None:
            request["params"] = params
            
        message = json.dumps(request) + '\n'
        print(f"\n发送请求: {message.strip()}")
        
        try:
            self.process.stdin.write(message)
            self.process.stdin.flush()
            self.request_id += 1
        except Exception as e:
            print(f"发送请求失败: {e}")
    
    def send_notification(self, method, params=None):
        """发送通知"""
        notification = {
            "jsonrpc": "2.0",
            "method": method
        }
        if params is not None:
            notification["params"] = params
            
        message = json.dumps(notification) + '\n'
        print(f"\n发送通知: {message.strip()}")
        
        try:
            self.process.stdin.write(message)
            self.process.stdin.flush()
        except Exception as e:
            print(f"发送通知失败: {e}")
    
    def test_sequence(self):
        """执行测试序列"""
        print("\n=== 1. 发送初始化请求 ===")
        self.send_request('initialize', {
            "protocolVersion": "2024-11-05",
            "capabilities": {
                "roots": {"listChanged": True},
                "sampling": {}
            },
            "clientInfo": {
                "name": "Test MCP Client",
                "version": "1.0.0"
            }
        })
        
        time.sleep(2)
        
        print("\n=== 2. 发送初始化完成通知 ===")
        self.send_notification("notifications/initialized")
        
        time.sleep(1)
        
        print("\n=== 3. 获取工具列表 ===")
        self.send_request('tools/list')
        
        time.sleep(2)
        
        print("\n=== 4. 获取资源列表 ===")
        self.send_request('resources/list')
        
        time.sleep(2)
        
        print("\n=== 5. 获取服务器信息 ===")
        self.send_request('server/info')
        
        time.sleep(2)
        
    def cleanup(self):
        """清理资源"""
        if self.process:
            print("\n=== 清理资源 ===")
            self.process.terminate()
            try:
                self.process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.process.kill()

def main():
    client = MCPClient()
    
    try:
        client.start_memory_server()
        client.test_sequence()
        
        print("\n=== 等待响应... ===")
        time.sleep(5)
        
    except KeyboardInterrupt:
        print("\n收到中断信号...")
    except Exception as e:
        print(f"测试失败: {e}")
    finally:
        client.cleanup()
        print("测试完成")

if __name__ == "__main__":
    main()
