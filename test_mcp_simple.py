#!/usr/bin/env python3

import json
import sys

def create_initialize_request():
    """创建初始化请求"""
    return {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "initialize",
        "params": {
            "protocolVersion": "2024-11-05",
            "capabilities": {
                "roots": {"listChanged": True},
                "sampling": {}
            },
            "clientInfo": {
                "name": "Test MCP Client",
                "version": "1.0.0"
            }
        }
    }

def create_initialized_notification():
    """创建初始化完成通知"""
    return {
        "jsonrpc": "2.0",
        "method": "notifications/initialized"
    }

def create_tools_list_request():
    """创建工具列表请求"""
    return {
        "jsonrpc": "2.0",
        "id": 2,
        "method": "tools/list"
    }

def create_resources_list_request():
    """创建资源列表请求"""
    return {
        "jsonrpc": "2.0",
        "id": 3,
        "method": "resources/list"
    }

def main():
    print("=== MCP JSON-RPC 测试消息 ===\n")
    
    print("1. 初始化请求:")
    init_req = create_initialize_request()
    print(json.dumps(init_req, indent=2, ensure_ascii=False))
    print(f"单行格式: {json.dumps(init_req, ensure_ascii=False)}")
    
    print("\n2. 初始化完成通知:")
    init_notif = create_initialized_notification()
    print(json.dumps(init_notif, indent=2, ensure_ascii=False))
    print(f"单行格式: {json.dumps(init_notif, ensure_ascii=False)}")
    
    print("\n3. 工具列表请求:")
    tools_req = create_tools_list_request()
    print(json.dumps(tools_req, indent=2, ensure_ascii=False))
    print(f"单行格式: {json.dumps(tools_req, ensure_ascii=False)}")
    
    print("\n4. 资源列表请求:")
    resources_req = create_resources_list_request()
    print(json.dumps(resources_req, indent=2, ensure_ascii=False))
    print(f"单行格式: {json.dumps(resources_req, ensure_ascii=False)}")
    
    print("\n=== 测试说明 ===")
    print("1. 启动memory服务: npx -y @modelcontextprotocol/server-memory")
    print("2. 将上述JSON消息逐个发送到服务的stdin")
    print("3. 观察stdout的响应")
    print("4. memory服务应该响应初始化请求，但可能不响应tools/list和resources/list")

if __name__ == "__main__":
    main()
